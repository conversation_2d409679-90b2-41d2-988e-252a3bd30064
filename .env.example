# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Email Configuration (SMTP)
# For Gmail:
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# For Outlook/Hotmail:
# SMTP_HOST=smtp-mail.outlook.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-password

# For Yahoo:
# SMTP_HOST=smtp.mail.yahoo.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# For Custom SMTP (like your hosting provider):
# SMTP_HOST=mail.yourdomain.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-password

# Email Settings
EMAIL_FROM_NAME="Mispri"

# Website URL (for email links)
NEXT_PUBLIC_WEBSITE_URL=http://localhost:3001

# Other environment variables...
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
