# Gmail SMTP <NAME_EMAIL>
# Copy these settings to your .env file

# Database (keep your existing DATABASE_URL)
DATABASE_URL="your-existing-database-url"

# Gmail SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=kgbw bcef kbhr mlwf

# Email Settings
EMAIL_FROM_NAME="Mispri"

# Website URL (update for production)
NEXT_PUBLIC_WEBSITE_URL=http://localhost:3001

# Other settings (keep your existing values)
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# Instructions:
# 1. Go to https://myaccount.google.com/
# 2. Security → 2-Step Verification → App passwords
# 3. Generate app password for "Mail"
# 4. Replace "your-gmail-app-password-here" with the 16-character password
# 5. Copy these settings to your .env file
# 6. Restart your application
