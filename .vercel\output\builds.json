{"//": "This file was generated by the `vercel build` command. It is not part of the Build Output API.", "target": "production", "argv": ["C:\\Program Files\\nodejs\\node.exe", "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\vercel\\dist\\vc.js", "build", "--prod"], "builds": [{"require": "@vercel/next", "requirePath": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\vercel\\node_modules\\@vercel\\next\\dist\\index", "apiVersion": 2, "src": "package.json", "use": "@vercel/next", "error": {"name": "Error", "stack": "Error: Command \"npm run build\" exited with 1\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\vercel\\node_modules\\@vercel\\build-utils\\dist\\index.js:23106:9)\n    at ChildProcess.emit (node:events:518:28)\n    at ChildProcess.emit (node:domain:489:12)\n    at cp.emit (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\vercel\\node_modules\\@vercel\\build-utils\\dist\\index.js:14277:29)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)\n    at Process.callbackTrampoline (node:internal/async_hooks:130:17)", "message": "Command \"npm run build\" exited with 1", "hideStackTrace": true, "code": "BUILD_UTILS_SPAWN_1"}}], "error": {"name": "Error", "stack": "Error: Command \"npm run build\" exited with 1\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\vercel\\node_modules\\@vercel\\build-utils\\dist\\index.js:23106:9)\n    at ChildProcess.emit (node:events:518:28)\n    at ChildProcess.emit (node:domain:489:12)\n    at cp.emit (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\vercel\\node_modules\\@vercel\\build-utils\\dist\\index.js:14277:29)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)\n    at Process.callbackTrampoline (node:internal/async_hooks:130:17)", "message": "Command \"npm run build\" exited with 1", "hideStackTrace": true, "code": "BUILD_UTILS_SPAWN_1"}}