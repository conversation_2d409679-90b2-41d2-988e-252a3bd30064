# Order Management System

This document explains the role-based order visibility and assignment system implemented in the Mispri admin panel.

## 🎯 **System Overview**

The order management system follows a strict role-based access control where:
- **Super Admin** manually assigns orders to stores
- **Store Managers/Staff** can only see orders assigned to their specific store
- Orders progress through defined status stages

## 📋 **Order Status Flow**

### **Status Progression:**
```
Customer Places Order → PENDING_ASSIGNMENT → ASSIGNED → IN_PROGRESS → COMPLETED → DELIVERED
                                    ↓
                               CANCELLED (can happen at any stage)
```

### **Status Definitions:**

| Status | Description | Who Can Set | Visibility |
|--------|-------------|-------------|------------|
| `PENDING_ASSIGNMENT` | Order placed but not assigned to any store | System (automatic) | Super Admin only |
| `ASSIGNED` | Order assigned to store by super admin | Super Admin | Super Admin + Assigned Store |
| `IN_PROGRESS` | Store is working on the order | Store Manager/Staff | Super Admin + Assigned Store |
| `COMPLETED` | Order completed by store | Store Manager/Staff | Super Admin + Assigned Store |
| `DELIVERED` | Order delivered to customer | Store Manager/Staff | Super Admin + Assigned Store |
| `CANCELLED` | Order cancelled | Super Admin/Store Manager | Super Admin + Assigned Store |

## 👥 **Role-Based Access Control**

### **Super Admin (ADMIN Role)**
**Permissions:**
- ✅ View ALL orders regardless of assignment status
- ✅ Assign orders to any store
- ✅ Reassign orders between stores
- ✅ Unassign orders (reset to PENDING_ASSIGNMENT)
- ✅ Update order status to any value
- ✅ View order assignment history

**Order Visibility:**
- All online orders
- Orders in all statuses including PENDING_ASSIGNMENT
- Complete order details including assignment information

### **Store Manager (STORE_MANAGER Role)**
**Permissions:**
- ✅ View orders assigned to their store only
- ✅ Update status of assigned orders (ASSIGNED → IN_PROGRESS → COMPLETED → DELIVERED)
- ✅ Cancel orders assigned to their store
- ❌ Cannot see unassigned orders (PENDING_ASSIGNMENT)
- ❌ Cannot assign orders to stores
- ❌ Cannot see orders from other stores

**Order Visibility:**
- Only orders where `storeId = their_store_id`
- Only orders with status ≠ PENDING_ASSIGNMENT
- Order details relevant to store operations

### **Staff (STAFF Role)**
**Permissions:**
- Same as Store Manager but limited to their assigned store
- ✅ View orders assigned to their store
- ✅ Update order status (limited progression)
- ❌ Cannot see unassigned orders
- ❌ Cannot assign orders

## 🔄 **Order Assignment Workflow**

### **1. Customer Places Order**
```javascript
// New orders start with PENDING_ASSIGNMENT status
{
  status: 'PENDING_ASSIGNMENT',
  storeId: null,
  assignedBy: null,
  orderType: 'ONLINE'
}
```

### **2. Super Admin Assigns Order**
```javascript
// POST /api/orders/assign
{
  orderId: "order_id",
  storeId: "store_id", 
  assignedBy: "admin_user_id"
}

// Order updated to:
{
  status: 'ASSIGNED',
  storeId: "store_id",
  assignedBy: "admin_user_id"
}
```

### **3. Store Processes Order**
```javascript
// PUT /api/orders/[id]/status
{
  status: 'IN_PROGRESS',
  userRole: 'STORE_MANAGER',
  storeId: "store_id"
}
```

## 🛡️ **Security Implementation**

### **API Endpoint Security**

#### **GET /api/orders**
```javascript
// Role-based filtering
if (userRole === 'ADMIN') {
  // See all online orders
  whereClause.orderType = 'ONLINE';
} else if (userRole === 'STORE_MANAGER' || userRole === 'STAFF') {
  // Only assigned orders for their store
  whereClause.storeId = storeId;
  whereClause.status = { not: 'PENDING_ASSIGNMENT' };
}
```

#### **PUT /api/orders/[id]/status**
```javascript
// Verify store ownership
if (userRole === 'STORE_MANAGER' || userRole === 'STAFF') {
  if (!order.storeId || order.storeId !== storeId) {
    return 403; // Forbidden
  }
}
```

## 📊 **Database Schema**

### **Order Model**
```prisma
model Order {
  id              String        @id @default(cuid())
  customerId      String        @map("customer_id")
  storeId         String?       @map("store_id") // Store assigned by super admin
  status          OrderStatus   @default(PENDING_ASSIGNMENT)
  assignedBy      String?       @map("assigned_by") // Super admin who assigned
  orderType       OrderType     @default(ONLINE)
  // ... other fields
}
```

### **OrderStatus Enum**
```prisma
enum OrderStatus {
  PENDING_ASSIGNMENT  // Order placed but not assigned to any store
  ASSIGNED           // Order assigned to store by super admin
  IN_PROGRESS        // Store is working on the order
  COMPLETED          // Order completed by store
  DELIVERED          // Order delivered to customer
  CANCELLED          // Order cancelled
}
```

## 🔧 **API Endpoints**

### **Order Assignment**
- `POST /api/orders/assign` - Assign order to store (Super Admin only)
- `PUT /api/orders/assign` - Update/reassign order (Super Admin only)

### **Order Status Management**
- `PUT /api/orders/[id]/status` - Update order status (Role-based)

### **Order Retrieval**
- `GET /api/orders` - Get orders with role-based filtering
- `GET /api/orders/[id]` - Get specific order (if accessible)

## 🎨 **UI Components**

### **Order List**
- **Admin View**: Shows all orders with assignment controls
- **Store View**: Shows only assigned orders with status update controls

### **Order Detail Modal**
- **Admin Section**: Store assignment information
- **Store Section**: Status update controls
- **Customer Section**: Customer and delivery information
- **Items Section**: Order items and totals

### **Status Badges**
- Color-coded status indicators
- Role-appropriate status options
- Progress indicators

## 🚀 **Implementation Status**

### ✅ **Completed**
- [x] Updated OrderStatus enum with assignment statuses
- [x] Role-based API filtering for order visibility
- [x] Order assignment API endpoints
- [x] Order status update API with role validation
- [x] UI components for status management
- [x] Order detail modal with role-based sections

### 🔄 **Pending Database Migration**
- [ ] Apply schema changes to production database
- [ ] Migrate existing order statuses
- [ ] Update existing orders with proper assignment status

## 📝 **Usage Examples**

### **Super Admin Workflow**
1. View all orders in admin panel
2. See orders with PENDING_ASSIGNMENT status
3. Click "Assign to Store" button
4. Select target store from dropdown
5. Order status changes to ASSIGNED
6. Order becomes visible to selected store

### **Store Manager Workflow**
1. Login to store panel
2. See only orders assigned to their store
3. Orders show status: ASSIGNED, IN_PROGRESS, COMPLETED, etc.
4. Update order status as work progresses
5. Mark as COMPLETED when ready
6. Mark as DELIVERED when customer receives order

This system ensures complete separation of concerns and maintains data security while providing efficient order management workflows for all user roles.
