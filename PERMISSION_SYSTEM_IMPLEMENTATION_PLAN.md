# 🛡️ Role-Based Access Control (RBAC) Implementation Plan

## 🚨 **CRITICAL SECURITY ISSUE IDENTIFIED**

**Current Problem**: All logged-in users can access ALL dashboard components and data, regardless of their role. This is a major security vulnerability.

**Impact**: 
- Staff can see admin-only data
- Store managers can access other stores' data
- No proper access control on sensitive information
- Potential data breaches and unauthorized actions

---

## 🎯 **Proposed Solution: Comprehensive RBAC System**

### **1. Permission-Based Access Control**

#### **User Roles & Permissions Matrix:**

| Feature | ADMIN | WAREHOUSE_MANAGER | STORE_MANAGER | STAFF |
|---------|-------|------------------|---------------|-------|
| **Dashboard** | ✅ Full Access | ✅ Limited View | ✅ Store View | ✅ Basic View |
| **Products** | ✅ CRUD | ✅ Create/Edit | ✅ Edit Only | ❌ View Only |
| **Categories** | ✅ CRUD | ✅ Create/Edit | ❌ View Only | ❌ View Only |
| **Orders** | ✅ All Stores | ✅ All Stores | ✅ Own Store | ✅ Own Store |
| **Customers** | ✅ All Stores | ❌ No Access | ✅ Own Store | ✅ Own Store |
| **Users** | ✅ CRUD | ❌ No Access | ❌ No Access | ❌ No Access |
| **Stores** | ✅ CRUD + Access | ❌ View Only | ❌ No Access | ❌ No Access |
| **Inventory** | ✅ All Stores | ✅ All Stores | ✅ Own Store | ❌ View Only |
| **Raw Materials** | ✅ CRUD | ✅ CRUD | ❌ View Only | ❌ No Access |
| **Recipes** | ✅ CRUD | ✅ Create/Edit | ❌ View Only | ❌ No Access |
| **Production** | ✅ CRUD | ✅ CRUD | ❌ View Only | ❌ No Access |
| **Sales/POS** | ✅ All Stores | ✅ All Stores | ✅ Own Store | ✅ Own Store |
| **Purchases** | ✅ CRUD | ✅ CRUD | ❌ No Access | ❌ No Access |
| **Expenses** | ✅ All Stores | ❌ No Access | ✅ Own Store | ❌ No Access |
| **Wastage** | ✅ All Stores | ✅ All Stores | ✅ Own Store | ❌ No Access |
| **Reports** | ✅ All Reports | ✅ Inventory/Production | ✅ Store Reports | ❌ No Access |
| **Settings** | ✅ Full Access | ❌ No Access | ❌ No Access | ❌ No Access |

### **2. Store-Based Data Isolation**

#### **Data Access Rules:**
- **ADMIN**: Can see all stores' data
- **WAREHOUSE_MANAGER**: Can see all stores' data (for inventory management)
- **STORE_MANAGER**: Can only see their assigned store's data
- **STAFF**: Can only see their assigned store's data

#### **Implementation:**
- Filter API responses based on user's store assignment
- Hide navigation items user doesn't have access to
- Protect individual components with permission guards
- Validate all API requests for proper access rights

---

## 🔧 **Implementation Steps**

### **Phase 1: Core Permission System** ✅ CREATED
1. **Permission Definitions** (`src/lib/permissions/permissions.ts`)
   - Define all permissions and role mappings
   - Create permission checking functions
   - Store access validation logic

2. **Permission Hooks** (`src/lib/permissions/hooks.ts`)
   - React hooks for permission checking
   - Store access hooks
   - Navigation filtering hooks

3. **Permission Components** (`src/components/permissions/PermissionGuard.tsx`)
   - `<PermissionGuard>` component for conditional rendering
   - `<RoleGuard>` component for role-based access
   - `<StoreGuard>` component for store-based access
   - Higher-order components for page protection

4. **API Middleware** (`src/lib/permissions/api-middleware.ts`)
   - API route protection middleware
   - User extraction from requests
   - Data filtering based on permissions

### **Phase 2: Frontend Implementation** ⏳ PENDING
1. **Update Navigation**
   - Filter sidebar items based on permissions
   - Hide unauthorized menu items
   - Show role-appropriate dashboard

2. **Protect Dashboard Components**
   - Wrap components with permission guards
   - Filter data based on store access
   - Hide unauthorized buttons/actions

3. **Update Forms and Actions**
   - Disable unauthorized form fields
   - Hide create/edit/delete buttons based on permissions
   - Validate actions before execution

### **Phase 3: Backend Implementation** ⏳ PENDING
1. **Protect API Routes**
   - Add permission middleware to all API endpoints
   - Validate user permissions before data access
   - Filter responses based on store access

2. **Database Query Filtering**
   - Modify queries to respect store assignments
   - Add permission checks to all CRUD operations
   - Implement audit logging for sensitive actions

3. **Authentication Enhancement**
   - Improve user session management
   - Add proper JWT token validation
   - Implement session-based permission caching

### **Phase 4: Testing & Validation** ⏳ PENDING
1. **Permission Testing**
   - Test all role combinations
   - Verify store data isolation
   - Validate API endpoint protection

2. **Security Audit**
   - Check for permission bypass vulnerabilities
   - Validate data access restrictions
   - Test unauthorized access attempts

---

## 📋 **Detailed Implementation Checklist**

### **Frontend Components to Update:**

#### **Navigation & Layout:**
- [ ] `src/components/dashboard/dashboard-layout.tsx` - Filter sidebar items
- [ ] `src/components/dashboard/simple-dashboard-layout.tsx` - Filter navigation
- [ ] Update header to show user role and permissions

#### **Dashboard Pages:**
- [ ] `src/app/dashboard/page.tsx` - Role-based dashboard content
- [ ] `src/app/dashboard/products/page.tsx` - Product management permissions
- [ ] `src/app/dashboard/categories/page.tsx` - Category management permissions
- [ ] `src/app/dashboard/orders/page.tsx` - Store-filtered orders
- [ ] `src/app/dashboard/customers/page.tsx` - Store-filtered customers
- [ ] `src/app/dashboard/users/page.tsx` - Admin-only access
- [ ] `src/app/dashboard/stores/page.tsx` - Store management permissions
- [ ] `src/app/dashboard/inventory/page.tsx` - Store-filtered inventory
- [ ] `src/app/dashboard/reports/page.tsx` - Role-based reports

#### **API Routes to Protect:**
- [ ] `src/app/api/products/route.ts` - Product CRUD permissions
- [ ] `src/app/api/categories/route.ts` - Category CRUD permissions
- [ ] `src/app/api/orders/route.ts` - Store-filtered orders
- [ ] `src/app/api/customers/route.ts` - Store-filtered customers
- [ ] `src/app/api/users/route.ts` - Admin-only access
- [ ] `src/app/api/stores/route.ts` - Store management permissions
- [ ] `src/app/api/inventory/route.ts` - Store-filtered inventory
- [ ] All other API endpoints

---

## 🚀 **Quick Implementation Priority**

### **IMMEDIATE (Critical Security):**
1. **Protect User Management** - Only admins should access user management
2. **Filter Store Data** - Users should only see their store's data
3. **Hide Unauthorized Navigation** - Remove menu items users can't access

### **HIGH PRIORITY:**
1. **Protect API Endpoints** - Add permission middleware to all APIs
2. **Component-Level Guards** - Wrap sensitive components with permission guards
3. **Role-Based Dashboard** - Show different dashboard content based on role

### **MEDIUM PRIORITY:**
1. **Advanced Permissions** - Fine-grained action permissions
2. **Audit Logging** - Track user actions for security
3. **Permission Management UI** - Allow admins to manage permissions

---

## 🔒 **Security Considerations**

### **Data Protection:**
- Never trust frontend permission checks alone
- Always validate permissions on the backend
- Filter sensitive data before sending to frontend
- Implement proper session management

### **Access Control:**
- Use principle of least privilege
- Regularly audit user permissions
- Implement permission inheritance
- Log all permission changes

### **Store Isolation:**
- Ensure complete data separation between stores
- Validate store access on every request
- Prevent cross-store data leakage
- Implement store-based user assignment validation

---

## ⚠️ **IMPORTANT NOTES**

1. **DO NOT DEPLOY** without implementing proper permission checks
2. **CURRENT SYSTEM IS VULNERABLE** - all users can access all data
3. **IMPLEMENT BACKEND PROTECTION FIRST** - frontend guards are not sufficient
4. **TEST THOROUGHLY** - verify all permission scenarios work correctly
5. **AUDIT EXISTING DATA** - ensure no unauthorized access has occurred

---

## 🎯 **Next Steps**

1. **Review this plan** and approve the permission structure
2. **Implement Phase 2** - Frontend permission guards
3. **Implement Phase 3** - Backend API protection
4. **Test thoroughly** with different user roles
5. **Deploy only after complete implementation**

**CRITICAL**: This system must be implemented completely before any production deployment to prevent security breaches.
