# 🚀 SEPARATE DEPLOYMENT GUIDE - ADMIN PANEL & WEBSITE

## 📋 **DEPLOYMENT OVERVIEW**

You have two separate Next.js applications that need to be deployed independently:
- **Admin Panel** (Base directory) - Already deployed ✅
- **Website** (website folder) - Needs deployment 🎯

## 🎯 **RECOMMENDED DEPLOYMENT STRATEGY**

### **Option 1: Vercel Separate Projects (RECOMMENDED)**
Deploy each as a separate Vercel project with different URLs:
- **Admin Panel:** `https://admin-bakery.vercel.app` (or custom domain)
- **Website:** `https://bakery-website.vercel.app` (or custom domain)

### **Option 2: Subdomain Strategy**
Use custom domains with subdomains:
- **Admin Panel:** `https://admin.yourdomain.com`
- **Website:** `https://www.yourdomain.com` or `https://yourdomain.com`

## 🚀 **STEP-BY-STEP DEPLOYMENT**

### **STEP 1: Prepare Website for Deployment**

#### 1.1 Navigate to Website Directory
```bash
cd website
```

#### 1.2 Install Dependencies
```bash
npm install
```

#### 1.3 Test Local Build
```bash
npm run build
npm start
```

#### 1.4 Check Environment Variables
Create `.env.local` in website folder:
```env
# Database (Same as admin panel)
DATABASE_URL=your_neondb_connection_string

# API Base URL (Point to admin panel APIs)
NEXT_PUBLIC_API_BASE_URL=https://mispri24.vercel.app/api

# Website specific variables
NEXT_PUBLIC_SITE_URL=https://your-website-domain.vercel.app
```

### **STEP 2: Deploy Website to Vercel**

#### 2.1 Create New Vercel Project
1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import your repository
4. **IMPORTANT:** Set root directory to `website`

#### 2.2 Configure Build Settings
```
Framework Preset: Next.js
Root Directory: website
Build Command: npm run build
Output Directory: .next
Install Command: npm install
```

#### 2.3 Environment Variables
Add these in Vercel dashboard:
```
DATABASE_URL=your_neondb_connection_string
NEXT_PUBLIC_API_BASE_URL=https://mispri24.vercel.app/api
NEXT_PUBLIC_SITE_URL=https://your-website-domain.vercel.app
```

#### 2.4 Deploy
Click "Deploy" and wait for completion.

### **STEP 3: Configure API Connections**

Since your website needs to connect to the admin panel's APIs, you have two options:

#### Option A: Use Admin Panel APIs (RECOMMENDED)
Update website API calls to point to admin panel:
```javascript
// In website components
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://mispri24.vercel.app/api';

// Example API call
const response = await fetch(`${API_BASE_URL}/products`);
```

#### Option B: Duplicate APIs in Website
Copy necessary API routes from admin panel to website folder.

### **STEP 4: Update CORS Settings**

#### 4.1 Update Admin Panel CORS
In your admin panel, update API routes to allow website domain:

```javascript
// In admin panel API routes
const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://your-website-domain.vercel.app',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(request) {
  // Your API logic
  const response = NextResponse.json(data);
  
  // Add CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}
```

## 🔧 **CONFIGURATION FILES**

### **Website Package.json Updates**
```json
{
  "name": "bakery-website",
  "scripts": {
    "dev": "next dev -p 3001",
    "build": "next build",
    "start": "next start -p 3001",
    "lint": "next lint"
  }
}
```

### **Website Next.config.js**
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['your-admin-domain.vercel.app'],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_BASE_URL}/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
```

## 🌐 **DOMAIN CONFIGURATION**

### **Option 1: Vercel Subdomains**
1. **Admin Panel:** Keep current domain or change to `admin-bakery.vercel.app`
2. **Website:** Deploy as `bakery-store.vercel.app`

### **Option 2: Custom Domain with Subdomains**
1. **Buy a domain** (e.g., `yourbakery.com`)
2. **Configure DNS:**
   - `admin.yourbakery.com` → Admin Panel Vercel project
   - `www.yourbakery.com` → Website Vercel project
   - `yourbakery.com` → Website Vercel project

### **DNS Configuration Example**
```
Type    Name    Value
CNAME   admin   admin-bakery.vercel.app
CNAME   www     bakery-store.vercel.app
CNAME   @       bakery-store.vercel.app
```

## 📊 **SHARED DATABASE STRATEGY**

Both applications will share the same NeonDB database:

### **Database Access Pattern**
```
🗄️ NeonDB Database
    ↙️        ↘️
Admin Panel   Website
(Full Access) (Read Access)
```

### **API Strategy**
```
Website → Admin Panel APIs → Database
   ↓           ↓              ↓
Frontend    Backend        Data
```

## 🔒 **SECURITY CONSIDERATIONS**

### **API Security**
1. **Rate Limiting:** Implement on admin panel APIs
2. **CORS:** Configure properly for website domain
3. **Authentication:** Separate auth for admin vs customers
4. **API Keys:** Use different keys for different services

### **Environment Variables**
```bash
# Admin Panel (.env.local)
DATABASE_URL=neondb_connection
NEXTAUTH_SECRET=admin_secret
ADMIN_API_KEY=admin_key

# Website (.env.local)
DATABASE_URL=neondb_connection
NEXT_PUBLIC_API_BASE_URL=https://admin-domain.vercel.app/api
WEBSITE_API_KEY=website_key
```

## 🚀 **DEPLOYMENT COMMANDS**

### **Deploy Admin Panel Updates**
```bash
# In root directory
git add .
git commit -m "Admin panel updates"
git push origin main
# Vercel auto-deploys
```

### **Deploy Website Updates**
```bash
# In website directory
cd website
git add .
git commit -m "Website updates"
git push origin main
# Vercel auto-deploys website project
```

## 📈 **MONITORING & MAINTENANCE**

### **Separate Analytics**
- **Admin Panel:** Internal usage analytics
- **Website:** Customer behavior analytics (Google Analytics)

### **Performance Monitoring**
- **Admin Panel:** Focus on admin user experience
- **Website:** Focus on customer experience and SEO

### **Backup Strategy**
- **Database:** Single backup covers both
- **Code:** Separate repositories or branches
- **Assets:** Separate CDN or storage

## 🎯 **QUICK START CHECKLIST**

### **For Website Deployment:**
- [ ] Navigate to `website` folder
- [ ] Run `npm install` and `npm run build`
- [ ] Create new Vercel project
- [ ] Set root directory to `website`
- [ ] Configure environment variables
- [ ] Deploy and test
- [ ] Update CORS in admin panel
- [ ] Configure custom domain (optional)

### **Post-Deployment:**
- [ ] Test all website functionality
- [ ] Verify API connections work
- [ ] Check mobile responsiveness
- [ ] Test payment flows (if applicable)
- [ ] Set up monitoring and analytics

## 🎉 **SUCCESS METRICS**

After successful deployment, you'll have:
- ✅ **Admin Panel:** Professional management interface
- ✅ **Website:** Customer-facing e-commerce site
- ✅ **Shared Database:** Unified data management
- ✅ **Separate Domains:** Clear separation of concerns
- ✅ **Independent Scaling:** Each can scale separately

## 🆘 **TROUBLESHOOTING**

### **Common Issues:**
1. **API CORS Errors:** Update CORS headers in admin panel
2. **Build Failures:** Check dependencies in website folder
3. **Environment Variables:** Ensure all required vars are set
4. **Database Connections:** Verify connection strings

### **Quick Fixes:**
```bash
# Clear build cache
rm -rf .next
npm run build

# Check environment variables
vercel env ls

# Test API connections
curl https://your-admin-domain.vercel.app/api/products
```

---

## 🎊 **READY FOR SEPARATE DEPLOYMENT!**

This guide provides everything you need to deploy your admin panel and website as separate, independent applications while sharing the same database and maintaining professional separation of concerns.

**Your bakery business will have:**
- 🎯 **Professional Admin Interface** for management
- 🌐 **Beautiful Customer Website** for sales
- 📊 **Unified Data Management** with shared database
- 🚀 **Scalable Architecture** for future growth
