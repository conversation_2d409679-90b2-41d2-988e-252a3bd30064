Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8790) msys-2.0.dll+0x1FE8E
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210286019, 0007FFFF9748, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9890  000210068E24 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B70  00021006A225 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB7BCA0000 ntdll.dll
7FFB7AB20000 KERNEL32.DLL
7FFB78F70000 KERNELBASE.dll
7FFB7A320000 USER32.dll
7FFB79810000 win32u.dll
7FFB7B750000 GDI32.dll
7FFB79490000 gdi32full.dll
000210040000 msys-2.0.dll
7FFB79840000 msvcp_win.dll
7FFB798F0000 ucrtbase.dll
7FFB7A260000 advapi32.dll
7FFB7A6B0000 msvcrt.dll
7FFB7AA70000 sechost.dll
7FFB7A7E0000 RPCRT4.dll
7FFB783F0000 CRYPTBASE.DLL
7FFB793F0000 bcryptPrimitives.dll
7FFB7B080000 IMM32.DLL
