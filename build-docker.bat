@echo off
echo Building the application using Docker...

REM Check if Docker is installed
docker --version > nul 2>&1
if %errorlevel% neq 0 (
  echo Docker is not installed or not in the PATH.
  echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop
  exit /b 1
)

REM Build the Docker image
echo Building the Docker image...
docker build -t mispri-app .

REM Create a container and copy the build output
echo Copying build output from the container...
docker create --name mispri-temp mispri-app
mkdir build-output 2>nul
docker cp mispri-temp:/app/.next ./build-output
docker cp mispri-temp:/app/public ./build-output
docker rm mispri-temp

echo Build completed successfully!
echo The build output is in the build-output directory.
pause
