const { execSync } = require('child_process');

// Set environment variables to disable problematic features
process.env.NEXT_DISABLE_ESLINT = '1';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';
process.env.NEXT_TELEMETRY_DISABLED = '1';

try {
  // Run the export command instead of build
  // This will generate a static version of the site
  console.log('Exporting the application as static files...');
  execSync('npx next export', { 
    stdio: 'inherit',
    env: process.env
  });
  console.log('Export completed successfully!');
} catch (error) {
  console.error('Export failed:', error.message);
  process.exit(1);
}
