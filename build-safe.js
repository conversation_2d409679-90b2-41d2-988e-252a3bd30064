const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting safe build process...');

try {
  // Check if Prisma client exists
  const prismaClientPath = path.join(__dirname, 'src', 'generated', 'prisma');
  
  if (!fs.existsSync(prismaClientPath)) {
    console.log('📦 Generating Prisma client...');
    try {
      execSync('npx prisma generate', { stdio: 'inherit' });
      console.log('✅ Prisma client generated successfully');
    } catch (error) {
      console.log('⚠️ Prisma generation failed, continuing with build...');
      console.log('Error:', error.message);
    }
  } else {
    console.log('✅ Prisma client already exists');
  }

  // Run Next.js build
  console.log('🏗️ Building Next.js application...');
  execSync('npx next build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully!');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
