const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create a temporary next.config.js with standalone output
const configPath = path.join(__dirname, 'next.config.js');
const originalConfig = fs.readFileSync(configPath, 'utf8');

const newConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: 'standalone',
  distDir: '.next',
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    // Disable features that might cause file system scanning
    disableOptimizedLoading: true,
    optimizeCss: false,
    optimizePackageImports: false,
    turbotrace: false,
  },
  images: {
    domains: ['picsum.photos', 'mispri-pi.vercel.app'],
    unoptimized: true,
  },
  // Disable telemetry
  telemetry: {
    disabled: true,
  },
}

module.exports = nextConfig`;

// Backup the original config
const backupPath = path.join(__dirname, 'next.config.backup.js');
fs.writeFileSync(backupPath, originalConfig);

// Write the new config
fs.writeFileSync(configPath, newConfig);

// Create a custom build script that skips Prisma generation
const buildScript = `
// Skip Prisma generation and run next build directly
const { execSync } = require('child_process');

try {
  execSync('next build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
`;

const buildScriptPath = path.join(__dirname, 'temp-build.js');
fs.writeFileSync(buildScriptPath, buildScript);

try {
  // First, generate Prisma client separately
  console.log('Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  // Then run the custom build script
  console.log('Building the application with standalone configuration...');
  execSync('node temp-build.js', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
} finally {
  // Restore the original config
  fs.writeFileSync(configPath, originalConfig);
  console.log('Original configuration restored.');
  
  // Remove the temporary build script
  fs.unlinkSync(buildScriptPath);
}
