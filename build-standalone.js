const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create a temporary next.config.js that uses standalone output
const configPath = path.join(__dirname, 'next.config.js');
const originalConfig = fs.readFileSync(configPath, 'utf8');

const newConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: 'standalone',
  typescript: {
    ignoreBuildErrors: true,
  },
}

module.exports = nextConfig`;

// Backup the original config
const backupPath = path.join(__dirname, 'next.config.backup.js');
fs.writeFileSync(backupPath, originalConfig);

// Write the new config
fs.writeFileSync(configPath, newConfig);

try {
  // Run the build command
  console.log('Building the application with standalone output...');
  execSync('npm run build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
} finally {
  // Restore the original config
  fs.writeFileSync(configPath, originalConfig);
  console.log('Original configuration restored.');
}
