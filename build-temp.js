const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Create a temporary directory for the build
const tempDir = path.join(os.tmpdir(), 'next-build-' + Date.now());
fs.mkdirSync(tempDir, { recursive: true });

// Set environment variables to disable problematic features
process.env.NEXT_DISABLE_ESLINT = '1';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';
process.env.NEXT_TELEMETRY_DISABLED = '1';
process.env.NEXT_DIST_DIR = tempDir;

try {
  // Run the build command with the environment variables
  console.log(`Building the application in temporary directory: ${tempDir}`);
  execSync('npm run build', { 
    stdio: 'inherit',
    env: process.env
  });
  console.log('Build completed successfully!');
  
  // Copy the build output to the project directory
  console.log('Copying build output to project directory...');
  const outDir = path.join(__dirname, 'out');
  if (fs.existsSync(outDir)) {
    fs.rmSync(outDir, { recursive: true, force: true });
  }
  fs.mkdirSync(outDir, { recursive: true });
  
  // Copy files from tempDir to outDir
  const copyDir = (src, dest) => {
    const entries = fs.readdirSync(src, { withFileTypes: true });
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      if (entry.isDirectory()) {
        fs.mkdirSync(destPath, { recursive: true });
        copyDir(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  };
  
  copyDir(path.join(tempDir, 'out'), outDir);
  console.log('Build output copied successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
} finally {
  // Clean up the temporary directory
  try {
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log('Temporary directory cleaned up.');
  } catch (cleanupError) {
    console.error('Failed to clean up temporary directory:', cleanupError.message);
  }
}
