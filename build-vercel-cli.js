const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if Vercel CLI is installed
try {
  execSync('vercel --version', { stdio: 'pipe' });
  console.log('Vercel CLI is installed.');
} catch (error) {
  console.log('Vercel CLI is not installed. Installing...');
  try {
    execSync('npm install -g vercel', { stdio: 'inherit' });
    console.log('Vercel CLI installed successfully.');
  } catch (installError) {
    console.error('Failed to install Vercel CLI:', installError.message);
    process.exit(1);
  }
}

// Create a vercel.json file
const vercelJsonPath = path.join(__dirname, 'vercel.json');
const vercelJson = {
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/$1"
    }
  ],
  "env": {
    "NEXT_TELEMETRY_DISABLED": "1"
  }
};

fs.writeFileSync(vercelJsonPath, JSON.stringify(vercelJson, null, 2));
console.log('Created vercel.json file.');

// Run Vercel build
console.log('Running Vercel build...');
try {
  execSync('vercel build --prod', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  console.log('Vercel build completed successfully!');
} catch (error) {
  console.error('Vercel build failed:', error.message);
  process.exit(1);
}
