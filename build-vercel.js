const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Preparing for Vercel-compatible build...');

// Create a temporary package.json that uses Vercel's build command
const packageJsonPath = path.join(__dirname, 'package.json');
const originalPackageJson = fs.readFileSync(packageJsonPath, 'utf8');
const packageJson = JSON.parse(originalPackageJson);

// Backup the original package.json
const backupPath = path.join(__dirname, 'package.json.backup');
fs.writeFileSync(backupPath, originalPackageJson);

// Modify the build script to use Vercel's build command
const originalBuildScript = packageJson.scripts.build;
packageJson.scripts.build = 'next build';
packageJson.scripts['original-build'] = originalBuildScript;

// Add Vercel-specific configuration
packageJson.engines = packageJson.engines || {};
packageJson.engines.node = packageJson.engines.node || '18.x';

// Write the modified package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

// Create a vercel.json file
const vercelJsonPath = path.join(__dirname, 'vercel.json');
const vercelJson = {
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/$1"
    }
  ]
};

fs.writeFileSync(vercelJsonPath, JSON.stringify(vercelJson, null, 2));

try {
  // Install Vercel CLI
  console.log('Installing Vercel CLI...');
  execSync('npm install -g vercel', { stdio: 'inherit' });

  // Run Vercel build locally
  console.log('Running Vercel build locally...');
  execSync('vercel build --prod', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
} finally {
  // Restore the original package.json
  fs.writeFileSync(packageJsonPath, originalPackageJson);
  console.log('Original package.json restored.');
  
  // Remove the vercel.json file if it didn't exist before
  if (!fs.existsSync(path.join(__dirname, 'vercel.json.backup'))) {
    fs.unlinkSync(vercelJsonPath);
  }
}
