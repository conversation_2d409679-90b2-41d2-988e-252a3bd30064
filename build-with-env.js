const { execSync } = require('child_process');

// Set environment variables to disable problematic features
process.env.NEXT_DISABLE_ESLINT = '1';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';
process.env.NEXT_TELEMETRY_DISABLED = '1';

// This is a workaround to prevent Next.js from scanning the Application Data directory
process.env.NEXT_IGNORE_APPDATA = '1';

try {
  // Run the build command with the environment variables
  console.log('Building the application with custom environment variables...');
  execSync('npm run build', { 
    stdio: 'inherit',
    env: process.env
  });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
