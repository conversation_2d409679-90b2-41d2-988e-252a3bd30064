/**
 * Build with explicit exclusion of problematic directories
 * 
 * This script:
 * 1. Creates a temporary next.config.js that excludes problematic directories
 * 2. Runs the build command
 * 3. Restores the original next.config.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Path to next.config.js
const nextConfigPath = path.join(__dirname, 'next.config.js');
const backupPath = path.join(__dirname, 'next.config.backup.js');

// Read the original config
let originalConfig;
try {
  originalConfig = fs.readFileSync(nextConfigPath, 'utf8');
  console.log('Read original next.config.js');
} catch (error) {
  console.error(`Failed to read next.config.js: ${error.message}`);
  process.exit(1);
}

// Create a backup
fs.writeFileSync(backupPath, originalConfig);
console.log('Created backup of next.config.js');

// Create a new config with explicit exclusions
const newConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer }) => {
    // Exclude problematic directories
    config.watchOptions = {
      ...config.watchOptions,
      ignored: [
        '**/node_modules/**',
        '**/Application Data/**',
        '**/Application Data_backup_*/**',
        '**/Cookies/**',
        '**/AppData/**',
        '**/Local Settings/**',
        '**/NetHood/**',
        '**/PrintHood/**',
        '**/Recent/**',
        '**/SendTo/**',
        '**/Templates/**',
        '**/Start Menu/**',
        '**/My Documents/**',
        '**/My Music/**',
        '**/My Pictures/**',
        '**/My Videos/**',
        '**/Documents/My Pictures/**',
        '**/Documents/My Music/**',
        '**/Documents/My Videos/**',
        'C:/Users/<USER>/Documents/My Pictures/**',
        'C:\\\\Users\\\\<USER>\\\\Documents\\\\My Pictures/**',
      ]
    };
    
    // Disable file system access for webpack
    config.snapshot = {
      ...config.snapshot,
      managedPaths: [],
      immutablePaths: [],
    };
    
    return config;
  },
  images: {
    domains: ['picsum.photos', 'mispri-pi.vercel.app'],
    unoptimized: true,
  },
}

module.exports = nextConfig`;

// Write the new config
fs.writeFileSync(nextConfigPath, newConfig);
console.log('Created new next.config.js with exclusions');

try {
  // Run the build command
  console.log('\nRunning build command...');
  execSync('npm run build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  console.log('\nBuild completed successfully!');
} catch (error) {
  console.error('\nBuild failed:', error.message);
  process.exit(1);
} finally {
  // Restore the original config
  fs.writeFileSync(nextConfigPath, originalConfig);
  console.log('Restored original next.config.js');
  
  // Remove the backup
  fs.unlinkSync(backupPath);
  console.log('Removed backup file');
}
