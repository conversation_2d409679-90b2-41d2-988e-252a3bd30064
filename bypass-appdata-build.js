const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Create a temporary directory to use as a junction point
const tempDir = path.join(os.tmpdir(), 'appdata-bypass-' + Date.now());
console.log(`Creating temporary directory: ${tempDir}`);
fs.mkdirSync(tempDir, { recursive: true });

// Path to the problematic Application Data directory
const appDataPath = 'C:\\Users\\<USER>\\Application Data';

try {
  // Check if the Application Data directory exists
  console.log(`Checking if ${appDataPath} exists...`);
  const appDataExists = fs.existsSync(appDataPath);
  console.log(`${appDataPath} exists: ${appDataExists}`);

  if (appDataExists) {
    // Create a backup of the Application Data directory by creating a junction point
    console.log('Creating a junction point to bypass the Application Data directory...');
    
    // Rename the Application Data directory temporarily
    const backupPath = `${appDataPath}_backup_${Date.now()}`;
    console.log(`Renaming ${appDataPath} to ${backupPath}...`);
    
    try {
      // Try to rename using Node.js
      fs.renameSync(appDataPath, backupPath);
    } catch (renameError) {
      console.log(`Failed to rename using Node.js: ${renameError.message}`);
      
      // Try to rename using command prompt
      try {
        console.log('Trying to rename using command prompt...');
        execSync(`ren "${appDataPath}" "Application Data_backup_${Date.now()}"`, { stdio: 'inherit' });
      } catch (cmdRenameError) {
        console.log(`Failed to rename using command prompt: ${cmdRenameError.message}`);
        
        // If we can't rename, try to create a junction point to an empty directory
        console.log('Creating a junction point to an empty directory...');
        execSync(`mklink /J "${tempDir}\\Application Data" "${tempDir}"`, { stdio: 'inherit' });
      }
    }
    
    // Create a junction point from the original path to our temporary directory
    console.log(`Creating a junction point from ${appDataPath} to ${tempDir}...`);
    execSync(`mklink /J "${appDataPath}" "${tempDir}"`, { stdio: 'inherit' });
  } else {
    // If the directory doesn't exist, create it as a junction point to our temporary directory
    console.log(`Creating ${appDataPath} as a junction point to ${tempDir}...`);
    execSync(`mklink /J "${appDataPath}" "${tempDir}"`, { stdio: 'inherit' });
  }

  // Run the build command
  console.log('Running the build command...');
  execSync('npm run build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Error:', error.message);
} finally {
  // Clean up: Remove the junction point and restore the original directory
  console.log('Cleaning up...');
  
  try {
    // Remove the junction point
    console.log(`Removing the junction point at ${appDataPath}...`);
    execSync(`rmdir "${appDataPath}"`, { stdio: 'inherit' });
    
    // Restore the original directory if it was renamed
    const backupFiles = fs.readdirSync(path.dirname(appDataPath))
      .filter(file => file.startsWith('Application Data_backup_'));
    
    if (backupFiles.length > 0) {
      const latestBackup = backupFiles.sort().pop();
      const backupPath = path.join(path.dirname(appDataPath), latestBackup);
      
      console.log(`Restoring ${backupPath} to ${appDataPath}...`);
      execSync(`ren "${backupPath}" "Application Data"`, { stdio: 'inherit' });
    }
  } catch (cleanupError) {
    console.error('Cleanup error:', cleanupError.message);
  }
  
  // Remove the temporary directory
  try {
    console.log(`Removing the temporary directory at ${tempDir}...`);
    fs.rmSync(tempDir, { recursive: true, force: true });
  } catch (tempDirError) {
    console.error('Failed to remove temporary directory:', tempDirError.message);
  }
  
  console.log('Cleanup completed.');
}
