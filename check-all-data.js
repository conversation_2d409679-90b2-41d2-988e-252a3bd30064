// This script checks all data in the database
const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function checkAllData() {
  try {
    console.log('Checking all data in the database...');

    // Check users
    const users = await prisma.user.findMany();
    console.log(`\nUsers: ${users.length}`);
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}), Role: ${user.role}`);
    });

    // Check stores
    const stores = await prisma.store.findMany();
    console.log(`\nStores: ${stores.length}`);
    stores.forEach(store => {
      console.log(`- ${store.name} (${store.location})`);
    });

    // Check warehouses
    const warehouses = await prisma.warehouse.findMany();
    console.log(`\nWarehouses: ${warehouses.length}`);
    warehouses.forEach(warehouse => {
      console.log(`- ${warehouse.name} (${warehouse.location})`);
    });

    // Check products
    const products = await prisma.product.findMany();
    console.log(`\nProducts: ${products.length}`);
    products.forEach(product => {
      console.log(`- ${product.name} (${product.category}), Price: ₹${product.price}`);
      console.log(`  SEO: ${product.metaTitle || 'N/A'}`);
    });

    // Check raw materials
    const rawMaterials = await prisma.rawMaterial.findMany();
    console.log(`\nRaw Materials: ${rawMaterials.length}`);
    rawMaterials.forEach(material => {
      console.log(`- ${material.name} (${material.unit}), Cost: ₹${material.costPerUnit}, Stock: ${material.currentStock}`);
    });

    // Check product raw materials
    const productRawMaterials = await prisma.productRawMaterial.findMany({
      include: {
        product: true,
        rawMaterial: true
      }
    });
    console.log(`\nProduct Raw Materials: ${productRawMaterials.length}`);
    productRawMaterials.forEach(link => {
      console.log(`- ${link.product.name} requires ${link.quantityRequired} of ${link.rawMaterial.name}`);
    });

    // Check warehouse inventory
    const warehouseInventory = await prisma.warehouseInventory.findMany({
      include: {
        warehouse: true,
        product: true
      }
    });
    console.log(`\nWarehouse Inventory: ${warehouseInventory.length}`);
    warehouseInventory.forEach(inventory => {
      console.log(`- ${inventory.warehouse.name}: ${inventory.quantity} ${inventory.product.name}`);
    });

    // Check store inventory
    const storeInventory = await prisma.storeInventory.findMany({
      include: {
        store: true,
        product: true
      }
    });
    console.log(`\nStore Inventory: ${storeInventory.length}`);
    storeInventory.forEach(inventory => {
      console.log(`- ${inventory.store.name}: ${inventory.quantity} ${inventory.product.name}`);
    });

    // Check recipes
    const recipes = await prisma.recipe.findMany({
      include: {
        ingredients: true,
        instructions: true,
        tips: true,
        tags: true,
        nutritionInfo: true
      }
    });
    console.log(`\nRecipes: ${recipes.length}`);
    recipes.forEach(recipe => {
      console.log(`- ${recipe.name} (${recipe.category}), Difficulty: ${recipe.difficulty}`);
      console.log(`  Ingredients: ${recipe.ingredients.length}, Instructions: ${recipe.instructions.length}`);
      console.log(`  Tags: ${recipe.tags.map(tag => tag.name).join(', ')}`);
    });

    // Check bank accounts
    const bankAccounts = await prisma.bankAccount.findMany();
    console.log(`\nBank Accounts: ${bankAccounts.length}`);
    bankAccounts.forEach(account => {
      console.log(`- ${account.bankName} (${account.accountNumber}), Balance: ₹${account.balance}`);
    });

    // Check bank transactions
    const bankTransactions = await prisma.bankTransaction.findMany({
      include: {
        bankAccount: true
      }
    });
    console.log(`\nBank Transactions: ${bankTransactions.length}`);
    bankTransactions.forEach(transaction => {
      console.log(`- ${transaction.type} of ₹${transaction.amount} in ${transaction.bankAccount.bankName}`);
      console.log(`  Description: ${transaction.description}, Date: ${transaction.date.toLocaleDateString()}`);
    });

    // Check expenses
    const expenses = await prisma.expense.findMany({
      include: {
        store: true,
        user: true
      }
    });
    console.log(`\nExpenses: ${expenses.length}`);
    expenses.forEach(expense => {
      console.log(`- ${expense.category}: ₹${expense.amount} for ${expense.store?.name || 'N/A'}`);
      console.log(`  Description: ${expense.description}, Date: ${expense.date.toLocaleDateString()}`);
    });

    // Check wastage
    const wastage = await prisma.wastage.findMany({
      include: {
        product: true
      }
    });
    console.log(`\nWastage: ${wastage.length}`);
    wastage.forEach(record => {
      console.log(`- ${record.quantity} ${record.product.name}, Reason: ${record.reason}`);
      console.log(`  Date: ${record.date.toLocaleDateString()}`);
    });

    // Check API integrations
    const apiIntegrations = await prisma.apiIntegration.findMany();
    console.log(`\nAPI Integrations: ${apiIntegrations.length}`);
    apiIntegrations.forEach(integration => {
      console.log(`- ${integration.name} (${integration.type}), Active: ${integration.isActive}`);
    });

    // Check system settings
    const systemSettings = await prisma.systemSetting.findMany();
    console.log(`\nSystem Settings: ${systemSettings.length}`);
    systemSettings.forEach(setting => {
      console.log(`- ${setting.category}.${setting.key}: ${setting.value}`);
    });

    // Check customer profiles
    const customers = await prisma.customer.findMany({
      include: {
        user: true,
        customerTags: true,
        addresses: true
      }
    });
    console.log(`\nCustomer Profiles: ${customers.length}`);
    customers.forEach(customer => {
      console.log(`- ${customer.firstName} ${customer.lastName} (${customer.user.email})`);
      console.log(`  Tags: ${customer.customerTags.map(tag => tag.name).join(', ')}`);
      console.log(`  Addresses: ${customer.addresses.length}`);
    });

    // Check carts
    const carts = await prisma.cart.findMany({
      include: {
        user: true,
        items: {
          include: {
            product: true
          }
        }
      }
    });
    console.log(`\nCarts: ${carts.length}`);
    carts.forEach(cart => {
      console.log(`- Cart for ${cart.user.name}`);
      console.log(`  Items: ${cart.items.length}`);
      cart.items.forEach(item => {
        console.log(`  - ${item.quantity}x ${item.product.name}`);
      });
    });

    // Check orders
    const orders = await prisma.transaction.findMany({
      where: { type: 'SALE' },
      include: {
        user: true,
        items: {
          include: {
            product: true
          }
        },
        shippingAddress: true,
        billingAddress: true,
        coupon: true
      }
    });
    console.log(`\nOrders: ${orders.length}`);
    orders.forEach(order => {
      console.log(`- Order #${order.orderNumber || 'N/A'} for ${order.user.name}`);
      console.log(`  Status: ${order.orderStatus || order.status}, Payment: ${order.paymentStatus || 'N/A'}`);
      console.log(`  Total: ₹${order.totalAmount} (${order.items.length} items)`);
      if (order.coupon) {
        console.log(`  Coupon: ${order.coupon.code} (₹${order.discount} off)`);
      }
    });

    console.log('\nAll data check completed successfully!');
  } catch (error) {
    console.error('Error checking data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkAllData();
