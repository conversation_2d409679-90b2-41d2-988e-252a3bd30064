// This script checks products by category
const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function checkCategories() {
  try {
    console.log('Checking products by category...');

    // Get all distinct categories
    const categories = await prisma.product.findMany({
      select: {
        category: true
      },
      distinct: ['category']
    });

    console.log(`\nFound ${categories.length} distinct categories:`);
    for (const { category } of categories) {
      console.log(`- ${category}`);
    }

    // Get products for each category
    for (const { category } of categories) {
      const products = await prisma.product.findMany({
        where: { category },
        include: {
          productImages: true
        }
      });

      console.log(`\nCategory: ${category} (${products.length} products)`);
      for (const product of products) {
        console.log(`- ${product.name}: ₹${product.price}`);
        console.log(`  Description: ${product.description}`);
        if (product.productImages && product.productImages.length > 0) {
          console.log(`  Main Image: ${product.productImages.find(img => img.isMain)?.url || product.productImages[0].url}`);
        }
      }
    }

    // Get total product count
    const totalProducts = await prisma.product.count();
    console.log(`\nTotal products in database: ${totalProducts}`);

    console.log('\nCategory check completed successfully!');
  } catch (error) {
    console.error('Error checking categories:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkCategories();
