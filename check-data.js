const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('🔍 Checking existing data...');

    // Check categories
    const categories = await prisma.category.findMany({
      take: 5
    });
    console.log('\n📂 Categories:');
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (ID: ${cat.id})`);
    });

    // Check warehouses
    const warehouses = await prisma.warehouse.findMany({
      take: 5
    });
    console.log('\n🏭 Warehouses:');
    warehouses.forEach(wh => {
      console.log(`  - ${wh.name} - ${wh.location} (ID: ${wh.id})`);
    });

    // Check existing products with variants
    const productsWithVariants = await prisma.product.findMany({
      include: {
        variants: true
      },
      take: 3
    });

    console.log('\n📦 Existing products with variants:');
    productsWithVariants.forEach(product => {
      console.log(`  - ${product.name}: ${product.variants.length} variants`);
      product.variants.forEach(variant => {
        console.log(`    * ${variant.weight}: ₹${variant.price} ${variant.isDefault ? '(Default)' : ''}`);
      });
    });

    if (categories.length > 0 && warehouses.length > 0) {
      console.log('\n✅ Ready to test API with:');
      console.log(`Category ID: ${categories[0].id}`);
      console.log(`Warehouse ID: ${warehouses[0].id}`);
    } else {
      console.log('\n❌ Missing required data:');
      if (categories.length === 0) console.log('  - No categories found');
      if (warehouses.length === 0) console.log('  - No warehouses found');
    }

  } catch (error) {
    console.error('❌ Error checking data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();
