// This script checks the e-commerce data in the database
const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function checkEcommerceData() {
  try {
    console.log('Checking e-commerce data in the database...');

    // Check customer users
    const customerUsers = await prisma.user.findMany({
      where: { role: 'CUSTOMER' }
    });
    console.log(`\nCustomer Users: ${customerUsers.length}`);
    customerUsers.forEach(user => {
      console.log(`- ${user.name} (${user.email})`);
    });

    // Check customer profiles
    const customers = await prisma.customer.findMany({
      include: {
        user: true,
        customerTags: true,
        addresses: true
      }
    });
    console.log(`\nCustomer Profiles: ${customers.length}`);
    customers.forEach(customer => {
      console.log(`- ${customer.firstName} ${customer.lastName} (${customer.user.email})`);
      console.log(`  Tags: ${customer.customerTags.map(tag => tag.name).join(', ')}`);
      console.log(`  Addresses: ${customer.addresses.length}`);
    });

    // Check product images
    const productImages = await prisma.productImage.findMany({
      include: {
        product: true
      }
    });
    console.log(`\nProduct Images: ${productImages.length}`);
    const productImageCounts = {};
    productImages.forEach(image => {
      productImageCounts[image.product.name] = (productImageCounts[image.product.name] || 0) + 1;
    });
    Object.entries(productImageCounts).forEach(([productName, count]) => {
      console.log(`- ${productName}: ${count} images`);
    });

    // Check product relations
    const productRelations = await prisma.productRelation.findMany({
      include: {
        product: true,
        relatedProduct: true
      }
    });
    console.log(`\nProduct Relations: ${productRelations.length}`);
    productRelations.forEach(relation => {
      console.log(`- ${relation.product.name} → ${relation.relatedProduct.name} (${relation.relationType})`);
    });

    // Check coupons
    const coupons = await prisma.coupon.findMany();
    console.log(`\nCoupons: ${coupons.length}`);
    coupons.forEach(coupon => {
      console.log(`- ${coupon.code}: ${coupon.description}`);
      console.log(`  ${coupon.discountType === 'percentage' ? coupon.discountValue + '%' : '₹' + coupon.discountValue} off`);
      console.log(`  Valid until: ${coupon.endDate.toLocaleDateString()}`);
    });

    // Check carts
    const carts = await prisma.cart.findMany({
      include: {
        user: true,
        items: {
          include: {
            product: true
          }
        }
      }
    });
    console.log(`\nCarts: ${carts.length}`);
    carts.forEach(cart => {
      console.log(`- Cart for ${cart.user.name}`);
      console.log(`  Items: ${cart.items.length}`);
      cart.items.forEach(item => {
        console.log(`  - ${item.quantity}x ${item.product.name}`);
      });
    });

    // Check orders
    const orders = await prisma.transaction.findMany({
      where: { type: 'SALE' },
      include: {
        user: true,
        items: {
          include: {
            product: true
          }
        },
        shippingAddress: true,
        billingAddress: true,
        coupon: true
      }
    });
    console.log(`\nOrders: ${orders.length}`);
    orders.forEach(order => {
      console.log(`- Order #${order.orderNumber} for ${order.user.name}`);
      console.log(`  Status: ${order.orderStatus || order.status}, Payment: ${order.paymentStatus || 'N/A'}`);
      console.log(`  Total: ₹${order.totalAmount} (${order.items.length} items)`);
      if (order.coupon) {
        console.log(`  Coupon: ${order.coupon.code} (₹${order.discount} off)`);
      }
    });

    console.log('\nE-commerce data check completed successfully!');
  } catch (error) {
    console.error('Error checking e-commerce data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkEcommerceData();
