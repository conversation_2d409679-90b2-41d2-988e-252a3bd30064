// This script checks the e-commerce tables in the database
const { Client } = require('pg');
require('dotenv').config();

async function checkEcommerceTables() {
  try {
    console.log('Checking e-commerce tables in the database...');

    // Parse the DATABASE_URL to get connection details
    const url = process.env.DATABASE_URL;
    const match = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):?(\d+)?\/([^?]+)\?(.+)/);

    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }

    const [, user, password, host, port = '5432', database, params] = match;
    const schema = 'bakery'; // Explicitly use the bakery schema

    // Create a new PostgreSQL client
    const client = new Client({
      user,
      password,
      host,
      port,
      database,
      ssl: {
        rejectUnauthorized: false
      }
    });

    // Connect to the database
    await client.connect();
    console.log('Connected to the database');

    // List all e-commerce related tables in the schema
    const ecommerceTables = [
      'customers',
      'customer_tags',
      'addresses',
      'carts',
      'cart_items',
      'product_images',
      'product_relations',
      'coupons'
    ];

    console.log(`\nChecking e-commerce tables in schema '${schema}':`);

    for (const tableName of ecommerceTables) {
      try {
        const tableCheckQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = $1
            AND table_name = $2
          );
        `;
        const tableCheckResult = await client.query(tableCheckQuery, [schema, tableName]);

        if (tableCheckResult.rows[0].exists) {
          console.log(`✅ Table '${tableName}' exists`);

          // Count rows in the table
          const countQuery = `SELECT COUNT(*) FROM "${schema}"."${tableName}";`;
          const countResult = await client.query(countQuery);
          console.log(`   - Row count: ${countResult.rows[0].count}`);
        } else {
          console.log(`❌ Table '${tableName}' does not exist`);
        }
      } catch (error) {
        console.log(`❌ Error checking table '${tableName}':`, error.message);
      }
    }

    // Check if UserRole enum has CUSTOMER value
    console.log('\nChecking if UserRole enum has CUSTOMER value:');
    try {
      const enumCheckQuery = `
        SELECT e.enumlabel
        FROM pg_type t
        JOIN pg_enum e ON t.oid = e.enumtypid
        WHERE t.typname = 'UserRole'
        ORDER BY e.enumsortorder;
      `;
      const enumCheckResult = await client.query(enumCheckQuery);

      console.log('UserRole enum values:');
      const hasCustomer = enumCheckResult.rows.some(row => row.enumlabel === 'CUSTOMER');

      for (const row of enumCheckResult.rows) {
        console.log(`- ${row.enumlabel}`);
      }

      if (hasCustomer) {
        console.log('✅ UserRole enum has CUSTOMER value');
      } else {
        console.log('❌ UserRole enum does not have CUSTOMER value');
      }
    } catch (error) {
      console.log('❌ Error checking UserRole enum:', error.message);
    }

    // Disconnect from the database
    await client.end();
  } catch (error) {
    console.error('Error checking e-commerce tables:', error);
    process.exit(1);
  }
}

// Run the function
checkEcommerceTables();
