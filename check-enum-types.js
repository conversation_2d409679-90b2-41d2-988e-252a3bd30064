// This script checks the enum types in the database
const { Client } = require('pg');
require('dotenv').config();

async function checkEnumTypes() {
  try {
    console.log('Checking enum types in the database...');

    // Parse the DATABASE_URL to get connection details
    const url = process.env.DATABASE_URL;
    const match = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):?(\d+)?\/([^?]+)\?(.+)/);

    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }

    const [, user, password, host, port = '5432', database, params] = match;
    const schema = 'bakery'; // Explicitly use the bakery schema

    // Create a new PostgreSQL client
    const client = new Client({
      user,
      password,
      host,
      port,
      database,
      ssl: {
        rejectUnauthorized: false
      }
    });

    // Connect to the database
    await client.connect();
    console.log('Connected to the database');

    // List all enum types in the database
    const enumTypesQuery = `
      SELECT t.typname AS enum_name
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      GROUP BY t.typname
      ORDER BY t.typname;
    `;
    const enumTypesResult = await client.query(enumTypesQuery);

    console.log('\nEnum types in the database:');
    if (enumTypesResult.rows.length === 0) {
      console.log('No enum types found.');
    } else {
      for (const row of enumTypesResult.rows) {
        // Get enum values for this type
        const enumValuesQuery = `
          SELECT e.enumlabel
          FROM pg_type t
          JOIN pg_enum e ON t.oid = e.enumtypid
          WHERE t.typname = $1
          ORDER BY e.enumsortorder;
        `;
        const enumValuesResult = await client.query(enumValuesQuery, [row.enum_name]);
        const enumValues = enumValuesResult.rows.map(r => r.enumlabel).join(', ');

        console.log(`- ${row.enum_name}: ${enumValues}`);
      }
    }

    // Disconnect from the database
    await client.end();
  } catch (error) {
    console.error('Error checking enum types:', error);
    process.exit(1);
  }
}

// Run the function
checkEnumTypes();
