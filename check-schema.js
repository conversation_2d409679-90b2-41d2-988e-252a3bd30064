// This script checks the schema of the users table
const { Client } = require('pg');
require('dotenv').config();

async function checkSchema() {
  try {
    console.log('Checking database schema...');
    
    // Parse the DATABASE_URL to get connection details
    const url = process.env.DATABASE_URL;
    const match = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):?(\d+)?\/([^?]+)\?(.+)/);
    
    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }
    
    const [, user, password, host, port = '5432', database, params] = match;
    const schema = params.split('&').find(p => p.startsWith('schema='))?.split('=')[1] || 'public';
    
    // Create a new PostgreSQL client
    const client = new Client({
      user,
      password,
      host,
      port,
      database,
      ssl: {
        rejectUnauthorized: false
      }
    });
    
    // Connect to the database
    await client.connect();
    console.log('Connected to the database');
    
    // Check the schema of the users table
    const schemaQuery = `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = $1 
      AND table_name = 'users';
    `;
    const schemaResult = await client.query(schemaQuery, [schema]);
    
    console.log('Users table schema:');
    schemaResult.rows.forEach(row => {
      console.log(`${row.column_name}: ${row.data_type}`);
    });
    
    // Disconnect from the database
    await client.end();
  } catch (error) {
    console.error('Error checking schema:', error);
    process.exit(1);
  }
}

// Run the function
checkSchema();
