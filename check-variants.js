const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkVariants() {
  try {
    console.log('🔍 Checking products and variants in database...\n');

    // Check products with variants
    const productsWithVariants = await prisma.product.findMany({
      include: {
        variants: {
          orderBy: { sortOrder: 'asc' }
        }
      },
      take: 5
    });

    console.log(`📦 Found ${productsWithVariants.length} products in database\n`);

    productsWithVariants.forEach((product, index) => {
      console.log(`${index + 1}. Product: ${product.name}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Base Price: ₹${product.price}`);
      console.log(`   Variants: ${product.variants.length}`);
      
      if (product.variants.length > 0) {
        product.variants.forEach((variant, vIndex) => {
          console.log(`     ${vIndex + 1}. ${variant.weight} - ₹${variant.price} ${variant.isDefault ? '(Default)' : ''} ${variant.isActive ? '✅' : '❌'}`);
        });
      } else {
        console.log('     ⚠️ No variants found');
      }
      console.log('');
    });

    // Check total counts
    const totalProducts = await prisma.product.count();
    const totalVariants = await prisma.productVariant.count();
    
    console.log(`📊 Database Summary:`);
    console.log(`   Total Products: ${totalProducts}`);
    console.log(`   Total Variants: ${totalVariants}`);
    console.log(`   Products with Variants: ${productsWithVariants.filter(p => p.variants.length > 0).length}`);

    // Check for products without variants
    const productsWithoutVariants = await prisma.product.findMany({
      where: {
        variants: {
          none: {}
        }
      },
      select: {
        id: true,
        name: true,
        price: true
      },
      take: 5
    });

    if (productsWithoutVariants.length > 0) {
      console.log(`\n⚠️ Products without variants (${productsWithoutVariants.length}):`);
      productsWithoutVariants.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name} (₹${product.price})`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkVariants();
