const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// List of problematic directories
const problematicDirs = [
  'C:\\Users\\<USER>\\Application Data_backup_1746554862232',
  'C:\\Users\\<USER>\\Application Data_backup_1746554862232.bak',
  'C:\\Users\\<USER>\\Cookies',
  'C:\\Users\\<USER>\\Cookies.bak',
  'C:\\Users\\<USER>\\Local Settings',
  'C:\\Users\\<USER>\\Local Settings.bak',
  'C:\\Users\\<USER>\\NetHood',
  'C:\\Users\\<USER>\\NetHood.bak',
  'C:\\Users\\<USER>\\PrintHood',
  'C:\\Users\\<USER>\\PrintHood.bak',
  'C:\\Users\\<USER>\\Recent',
  'C:\\Users\\<USER>\\Recent.bak',
  'C:\\Users\\<USER>\\SendTo',
  'C:\\Users\\<USER>\\SendTo.bak',
  'C:\\Users\\<USER>\\Templates',
  'C:\\Users\\<USER>\\Templates.bak',
  'C:\\Users\\<USER>\\Start Menu',
  'C:\\Users\\<USER>\\Start Menu.bak',
  'C:\\Users\\<USER>\\My Documents',
  'C:\\Users\\<USER>\\My Documents.bak',
  'C:\\Users\\<USER>\\My Music',
  'C:\\Users\\<USER>\\My Music.bak',
  'C:\\Users\\<USER>\\My Pictures',
  'C:\\Users\\<USER>\\My Pictures.bak',
  'C:\\Users\\<USER>\\My Videos',
  'C:\\Users\\<USER>\\My Videos.bak',
  // Additional directories from Documents folder
  'C:\\Users\\<USER>\\Documents\\My Music',
  'C:\\Users\\<USER>\\Documents\\My Pictures',
  'C:\\Users\\<USER>\\Documents\\My Videos',
  'C:\\Users\\<USER>\\Documents\\NetHood',
  'C:\\Users\\<USER>\\Documents\\PrintHood',
  'C:\\Users\\<USER>\\Documents\\Recent',
  'C:\\Users\\<USER>\\Documents\\SendTo',
  'C:\\Users\\<USER>\\Documents\\Templates',
  'C:\\Users\\<USER>\\Documents\\Start Menu'
];

// Function to safely remove a directory
function safeRemoveDir(dir) {
  try {
    if (fs.existsSync(dir)) {
      console.log(`Removing directory: ${dir}`);

      // Try to use Node.js fs.rmSync
      try {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`Successfully removed ${dir} using Node.js`);
        return true;
      } catch (rmError) {
        console.log(`Failed to remove using Node.js: ${rmError.message}`);

        // Try using rmdir command
        try {
          execSync(`rmdir /S /Q "${dir}"`, { stdio: 'inherit' });
          console.log(`Successfully removed ${dir} using rmdir command`);
          return true;
        } catch (rmdirError) {
          console.log(`Failed to remove using rmdir: ${rmdirError.message}`);
          return false;
        }
      }
    } else {
      console.log(`Directory does not exist: ${dir}`);
      return true;
    }
  } catch (error) {
    console.error(`Error checking directory ${dir}: ${error.message}`);
    return false;
  }
}

// Remove all problematic directories
console.log('Removing problematic directories...');
let allRemoved = true;

for (const dir of problematicDirs) {
  const success = safeRemoveDir(dir);
  if (!success) {
    allRemoved = false;
  }
}

if (allRemoved) {
  console.log('All problematic directories have been removed or do not exist.');
  console.log('You can now try running the build command again.');
} else {
  console.log('Some directories could not be removed. You may need administrator privileges.');
  console.log('Try running this script as an administrator or manually remove the directories.');
}
