@echo off
echo Creating a clean repository without node_modules...

:: Create a temporary directory
mkdir ..\temp_mispri_website

:: Copy important files to the temporary directory (excluding node_modules and .git)
xcopy /E /I /H /Y /EXCLUDE:exclude.txt . ..\temp_mispri_website

:: Create the exclude.txt file
echo node_modules\ > exclude.txt
echo .git\ >> exclude.txt

:: Navigate to the temporary directory
cd ..\temp_mispri_website

:: Initialize a new Git repository
git init
git remote add origin https://github.com/BhardwajVaishnavi/mispri-website.git

:: Add all files
git add .

:: Commit
git commit -m "Initial commit with clean repository"

:: Push to GitHub
git push -u origin main --force

echo.
echo Repository cleaned and pushed to GitHub.
echo You can now delete this temporary directory and clone the clean repository.
echo.
