#!/bin/bash

# Create a temporary directory
mkdir -p ../temp_backup

# Copy important files to the temporary directory (excluding node_modules and .git)
cp -r $(ls -A | grep -v "node_modules\|.git") ../temp_backup/

# Create a new repository
cd ../temp_backup
git init
git remote add origin https://github.com/BhardwajVaishnavi/mispri-website.git

# Add all files
git add .

# Commit
git commit -m "Initial commit with clean repository"

# Push to GitHub
git push -u origin main --force

echo "Repository cleaned and pushed to GitHub."
echo "You can now delete this temporary directory and clone the clean repository."
