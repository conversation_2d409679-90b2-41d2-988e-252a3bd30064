# Mispri E-commerce Website

This is the customer-facing e-commerce website for Mispri, a bakery and gift shop in Bhubaneswar. The website connects to the same database as the admin panel (deployed at https://mispri24.vercel.app) but is deployed as a separate application.

## Features

- Browse products by category
- View product details
- Add products to cart
- Checkout process
- User authentication
- Responsive design for mobile and desktop
- Contact form
- About page
- User account management

## Pages Implemented

- **Home Page**: Featured products, categories, and promotional sections
- **Products Page**: Browse all products with filtering by category
- **Category Pages**: View products by specific category
- **Product Detail Page**: Detailed product information with related products
- **Cart Page**: View and manage shopping cart
- **Checkout Page**: Complete purchase with shipping and payment information
- **Login/Register Page**: User authentication
- **Account Page**: User profile, orders, wishlist, and addresses
- **Contact Page**: Contact form and store information
- **About Page**: Company information and team

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn
- PostgreSQL database (shared with the admin panel)

### Installation

1. Clone the repository
2. Navigate to the website directory:
   ```bash
   cd website
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Set up environment variables:
   Create a `.env` file in the website directory with the following content:
   ```
   DATABASE_URL="your_postgresql_connection_string"
   DIRECT_URL="your_postgresql_connection_string"
   NEXT_PUBLIC_API_URL="https://mispri24.vercel.app/api"
   ```
5. Generate Prisma client:
   ```bash
   npx prisma generate
   ```
6. Start the development server:
   ```bash
   npm run dev
   ```
7. Open [http://localhost:3001](http://localhost:3001) in your browser

## Deployment

See the [deploy-to-vercel.md](./deploy-to-vercel.md) file for detailed deployment instructions.

### Quick Deployment Steps

1. Create a new project in Vercel
2. Connect your GitHub repository
3. Configure the project:
   - Framework Preset: Next.js
   - Root Directory: Leave empty
   - Build Command: `npm run build`
   - Install Command: `npm install`
4. Configure Environment Variables:
   - Add `DATABASE_URL` with your PostgreSQL connection string
   - Add `DIRECT_URL` with the same PostgreSQL connection string
   - Add `NEXT_PUBLIC_API_URL` with value: `https://mispri24.vercel.app/api`
5. Click "Deploy"
6. Set up a custom domain if desired

## Project Structure

```
/website
├── /src                      # Source code
│   ├── /app                  # Next.js app directory
│   │   ├── /page.tsx         # Homepage
│   │   ├── /category         # Category pages
│   │   ├── /product          # Product pages
│   │   ├── /cart             # Shopping cart
│   │   ├── /checkout         # Checkout process
│   │   ├── /account          # User account
│   │   ├── /login            # Authentication
│   │   ├── /about            # About page
│   │   └── /contact          # Contact page
│   ├── /components           # React components
│   │   ├── /Header.tsx       # Header component
│   │   ├── /Footer.tsx       # Footer component
│   │   ├── /ProductCard.tsx  # Product card component
│   │   └── /...              # Other components
│   └── /lib                  # Utility functions
│       └── /db.ts            # Database client
├── /prisma                   # Prisma configuration
│   └── /schema.prisma        # Database schema
├── /public                   # Static assets
└── /...                      # Configuration files
```

## Technologies Used

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Prisma](https://www.prisma.io/) - ORM
- [PostgreSQL](https://www.postgresql.org/) - Database
- [React Icons](https://react-icons.github.io/react-icons/) - Icon library

## Future Enhancements

- Implement authentication with NextAuth.js
- Add product search functionality
- Implement wishlist functionality
- Add product reviews and ratings
- Integrate payment gateways
- Implement order tracking
- Add product filtering and sorting options
- Implement internationalization for multiple languages

## License

This project is licensed under the MIT License.
