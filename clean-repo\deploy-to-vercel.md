# Deploying the Mispri Website to Vercel

Follow these steps to deploy the Mispri website to Vercel, separate from the admin panel.

## Step 1: Prepare the Files

1. Copy all website files to this clean repository folder, excluding node_modules and .next:
   ```bash
   xcopy ..\website\* . /E /I /H /EXCLUDE:node_modules,.next
   ```

## Step 2: Initialize Git Repository

```bash
git init
git add .
git commit -m "Initial commit"
```

## Step 3: Create GitHub Repository

1. Go to GitHub and create a new repository (e.g., mispri-website)
2. Connect your local repository:
   ```bash
   git remote add origin https://github.com/YourUsername/mispri-website.git
   git branch -M main
   git push -u origin main
   ```

## Step 4: Deploy to Vercel

1. Go to [Vercel](https://vercel.com/) and sign in
2. Click "Add New" > "Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Next.js
   - Root Directory: Leave empty
   - Build Command: `npm run build`
   - Install Command: `npm install`

5. Configure Environment Variables:
   - Add `DATABASE_URL` with your NeonDB PostgreSQL connection string
   - Add `DIRECT_URL` with the same PostgreSQL connection string
   - Add `NEXT_PUBLIC_API_URL` with value: `https://mispri24.vercel.app/api`

6. Click "Deploy"

## Step 5: Verify Deployment

1. After deployment, Vercel will provide a URL for your website
2. Test all functionality to ensure everything works correctly

## Step 6: Set Up Custom Domain (Optional)

1. In your Vercel project dashboard, go to "Settings" > "Domains"
2. Add your custom domain
3. Follow Vercel's instructions to configure your DNS settings

## Important Notes

- The website will connect to the admin panel's API at https://mispri24.vercel.app/api
- Both the admin panel and website use the same database
- Make sure all environment variables are correctly set in Vercel
