const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupDatabase() {
  try {
    console.log('🧹 Starting database cleanup...');
    console.log('⚠️  This will delete ALL data from the database!');

    // Delete in order to respect foreign key constraints
    console.log('\n🗑️ Step 1: Deleting order items...');
    const deletedOrderItems = await prisma.orderItem.deleteMany({});
    console.log(`✅ Deleted ${deletedOrderItems.count} order items`);

    console.log('🗑️ Step 2: Deleting orders...');
    const deletedOrders = await prisma.order.deleteMany({});
    console.log(`✅ Deleted ${deletedOrders.count} orders`);

    console.log('🗑️ Step 3: Deleting cart items...');
    const deletedCartItems = await prisma.cartItem.deleteMany({});
    console.log(`✅ Deleted ${deletedCartItems.count} cart items`);

    console.log('🗑️ Step 4: Deleting carts...');
    const deletedCarts = await prisma.cart.deleteMany({});
    console.log(`✅ Deleted ${deletedCarts.count} carts`);

    console.log('🗑️ Step 5: Deleting addresses...');
    const deletedAddresses = await prisma.address.deleteMany({});
    console.log(`✅ Deleted ${deletedAddresses.count} addresses`);

    console.log('🗑️ Step 6: Deleting customer tags...');
    const deletedCustomerTags = await prisma.customerTag.deleteMany({});
    console.log(`✅ Deleted ${deletedCustomerTags.count} customer tags`);

    console.log('🗑️ Step 7: Deleting customers...');
    const deletedCustomers = await prisma.customer.deleteMany({});
    console.log(`✅ Deleted ${deletedCustomers.count} customers`);

    console.log('🗑️ Step 8: Deleting product images...');
    const deletedProductImages = await prisma.productImage.deleteMany({});
    console.log(`✅ Deleted ${deletedProductImages.count} product images`);

    console.log('🗑️ Step 9: Deleting product relations...');
    const deletedProductRelations = await prisma.productRelation.deleteMany({});
    console.log(`✅ Deleted ${deletedProductRelations.count} product relations`);

    console.log('🗑️ Step 10: Skipping warehouse products (model may not exist)...');
    const deletedWarehouseProducts = { count: 0 };

    console.log('🗑️ Step 11: Skipping store products (model may not exist)...');
    const deletedStoreProducts = { count: 0 };

    console.log('🗑️ Step 12: Deleting products...');
    const deletedProducts = await prisma.product.deleteMany({});
    console.log(`✅ Deleted ${deletedProducts.count} products`);

    console.log('🗑️ Step 13: Deleting categories...');
    const deletedCategories = await prisma.category.deleteMany({});
    console.log(`✅ Deleted ${deletedCategories.count} categories`);

    console.log('🗑️ Step 14: Deleting coupons...');
    const deletedCoupons = await prisma.coupon.deleteMany({});
    console.log(`✅ Deleted ${deletedCoupons.count} coupons`);

    console.log('🗑️ Step 15: Deleting contact submissions...');
    const deletedContactSubmissions = await prisma.contactSubmission.deleteMany({});
    console.log(`✅ Deleted ${deletedContactSubmissions.count} contact submissions`);

    console.log('🗑️ Step 16: Deleting transactions...');
    const deletedTransactions = await prisma.transaction.deleteMany({});
    console.log(`✅ Deleted ${deletedTransactions.count} transactions`);

    console.log('🗑️ Step 17: Deleting stores...');
    const deletedStores = await prisma.store.deleteMany({});
    console.log(`✅ Deleted ${deletedStores.count} stores`);

    console.log('🗑️ Step 18: Deleting warehouses...');
    const deletedWarehouses = await prisma.warehouse.deleteMany({});
    console.log(`✅ Deleted ${deletedWarehouses.count} warehouses`);

    console.log('🗑️ Step 19: Deleting non-admin users...');
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        role: {
          not: 'ADMIN'
        }
      }
    });
    console.log(`✅ Deleted ${deletedUsers.count} non-admin users`);

    console.log('\n✅ Database cleanup completed successfully!');
    console.log('\n📊 CLEANUP SUMMARY:');
    console.log('═══════════════════════════════════════');
    console.log(`📦 Order Items:        ${deletedOrderItems.count}`);
    console.log(`🛒 Orders:             ${deletedOrders.count}`);
    console.log(`🛍️  Cart Items:         ${deletedCartItems.count}`);
    console.log(`🛒 Carts:              ${deletedCarts.count}`);
    console.log(`📍 Addresses:          ${deletedAddresses.count}`);
    console.log(`🏷️  Customer Tags:      ${deletedCustomerTags.count}`);
    console.log(`👥 Customers:          ${deletedCustomers.count}`);
    console.log(`🖼️  Product Images:     ${deletedProductImages.count}`);
    console.log(`🔗 Product Relations:  ${deletedProductRelations.count}`);
    console.log(`📦 Warehouse Products: ${deletedWarehouseProducts.count}`);
    console.log(`🏪 Store Products:     ${deletedStoreProducts.count}`);
    console.log(`🎂 Products:           ${deletedProducts.count}`);
    console.log(`📂 Categories:         ${deletedCategories.count}`);
    console.log(`🎫 Coupons:            ${deletedCoupons.count}`);
    console.log(`📧 Contact Submissions:${deletedContactSubmissions.count}`);
    console.log(`💳 Transactions:       ${deletedTransactions.count}`);
    console.log(`🏪 Stores:             ${deletedStores.count}`);
    console.log(`🏭 Warehouses:         ${deletedWarehouses.count}`);
    console.log(`👤 Non-admin Users:    ${deletedUsers.count}`);
    console.log('═══════════════════════════════════════');
    console.log('\n🎉 Your database is now clean and ready for fresh testing!');

  } catch (error) {
    console.error('\n❌ Error during database cleanup:', error);
    console.error('\n💡 Make sure your database connection is working and try again.');
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
cleanupDatabase()
  .then(() => {
    console.log('\n🚀 Database cleanup script completed successfully!');
    console.log('💡 You can now start fresh with your testing.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Database cleanup script failed:', error);
    process.exit(1);
  });
