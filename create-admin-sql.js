// This script creates an admin user directly using SQL
const { Client } = require('pg');
require('dotenv').config();

async function createAdminWithSQL() {
  try {
    console.log('Creating admin user using direct SQL...');

    // Parse the DATABASE_URL to get connection details
    const url = process.env.DATABASE_URL;
    const match = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):?(\d+)?\/([^?]+)\?(.+)/);

    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }

    const [, user, password, host, port = '5432', database, params] = match;
    const schema = params.split('&').find(p => p.startsWith('schema='))?.split('=')[1] || 'public';

    // Create a new PostgreSQL client
    const client = new Client({
      user,
      password,
      host,
      port,
      database,
      ssl: {
        rejectUnauthorized: false
      }
    });

    // Connect to the database
    await client.connect();
    console.log('Connected to the database');

    // Check if the users table exists
    const tableCheckQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = $1
        AND table_name = 'users'
      );
    `;
    const tableCheckResult = await client.query(tableCheckQuery, [schema]);

    if (!tableCheckResult.rows[0].exists) {
      console.log('Users table does not exist. Please run npx prisma db push first.');
      await client.end();
      return;
    }

    // Check if admin user already exists
    const checkQuery = `
      SELECT * FROM "${schema}"."users" WHERE email = '<EMAIL>';
    `;
    const checkResult = await client.query(checkQuery);

    if (checkResult.rows.length > 0) {
      console.log('Admin user already exists');
    } else {
      // Create admin user
      const insertQuery = `
        INSERT INTO "${schema}"."users" (
          id, name, email, password, role, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7
        );
      `;

      const id = generateUUID();
      const now = new Date().toISOString();

      await client.query(insertQuery, [
        id,
        'Admin User',
        '<EMAIL>',
        'admin123', // In a real app, you'd hash this password
        'ADMIN',
        now,
        now
      ]);

      console.log(`Created admin user: Admin User (<EMAIL>)`);
    }

    // Disconnect from the database
    await client.end();
    console.log('Admin user creation completed successfully!');
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

// Helper function to generate a UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Run the function
createAdminWithSQL();
