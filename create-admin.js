// This script creates an admin user directly
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function createAdmin() {
  try {
    console.log('Creating admin user...');

    // Create a new PrismaClient instance
    const prisma = new PrismaClient();

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin@123', 10);

    // Create admin user
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    console.log(`Created admin user: ${adminUser.name} (${adminUser.email})`);

    // Disconnect from the database
    await prisma.$disconnect();

    console.log('Admin user creation completed successfully!');
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

// Run the function
createAdmin();
