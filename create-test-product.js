const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestProduct() {
  try {
    console.log('🧪 Creating test product for deletion testing...');

    const testProduct = await prisma.product.create({
      data: {
        name: 'Test Product for Deletion',
        description: 'This is a test product that will be used to test deletion functionality',
        category: 'Test Category',
        price: 99.99,
        costPrice: 50.00,
        unit: 'piece',
        lowStockThreshold: 10,
        sku: 'TEST-001',
        imageUrl: '/test-image.jpg',
        metaTitle: 'Test Product',
        metaDescription: 'Test product for deletion testing',
        isActive: true
      }
    });

    console.log('✅ Test product created successfully:');
    console.log(`  ID: ${testProduct.id}`);
    console.log(`  Name: ${testProduct.name}`);
    console.log(`  Category: ${testProduct.category}`);
    console.log(`  Price: $${testProduct.price}`);

    // Also create some related records to test the deletion cascade
    console.log('\n🔗 Creating related records...');

    // Create warehouse inventory
    const warehouses = await prisma.warehouse.findMany({ take: 1 });
    if (warehouses.length > 0) {
      await prisma.warehouseInventory.create({
        data: {
          warehouseId: warehouses[0].id,
          productId: testProduct.id,
          quantity: 100
        }
      });
      console.log('  ✅ Warehouse inventory created');
    }

    // Create store inventory
    const stores = await prisma.store.findMany({ take: 1 });
    if (stores.length > 0) {
      await prisma.storeInventory.create({
        data: {
          storeId: stores[0].id,
          productId: testProduct.id,
          quantity: 50
        }
      });
      console.log('  ✅ Store inventory created');
    }

    console.log('\n🎯 Test product and related records created successfully!');
    console.log('You can now test the deletion functionality in the admin panel.');

  } catch (error) {
    console.error('❌ Error creating test product:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestProduct();
