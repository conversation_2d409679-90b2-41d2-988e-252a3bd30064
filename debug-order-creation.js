// Debug script to test order creation flow
const API_BASE_URL = 'https://mispri24.vercel.app/api';

async function testOrderCreation() {
  console.log('🔍 Testing Order Creation Flow...\n');

  // Step 1: Test customer login
  console.log('1. Testing customer login...');
  try {
    const loginResponse = await fetch(`${API_BASE_URL}/auth/customer-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'customer123'
      }),
    });

    if (!loginResponse.ok) {
      const error = await loginResponse.text();
      console.error('❌ Login failed:', error);
      return;
    }

    const user = await loginResponse.json();
    console.log('✅ Login successful:', user);

    // Step 2: Test product fetch
    console.log('\n2. Testing product fetch...');
    const productsResponse = await fetch(`${API_BASE_URL}/products?limit=1`);
    
    if (!productsResponse.ok) {
      console.error('❌ Products fetch failed');
      return;
    }

    const products = await productsResponse.json();
    console.log('✅ Products fetched:', products.length, 'products');

    if (products.length === 0) {
      console.error('❌ No products available for testing');
      return;
    }

    const testProduct = products[0];
    console.log('📦 Using test product:', testProduct.name, 'Price:', testProduct.price);

    // Step 3: Test order creation
    console.log('\n3. Testing order creation...');
    const orderData = {
      userId: user.id,
      items: [{
        productId: testProduct.id,
        quantity: 1,
        unitPrice: testProduct.price || 100,
      }],
      shippingAddress: {
        street: '123 Test Street',
        city: 'Bhubaneswar',
        state: 'Odisha',
        pincode: '751001',
        country: 'India',
        firstName: 'Test',
        lastName: 'Customer',
        phone: '+91 9876543210',
      },
      paymentMethod: 'COD',
      totalAmount: testProduct.price || 100,
      subtotal: testProduct.price || 100,
      shipping: 0,
    };

    console.log('📋 Order data:', JSON.stringify(orderData, null, 2));

    const orderResponse = await fetch(`${API_BASE_URL}/customer-orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    console.log('📡 Order response status:', orderResponse.status);

    if (!orderResponse.ok) {
      const errorText = await orderResponse.text();
      console.error('❌ Order creation failed:', errorText);
      
      try {
        const errorData = JSON.parse(errorText);
        console.error('❌ Error details:', errorData);
      } catch (e) {
        console.error('❌ Raw error:', errorText);
      }
      return;
    }

    const order = await orderResponse.json();
    console.log('✅ Order created successfully:', order);

    // Step 4: Verify order in database
    console.log('\n4. Verifying order in database...');
    const verifyResponse = await fetch(`${API_BASE_URL}/customer-orders?userId=${user.id}`);
    
    if (verifyResponse.ok) {
      const orders = await verifyResponse.json();
      console.log('✅ Orders in database:', orders.length);
      if (orders.length > 0) {
        console.log('📋 Latest order:', orders[0]);
      }
    } else {
      console.error('❌ Failed to verify orders');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testOrderCreation();
