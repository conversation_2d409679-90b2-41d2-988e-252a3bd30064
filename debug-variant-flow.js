// Debug script to check the complete data flow from admin to website
async function debugVariantFlow() {
  try {
    console.log('🔍 DEBUGGING VARIANT DATA FLOW\n');

    // Step 1: Check what's in the database
    console.log('1. 📊 CHECKING DATABASE DIRECTLY:');
    const dbResponse = await fetch('http://localhost:3000/api/products-with-variants');
    if (dbResponse.ok) {
      const dbData = await dbResponse.json();
      console.log(`   ✅ Found ${dbData.products?.length || 0} products in database`);
      
      dbData.products?.forEach((product, index) => {
        console.log(`\n   ${index + 1}. ${product.name}`);
        console.log(`      ID: ${product.id}`);
        console.log(`      Base Price: ₹${product.price}`);
        console.log(`      Variants: ${product.variants?.length || 0}`);
        
        if (product.variants && product.variants.length > 0) {
          product.variants.forEach((variant, vIndex) => {
            console.log(`         ${vIndex + 1}. ${variant.weight} - ₹${variant.price} ${variant.isDefault ? '(Default)' : ''} ${variant.isActive ? '✅' : '❌'}`);
          });
        } else {
          console.log('         ⚠️ NO VARIANTS - Website will show fallback weights');
        }
      });
    } else {
      console.log('   ❌ Database API failed');
    }

    // Step 2: Check what website API returns
    console.log('\n\n2. 🌐 CHECKING WEBSITE API:');
    try {
      const websiteResponse = await fetch('http://localhost:3001/api/products-with-variants');
      if (websiteResponse.ok) {
        const websiteData = await websiteResponse.json();
        console.log(`   ✅ Website API returned ${websiteData.products?.length || 0} products`);
        
        // Compare with database data
        if (websiteData.products && dbData.products) {
          const dbProduct = dbData.products.find(p => p.variants && p.variants.length > 0);
          const websiteProduct = websiteData.products.find(p => p.id === dbProduct?.id);
          
          if (dbProduct && websiteProduct) {
            console.log(`\n   🔄 COMPARING DATA FOR: ${dbProduct.name}`);
            console.log(`      Database variants: ${dbProduct.variants.length}`);
            console.log(`      Website variants: ${websiteProduct.variants?.length || 0}`);
            
            if (dbProduct.variants.length === websiteProduct.variants?.length) {
              console.log('      ✅ Variant counts match');
            } else {
              console.log('      ❌ Variant counts DO NOT match');
            }
          }
        }
      } else {
        console.log(`   ❌ Website API failed: ${websiteResponse.status}`);
        console.log('   ⚠️ This means website cannot get variant data');
      }
    } catch (websiteError) {
      console.log(`   ❌ Website API error: ${websiteError.message}`);
      console.log('   ⚠️ Website server may not be running');
    }

    // Step 3: Check specific product API
    console.log('\n\n3. 🎯 CHECKING SPECIFIC PRODUCT API:');
    const productId = 'cmbymr1an000unid4n26ikks7'; // Chocolate Truffle Cake
    
    // Check admin API
    const adminProductResponse = await fetch(`http://localhost:3000/api/products-with-variants/${productId}`);
    if (adminProductResponse.ok) {
      const adminProduct = await adminProductResponse.json();
      console.log(`   ✅ Admin API - ${adminProduct.name}:`);
      console.log(`      Variants: ${adminProduct.variants?.length || 0}`);
      adminProduct.variants?.forEach((v, i) => {
        console.log(`         ${i + 1}. ${v.weight} - ₹${v.price}`);
      });
    }

    // Check website API for same product
    try {
      const websiteProductResponse = await fetch(`http://localhost:3001/api/products-with-variants/${productId}`);
      if (websiteProductResponse.ok) {
        const websiteProduct = await websiteProductResponse.json();
        console.log(`\n   ✅ Website API - ${websiteProduct.name}:`);
        console.log(`      Variants: ${websiteProduct.variants?.length || 0}`);
        websiteProduct.variants?.forEach((v, i) => {
          console.log(`         ${i + 1}. ${v.weight} - ₹${v.price}`);
        });
      } else {
        console.log(`\n   ❌ Website product API failed: ${websiteProductResponse.status}`);
      }
    } catch (error) {
      console.log(`\n   ❌ Website product API error: ${error.message}`);
    }

    // Step 4: Check website environment
    console.log('\n\n4. ⚙️ CHECKING WEBSITE CONFIGURATION:');
    console.log('   Website should be configured to use:');
    console.log('   NEXT_PUBLIC_API_URL="http://localhost:3000/api"');
    console.log('   \n   If website shows wrong data, check:');
    console.log('   - Is website server running on port 3001?');
    console.log('   - Is website .env file correct?');
    console.log('   - Is website using cached data?');

    console.log('\n\n📋 SUMMARY:');
    console.log('   1. Admin panel has variant data ✅');
    console.log('   2. Database stores variants correctly ✅');
    console.log('   3. Admin API returns variants ✅');
    console.log('   4. Website API status: Check above');
    console.log('   5. Website display: Depends on API');

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

// Run the debug
debugVariantFlow();
