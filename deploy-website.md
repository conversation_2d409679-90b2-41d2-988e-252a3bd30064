# Deployment Instructions for Mispri Website

This document provides step-by-step instructions for deploying the Mispri website to Vercel.

## Prerequisites

1. A Vercel account
2. Access to the NeonDB PostgreSQL database
3. GitHub account

## Step 1: Prepare the Repository

1. Create a new GitHub repository for your website
2. Push your code to this repository:
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/your-repo-name.git
   git push -u origin main
   ```

## Step 2: Configure Environment Variables

Before deploying, make sure you have the following environment variables ready:

1. `DATABASE_URL`: Your NeonDB PostgreSQL connection string (should start with `postgresql://`)
2. `NEXT_PUBLIC_API_URL`: The URL of your admin panel API (e.g., `https://mispri24.vercel.app/api`)

## Step 3: Deploy to Vercel

1. Go to [Vercel](https://vercel.com/) and sign in
2. Click "Add New" > "Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Next.js
   - Root Directory: website (if your repository contains both admin and website)
   - Build Command: `npm run build`
   - Install Command: `npm install`
   - Output Directory: `.next`

5. Configure Environment Variables:
   - Add `DATABASE_URL` with your NeonDB PostgreSQL connection string
   - Add `NEXT_PUBLIC_API_URL` with your admin panel API URL

6. Click "Deploy"

## Step 4: Configure Custom Domain (Optional)

1. In your Vercel project dashboard, go to "Settings" > "Domains"
2. Add your custom domain (e.g., www.yourdomain.com)
3. Follow Vercel's instructions to configure your DNS settings

## Troubleshooting

If you encounter issues during deployment:

1. **Database Connection Issues**:
   - Make sure your `DATABASE_URL` is correct and includes the protocol (`postgresql://`)
   - Check that your database is accessible from Vercel's servers
   - Verify that your database credentials are correct

2. **Build Errors**:
   - Check the build logs in Vercel for specific error messages
   - If there are TypeScript errors, you can set `ignoreBuildErrors: true` in your `next.config.js`
   - If there are ESLint errors, you can set `ignoreDuringBuilds: true` in your `next.config.js`

3. **API Connection Issues**:
   - Make sure your `NEXT_PUBLIC_API_URL` is correct
   - Verify that your admin panel API is accessible

## Post-Deployment

After successful deployment:

1. Test your website thoroughly
2. Check that all pages load correctly
3. Verify that API calls to the admin panel work properly
4. Test the authentication system
5. Test the checkout process

## Updating Your Website

To update your website after making changes:

1. Commit your changes to your GitHub repository
2. Vercel will automatically deploy the changes

If you need to manually trigger a deployment:

1. Go to your project in Vercel
2. Click "Deployments"
3. Click "Redeploy" on the latest deployment
