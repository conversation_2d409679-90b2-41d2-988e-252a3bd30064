#!/bin/bash

# 🚀 Website Deployment Script
# This script helps you deploy your website separately from the admin panel

echo "🚀 Starting Website Deployment Process..."
echo "================================================"

# Check if we're in the right directory
if [ ! -d "website" ]; then
    echo "❌ Error: 'website' folder not found!"
    echo "Please run this script from the root directory where the 'website' folder exists."
    exit 1
fi

echo "✅ Found website folder"

# Navigate to website directory
cd website

echo "📁 Navigating to website directory..."

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found in website folder!"
    exit 1
fi

echo "✅ Found package.json"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create environment file template if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "🔧 Creating environment file template..."
    cat > .env.local << EOL
# Database Connection (Same as admin panel)
DATABASE_URL=your_neondb_connection_string_here

# API Base URL (Point to your admin panel)
NEXT_PUBLIC_API_BASE_URL=https://mispri24.vercel.app/api

# Website URL (Update after deployment)
NEXT_PUBLIC_SITE_URL=https://your-website-domain.vercel.app

# Add other environment variables as needed
EOL
    echo "⚠️  Please update .env.local with your actual values before deploying!"
fi

# Test build
echo "🔨 Testing build process..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Error: Build failed!"
    echo "Please fix the build errors before deploying."
    exit 1
fi

echo "✅ Build successful!"

# Clean up build files for deployment
echo "🧹 Cleaning up for deployment..."
rm -rf .next

echo "🎉 Website is ready for deployment!"
echo "================================================"
echo ""
echo "📋 NEXT STEPS:"
echo ""
echo "1. 🌐 Go to https://vercel.com"
echo "2. ➕ Click 'New Project'"
echo "3. 📂 Import your repository"
echo "4. ⚙️  Set these configuration:"
echo "   - Framework Preset: Next.js"
echo "   - Root Directory: website"
echo "   - Build Command: npm run build"
echo "   - Output Directory: .next"
echo ""
echo "5. 🔧 Add Environment Variables:"
echo "   - DATABASE_URL=your_neondb_connection"
echo "   - NEXT_PUBLIC_API_BASE_URL=https://mispri24.vercel.app/api"
echo "   - NEXT_PUBLIC_SITE_URL=https://your-new-domain.vercel.app"
echo ""
echo "6. 🚀 Click Deploy!"
echo ""
echo "📝 IMPORTANT NOTES:"
echo "- Your admin panel will remain at: https://mispri24.vercel.app"
echo "- Your website will be at a new URL"
echo "- Both will share the same database"
echo "- Update CORS settings in admin panel after deployment"
echo ""
echo "🎊 Happy Deploying!"

# Return to original directory
cd ..

echo "✅ Script completed successfully!"
