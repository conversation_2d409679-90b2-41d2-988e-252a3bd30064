/**
 * Run the development server with a modified Next.js configuration
 * 
 * This script:
 * 1. Temporarily replaces the next.config.js file with a modified version
 * 2. Starts the development server
 * 3. Restores the original configuration when the server is stopped
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Path to Next.js configuration files
const nextConfigPath = path.join(__dirname, 'next.config.js');
const nextConfigDevPath = path.join(__dirname, 'next.config.dev.js');
const nextConfigBackupPath = path.join(__dirname, 'next.config.backup.js');

// Check if the configuration files exist
if (!fs.existsSync(nextConfigDevPath)) {
  console.error(`Modified configuration file not found: ${nextConfigDevPath}`);
  process.exit(1);
}

// Backup the original configuration
if (fs.existsSync(nextConfigPath)) {
  console.log('Backing up original Next.js configuration...');
  fs.copyFileSync(nextConfigPath, nextConfigBackupPath);
  console.log('Backup created successfully.');
} else {
  console.warn('Original Next.js configuration not found. Creating an empty one...');
  fs.writeFileSync(nextConfigBackupPath, '/** @type {import("next").NextConfig} */\nconst nextConfig = {}\n\nmodule.exports = nextConfig');
}

// Replace the configuration with the modified version
console.log('Replacing Next.js configuration with modified version...');
fs.copyFileSync(nextConfigDevPath, nextConfigPath);
console.log('Configuration replaced successfully.');

// Start the development server
console.log('\nStarting development server...');
const devServer = spawn('npx', ['next', 'dev'], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NEXT_TELEMETRY_DISABLED: '1',
  }
});

// Handle server exit
devServer.on('exit', (code) => {
  console.log(`\nDevelopment server exited with code ${code}`);
  
  // Restore the original configuration
  console.log('Restoring original Next.js configuration...');
  fs.copyFileSync(nextConfigBackupPath, nextConfigPath);
  fs.unlinkSync(nextConfigBackupPath);
  console.log('Configuration restored successfully.');
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT. Cleaning up...');
  
  // Restore the original configuration
  console.log('Restoring original Next.js configuration...');
  fs.copyFileSync(nextConfigBackupPath, nextConfigPath);
  fs.unlinkSync(nextConfigBackupPath);
  console.log('Configuration restored successfully.');
  
  process.exit(0);
});
