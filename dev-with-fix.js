/**
 * Run the development server with a fix for the "My Pictures" permission issue
 * 
 * This script:
 * 1. Temporarily renames the problematic directory
 * 2. Starts the development server
 * 3. Restores the original directory name when the server is stopped
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Path to the problematic directory
const myPicturesPath = 'C:\\Users\\<USER>\\Documents\\My Pictures';
const tempPath = 'C:\\Users\\<USER>\\Documents\\My_Pictures_Temp';

// Check if the directory exists
if (fs.existsSync(myPicturesPath)) {
  console.log(`Found problematic directory: ${myPicturesPath}`);
  console.log(`Renaming to: ${tempPath}`);
  
  try {
    // Rename the directory
    fs.renameSync(myPicturesPath, tempPath);
    console.log('Directory renamed successfully.');
    
    // Start the development server
    console.log('\nStarting development server...');
    const devServer = spawn('npx', ['next', 'dev'], {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        NEXT_TELEMETRY_DISABLED: '1',
      }
    });
    
    // Handle server exit
    devServer.on('exit', (code) => {
      console.log(`\nDevelopment server exited with code ${code}`);
      
      // Restore the original directory name
      try {
        if (fs.existsSync(tempPath)) {
          console.log(`\nRestoring original directory name...`);
          fs.renameSync(tempPath, myPicturesPath);
          console.log('Directory restored successfully.');
        }
      } catch (restoreError) {
        console.error(`Failed to restore directory: ${restoreError.message}`);
        console.error(`Please manually rename ${tempPath} back to ${myPicturesPath}`);
      }
    });
    
    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\nReceived SIGINT. Cleaning up...');
      
      // Restore the original directory name
      try {
        if (fs.existsSync(tempPath)) {
          console.log(`Restoring original directory name...`);
          fs.renameSync(tempPath, myPicturesPath);
          console.log('Directory restored successfully.');
        }
      } catch (restoreError) {
        console.error(`Failed to restore directory: ${restoreError.message}`);
        console.error(`Please manually rename ${tempPath} back to ${myPicturesPath}`);
      }
      
      process.exit(0);
    });
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
} else {
  console.log(`Directory does not exist: ${myPicturesPath}`);
  console.log('Starting development server directly...');
  
  // Start the development server
  const devServer = spawn('npx', ['next', 'dev'], {
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
}
