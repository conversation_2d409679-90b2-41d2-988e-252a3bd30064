# 📧 Email Setup Guide - Nodemailer with SMTP

This guide will help you configure email functionality for the Mispri application using Nodemail<PERSON> with various SMTP providers.

## 🚀 Quick Setup

1. **Copy environment variables**:
   ```bash
   cp .env.example .env.local
   ```

2. **Configure your SMTP settings** in `.env.local`

3. **Restart your application** to load the new environment variables

## 📮 Supported Email Providers

### 1. Gmail (Recommended for Development)

**Setup Steps:**
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use these settings:

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
EMAIL_FROM_NAME="Mispri"
```

### 2. Outlook/Hotmail

```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
EMAIL_FROM_NAME="Mispri"
```

### 3. Yahoo Mail

**Setup Steps:**
1. Enable 2-Factor Authentication
2. Generate App Password in Account Security settings
3. Use these settings:

```env
SMTP_HOST=smtp.mail.yahoo.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM_NAME="Mispri"
```

### 4. Custom SMTP (Hosting Provider)

Most hosting providers offer SMTP services:

```env
SMTP_HOST=mail.yourdomain.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
EMAIL_FROM_NAME="Mispri"
```

### 5. Professional Email Services

#### SendGrid
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
EMAIL_FROM_NAME="Mispri"
```

#### Mailgun
```env
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your-mailgun-username
SMTP_PASS=your-mailgun-password
EMAIL_FROM_NAME="Mispri"
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `SMTP_HOST` | SMTP server hostname | `smtp.gmail.com` |
| `SMTP_PORT` | SMTP server port | `587` |
| `SMTP_SECURE` | Use SSL/TLS | `false` for port 587, `true` for port 465 |
| `SMTP_USER` | SMTP username (usually email) | `<EMAIL>` |
| `SMTP_PASS` | SMTP password or app password | `your-app-password` |
| `EMAIL_FROM_NAME` | Display name for emails | `"Mispri"` |
| `NEXT_PUBLIC_WEBSITE_URL` | Website URL for email links | `http://localhost:3001` |

### Port Configuration

- **Port 587**: STARTTLS (recommended) - Set `SMTP_SECURE=false`
- **Port 465**: SSL/TLS - Set `SMTP_SECURE=true`
- **Port 25**: Plain text (not recommended)

## 🧪 Testing Email Configuration

### 1. Test Forgot Password Email

```bash
node scripts/test-forgot-password.js
```

### 2. Test Welcome Email

Register a new customer account through the website.

### 3. Manual Test Script

Create a test script:

```javascript
// test-email.js
const emailService = require('./src/lib/services/nodemailer-service').default;

async function testEmail() {
  const result = await emailService.sendPasswordResetEmail(
    '<EMAIL>',
    'Test User',
    'http://localhost:3001/reset-password?token=test123'
  );
  
  console.log('Email sent:', result);
}

testEmail();
```

## 🔍 Troubleshooting

### Common Issues

1. **"Authentication failed"**
   - Check username/password
   - For Gmail: Use App Password, not regular password
   - Enable "Less secure app access" if needed

2. **"Connection timeout"**
   - Check SMTP host and port
   - Verify firewall settings
   - Try different ports (587, 465, 25)

3. **"Self signed certificate"**
   - Add to Nodemailer config: `tls: { rejectUnauthorized: false }`

4. **Emails not being sent**
   - Check spam folder
   - Verify email service logs
   - Test with a different email provider

### Debug Mode

Enable debug logging by adding to your email service:

```javascript
const transporter = createTransport({
  // ... your config
  debug: true,
  logger: true
});
```

## 🔒 Security Best Practices

1. **Use App Passwords**: Never use your main email password
2. **Environment Variables**: Keep credentials in `.env.local`, never commit to git
3. **Rate Limiting**: Implement rate limiting for email endpoints
4. **Validation**: Always validate email addresses before sending
5. **Error Handling**: Don't expose SMTP errors to users

## 📧 Email Templates

The application includes pre-built email templates:

### Password Reset Email
- Professional HTML design
- Clear call-to-action button
- Security warnings
- Expiration notice

### Welcome Email
- Branded welcome message
- Getting started links
- Company information

### Customization

To customize email templates, edit:
- `src/lib/services/nodemailer-service.ts`
- Update `generatePasswordResetHTML()` and `generatePasswordResetText()` methods

## 🚀 Production Deployment

### Recommended Services for Production

1. **SendGrid** - Reliable, good free tier
2. **Mailgun** - Developer-friendly
3. **Amazon SES** - Cost-effective for high volume
4. **Postmark** - Excellent deliverability

### Production Checklist

- [ ] Use professional email service (not Gmail)
- [ ] Configure SPF, DKIM, DMARC records
- [ ] Set up proper domain authentication
- [ ] Implement email bounce handling
- [ ] Monitor email delivery rates
- [ ] Set up email analytics

## 📊 Monitoring

Monitor email delivery with:
- Email service dashboards
- Application logs
- Bounce/complaint tracking
- Delivery rate monitoring

---

## 🆘 Need Help?

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify your SMTP credentials
3. Test with a simple email client first
4. Check your email provider's documentation
5. Review firewall and network settings
