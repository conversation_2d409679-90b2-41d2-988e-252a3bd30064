# 📧 Gmail SMTP <NAME_EMAIL>

## 🎯 Quick Setup (5 minutes)

### Step 1: Enable 2-Factor Authentication

1. **Go to Google Account Settings**:
   - Visit: https://myaccount.google.com/
   - Sign in with `<EMAIL>`

2. **Navigate to Security**:
   - Click "Security" in the left sidebar
   - Find "2-Step Verification"

3. **Enable 2-Step Verification**:
   - Click "2-Step Verification"
   - Follow the setup process (use your phone number)
   - **This is required for app passwords**

### Step 2: Generate App Password

1. **After enabling 2FA, go back to Security**
2. **Find "App passwords"** (it will only appear after 2FA is enabled)
3. **Click "App passwords"**
4. **Select "Mail" from the dropdown**
5. **Click "Generate"**
6. **Copy the 16-character password** (something like: `abcd efgh ijkl mnop`)

### Step 3: Configure Your Application

1. **Copy your existing `.env` file**:
   ```bash
   cp .env .env.backup
   ```

2. **Add these lines to your `.env` file**:
   ```env
   # Gmail SMTP Configuration
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-16-character-app-password-here
   EMAIL_FROM_NAME="Mispri"
   ```

3. **Replace `your-16-character-app-password-here`** with the password from Step 2

### Step 4: Test the Configuration

1. **Restart your application**:
   ```bash
   npm run dev
   ```

2. **Run the email test**:
   ```bash
   node scripts/test-email-config.js
   ```

3. **Test forgot password**:
   ```bash
   node scripts/test-forgot-password.js
   ```

## 🧪 Testing Steps

### Test 1: Configuration Test
```bash
node scripts/test-email-config.js
```
**Expected**: ✅ SMTP connection successful + test email sent

### Test 2: Forgot Password
1. Go to: http://localhost:3001/forgot-password
2. Enter: `<EMAIL>` (or any email)
3. Check console logs for email content
4. **Expected**: Beautiful HTML email logged to console

### Test 3: Live Email Test
1. Go to: http://localhost:3001/forgot-password
2. Enter: `<EMAIL>`
3. Check your Gmail inbox
4. **Expected**: Actual email received!

## 🔍 Troubleshooting

### "Authentication failed"
- ✅ Make sure 2FA is enabled
- ✅ Use App Password, not your regular Gmail password
- ✅ Check the 16-character password is correct

### "Connection timeout"
- ✅ Check internet connection
- ✅ Try port 465 with `SMTP_SECURE=true`
- ✅ Check firewall settings

### "App passwords not showing"
- ✅ Make sure 2-Step Verification is fully enabled
- ✅ Wait a few minutes after enabling 2FA
- ✅ Refresh the Google Account page

## 📊 Gmail Limits

- **✅ 500 emails per day** (free forever)
- **✅ No monthly limits**
- **✅ No credit card required**
- **✅ Professional deliverability**

## 🎉 Success Indicators

When everything is working, you'll see:

1. **Console Output**:
   ```
   ✅ SMTP connection successful!
   ✅ Test email sent successfully!
   📧 Message ID: <some-id>
   📬 Check your inbox: <EMAIL>
   ```

2. **Email in Gmail Inbox**:
   - Subject: "🧪 Test Email from Mispri - Email Configuration Working!"
   - From: Mispri <<EMAIL>>
   - Beautiful HTML content

3. **Forgot Password Working**:
   - Users receive actual reset emails
   - Professional Mispri-branded design
   - Working reset links

## 🚀 Production Notes

For production deployment:
- ✅ Gmail SMTP works perfectly
- ✅ Update `NEXT_PUBLIC_WEBSITE_URL` to your domain
- ✅ Consider upgrading to Google Workspace for custom domain
- ✅ Monitor daily email usage (500/day limit)

## 📞 Need Help?

If you encounter issues:
1. Check the console logs for detailed errors
2. Verify your app password is correct
3. Make sure 2FA is properly enabled
4. Test with a simple email client first

---

## ✅ Quick Checklist

- [ ] 2-Factor Authentication enabled on Gmail
- [ ] App password generated
- [ ] SMTP settings added to `.env` file
- [ ] Application restarted
- [ ] Test email configuration script run
- [ ] Forgot password functionality tested
- [ ] Actual email received in Gmail inbox

**Once all checkboxes are ✅, your email system is fully operational!** 🎊
