const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Function to check if a directory exists
function directoryExists(dirPath) {
  try {
    return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  } catch (error) {
    return false;
  }
}

// Function to safely execute a command
function safeExec(command) {
  try {
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Command failed: ${command}`);
    console.error(error.message);
    return false;
  }
}

// List of problematic directories
const problematicDirs = [
  'C:\\Users\\<USER>\\Application Data',
  'C:\\Users\\<USER>\\Application Data_backup_1746554862232',
  'C:\\Users\\<USER>\\Cookies',
  'C:\\Users\\<USER>\\Local Settings',
  'C:\\Users\\<USER>\\NetHood',
  'C:\\Users\\<USER>\\PrintHood',
  'C:\\Users\\<USER>\\Recent',
  'C:\\Users\\<USER>\\SendTo',
  'C:\\Users\\<USER>\\Templates',
  'C:\\Users\\<USER>\\Start Menu',
  'C:\\Users\\<USER>\\My Documents',
  'C:\\Users\\<USER>\\My Music',
  'C:\\Users\\<USER>\\My Pictures',
  'C:\\Users\\<USER>\\My Videos'
];

// Create a temporary directory
const tempDir = path.join(os.tmpdir(), 'nextjs-build-' + Date.now());
fs.mkdirSync(tempDir, { recursive: true });
console.log(`Created temporary directory: ${tempDir}`);

// Process each problematic directory
for (const dir of problematicDirs) {
  if (directoryExists(dir)) {
    console.log(`Found problematic directory: ${dir}`);
    
    // Create a safe name for the backup
    const dirName = path.basename(dir);
    const backupDir = path.join(path.dirname(dir), `${dirName}.bak`);
    
    // Try to rename the directory
    try {
      console.log(`Renaming ${dir} to ${backupDir}...`);
      fs.renameSync(dir, backupDir);
      console.log(`Successfully renamed to ${backupDir}`);
    } catch (error) {
      console.error(`Failed to rename directory: ${error.message}`);
      
      // If rename fails, try to use junction points
      console.log(`Trying to create a junction point...`);
      const junctionDir = path.join(tempDir, dirName);
      fs.mkdirSync(junctionDir, { recursive: true });
      
      // Create a junction point using mklink
      const mkLinkCommand = `mklink /J "${dir}" "${junctionDir}"`;
      if (safeExec(mkLinkCommand)) {
        console.log(`Created junction point: ${dir} -> ${junctionDir}`);
      } else {
        console.error(`Failed to create junction point for ${dir}`);
      }
    }
  }
}

// Now run the build with environment variables to disable problematic features
console.log('\nRunning build command with optimized settings...');
try {
  execSync('npm run build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
} finally {
  // Clean up: Remove junction points and restore original directories
  console.log('\nCleaning up...');
  for (const dir of problematicDirs) {
    // Check if it's a junction point now
    try {
      const stats = fs.lstatSync(dir);
      if (stats.isSymbolicLink() || (stats.isDirectory() && stats.nlink > 1)) {
        console.log(`Removing junction point: ${dir}`);
        safeExec(`rmdir "${dir}"`);
      }
    } catch (error) {
      // Directory might not exist, which is fine
    }
    
    // Check if there's a backup to restore
    const dirName = path.basename(dir);
    const backupDir = path.join(path.dirname(dir), `${dirName}.bak`);
    if (directoryExists(backupDir)) {
      console.log(`Restoring ${backupDir} to ${dir}...`);
      try {
        fs.renameSync(backupDir, dir);
        console.log(`Successfully restored ${dir}`);
      } catch (error) {
        console.error(`Failed to restore directory: ${error.message}`);
      }
    }
  }
  
  // Remove the temporary directory
  try {
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log(`Removed temporary directory: ${tempDir}`);
  } catch (error) {
    console.error(`Failed to remove temporary directory: ${error.message}`);
  }
}
