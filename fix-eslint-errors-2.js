const fs = require('fs');
const path = require('path');

// Function to fix unused variables by adding underscore prefix
function fixUnusedVariables(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Fix unused request parameter
  if (content.includes('request: NextRequest') && !content.includes('request.')) {
    content = content.replace(/request: NextRequest/g, '_request: NextRequest');
  }
  
  // Fix unused variables with destructuring
  if (content.includes('const { password: _, ...userData } = user;')) {
    content = content.replace('const { password: _, ...userData } = user;', 'const { password: _password, ...userData } = user;');
  }
  
  // Fix JSX components that are using renamed imports
  const iconComponents = ['Plus', 'Minus', 'Edit', 'Eye', 'Trash', 'Phone', 'Mail', 'MapPin', 'CreditCard', 'Printer', 'Receipt', 'DollarSign'];
  
  iconComponents.forEach(component => {
    // Fix JSX opening tags
    const openingTagRegex = new RegExp(`<${component}(\\s|>|\\/)`, 'g');
    if (content.match(openingTagRegex)) {
      content = content.replace(openingTagRegex, `<${component.toLowerCase()}$1`);
    }
    
    // Fix JSX closing tags
    const closingTagRegex = new RegExp(`<\\/${component}>`, 'g');
    if (content.match(closingTagRegex)) {
      content = content.replace(closingTagRegex, `</${component.toLowerCase()}>`);
    }
  });
  
  // Fix unescaped entities in JSX
  content = content.replace(/"([^"]*)"([^"]*)"([^"]*)"/g, '"$1&quot;$2&quot;$3"');
  
  // Fix any type
  content = content.replace(/: any([,)])/g, ': unknown$1');
  content = content.replace(/: any\[\]/g, ': Record<string, unknown>[]');
  
  fs.writeFileSync(filePath, content);
  console.log(`Fixed: ${filePath}`);
}

// Process all TypeScript files in the src/app directory
function processFiles(directory) {
  const files = fs.readdirSync(directory);
  
  files.forEach(file => {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processFiles(filePath);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fixUnusedVariables(filePath);
    }
  });
}

// Start processing files
processFiles(path.join(__dirname, 'src', 'app'));
console.log('ESLint error fixing completed!');
