/**
 * Fix build error related to "My Pictures" directory
 * 
 * This script:
 * 1. Temporarily renames the "My Pictures" directory
 * 2. Runs the build command
 * 3. Restores the original directory name
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Path to the problematic directory
const myPicturesPath = 'C:\\Users\\<USER>\\Documents\\My Pictures';
const tempPath = 'C:\\Users\\<USER>\\Documents\\My_Pictures_Temp';

// Function to safely execute a command
function safeExec(command, options = {}) {
  try {
    console.log(`Executing: ${command}`);
    execSync(command, { stdio: 'inherit', ...options });
    return true;
  } catch (error) {
    console.error(`Command failed: ${command}`);
    console.error(error.message);
    return false;
  }
}

// Main function
async function fixBuildError() {
  console.log('=== Fixing "My Pictures" Build Error ===\n');
  
  // Check if the directory exists
  if (!fs.existsSync(myPicturesPath)) {
    console.log(`Directory does not exist: ${myPicturesPath}`);
    console.log('Running build command directly...');
    return safeExec('npm run build', {
      env: {
        ...process.env,
        NEXT_DISABLE_ESLINT: '1',
        NODE_OPTIONS: '--max-old-space-size=4096',
        NEXT_TELEMETRY_DISABLED: '1',
      }
    });
  }
  
  try {
    // Step 1: Rename the directory
    console.log(`Renaming ${myPicturesPath} to ${tempPath}...`);
    fs.renameSync(myPicturesPath, tempPath);
    console.log('Directory renamed successfully.');
    
    // Step 2: Run the build command
    console.log('\nRunning build command...');
    const buildSuccess = safeExec('npm run build', {
      env: {
        ...process.env,
        NEXT_DISABLE_ESLINT: '1',
        NODE_OPTIONS: '--max-old-space-size=4096',
        NEXT_TELEMETRY_DISABLED: '1',
      }
    });
    
    if (buildSuccess) {
      console.log('\nBuild completed successfully!');
    } else {
      console.error('\nBuild failed.');
    }
    
    return buildSuccess;
  } catch (error) {
    console.error(`Error: ${error.message}`);
    return false;
  } finally {
    // Step 3: Restore the original directory name
    try {
      if (fs.existsSync(tempPath)) {
        console.log(`\nRestoring original directory name...`);
        fs.renameSync(tempPath, myPicturesPath);
        console.log('Directory restored successfully.');
      }
    } catch (restoreError) {
      console.error(`Failed to restore directory: ${restoreError.message}`);
      console.error(`Please manually rename ${tempPath} back to ${myPicturesPath}`);
    }
  }
}

// Run the function
fixBuildError().then(success => {
  if (success) {
    console.log('\n=== Build process completed successfully! ===');
    process.exit(0);
  } else {
    console.error('\n=== Build process failed. ===');
    console.log('Consider deploying to Vercel using GitHub integration instead.');
    console.log('See vercel-github-deployment-guide.md for instructions.');
    process.exit(1);
  }
}).catch(error => {
  console.error(`Unexpected error: ${error.message}`);
  process.exit(1);
});
