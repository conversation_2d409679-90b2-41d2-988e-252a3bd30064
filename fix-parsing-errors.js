const fs = require('fs');
const path = require('path');

// Function to fix parsing errors in JSX files
function fixParsingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // Restore icon component names to their original form
    const iconComponents = ['plus', 'minus', 'edit', 'eye', 'trash', 'phone', 'mail', 'mappin', 'creditcard', 'printer', 'receipt', 'dollarsign'];
    
    iconComponents.forEach(component => {
      // Fix JSX opening tags
      const openingTagRegex = new RegExp(`<${component}(\\s|>|\\/)`, 'g');
      if (content.match(openingTagRegex)) {
        const capitalizedComponent = component.charAt(0).toUpperCase() + component.slice(1);
        content = content.replace(openingTagRegex, `<${capitalizedComponent}$1`);
      }
      
      // Fix JSX closing tags
      const closingTagRegex = new RegExp(`<\\/${component}>`, 'g');
      if (content.match(closingTagRegex)) {
        const capitalizedComponent = component.charAt(0).toUpperCase() + component.slice(1);
        content = content.replace(closingTagRegex, `</${capitalizedComponent}>`);
      }
    });
    
    // Fix unterminated string literals
    content = content.replace(/("(?:[^"\\]|\\.)*$)/g, '$1"');
    
    // Only write the file if changes were made
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed parsing errors in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}: ${error.message}`);
  }
}

// Process all TypeScript files in the src/app directory
function processFiles(directory) {
  const files = fs.readdirSync(directory);
  
  files.forEach(file => {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processFiles(filePath);
    } else if (file.endsWith('.tsx')) {
      fixParsingErrors(filePath);
    }
  });
}

// Start processing files
processFiles(path.join(__dirname, 'src', 'app'));
console.log('Parsing error fixing completed!');
