const fs = require('fs');
const path = require('path');

// Function to fix remaining ESLint errors
function fixRemainingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // Fix unused request parameter
    if (content.includes('request: NextRequest') && !content.includes('request.')) {
      content = content.replace(/request: NextRequest/g, '_request: NextRequest');
    }
    
    // Fix unused imports
    const unusedImports = [
      'generateOrderNumber', 'generateInvoiceNumber', 'err'
    ];
    
    unusedImports.forEach(importName => {
      // Remove import statements with the unused import
      const importRegex = new RegExp(`import\\s+${importName}\\s+from\\s+['\"][^'\"]+['\"];?\\\\n?`, 'g');
      if (content.match(importRegex)) {
        content = content.replace(importRegex, '');
      }
      
      // Remove from destructured imports
      const destructureRegex = new RegExp(`({[^}]*?)(\\\\s*${importName}\\\\s*,?)([^}]*?})`, 'g');
      if (content.match(destructureRegex)) {
        content = content.replace(destructureRegex, (match, start, target, end) => {
          return `${start}${end}`;
        });
      }
    });
    
    // Fix any type
    content = content.replace(/: any([,)])/g, ': unknown$1');
    content = content.replace(/: any\[\]/g, ': Record<string, unknown>[]');
    
    // Fix unused variables
    if (content.includes('_password') && !content.includes('_password.')) {
      content = content.replace('_password', '__password');
    }
    
    if (content.includes('_product') && !content.includes('_product.')) {
      content = content.replace(/_product/g, '__product');
    }
    
    // Only write the file if changes were made
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed remaining errors in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}: ${error.message}`);
  }
}

// Process all TypeScript files in the src/app directory
function processFiles(directory) {
  const files = fs.readdirSync(directory);
  
  files.forEach(file => {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processFiles(filePath);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fixRemainingErrors(filePath);
    }
  });
}

// Start processing files
processFiles(path.join(__dirname, 'src', 'app'));
console.log('Remaining ESLint error fixing completed!');
