/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer }) => {
    // Exclude problematic directories
    config.watchOptions = {
      ...config.watchOptions,
      ignored: [
        '**/node_modules/**',
        '**/Application Data/**',
        '**/Application Data_backup_*/**',
        '**/Cookies/**',
        '**/AppData/**',
        '**/Local Settings/**',
        '**/NetHood/**',
        '**/PrintHood/**',
        '**/Recent/**',
        '**/SendTo/**',
        '**/Templates/**',
        '**/Start Menu/**',
        '**/My Documents/**',
        '**/My Music/**',
        '**/My Pictures/**',
        '**/My Videos/**',
        '**/Documents/My Pictures/**',
        '**/Documents/My Music/**',
        '**/Documents/My Videos/**',
        'C:/Users/<USER>/Documents/My Pictures/**',
        'C:\\\\Users\\\\<USER>\\\\Documents\\\\My Pictures/**',
      ]
    };
    
    // Disable file system access for webpack
    config.snapshot = {
      ...config.snapshot,
      managedPaths: [],
      immutablePaths: [],
    };
    
    return config;
  },
  images: {
    domains: ['picsum.photos', 'mispri-pi.vercel.app', 'images.unsplash.com'],
    unoptimized: true,
  },
}

module.exports = nextConfig
