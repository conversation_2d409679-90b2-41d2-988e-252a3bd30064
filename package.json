{"name": "mispri", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "build-no-lint": "set NEXT_DISABLE_ESLINT=1 && next build", "build-custom": "node build-custom.js", "start": "next start", "start-all": "node start-all.js", "lint": "next lint", "lint-fix": "next lint --fix", "lint-app": "eslint src/app/**/*.ts src/app/**/*.tsx --max-warnings=0", "lint-app-fix": "eslint src/app/**/*.ts src/app/**/*.tsx --fix", "db:seed": "node prisma/seed.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "setup-db": "node setup-db.js", "setup-db-simple": "node setup-db-simple.js", "create-admin": "node create-admin.js", "create-admin-sql": "node create-admin-sql.js", "check-schema": "node check-schema.js", "check-data": "node check-data.js", "setup-db-new": "node setup-db-new.js"}, "dependencies": {"@prisma/client": "^6.6.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.74.4", "@types/bcrypt": "^5.0.2", "autoprefixer": "^10.4.21", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "lucide-react": "^0.488.0", "next": "15.3.1", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "pg": "^8.14.1", "postcss": "^8.5.4", "prisma": "^6.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "recharts": "^2.15.3", "tailwindcss": "^4.1.8", "zod": "^3.24.3"}, "prisma": {"seed": "node prisma/seed.js"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "ts-node": "^10.9.2", "typescript": "^5"}}