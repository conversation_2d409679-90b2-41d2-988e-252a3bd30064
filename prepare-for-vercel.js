/**
 * Prepare the repository for Vercel deployment
 * 
 * This script:
 * 1. Creates a vercel.json file with optimized settings
 * 2. Updates next.config.js to be compatible with Vercel
 * 3. Creates a .env.production file with necessary environment variables
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create a readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function prepareForVercel() {
  console.log('=== Preparing for Vercel Deployment ===\n');
  
  // Step 1: Create vercel.json
  console.log('Creating vercel.json...');
  const vercelJsonPath = path.join(__dirname, 'vercel.json');
  const vercelJson = {
    "version": 2,
    "buildCommand": "npm run build",
    "outputDirectory": ".next",
    "framework": "nextjs",
    "env": {
      "NEXT_TELEMETRY_DISABLED": "1",
      "NEXT_DISABLE_ESLINT": "1"
    }
  };
  
  fs.writeFileSync(vercelJsonPath, JSON.stringify(vercelJson, null, 2));
  console.log('✅ Created vercel.json');
  
  // Step 2: Check and update next.config.js
  console.log('\nChecking next.config.js...');
  const nextConfigPath = path.join(__dirname, 'next.config.js');
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Check if output is set to 'standalone' and remove it if it is
  if (nextConfig.includes("output: 'standalone'")) {
    console.log('Removing "output: standalone" from next.config.js (not needed for Vercel)...');
    nextConfig = nextConfig.replace(/output: ['"]standalone['"],?\n?/g, '');
    fs.writeFileSync(nextConfigPath, nextConfig);
    console.log('✅ Updated next.config.js');
  } else {
    console.log('✅ next.config.js is already compatible with Vercel');
  }
  
  // Step 3: Create .env.production file
  console.log('\nCreating .env.production file...');
  const envPath = path.join(__dirname, '.env.production');
  
  // Check if .env file exists and read it
  let envVars = {};
  const dotEnvPath = path.join(__dirname, '.env');
  if (fs.existsSync(dotEnvPath)) {
    const envContent = fs.readFileSync(dotEnvPath, 'utf8');
    const envLines = envContent.split('\n');
    
    for (const line of envLines) {
      if (line.trim() && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('=');
        if (key && value) {
          envVars[key.trim()] = value.trim();
        }
      }
    }
  }
  
  // Ask for DATABASE_URL if not found
  if (!envVars.DATABASE_URL) {
    const dbUrl = await prompt('Enter your DATABASE_URL (PostgreSQL connection string): ');
    if (dbUrl) {
      envVars.DATABASE_URL = dbUrl;
    }
  }
  
  // Set required environment variables
  envVars.NEXT_TELEMETRY_DISABLED = '1';
  envVars.NEXT_DISABLE_ESLINT = '1';
  
  // Write to .env.production
  let envContent = '';
  for (const [key, value] of Object.entries(envVars)) {
    envContent += `${key}=${value}\n`;
  }
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Created .env.production');
  
  console.log('\n=== Preparation Complete ===');
  console.log('Your repository is now ready for Vercel deployment.');
  console.log('Follow the steps in vercel-github-deployment-guide.md to deploy your application.');
  
  rl.close();
}

// Run the preparation function
prepareForVercel().catch(error => {
  console.error('Error:', error);
  rl.close();
  process.exit(1);
});
