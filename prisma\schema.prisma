generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String               @id @default(cuid())
  name                String
  email               String               @unique
  password            String
  role                UserRole             @default(STAFF)
  storeId             String?              @map("store_id")
  resetToken          String?              @map("reset_token")
  resetTokenExpiry    DateTime?            @map("reset_token_expiry")
  resetOTP            String?              @map("reset_otp")
  resetOTPExpiry      DateTime?            @map("reset_otp_expiry")
  createdAt           DateTime             @default(now()) @map("created_at")
  updatedAt           DateTime             @updatedAt @map("updated_at")
  customerVisits      CustomerVisit[]
  expenses            Expense[]
  transactions        Transaction[]
  store               Store?               @relation(fields: [storeId], references: [id])
  wishlist_items      wishlist_items[]
  customer            Customer?
  cart                Cart?
  contactSubmissions  ContactSubmission[]
  assignedOrders      Order[]              @relation("OrderAssignedBy")

  @@map("users")
}

model Warehouse {
  id              String               @id @default(cuid())
  name            String
  location        String
  createdAt       DateTime             @default(now()) @map("created_at")
  updatedAt       DateTime             @updatedAt @map("updated_at")
  sourceTransfers InventoryTransfer[]  @relation("SourceWarehouse")
  transfers       Transfer[]
  inventory       WarehouseInventory[]

  @@map("warehouses")
}

model Store {
  id             String           @id @default(cuid())
  name           String
  location       String
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")
  customerVisits CustomerVisit[]
  expenses       Expense[]
  inventory      StoreInventory[]
  transactions   Transaction[]
  transfers      Transfer[]
  users          User[]
  orders         Order[]

  @@map("stores")
}

model Product {
  id                 String               @id @default(cuid())
  name               String
  description        String?
  category           String
  price              Float                // Base price (will be overridden by variants)
  discountedPrice    Float?               @map("discounted_price") // Discounted price for the product
  costPrice          Float                @map("cost_price")
  unit               String
  lowStockThreshold  Int                  @default(10) @map("low_stock_threshold")
  createdAt          DateTime             @default(now()) @map("created_at")
  updatedAt          DateTime             @updatedAt @map("updated_at")
  imageUrl           String?              @map("image_url")
  sku                String?
  metaTitle          String?              @map("meta_title")
  metaDescription    String?              @map("meta_description")
  isActive           Boolean              @default(true) @map("is_active")
  inventoryTransfers InventoryTransfer[]
  rawMaterials       ProductRawMaterial[]
  product_reviews    product_reviews[]
  storeInventory     StoreInventory[]
  transactionItems   TransactionItem[]
  warehouseInventory WarehouseInventory[]
  wastage            Wastage[]
  wishlist_items     wishlist_items[]
  productImages      ProductImage[]
  cartItems          CartItem[]
  orderItems         OrderItem[]
  relatedProducts    ProductRelation[]    @relation("ProductToRelated")
  relatedToProducts  ProductRelation[]    @relation("RelatedToProduct")
  variants           ProductVariant[]     // New: Product variants for different weights/sizes

  @@map("products")
}

model WarehouseInventory {
  id          String    @id @default(cuid())
  warehouseId String    @map("warehouse_id")
  productId   String    @map("product_id")
  quantity    Float
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  product     Product   @relation(fields: [productId], references: [id])
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])

  @@unique([warehouseId, productId])
  @@map("warehouse_inventory")
}

model StoreInventory {
  id        String   @id @default(cuid())
  storeId   String   @map("store_id")
  productId String   @map("product_id")
  quantity  Float
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  product   Product  @relation(fields: [productId], references: [id])
  store     Store    @relation(fields: [storeId], references: [id])

  @@unique([storeId, productId])
  @@map("store_inventory")
}

model InventoryTransfer {
  id                String    @id @default(cuid())
  productId         String    @map("product_id")
  sourceWarehouseId String    @map("source_warehouse_id")
  destinationType   String    @map("destination_type")
  destinationId     String    @map("destination_id")
  quantity          Float
  notes             String?
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  product           Product   @relation(fields: [productId], references: [id])
  sourceWarehouse   Warehouse @relation("SourceWarehouse", fields: [sourceWarehouseId], references: [id])

  @@map("inventory_transfers")
}

model Transfer {
  id          String         @id @default(cuid())
  warehouseId String         @map("warehouse_id")
  storeId     String         @map("store_id")
  status      TransferStatus @default(PENDING)
  createdAt   DateTime       @default(now()) @map("created_at")
  updatedAt   DateTime       @updatedAt @map("updated_at")
  items       TransferItem[]
  store       Store          @relation(fields: [storeId], references: [id])
  warehouse   Warehouse      @relation(fields: [warehouseId], references: [id])

  @@map("transfers")
}

model TransferItem {
  id         String   @id @default(cuid())
  transferId String   @map("transfer_id")
  productId  String   @map("product_id")
  quantity   Float
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  transfer   Transfer @relation(fields: [transferId], references: [id])

  @@map("transfer_items")
}

model Transaction {
  id                String            @id @default(cuid())
  type              TransactionType
  storeId           String?           @map("store_id")
  userId            String            @map("user_id")
  partyName         String?           @map("party_name")
  partyContact      String?           @map("party_contact")
  totalAmount       Float             @map("total_amount")
  discount          Float             @default(0)
  paymentMethod     PaymentMethod     @default(CASH) @map("payment_method")
  status            TransactionStatus @default(COMPLETED)
  orderNumber       String?           @map("order_number")
  shippingAddressId String?           @map("shipping_address_id")
  billingAddressId  String?           @map("billing_address_id")
  trackingNumber    String?           @map("tracking_number")
  paymentStatus     PaymentStatus?    @default(PENDING) @map("payment_status")
  orderStatus       OrderStatus?      @map("order_status")
  couponId          String?           @map("coupon_id")
  notes             String?
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @updatedAt @map("updated_at")
  items             TransactionItem[]
  store             Store?            @relation(fields: [storeId], references: [id])
  user              User              @relation(fields: [userId], references: [id])
  shippingAddress   Address?          @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  billingAddress    Address?          @relation("BillingAddress", fields: [billingAddressId], references: [id])
  coupon            Coupon?           @relation(fields: [couponId], references: [id])

  @@map("transactions")
}

model TransactionItem {
  id            String      @id @default(cuid())
  transactionId String      @map("transaction_id")
  productId     String      @map("product_id")
  quantity      Float
  unitPrice     Float       @map("unit_price")
  totalPrice    Float       @map("total_price")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")
  product       Product     @relation(fields: [productId], references: [id])
  transaction   Transaction @relation(fields: [transactionId], references: [id])

  @@map("transaction_items")
}

model RawMaterial {
  id                String                   @id @default(cuid())
  name              String
  unit              String
  costPerUnit       Float                    @map("cost_per_unit")
  currentStock      Float                    @map("current_stock")
  lowStockThreshold Int                      @default(10) @map("low_stock_threshold")
  createdAt         DateTime                 @default(now()) @map("created_at")
  updatedAt         DateTime                 @updatedAt @map("updated_at")
  products          ProductRawMaterial[]
  consumption       RawMaterialConsumption[]

  @@map("raw_materials")
}

model ProductRawMaterial {
  id               String      @id @default(cuid())
  productId        String      @map("product_id")
  rawMaterialId    String      @map("raw_material_id")
  quantityRequired Float       @map("quantity_required")
  createdAt        DateTime    @default(now()) @map("created_at")
  updatedAt        DateTime    @updatedAt @map("updated_at")
  product          Product     @relation(fields: [productId], references: [id])
  rawMaterial      RawMaterial @relation(fields: [rawMaterialId], references: [id])

  @@unique([productId, rawMaterialId])
  @@map("product_raw_materials")
}

model Production {
  id          String                   @id @default(cuid())
  productId   String                   @map("product_id")
  quantity    Float
  date        DateTime
  createdAt   DateTime                 @default(now()) @map("created_at")
  updatedAt   DateTime                 @updatedAt @map("updated_at")
  consumption RawMaterialConsumption[]

  @@map("production")
}

model RawMaterialConsumption {
  id            String      @id @default(cuid())
  productionId  String      @map("production_id")
  rawMaterialId String      @map("raw_material_id")
  quantity      Float
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")
  production    Production  @relation(fields: [productionId], references: [id])
  rawMaterial   RawMaterial @relation(fields: [rawMaterialId], references: [id])

  @@map("raw_material_consumption")
}

model Expense {
  id          String   @id @default(cuid())
  storeId     String?  @map("store_id")
  userId      String   @map("user_id")
  category    String
  amount      Float
  description String?
  date        DateTime
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  store       Store?   @relation(fields: [storeId], references: [id])
  user        User     @relation(fields: [userId], references: [id])

  @@map("expenses")
}

model BankAccount {
  id            String            @id @default(cuid())
  bankName      String            @map("bank_name")
  accountNumber String            @map("account_number")
  balance       Float
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")
  transactions  BankTransaction[]

  @@map("bank_accounts")
}

model BankTransaction {
  id            String              @id @default(cuid())
  bankAccountId String              @map("bank_account_id")
  type          BankTransactionType
  amount        Float
  description   String?
  date          DateTime
  createdAt     DateTime            @default(now()) @map("created_at")
  updatedAt     DateTime            @updatedAt @map("updated_at")
  bankAccount   BankAccount         @relation(fields: [bankAccountId], references: [id])

  @@map("bank_transactions")
}

model Wastage {
  id        String   @id @default(cuid())
  productId String   @map("product_id")
  quantity  Float
  reason    String?
  date      DateTime
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  product   Product  @relation(fields: [productId], references: [id])

  @@map("wastage")
}

model CustomerVisit {
  id              String   @id @default(cuid())
  storeId         String   @map("store_id")
  userId          String   @map("user_id")
  customerName    String   @map("customer_name")
  customerContact String?  @map("customer_contact")
  purpose         String?
  date            DateTime
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  store           Store    @relation(fields: [storeId], references: [id])
  user            User     @relation(fields: [userId], references: [id])

  @@map("customer_visits")
}

model Recipe {
  id              String              @id @default(cuid())
  name            String
  description     String
  preparationTime Int
  bakingTime      Int
  restingTime     Int?
  totalTime       Int?
  yield           Int
  yieldUnit       String
  difficulty      String
  category        String
  notes           String?
  imageUrl        String?
  costPerUnit     Float?
  sellingPrice    Float?
  profitMargin    Float?
  isActive        Boolean             @default(true)
  createdAt       DateTime            @default(now()) @map("created_at")
  updatedAt       DateTime            @updatedAt @map("updated_at")
  ingredients     RecipeIngredient[]
  instructions    RecipeInstruction[]
  nutritionInfo   RecipeNutrition?
  tags            RecipeTag[]
  tips            RecipeTip[]

  @@map("recipes")
}

model RecipeTag {
  id        String   @id @default(cuid())
  recipeId  String   @map("recipe_id")
  name      String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  recipe    Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  @@map("recipe_tags")
}

model RecipeIngredient {
  id          String   @id @default(cuid())
  recipeId    String   @map("recipe_id")
  name        String
  quantity    Float
  unit        String
  cost        Float?
  category    String?
  isOptional  Boolean  @default(false) @map("is_optional")
  substitutes String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  recipe      Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  @@map("recipe_ingredients")
}

model RecipeInstruction {
  id         String   @id @default(cuid())
  recipeId   String   @map("recipe_id")
  stepNumber Int      @map("step_number")
  text       String
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  recipe     Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  @@map("recipe_instructions")
}

model RecipeTip {
  id        String   @id @default(cuid())
  recipeId  String   @map("recipe_id")
  text      String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  recipe    Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  @@map("recipe_tips")
}

model RecipeNutrition {
  id        String   @id @default(cuid())
  recipeId  String   @unique @map("recipe_id")
  calories  Float?
  protein   Float?
  carbs     Float?
  fat       Float?
  fiber     Float?
  sugar     Float?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  recipe    Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  @@map("recipe_nutrition")
}

model ApiIntegration {
  id          String   @id @default(cuid())
  type        ApiType
  name        String
  isActive    Boolean  @default(false) @map("is_active")
  credentials Json?
  settings    Json?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("api_integrations")
}

model SystemSetting {
  id        String   @id @default(cuid())
  category  String
  key       String
  value     String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([category, key])
  @@map("system_settings")
}

model product_reviews {
  id          String       @id
  product_id  String
  user_id     String?
  user_name   String
  user_email  String
  rating      Int
  title       String?
  comment     String
  status      ReviewStatus @default(PENDING)
  is_verified Boolean      @default(false)
  created_at  DateTime     @default(now())
  updated_at  DateTime
  products    Product      @relation(fields: [product_id], references: [id])
}

model wishlist_items {
  id         String   @id
  user_id    String
  product_id String
  created_at DateTime @default(now())
  updated_at DateTime
  products   Product  @relation(fields: [product_id], references: [id])
  users      User     @relation(fields: [user_id], references: [id])

  @@unique([user_id, product_id])
}

enum UserRole {
  ADMIN
  WAREHOUSE_MANAGER
  STORE_MANAGER
  STAFF
  CUSTOMER
}

enum TransferStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum TransactionType {
  SALE
  PURCHASE
}

enum PaymentMethod {
  CASH
  CARD
  BANK_TRANSFER
  ONLINE
  PAYPAL
  GOOGLE_PAY
  APPLE_PAY
  RAZORPAY
  STRIPE
  BUY_NOW_PAY_LATER
  COD
}

enum TransactionStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum BankTransactionType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
}

enum ApiType {
  PAYMENT_GATEWAY
  WHATSAPP
  FACEBOOK_PIXEL
  GOOGLE_TAG_MANAGER
  EMAIL_SERVICE
  SMS_SERVICE
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

enum AddressType {
  SHIPPING
  BILLING
  BOTH
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  RETURNED
  PENDING_ASSIGNMENT  // Order placed but not assigned to any store
  ASSIGNED           // Order assigned to store by super admin
  IN_PROGRESS        // Store is working on the order
  COMPLETED          // Order completed by store
}

enum OrderType {
  ONLINE
  STORE
}

model Customer {
  id                String    @id @default(cuid())
  userId            String    @unique @map("user_id")
  firstName         String    @map("first_name")
  lastName          String    @map("last_name")
  phone             String?
  birthdate         DateTime?
  isSubscribed      Boolean   @default(false) @map("is_subscribed")
  notes             String?
  loyaltyPoints     Int       @default(0) @map("loyalty_points")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  user              User      @relation(fields: [userId], references: [id])
  addresses         Address[]
  customerTags      CustomerTag[]
  orders            Order[]

  @@map("customers")
}

model CustomerTag {
  id         String   @id @default(cuid())
  customerId String   @map("customer_id")
  name       String
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_tags")
}

model Address {
  id                String      @id @default(cuid())
  customerId        String      @map("customer_id")
  type              AddressType @default(BOTH)
  isDefault         Boolean     @default(false) @map("is_default")
  firstName         String?     @map("first_name")
  lastName          String?     @map("last_name")
  phone             String?
  street            String
  city              String
  state             String
  postalCode        String      @map("postal_code")
  country           String
  createdAt         DateTime    @default(now()) @map("created_at")
  updatedAt         DateTime    @updatedAt @map("updated_at")
  customer          Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  shippingAddresses Transaction[] @relation("ShippingAddress")
  billingAddresses  Transaction[] @relation("BillingAddress")
  orders            Order[]

  @@map("addresses")
}

model Cart {
  id        String     @id @default(cuid())
  userId    String     @unique @map("user_id")
  createdAt DateTime   @default(now()) @map("created_at")
  updatedAt DateTime   @updatedAt @map("updated_at")
  items     CartItem[]
  user      User       @relation(fields: [userId], references: [id])

  @@map("carts")
}

model CartItem {
  id        String          @id @default(cuid())
  cartId    String          @map("cart_id")
  productId String          @map("product_id")
  variantId String?         @map("variant_id") // New: Selected variant
  quantity  Int
  createdAt DateTime        @default(now()) @map("created_at")
  updatedAt DateTime        @updatedAt @map("updated_at")
  cart      Cart            @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product   Product         @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("cart_items")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String   @map("product_id")
  url       String
  alt       String?
  isMain    Boolean  @default(false) @map("is_main")
  sortOrder Int      @default(0) @map("sort_order")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductRelation {
  id              String   @id @default(cuid())
  productId       String   @map("product_id")
  relatedProductId String   @map("related_product_id")
  relationType    String   @default("related") @map("relation_type")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  product         Product  @relation("ProductToRelated", fields: [productId], references: [id], onDelete: Cascade)
  relatedProduct  Product  @relation("RelatedToProduct", fields: [relatedProductId], references: [id], onDelete: Cascade)

  @@unique([productId, relatedProductId])
  @@map("product_relations")
}

model Coupon {
  id              String    @id @default(cuid())
  code            String    @unique
  description     String?
  discountType    String    @map("discount_type") // percentage, fixed
  discountValue   Float     @map("discount_value")
  minOrderValue   Float?    @map("min_order_value")
  maxUses         Int?      @map("max_uses")
  usedCount       Int       @default(0) @map("used_count")
  startDate       DateTime  @map("start_date")
  endDate         DateTime  @map("end_date")
  isActive        Boolean   @default(true) @map("is_active")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  transactions    Transaction[]
  orders          Order[]

  @@map("coupons")
}

model Order {
  id              String        @id @default(cuid())
  customerId      String        @map("customer_id")
  addressId       String?       @map("address_id")
  storeId         String?       @map("store_id") // Store assigned by super admin
  orderNumber     String?       @unique @map("order_number")
  status          OrderStatus   @default(PENDING_ASSIGNMENT)
  paymentStatus   PaymentStatus @default(PENDING) @map("payment_status")
  paymentMethod   PaymentMethod @default(CASH) @map("payment_method")
  totalAmount     Float         @map("total_amount")
  subtotal        Float?        @default(0) // Subtotal before shipping
  shipping        Float?        @default(0) // Shipping cost
  discount        Float         @default(0)
  couponId        String?       @map("coupon_id")
  trackingNumber  String?       @map("tracking_number")
  orderType       OrderType     @default(ONLINE) @map("order_type") // ONLINE, STORE
  assignedBy      String?       @map("assigned_by") // Super admin who assigned to store
  notes           String?
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  customer        Customer      @relation(fields: [customerId], references: [id])
  address         Address?      @relation(fields: [addressId], references: [id])
  coupon          Coupon?       @relation(fields: [couponId], references: [id])
  store           Store?        @relation(fields: [storeId], references: [id])
  assignedByUser  User?         @relation("OrderAssignedBy", fields: [assignedBy], references: [id])
  orderItems      OrderItem[]

  @@map("orders")
}

model OrderItem {
  id          String          @id @default(cuid())
  orderId     String          @map("order_id")
  productId   String          @map("product_id")
  variantId   String?         @map("variant_id") // New: Selected variant
  quantity    Int
  unitPrice   Float           @map("unit_price")
  weight      String?         // New: Weight/size selected (e.g., "0.5 Kg", "1 Kg")
  createdAt   DateTime        @default(now()) @map("created_at")
  updatedAt   DateTime        @updatedAt @map("updated_at")
  order       Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product         @relation(fields: [productId], references: [id])
  variant     ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model ProductVariant {
  id              String      @id @default(cuid())
  productId       String      @map("product_id")
  weight          String      // e.g., "0.5 Kg", "1 Kg", "1.5 Kg", "2 Kg"
  price           Float       // Original price for this specific weight
  discountedPrice Float?      @map("discounted_price") // Discounted price for this variant
  costPrice       Float?      @map("cost_price") // Cost price for this weight
  sku             String?     // SKU for this specific variant
  isDefault       Boolean     @default(false) @map("is_default") // Default variant to show
  isActive        Boolean     @default(true) @map("is_active")
  sortOrder       Int         @default(0) @map("sort_order") // Order to display variants
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  cartItems   CartItem[]
  orderItems  OrderItem[]

  @@unique([productId, weight])
  @@map("product_variants")
}

model DeliveryLocation {
  id          String   @id @default(cuid())
  pincode     String   @unique
  area        String
  city        String   @default("Bhubaneswar")
  state       String   @default("Odisha")
  isActive    Boolean  @default(true) @map("is_active")
  deliveryFee Float    @default(0) @map("delivery_fee") // Delivery charge for this location
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("delivery_locations")
}

model ContactSubmission {
  id        String   @id @default(cuid())
  name      String
  email     String
  phone     String?
  subject   String
  message   String
  userId    String?  @map("user_id")
  status    String   @default("NEW") // NEW, READ, REPLIED, CLOSED
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  user      User?    @relation(fields: [userId], references: [id])

  @@map("contact_submissions")
}
