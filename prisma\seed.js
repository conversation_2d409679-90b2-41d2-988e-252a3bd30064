const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('✅ Admin user already exists, skipping seed');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    console.log(`✅ Created admin user: ${adminUser.name} (${adminUser.email})`);
    console.log('🔐 Login credentials: <EMAIL> / admin123');
    console.log('✅ Database seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
