import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting comprehensive user seeding...');

  // Create warehouses first
  console.log('📦 Creating warehouses...');
  const mainWarehouse = await prisma.warehouse.upsert({
    where: { id: 'main-warehouse' },
    update: {},
    create: {
      id: 'main-warehouse',
      name: 'Main Warehouse',
      location: 'Bhubaneswar Central Warehouse, Odisha',
    },
  });
  console.log(`✅ Created warehouse: ${mainWarehouse.name}`);

  const secondaryWarehouse = await prisma.warehouse.upsert({
    where: { id: 'secondary-warehouse' },
    update: {},
    create: {
      id: 'secondary-warehouse',
      name: 'Secondary Warehouse',
      location: 'Cuttack Storage Facility, Odisha',
    },
  });
  console.log(`✅ Created warehouse: ${secondaryWarehouse.name}`);

  // Create stores
  console.log('🏪 Creating stores...');
  const mainStore = await prisma.store.upsert({
    where: { id: 'main-store' },
    update: {},
    create: {
      id: 'main-store',
      name: 'Mispri Main Store',
      location: 'Saheed Nagar, Bhubaneswar, Odisha',
    },
  });
  console.log(`✅ Created store: ${mainStore.name}`);

  const branchStore = await prisma.store.upsert({
    where: { id: 'branch-store' },
    update: {},
    create: {
      id: 'branch-store',
      name: 'Mispri Branch Store',
      location: 'Patia, Bhubaneswar, Odisha',
    },
  });
  console.log(`✅ Created store: ${branchStore.name}`);

  const cuttackStore = await prisma.store.upsert({
    where: { id: 'cuttack-store' },
    update: {},
    create: {
      id: 'cuttack-store',
      name: 'Mispri Cuttack Store',
      location: 'Badambadi, Cuttack, Odisha',
    },
  });
  console.log(`✅ Created store: ${cuttackStore.name}`);

  // Admin Users
  console.log('👑 Creating admin users...');

  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Vaishnavi Bhardwaj',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      role: 'ADMIN',
    },
  });
  console.log(`✅ Created super admin: ${superAdmin.name} (${superAdmin.email})`);

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      role: 'ADMIN',
    },
  });
  console.log(`✅ Created admin: ${adminUser.name} (${adminUser.email})`);

  const adminFinance = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      role: 'ADMIN',
    },
  });
  console.log(`✅ Created admin: ${adminFinance.name} (${adminFinance.email})`);

  // Create the default admin user for development/testing
  const defaultAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Admin User',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      role: 'ADMIN',
    },
  });
  console.log(`✅ Created default admin: ${defaultAdmin.name} (${defaultAdmin.email})`);

  // Warehouse Managers
  console.log('📦 Creating warehouse managers...');

  const warehouseManager1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Amit Patel',
      email: '<EMAIL>',
      password: await bcrypt.hash('warehouse123', 10),
      role: 'WAREHOUSE_MANAGER',
    },
  });
  console.log(`✅ Created warehouse manager: ${warehouseManager1.name} (${warehouseManager1.email})`);

  const warehouseManager2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Sunita Das',
      email: '<EMAIL>',
      password: await bcrypt.hash('warehouse123', 10),
      role: 'WAREHOUSE_MANAGER',
    },
  });
  console.log(`✅ Created warehouse manager: ${warehouseManager2.name} (${warehouseManager2.email})`);

  // Store Managers
  console.log('🏪 Creating store managers...');

  const storeManager1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Ravi Mohanty',
      email: '<EMAIL>',
      password: await bcrypt.hash('store123', 10),
      role: 'STORE_MANAGER',
      storeId: mainStore.id,
    },
  });
  console.log(`✅ Created store manager: ${storeManager1.name} (${storeManager1.email}) - ${mainStore.name}`);

  const storeManager2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Meera Singh',
      email: '<EMAIL>',
      password: await bcrypt.hash('store123', 10),
      role: 'STORE_MANAGER',
      storeId: branchStore.id,
    },
  });
  console.log(`✅ Created store manager: ${storeManager2.name} (${storeManager2.email}) - ${branchStore.name}`);

  const storeManager3 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Bikash Sahoo',
      email: '<EMAIL>',
      password: await bcrypt.hash('store123', 10),
      role: 'STORE_MANAGER',
      storeId: cuttackStore.id,
    },
  });
  console.log(`✅ Created store manager: ${storeManager3.name} (${storeManager3.email}) - ${cuttackStore.name}`);

  // Staff Members
  console.log('👥 Creating staff members...');

  const staff1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Anita Jena',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: mainStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff1.name} (${staff1.email}) - ${mainStore.name}`);

  const staff2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Deepak Nayak',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: mainStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff2.name} (${staff2.email}) - ${mainStore.name}`);

  const staff3 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Sita Pradhan',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: branchStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff3.name} (${staff3.email}) - ${branchStore.name}`);

  const staff4 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Manoj Behera',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: branchStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff4.name} (${staff4.email}) - ${branchStore.name}`);

  const staff5 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Gita Mishra',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: cuttackStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff5.name} (${staff5.email}) - ${cuttackStore.name}`);

  const staff6 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Ramesh Swain',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: cuttackStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff6.name} (${staff6.email}) - ${cuttackStore.name}`);

  // Additional specialized staff
  const staff7 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Suresh Panda',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: mainStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff7.name} (${staff7.email}) - Baker - ${mainStore.name}`);

  const staff8 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Kavita Rout',
      email: '<EMAIL>',
      password: await bcrypt.hash('staff123', 10),
      role: 'STAFF',
      storeId: mainStore.id,
    },
  });
  console.log(`✅ Created staff: ${staff8.name} (${staff8.email}) - Decorator - ${mainStore.name}`);

  // Create sample products for each category
  console.log('🍰 Creating sample products...');
  const categories = ['Cakes', 'Flowers', 'Birthday', 'Anniversary', 'Gifts'];

  for (const category of categories) {
    try {
      const product = await prisma.product.create({
        data: {
          name: `Sample ${category}`,
          description: `A delicious sample ${category.toLowerCase()}`,
          category,
          price: Math.floor(Math.random() * 500) + 100, // Random price between 100 and 599
          costPrice: Math.floor(Math.random() * 200) + 50, // Random cost price between 50 and 249
          unit: category === 'Flowers' ? 'bouquet' : category === 'Cakes' ? 'cake' : 'piece',
          lowStockThreshold: 10,
        },
      });
      console.log(`✅ Created product: ${product.name} (${product.category})`);

      // Add inventory to warehouse
      await prisma.warehouseInventory.create({
        data: {
          warehouseId: mainWarehouse.id,
          productId: product.id,
          quantity: Math.floor(Math.random() * 50) + 10, // Random quantity between 10 and 59
        },
      });

      // Add inventory to stores
      await prisma.storeInventory.create({
        data: {
          storeId: mainStore.id,
          productId: product.id,
          quantity: Math.floor(Math.random() * 20) + 5, // Random quantity between 5 and 24
        },
      });
    } catch (error) {
      console.log(`⚠️ Skipped product: ${category} (might already exist)`);
    }
  }

  console.log('\n🎉 Seeding completed successfully!');
  console.log('\n📊 Summary:');
  console.log('👑 Admins: 4 users');
  console.log('📦 Warehouse Managers: 2 users');
  console.log('🏪 Store Managers: 3 users');
  console.log('👥 Staff Members: 8 users');
  console.log('🏢 Warehouses: 2 locations');
  console.log('🏪 Stores: 3 locations');
  console.log('🍰 Products: 5 sample items');

  console.log('\n🔐 Default Login Credentials:');
  console.log('Default Admin: <EMAIL> / admin123');
  console.log('Super Admin: <EMAIL> / admin123');
  console.log('Operations Admin: <EMAIL> / admin123');
  console.log('Finance Admin: <EMAIL> / admin123');
  console.log('Warehouse Manager: <EMAIL> / warehouse123');
  console.log('Store Manager: <EMAIL> / store123');
  console.log('Staff: <EMAIL> / staff123');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
