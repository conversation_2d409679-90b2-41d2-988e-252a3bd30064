<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-Plants" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00a56d;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow-Plants">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#bg-Plants)"/>
  
  <!-- Decorative circles -->
  <circle cx="80" cy="60" r="30" fill="rgba(255,255,255,0.1)"/>
  <circle cx="320" cy="240" r="40" fill="rgba(255,255,255,0.1)"/>
  <circle cx="350" cy="80" r="20" fill="rgba(255,255,255,0.15)"/>
  
  <!-- Main content area -->
  <rect x="40" y="60" width="320" height="180" rx="20" fill="rgba(255,255,255,0.95)" filter="url(#shadow-Plants)"/>
  
  <!-- Emoji -->
  <text x="200" y="130" font-family="Arial, sans-serif" font-size="48" text-anchor="middle" fill="#10b981">🌱</text>
  
  <!-- Category name -->
  <text x="200" y="170" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#1f2937">Plants</text>
  
  <!-- Subtitle -->
  <text x="200" y="195" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6b7280">Premium Quality</text>
</svg>