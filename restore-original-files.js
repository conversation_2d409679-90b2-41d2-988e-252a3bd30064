const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Restore original files using git
try {
  console.log('Restoring original files...');
  execSync('git checkout -- src/app', { stdio: 'inherit' });
  console.log('Original files restored successfully!');
} catch (error) {
  console.error('Failed to restore original files:', error.message);
  
  // If git checkout fails, try to restore from backup
  try {
    console.log('Trying to restore from backup...');
    const backupDir = path.join(__dirname, 'backup');
    if (fs.existsSync(backupDir)) {
      const files = fs.readdirSync(backupDir);
      files.forEach(file => {
        const backupFilePath = path.join(backupDir, file);
        const originalFilePath = path.join(__dirname, file);
        fs.copyFileSync(backupFilePath, originalFilePath);
      });
      console.log('Files restored from backup successfully!');
    } else {
      console.error('No backup directory found.');
    }
  } catch (backupError) {
    console.error('Failed to restore from backup:', backupError.message);
  }
}
