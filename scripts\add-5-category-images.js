const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Only 5 main categories with local images
const categoryData = {
  'Flowers': {
    description: 'Fresh flowers and beautiful arrangements for every occasion',
    imageUrl: '/images/flowers/pink_bouquet.jpg',
    displayOrder: 1
  },
  'Cakes': {
    description: 'Delicious cakes for all occasions - birthdays, anniversaries, and celebrations',
    imageUrl: '/images/cakes/cake_display.jpg',
    displayOrder: 2
  },
  'Gifts': {
    description: 'Perfect gifts for your loved ones on any occasion',
    imageUrl: '/images/combos/gift_combo.jpg',
    displayOrder: 3
  },
  'Plants': {
    description: 'Beautiful plants and green arrangements for home and office',
    imageUrl: '/images/plants/plant_display.jpg',
    displayOrder: 4
  },
  'Combos': {
    description: 'Perfect combinations of flowers, cakes, and gifts',
    imageUrl: '/images/combos/celebration_combo.jpg',
    displayOrder: 5
  }
};

// Create sample images if they don't exist
function createSampleImages() {
  const publicDir = path.join(process.cwd(), 'public');
  
  // Create directories
  const dirs = ['images/flowers', 'images/cakes', 'images/combos', 'images/plants'];
  dirs.forEach(dir => {
    const fullPath = path.join(publicDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });
  
  // Create simple colored placeholder images
  const createColoredSVG = (text, color, bgColor = '#f8f9fa') => `
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${bgColor}"/>
  <rect x="20" y="20" width="360" height="260" fill="${color}" rx="10"/>
  <text x="200" y="160" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">${text}</text>
</svg>`;

  const images = [
    { path: 'images/flowers/pink_bouquet.jpg', content: createColoredSVG('🌸 Flowers', '#ec4899') },
    { path: 'images/cakes/cake_display.jpg', content: createColoredSVG('🍰 Cakes', '#f59e0b') },
    { path: 'images/combos/gift_combo.jpg', content: createColoredSVG('🎁 Gifts', '#8b5cf6') },
    { path: 'images/plants/plant_display.jpg', content: createColoredSVG('🌱 Plants', '#10b981') },
    { path: 'images/combos/celebration_combo.jpg', content: createColoredSVG('🎉 Combos', '#ef4444') }
  ];
  
  images.forEach(({ path: imagePath, content }) => {
    const fullPath = path.join(publicDir, imagePath);
    if (!fs.existsSync(fullPath)) {
      fs.writeFileSync(fullPath, content);
      console.log(`🖼️ Created image: ${imagePath}`);
    }
  });
}

async function addCategoryImages() {
  console.log('🖼️ Adding images to 5 main categories...');
  
  try {
    // Create sample images first
    createSampleImages();
    
    // Add metadata for each category
    for (const [categoryName, data] of Object.entries(categoryData)) {
      console.log(`\n📂 Processing category: ${categoryName}`);
      
      const result = await prisma.systemSetting.upsert({
        where: {
          category_key: {
            category: 'category_metadata',
            key: categoryName
          }
        },
        update: {
          value: JSON.stringify({
            description: data.description,
            imageUrl: data.imageUrl,
            slug: categoryName.toLowerCase().replace(/\s+/g, '-'),
            displayOrder: data.displayOrder,
            isActive: true,
            updatedAt: new Date().toISOString(),
          }),
          updatedAt: new Date()
        },
        create: {
          category: 'category_metadata',
          key: categoryName,
          value: JSON.stringify({
            description: data.description,
            imageUrl: data.imageUrl,
            slug: categoryName.toLowerCase().replace(/\s+/g, '-'),
            displayOrder: data.displayOrder,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      console.log(`✅ Added image for: ${categoryName}`);
      console.log(`   📝 Description: ${data.description}`);
      console.log(`   🖼️ Image: ${data.imageUrl}`);
      console.log(`   📊 Order: ${data.displayOrder}`);
    }
    
    console.log('\n🎉 Successfully added images to all 5 categories!');
    console.log('\n📋 Categories with images:');
    Object.keys(categoryData).forEach((cat, index) => {
      console.log(`   ${index + 1}. ${cat} ✅`);
    });
    
    console.log('\n🔍 Next steps:');
    console.log('   1. Check admin panel → Categories');
    console.log('   2. Verify images are showing');
    console.log('   3. Test mobile website categories');
    
  } catch (error) {
    console.error('❌ Error adding category images:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

addCategoryImages();
