const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Category images mapping with high-quality images
const categoryImages = {
  'Flowers': {
    description: 'Fresh flowers and beautiful arrangements for every occasion',
    imageUrl: 'https://images.unsplash.com/photo-1518895949257-7621c3c786d7?w=400&h=300&fit=crop&crop=center',
    displayOrder: 1
  },
  'Cakes': {
    description: 'Delicious cakes for all occasions - birthdays, anniversaries, and celebrations',
    imageUrl: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop&crop=center',
    displayOrder: 2
  },
  'Birthday': {
    description: 'Special birthday gifts, cakes, and celebration packages',
    imageUrl: 'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400&h=300&fit=crop&crop=center',
    displayOrder: 3
  },
  'Anniversary': {
    description: 'Romantic gifts and arrangements for anniversary celebrations',
    imageUrl: 'https://images.unsplash.com/photo-1606890737304-57a1ca8a5b62?w=400&h=300&fit=crop&crop=center',
    displayOrder: 4
  },
  'Gifts': {
    description: 'Perfect gifts for your loved ones on any occasion',
    imageUrl: 'https://images.unsplash.com/photo-1549007994-cb92caebd54b?w=400&h=300&fit=crop&crop=center',
    displayOrder: 5
  },
  'Personalised': {
    description: 'Customized and personalized gifts made just for you',
    imageUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop&crop=center',
    displayOrder: 6
  },
  'Plants': {
    description: 'Beautiful plants and green arrangements for home and office',
    imageUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop&crop=center',
    displayOrder: 7
  },
  'Combos': {
    description: 'Perfect combinations of flowers, cakes, and gifts',
    imageUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop&crop=center',
    displayOrder: 8
  },
  'International': {
    description: 'International delivery options for global celebrations',
    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&crop=center',
    displayOrder: 9
  },
  'Occasions': {
    description: 'Special arrangements for all life occasions and celebrations',
    imageUrl: 'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400&h=300&fit=crop&crop=center',
    displayOrder: 10
  },
  'Birthday Gifts': {
    description: 'Thoughtful birthday gifts to make their day special',
    imageUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop&crop=center',
    displayOrder: 11
  },
  'Anniversary Gifts': {
    description: 'Romantic anniversary gifts to celebrate your love',
    imageUrl: 'https://images.unsplash.com/photo-1606890737304-57a1ca8a5b62?w=400&h=300&fit=crop&crop=center',
    displayOrder: 12
  },
  'Gifts for Her': {
    description: 'Beautiful gifts specially curated for the special women in your life',
    imageUrl: 'https://images.unsplash.com/photo-1549007994-cb92caebd54b?w=400&h=300&fit=crop&crop=center',
    displayOrder: 13
  }
};

async function addCategoryImages() {
  console.log('🖼️ Starting to add category images...');
  
  try {
    for (const [categoryName, categoryData] of Object.entries(categoryImages)) {
      try {
        console.log(`\n📂 Processing category: ${categoryName}`);
        
        // Store category metadata in system settings
        const result = await prisma.systemSetting.upsert({
          where: {
            category_key: {
              category: 'category_metadata',
              key: categoryName
            }
          },
          update: {
            value: JSON.stringify({
              description: categoryData.description,
              imageUrl: categoryData.imageUrl,
              slug: categoryName.toLowerCase().replace(/\s+/g, '-'),
              displayOrder: categoryData.displayOrder,
              isActive: true,
              updatedAt: new Date().toISOString(),
            }),
            updatedAt: new Date()
          },
          create: {
            category: 'category_metadata',
            key: categoryName,
            value: JSON.stringify({
              description: categoryData.description,
              imageUrl: categoryData.imageUrl,
              slug: categoryName.toLowerCase().replace(/\s+/g, '-'),
              displayOrder: categoryData.displayOrder,
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
        
        console.log(`✅ Successfully added image for category: ${categoryName}`);
        console.log(`   📝 Description: ${categoryData.description}`);
        console.log(`   🖼️ Image URL: ${categoryData.imageUrl}`);
        console.log(`   📊 Display Order: ${categoryData.displayOrder}`);
        
      } catch (error) {
        console.error(`❌ Error processing category ${categoryName}:`, error);
      }
    }
    
    console.log('\n🎉 Category image seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   Total categories processed: ${Object.keys(categoryImages).length}`);
    console.log('   All categories now have images and descriptions');
    console.log('\n🔍 Next steps:');
    console.log('   1. Go to your admin panel → Categories');
    console.log('   2. Check if all categories show images');
    console.log('   3. Test the mobile website to see if categories display correctly');
    
  } catch (error) {
    console.error('❌ Error seeding category images:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addCategoryImages();
