const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSampleImages() {
  try {
    // Get all products
    const products = await prisma.product.findMany();
    
    console.log(`Found ${products.length} products`);
    
    // Sample image URLs
    const sampleImages = [
      '/products/chocolate-cake.jpg',
      '/products/red-roses.jpg',
      '/products/birthday-gift.jpg',
      '/products/sample-cake.jpg',
      '/products/sample-flower.jpg',
      '/products/sample-gift.jpg'
    ];
    
    // Update first few products with sample images
    for (let i = 0; i < Math.min(products.length, sampleImages.length); i++) {
      const product = products[i];
      const imageUrl = sampleImages[i];
      
      await prisma.product.update({
        where: { id: product.id },
        data: { imageUrl: imageUrl }
      });
      
      console.log(`Updated product "${product.name}" with image: ${imageUrl}`);
    }
    
    console.log('Sample images added successfully!');
  } catch (error) {
    console.error('Error adding sample images:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addSampleImages();
