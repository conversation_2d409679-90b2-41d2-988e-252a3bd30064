// Simple test to add a product via API
const testProduct = {
  name: "Chocolate Birthday Cake",
  description: "Delicious chocolate cake perfect for birthdays with rich chocolate frosting",
  category: "Chocolate", // Using the Chocolate category
  price: "599.00",
  costPrice: "299.00",
  unit: "piece",
  lowStockThreshold: "5",
  sku: "CAK-CHO-001",
  barcode: "1234567890123",
  weight: "1kg",
  dimensions: "20x20x10 cm",
  isActive: true,
  isFeatured: true,
  imageUrl: "/products/chocolate-cake.jpg",
  tags: "chocolate, birthday, cake, dessert",
  metaTitle: "Chocolate Birthday Cake - Fresh Baked Daily",
  metaDescription: "Order our delicious chocolate birthday cake made with premium ingredients. Perfect for celebrations!"
};

async function addTestProduct() {
  try {
    const response = await fetch('http://localhost:3000/api/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API Error Response:', errorData);
      throw new Error(errorData.error || 'Failed to create product');
    }

    const newProduct = await response.json();
    console.log('Test product created successfully:', newProduct);
  } catch (error) {
    console.error('Error creating test product:', error);
  }
}

// Run the test
addTestProduct();
