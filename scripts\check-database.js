// Check what's in the database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  console.log('🔍 CHECKING DATABASE CONTENTS');
  console.log('=============================\n');

  try {
    // Test connection
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful\n');

    // Check products
    console.log('📦 PRODUCTS:');
    console.log('============');
    const products = await prisma.product.findMany();
    console.log(`Found ${products.length} products:`);
    products.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name} (${product.category}) - ₹${product.price}`);
    });

    // Check warehouses
    console.log('\n🏭 WAREHOUSES:');
    console.log('==============');
    const warehouses = await prisma.warehouse.findMany();
    console.log(`Found ${warehouses.length} warehouses:`);
    warehouses.forEach((warehouse, index) => {
      console.log(`   ${index + 1}. ${warehouse.name} - ${warehouse.location}`);
    });

    // Check stores
    console.log('\n🏪 STORES:');
    console.log('==========');
    const stores = await prisma.store.findMany();
    console.log(`Found ${stores.length} stores:`);
    stores.forEach((store, index) => {
      console.log(`   ${index + 1}. ${store.name} - ${store.location}`);
    });

    // Check warehouse inventory
    console.log('\n📦 WAREHOUSE INVENTORY:');
    console.log('=======================');
    const warehouseInventory = await prisma.warehouseInventory.findMany({
      include: {
        product: true,
        warehouse: true
      }
    });
    console.log(`Found ${warehouseInventory.length} warehouse inventory items:`);
    warehouseInventory.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.product.name} - ${item.quantity} ${item.product.unit} (${item.warehouse.name})`);
    });

    // Check store inventory
    console.log('\n🏪 STORE INVENTORY:');
    console.log('==================');
    const storeInventory = await prisma.storeInventory.findMany({
      include: {
        product: true,
        store: true
      }
    });
    console.log(`Found ${storeInventory.length} store inventory items:`);
    storeInventory.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.product.name} - ${item.quantity} ${item.product.unit} (${item.store.name})`);
    });

    // Check categories
    console.log('\n📂 CATEGORIES:');
    console.log('==============');
    const categories = await prisma.category.findMany();
    console.log(`Found ${categories.length} categories:`);
    categories.forEach((category, index) => {
      console.log(`   ${index + 1}. ${category.name} - ${category.description || 'No description'}`);
    });

    console.log('\n✅ DATABASE CHECK COMPLETED!');
    
    return {
      products: products.length,
      warehouses: warehouses.length,
      stores: stores.length,
      warehouseInventory: warehouseInventory.length,
      storeInventory: storeInventory.length,
      categories: categories.length
    };

  } catch (error) {
    console.error('\n❌ ERROR CHECKING DATABASE:', error);
    
    if (error.message.includes("Can't reach database server")) {
      console.log('\n💡 DATABASE CONNECTION ERROR:');
      console.log('============================');
      console.log('⏳ The database might be sleeping');
      console.log('💡 Wait 1-2 minutes for it to wake up');
      console.log('💡 Try running this script again');
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
if (require.main === module) {
  checkDatabase()
    .then((counts) => {
      console.log('\n📊 DATABASE SUMMARY:');
      console.log('====================');
      console.log(`Products: ${counts.products}`);
      console.log(`Warehouses: ${counts.warehouses}`);
      console.log(`Stores: ${counts.stores}`);
      console.log(`Warehouse Inventory: ${counts.warehouseInventory}`);
      console.log(`Store Inventory: ${counts.storeInventory}`);
      console.log(`Categories: ${counts.categories}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 DATABASE CHECK FAILED:', error.message);
      process.exit(1);
    });
}

module.exports = { checkDatabase };
