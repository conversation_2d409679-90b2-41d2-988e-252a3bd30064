const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Only keep these 5 main categories
const MAIN_CATEGORIES = [
  'Flowers',
  'Cakes', 
  'Gifts',
  'Plants',
  'Combos'
];

async function cleanupCategories() {
  console.log('🧹 Cleaning up categories - keeping only 5 main categories...');
  
  try {
    // Step 1: Get all current category metadata
    const allMetadata = await prisma.systemSetting.findMany({
      where: {
        category: 'category_metadata'
      }
    });
    
    console.log(`Found ${allMetadata.length} category metadata entries`);
    
    // Step 2: Delete metadata for categories not in main list
    const toDelete = allMetadata.filter(meta => !MAIN_CATEGORIES.includes(meta.key));
    
    console.log(`\n🗑️ Removing ${toDelete.length} extra categories:`);
    for (const meta of toDelete) {
      console.log(`   ❌ Removing: ${meta.key}`);
      await prisma.systemSetting.delete({
        where: {
          id: meta.id
        }
      });
    }
    
    // Step 3: Update products with non-main categories to "Gifts"
    console.log('\n📦 Updating products with removed categories...');
    
    const allProducts = await prisma.product.findMany({
      select: {
        id: true,
        category: true,
        name: true
      }
    });
    
    const productsToUpdate = allProducts.filter(product => 
      !MAIN_CATEGORIES.includes(product.category)
    );
    
    console.log(`Found ${productsToUpdate.length} products to move to "Gifts" category`);
    
    for (const product of productsToUpdate) {
      console.log(`   📦 Moving "${product.name}" from "${product.category}" to "Gifts"`);
      await prisma.product.update({
        where: { id: product.id },
        data: { category: 'Gifts' }
      });
    }
    
    console.log('\n✅ Cleanup completed!');
    console.log(`\n📋 Remaining categories: ${MAIN_CATEGORIES.join(', ')}`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupCategories();
