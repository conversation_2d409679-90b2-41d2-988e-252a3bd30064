import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupDatabase() {
  try {
    console.log('🧹 Starting database cleanup...');

    // Delete in order to respect foreign key constraints
    console.log('🗑️ Deleting order items...');
    const deletedOrderItems = await prisma.orderItem.deleteMany({});
    console.log(`✅ Deleted ${deletedOrderItems.count} order items`);

    console.log('🗑️ Deleting orders...');
    const deletedOrders = await prisma.order.deleteMany({});
    console.log(`✅ Deleted ${deletedOrders.count} orders`);

    console.log('🗑️ Deleting cart items...');
    const deletedCartItems = await prisma.cartItem.deleteMany({});
    console.log(`✅ Deleted ${deletedCartItems.count} cart items`);

    console.log('🗑️ Deleting carts...');
    const deletedCarts = await prisma.cart.deleteMany({});
    console.log(`✅ Deleted ${deletedCarts.count} carts`);

    console.log('🗑️ Deleting addresses...');
    const deletedAddresses = await prisma.address.deleteMany({});
    console.log(`✅ Deleted ${deletedAddresses.count} addresses`);

    console.log('🗑️ Deleting customer tags...');
    const deletedCustomerTags = await prisma.customerTag.deleteMany({});
    console.log(`✅ Deleted ${deletedCustomerTags.count} customer tags`);

    console.log('🗑️ Deleting customers...');
    const deletedCustomers = await prisma.customer.deleteMany({});
    console.log(`✅ Deleted ${deletedCustomers.count} customers`);

    console.log('🗑️ Deleting product images...');
    const deletedProductImages = await prisma.productImage.deleteMany({});
    console.log(`✅ Deleted ${deletedProductImages.count} product images`);

    console.log('🗑️ Deleting product relations...');
    const deletedProductRelations = await prisma.productRelation.deleteMany({});
    console.log(`✅ Deleted ${deletedProductRelations.count} product relations`);

    console.log('🗑️ Deleting warehouse products...');
    const deletedWarehouseProducts = await prisma.warehouseProduct.deleteMany({});
    console.log(`✅ Deleted ${deletedWarehouseProducts.count} warehouse products`);

    console.log('🗑️ Deleting store products...');
    const deletedStoreProducts = await prisma.storeProduct.deleteMany({});
    console.log(`✅ Deleted ${deletedStoreProducts.count} store products`);

    console.log('🗑️ Deleting products...');
    const deletedProducts = await prisma.product.deleteMany({});
    console.log(`✅ Deleted ${deletedProducts.count} products`);

    console.log('🗑️ Deleting categories...');
    const deletedCategories = await prisma.category.deleteMany({});
    console.log(`✅ Deleted ${deletedCategories.count} categories`);

    console.log('🗑️ Deleting coupons...');
    const deletedCoupons = await prisma.coupon.deleteMany({});
    console.log(`✅ Deleted ${deletedCoupons.count} coupons`);

    console.log('🗑️ Deleting contact submissions...');
    const deletedContactSubmissions = await prisma.contactSubmission.deleteMany({});
    console.log(`✅ Deleted ${deletedContactSubmissions.count} contact submissions`);

    console.log('🗑️ Deleting transactions...');
    const deletedTransactions = await prisma.transaction.deleteMany({});
    console.log(`✅ Deleted ${deletedTransactions.count} transactions`);

    console.log('🗑️ Deleting stores...');
    const deletedStores = await prisma.store.deleteMany({});
    console.log(`✅ Deleted ${deletedStores.count} stores`);

    console.log('🗑️ Deleting warehouses...');
    const deletedWarehouses = await prisma.warehouse.deleteMany({});
    console.log(`✅ Deleted ${deletedWarehouses.count} warehouses`);

    // Keep admin users but delete other users if needed
    console.log('🗑️ Deleting non-admin users...');
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        role: {
          not: 'ADMIN'
        }
      }
    });
    console.log(`✅ Deleted ${deletedUsers.count} non-admin users`);

    console.log('✅ Database cleanup completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Order Items: ${deletedOrderItems.count}`);
    console.log(`   - Orders: ${deletedOrders.count}`);
    console.log(`   - Cart Items: ${deletedCartItems.count}`);
    console.log(`   - Carts: ${deletedCarts.count}`);
    console.log(`   - Addresses: ${deletedAddresses.count}`);
    console.log(`   - Customer Tags: ${deletedCustomerTags.count}`);
    console.log(`   - Customers: ${deletedCustomers.count}`);
    console.log(`   - Product Images: ${deletedProductImages.count}`);
    console.log(`   - Product Relations: ${deletedProductRelations.count}`);
    console.log(`   - Warehouse Products: ${deletedWarehouseProducts.count}`);
    console.log(`   - Store Products: ${deletedStoreProducts.count}`);
    console.log(`   - Products: ${deletedProducts.count}`);
    console.log(`   - Categories: ${deletedCategories.count}`);
    console.log(`   - Coupons: ${deletedCoupons.count}`);
    console.log(`   - Contact Submissions: ${deletedContactSubmissions.count}`);
    console.log(`   - Transactions: ${deletedTransactions.count}`);
    console.log(`   - Stores: ${deletedStores.count}`);
    console.log(`   - Warehouses: ${deletedWarehouses.count}`);
    console.log(`   - Non-admin Users: ${deletedUsers.count}`);

  } catch (error) {
    console.error('❌ Error during database cleanup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
cleanupDatabase()
  .then(() => {
    console.log('🎉 Database cleanup script completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Database cleanup script failed:', error);
    process.exit(1);
  });
