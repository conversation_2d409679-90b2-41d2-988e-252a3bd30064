const { Client } = require('pg');
require('dotenv').config();

async function clearCategories() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to NeonDB');
    
    const result = await client.query('DELETE FROM bakery."categories";');
    console.log(`✅ Deleted ${result.rowCount} categories`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.end();
  }
}

clearCategories();
