// Script to clear data in correct order to avoid foreign key constraints
const { Client } = require('pg');
require('dotenv').config();

async function clearDatabaseOrdered() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to NeonDB');

    // Delete data in the correct order to avoid foreign key constraint errors
    const deleteOrder = [
      // Transaction related (depends on products, users, stores)
      'transaction_items',
      'transactions',
      
      // Transfer related (depends on products, warehouses, stores)
      'transfer_items',
      'transfers',
      'inventory_transfers',
      
      // Inventory (depends on products, warehouses, stores)
      'warehouse_inventory',
      'store_inventory',
      
      // Production related (depends on products, raw materials)
      'raw_material_consumption',
      'production',
      'product_raw_materials',
      'raw_materials',
      
      // Product related
      'wastage',
      'product_reviews',
      'wishlist_items',
      'product_images',
      'product_relations',
      
      // Recipe related
      'recipe_nutrition',
      'recipe_tips',
      'recipe_instructions',
      'recipe_ingredients',
      'recipe_tags',
      'recipes',
      
      // Customer related (depends on users)
      'customer_tags',
      'addresses',
      'customers',
      
      // Order related (depends on customers, products)
      'order_items',
      'orders',
      
      // Cart related (depends on users, products)
      'cart_items',
      'carts',
      
      // Financial data
      'bank_transactions',
      'bank_accounts',
      'expenses',
      
      // Other data
      'customer_visits',
      'contact_submissions',
      'coupons',
      
      // Core entities (delete these last)
      'products',
      'stores',
      'warehouses',
      'users',
      
      // System data (optional - you might want to keep these)
      // 'api_integrations',
      // 'system_settings',
    ];

    console.log('🗑️  Starting ordered deletion...');

    for (const table of deleteOrder) {
      try {
        console.log(`Deleting from: ${table}`);
        const result = await client.query(`DELETE FROM bakery."${table}";`);
        console.log(`✅ Deleted ${result.rowCount} rows from ${table}`);
      } catch (error) {
        console.log(`⚠️  Error deleting from ${table}:`, error.message);
      }
    }

    console.log('🎉 Database cleared successfully!');
    console.log('All main data has been removed from NeonDB.');
    console.log('System settings and API integrations were preserved.');

  } catch (error) {
    console.error('❌ Error clearing database:', error);
  } finally {
    await client.end();
    console.log('🔌 Disconnected from database');
  }
}

// Confirmation check
const args = process.argv.slice(2);
if (args.includes('--confirm')) {
  clearDatabaseOrdered()
    .then(() => {
      console.log('✨ Database cleanup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database cleanup failed:', error);
      process.exit(1);
    });
} else {
  console.log('⚠️  WARNING: This will delete ALL data from your NeonDB database!');
  console.log('This action cannot be undone.');
  console.log('System settings and API integrations will be preserved.');
  console.log('');
  console.log('To proceed, run:');
  console.log('node scripts/clear-data-ordered.js --confirm');
  console.log('');
}
