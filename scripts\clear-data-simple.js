// Simple script to clear data using direct SQL
const { Client } = require('pg');
require('dotenv').config();

async function clearDatabase() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to NeonDB');

    // Get all table names from the bakery schema
    const tablesResult = await client.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'bakery'
      ORDER BY tablename;
    `);

    const tables = tablesResult.rows.map(row => row.tablename);
    console.log('📋 Found tables:', tables);

    // Disable foreign key checks temporarily
    console.log('🔓 Disabling foreign key constraints...');
    await client.query('SET session_replication_role = replica;');

    // Delete data from all tables
    for (const table of tables) {
      try {
        console.log(`🗑️  Clearing table: ${table}`);
        await client.query(`DELETE FROM bakery."${table}";`);
        console.log(`✅ Cleared table: ${table}`);
      } catch (error) {
        console.log(`⚠️  Could not clear table ${table}:`, error.message);
      }
    }

    // Re-enable foreign key checks
    console.log('🔒 Re-enabling foreign key constraints...');
    await client.query('SET session_replication_role = DEFAULT;');

    console.log('🎉 Database cleared successfully!');
    console.log('All data has been removed from NeonDB.');

  } catch (error) {
    console.error('❌ Error clearing database:', error);
  } finally {
    await client.end();
    console.log('🔌 Disconnected from database');
  }
}

// Confirmation check
const args = process.argv.slice(2);
if (args.includes('--confirm')) {
  clearDatabase()
    .then(() => {
      console.log('✨ Database cleanup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database cleanup failed:', error);
      process.exit(1);
    });
} else {
  console.log('⚠️  WARNING: This will delete ALL data from your NeonDB database!');
  console.log('This action cannot be undone.');
  console.log('');
  console.log('To proceed, run:');
  console.log('node scripts/clear-data-simple.js --confirm');
  console.log('');
}
