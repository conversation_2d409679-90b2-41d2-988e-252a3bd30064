const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearAllData() {
  try {
    console.log('🗑️  Starting database cleanup...');
    
    // Delete data in the correct order to avoid foreign key constraint errors
    console.log('Deleting transaction-related data...');
    await prisma.transactionItem.deleteMany({});
    await prisma.transaction.deleteMany({});
    
    console.log('Deleting inventory and transfer data...');
    await prisma.transferItem.deleteMany({});
    await prisma.transfer.deleteMany({});
    await prisma.inventoryTransfer.deleteMany({});
    await prisma.warehouseInventory.deleteMany({});
    await prisma.storeInventory.deleteMany({});
    
    console.log('Deleting production and raw material data...');
    await prisma.rawMaterialConsumption.deleteMany({});
    await prisma.production.deleteMany({});
    await prisma.productRawMaterial.deleteMany({});
    await prisma.rawMaterial.deleteMany({});
    
    console.log('Deleting product-related data...');
    await prisma.wastage.deleteMany({});
    await prisma.product_reviews.deleteMany({});
    await prisma.wishlist_items.deleteMany({});
    
    console.log('Deleting recipe data...');
    await prisma.recipeNutrition.deleteMany({});
    await prisma.recipeTip.deleteMany({});
    await prisma.recipeInstruction.deleteMany({});
    await prisma.recipeIngredient.deleteMany({});
    await prisma.recipeTag.deleteMany({});
    await prisma.recipe.deleteMany({});
    
    console.log('Deleting customer and order data...');
    await prisma.customerTag.deleteMany({});
    await prisma.address.deleteMany({});
    await prisma.customer.deleteMany({});
    
    console.log('Deleting financial data...');
    await prisma.bankTransaction.deleteMany({});
    await prisma.bankAccount.deleteMany({});
    await prisma.expense.deleteMany({});
    
    console.log('Deleting visit data...');
    await prisma.customerVisit.deleteMany({});
    
    console.log('Deleting core entities...');
    await prisma.product.deleteMany({});
    await prisma.category.deleteMany({});
    await prisma.store.deleteMany({});
    await prisma.warehouse.deleteMany({});
    
    console.log('Deleting user data...');
    await prisma.user.deleteMany({});
    
    console.log('Deleting system data...');
    await prisma.apiIntegration.deleteMany({});
    await prisma.systemSetting.deleteMany({});
    
    console.log('✅ Database cleared successfully!');
    console.log('All data has been removed from NeonDB.');
    
  } catch (error) {
    console.error('❌ Error clearing database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Confirmation prompt
console.log('⚠️  WARNING: This will delete ALL data from your NeonDB database!');
console.log('This action cannot be undone.');
console.log('');
console.log('If you are sure you want to proceed, run this script with the --confirm flag:');
console.log('node scripts/clear-database.js --confirm');
console.log('');

// Check for confirmation flag
const args = process.argv.slice(2);
if (args.includes('--confirm')) {
  clearAllData()
    .then(() => {
      console.log('🎉 Database cleanup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database cleanup failed:', error);
      process.exit(1);
    });
} else {
  console.log('❌ Database cleanup cancelled. Use --confirm flag to proceed.');
  process.exit(0);
}
