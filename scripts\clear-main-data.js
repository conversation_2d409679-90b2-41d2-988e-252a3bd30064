const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearMainData() {
  try {
    console.log('🗑️  Starting main data cleanup...');
    console.log('This will preserve users, warehouses, stores, and categories.');
    
    // Delete transaction-related data
    console.log('Deleting transactions...');
    await prisma.transactionItem.deleteMany({});
    await prisma.transaction.deleteMany({});
    
    // Delete inventory and transfer data
    console.log('Deleting inventory data...');
    await prisma.transferItem.deleteMany({});
    await prisma.transfer.deleteMany({});
    await prisma.inventoryTransfer.deleteMany({});
    await prisma.warehouseInventory.deleteMany({});
    await prisma.storeInventory.deleteMany({});
    
    // Delete production data
    console.log('Deleting production data...');
    await prisma.rawMaterialConsumption.deleteMany({});
    await prisma.production.deleteMany({});
    await prisma.productRawMaterial.deleteMany({});
    await prisma.rawMaterial.deleteMany({});
    
    // Delete product-related data
    console.log('Deleting product-related data...');
    await prisma.wastage.deleteMany({});
    await prisma.product_reviews.deleteMany({});
    await prisma.wishlist_items.deleteMany({});
    
    // Delete recipes
    console.log('Deleting recipes...');
    await prisma.recipeNutrition.deleteMany({});
    await prisma.recipeTip.deleteMany({});
    await prisma.recipeInstruction.deleteMany({});
    await prisma.recipeIngredient.deleteMany({});
    await prisma.recipeTag.deleteMany({});
    await prisma.recipe.deleteMany({});
    
    // Delete customer data
    console.log('Deleting customer data...');
    await prisma.customerTag.deleteMany({});
    await prisma.address.deleteMany({});
    await prisma.customer.deleteMany({});
    
    // Delete financial data
    console.log('Deleting financial data...');
    await prisma.bankTransaction.deleteMany({});
    await prisma.bankAccount.deleteMany({});
    await prisma.expense.deleteMany({});
    
    // Delete visits
    console.log('Deleting customer visits...');
    await prisma.customerVisit.deleteMany({});
    
    // Delete products (main data)
    console.log('Deleting products...');
    await prisma.product.deleteMany({});
    
    console.log('✅ Main data cleared successfully!');
    console.log('Preserved: Users, Warehouses, Stores, Categories, System Settings');
    
  } catch (error) {
    console.error('❌ Error clearing main data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Confirmation prompt
console.log('⚠️  WARNING: This will delete main data from your NeonDB database!');
console.log('This will preserve users, warehouses, stores, and categories.');
console.log('This action cannot be undone.');
console.log('');
console.log('If you are sure you want to proceed, run this script with the --confirm flag:');
console.log('node scripts/clear-main-data.js --confirm');
console.log('');

// Check for confirmation flag
const args = process.argv.slice(2);
if (args.includes('--confirm')) {
  clearMainData()
    .then(() => {
      console.log('🎉 Main data cleanup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Main data cleanup failed:', error);
      process.exit(1);
    });
} else {
  console.log('❌ Data cleanup cancelled. Use --confirm flag to proceed.');
  process.exit(0);
}
