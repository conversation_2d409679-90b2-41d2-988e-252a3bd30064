const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createMultipleCustomers() {
  try {
    console.log('🧪 Creating multiple customer accounts for testing...\n');

    const customers = [
      {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'password123'
      }
    ];

    for (const customerData of customers) {
      // Check if customer already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: customerData.email }
      });

      if (existingUser) {
        console.log(`✅ Customer already exists: ${customerData.email}`);
        continue;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(customerData.password, 10);

      // Create customer account
      const customer = await prisma.user.create({
        data: {
          name: customerData.name,
          email: customerData.email,
          password: hashedPassword,
          role: 'CUSTOMER',
        },
      });

      console.log(`✅ Created customer: ${customer.email} (${customer.name})`);
    }

    console.log('\n🎉 All customers created successfully!');
    console.log('\n📧 Now ANY of these emails can use forgot password:');
    customers.forEach(customer => {
      console.log(`   - ${customer.email}`);
    });

    console.log('\n🧪 Test forgot password with any of these emails:');
    console.log('1. Go to: http://localhost:3001/forgot-password');
    console.log('2. Enter ANY of the above email addresses');
    console.log('3. The OTP will be sent to THAT specific email address');
    console.log('4. Each customer gets their OTP at their own email');

    console.log('\n📧 Email Configuration:');
    console.log('✅ Emails sent FROM: <EMAIL> (our SMTP)');
    console.log('✅ Emails sent TO: Whatever email the customer enters');
    console.log('✅ Each customer receives OTP at their own email address');

  } catch (error) {
    console.error('❌ Error creating customers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createMultipleCustomers();
