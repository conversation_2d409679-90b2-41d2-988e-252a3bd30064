const fs = require('fs');
const path = require('path');

// Create better quality images for categories
function createBetterCategoryImage(categoryName, color, emoji) {
  return `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-${categoryName}" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
      <stop offset="100%" style="stop-color:${adjustColor(color, -20)};stop-opacity:1" />
    </linearGradient>
    <filter id="shadow-${categoryName}">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#bg-${categoryName})"/>
  
  <!-- Decorative circles -->
  <circle cx="80" cy="60" r="30" fill="rgba(255,255,255,0.1)"/>
  <circle cx="320" cy="240" r="40" fill="rgba(255,255,255,0.1)"/>
  <circle cx="350" cy="80" r="20" fill="rgba(255,255,255,0.15)"/>
  
  <!-- Main content area -->
  <rect x="40" y="60" width="320" height="180" rx="20" fill="rgba(255,255,255,0.95)" filter="url(#shadow-${categoryName})"/>
  
  <!-- Emoji -->
  <text x="200" y="130" font-family="Arial, sans-serif" font-size="48" text-anchor="middle" fill="${color}">${emoji}</text>
  
  <!-- Category name -->
  <text x="200" y="170" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#1f2937">${categoryName}</text>
  
  <!-- Subtitle -->
  <text x="200" y="195" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6b7280">Premium Quality</text>
</svg>`;
}

function adjustColor(color, amount) {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = Math.max(0, Math.min(255, (num >> 16) + amount));
  const g = Math.max(0, Math.min(255, (num >> 8 & 0x00FF) + amount));
  const b = Math.max(0, Math.min(255, (num & 0x0000FF) + amount));
  return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
}

function createCategoryImages() {
  console.log('🎨 Creating beautiful category images...');
  
  const publicDir = path.join(process.cwd(), 'public');
  
  // Create directories if they don't exist
  const dirs = ['images/flowers', 'images/cakes', 'images/combos', 'images/plants'];
  dirs.forEach(dir => {
    const fullPath = path.join(publicDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });
  
  // Category data with better colors and emojis
  const categories = [
    {
      name: 'Flowers',
      path: 'images/flowers/pink_bouquet.jpg',
      color: '#ec4899',
      emoji: '🌸'
    },
    {
      name: 'Cakes',
      path: 'images/cakes/cake_display.jpg',
      color: '#f59e0b',
      emoji: '🍰'
    },
    {
      name: 'Gifts',
      path: 'images/combos/gift_combo.jpg',
      color: '#8b5cf6',
      emoji: '🎁'
    },
    {
      name: 'Plants',
      path: 'images/plants/plant_display.jpg',
      color: '#10b981',
      emoji: '🌱'
    },
    {
      name: 'Combos',
      path: 'images/combos/celebration_combo.jpg',
      color: '#ef4444',
      emoji: '🎉'
    }
  ];
  
  // Create images
  categories.forEach(({ name, path: imagePath, color, emoji }) => {
    const fullPath = path.join(publicDir, imagePath);
    const svgContent = createBetterCategoryImage(name, color, emoji);
    
    fs.writeFileSync(fullPath, svgContent);
    console.log(`✅ Created: ${imagePath}`);
  });
  
  console.log('\n🎉 All category images created successfully!');
  console.log('\n📋 Created images:');
  categories.forEach(({ name, path: imagePath }) => {
    console.log(`   🖼️ ${name}: ${imagePath}`);
  });
  
  console.log('\n🔍 Next steps:');
  console.log('   1. Check admin panel → Categories');
  console.log('   2. Images should now display properly');
  console.log('   3. Test mobile and desktop website');
}

// Run the script
createCategoryImages();
