const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createTestCustomer() {
  try {
    // Check if customer already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('Test customer already exists:', existingUser.email);
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 10);

    // Create test customer
    const customer = await prisma.user.create({
      data: {
        name: 'Test Customer',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'CUSTOMER',
      },
    });

    console.log('✅ Test customer created successfully:');
    console.log('Email:', customer.email);
    console.log('Password: password123');
    console.log('Role:', customer.role);
  } catch (error) {
    console.error('❌ Error creating test customer:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestCustomer();
