const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createVaishnaviCustomer() {
  try {
    console.log('🧪 Creating customer <NAME_EMAIL>...\n');

    // Check if customer already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Customer already exists:');
      console.log(`   - Email: ${existingUser.email}`);
      console.log(`   - Name: ${existingUser.name}`);
      console.log(`   - Role: ${existingUser.role}`);
      console.log(`   - Created: ${existingUser.createdAt}`);
      return existingUser;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 10);

    // Create customer account
    const customer = await prisma.user.create({
      data: {
        name: '<PERSON><PERSON><PERSON><PERSON>wa<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'CUSTOMER',
      },
    });

    console.log('✅ Customer account created successfully:');
    console.log(`   - Email: ${customer.email}`);
    console.log(`   - Name: ${customer.name}`);
    console.log(`   - Role: ${customer.role}`);
    console.log(`   - Password: password123`);
    console.log(`   - Created: ${customer.createdAt}`);

    console.log('\n🎉 Now you can test forgot <NAME_EMAIL>!');
    console.log('📧 Password reset emails will be sent to this address.');
    
    return customer;
  } catch (error) {
    console.error('❌ Error creating customer:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createVaishnaviCustomer();
