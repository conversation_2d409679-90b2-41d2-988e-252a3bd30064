const { default: fetch } = require('node-fetch');

async function debugAdminAPI() {
  console.log('🔍 DEBUGGING ADMIN PANEL API');
  console.log('============================\n');

  try {
    console.log('📧 Testing admin panel forgot password API...');
    console.log('🌐 URL: http://localhost:3003/api/auth/forgot-password');
    console.log('📧 Email: <EMAIL>\n');
    
    const response = await fetch('http://localhost:3003/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📋 Response Headers:`, response.headers.raw());
    
    const responseText = await response.text();
    console.log('📄 Raw Response:', responseText.substring(0, 500));

    try {
      const data = JSON.parse(responseText);
      console.log('📋 Parsed JSON:', JSON.stringify(data, null, 2));
    } catch (parseError) {
      console.log('❌ Response is not valid JSON');
      console.log('📄 Response content type:', response.headers.get('content-type'));
    }

    if (response.ok) {
      console.log('\n✅ SUCCESS! Admin panel API is working!');
    } else {
      console.log('\n❌ FAILED! Admin panel API error');
      console.log('🔧 Check the response details above');
    }

  } catch (error) {
    console.error('\n❌ Request failed:', error.message);
    console.log('🔧 Possible issues:');
    console.log('1. Admin panel not running on port 3003');
    console.log('2. Network connectivity issues');
    console.log('3. API route not found');
  }
}

debugAdminAPI();
