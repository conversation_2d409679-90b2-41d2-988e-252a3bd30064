const { default: fetch } = require('node-fetch');

async function debugWebsiteEmail() {
  try {
    console.log('🔍 DEBUGGING WEBSITE EMAIL SYSTEM');
    console.log('=====================================\n');

    // Step 1: Test website forgot password API
    console.log('📧 Step 1: Testing website forgot password API...');
    console.log('🌐 URL: http://localhost:3001/api/auth/forgot-password');
    console.log('📧 Email: <EMAIL>\n');

    const response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    console.log(`📊 Response Status: ${response.status}`);
    const data = await response.json();
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.log('\n❌ Website API failed - this is why no emails are sent!');
      return;
    }

    console.log('\n✅ Website API successful');

    // Step 2: Test admin panel API directly
    console.log('\n📧 Step 2: Testing admin panel API directly...');
    console.log('🌐 URL: http://localhost:3000/api/auth/forgot-password');

    const adminResponse = await fetch('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    console.log(`📊 Admin Response Status: ${adminResponse.status}`);
    const adminData = await adminResponse.json();
    console.log('📋 Admin Response Data:', JSON.stringify(adminData, null, 2));

    if (!adminResponse.ok) {
      console.log('\n❌ Admin panel API failed - this is why no emails are sent!');
      return;
    }

    console.log('\n✅ Admin panel API successful');

    // Step 3: Check email service configuration
    console.log('\n📧 Step 3: Checking email service configuration...');
    
    // Test the email service directly
    const emailTestResponse = await fetch('http://localhost:3000/api/test-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: '<EMAIL>',
        subject: 'Test Email from Debug Script',
        message: 'This is a test email to verify the email service is working.'
      }),
    });

    if (emailTestResponse.ok) {
      console.log('✅ Email service test successful');
    } else {
      console.log('⚠️ Email service test endpoint not available (this is normal)');
    }

    console.log('\n🔍 DIAGNOSIS:');
    console.log('=============');
    
    if (response.ok && adminResponse.ok) {
      console.log('✅ Both APIs are working correctly');
      console.log('📧 Emails should be sent to: <EMAIL>');
      console.log('\n🔧 If emails are not received, check:');
      console.log('1. 📬 Gmail inbox (including spam folder)');
      console.log('2. 📧 Gmail SMTP configuration in .env file');
      console.log('3. 🔑 App password is correct');
      console.log('4. 🌐 Internet connectivity');
      console.log('5. 📊 Admin panel console logs for email sending status');
      
      console.log('\n🧪 NEXT STEPS:');
      console.log('1. Go to http://localhost:3001/forgot-password');
      console.log('2. Enter: <EMAIL>');
      console.log('3. Click "Send reset link"');
      console.log('4. Check Gmail inbox immediately');
      console.log('5. Check admin panel console for email logs');
    } else {
      console.log('❌ API issues detected - this explains missing emails');
    }

  } catch (error) {
    console.error('\n❌ Debug test failed:', error.message);
    console.log('\n🔧 Possible issues:');
    console.log('1. 🌐 Website not running on port 3001');
    console.log('2. 🌐 Admin panel not running on port 3000');
    console.log('3. 🔌 Network connectivity problems');
    console.log('4. ⚙️ Configuration issues');
  }
}

debugWebsiteEmail();
