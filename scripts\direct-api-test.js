// Direct API test for store assignment
const fetch = require('node-fetch');

async function testStoreAssignmentAPI() {
  console.log('🧪 DIRECT API TEST - STORE ASSIGNMENT');
  console.log('=====================================\n');

  const adminURL = 'http://localhost:3002';
  const testOrderId = 'order-1';
  const testStoreId = 'store-1';

  try {
    console.log('📋 Test Parameters:');
    console.log(`   Admin URL: ${adminURL}`);
    console.log(`   Order ID: ${testOrderId}`);
    console.log(`   Store ID: ${testStoreId}`);

    console.log('\n🔄 Step 1: Testing store assignment API...');
    
    const response = await fetch(`${adminURL}/api/orders/${testOrderId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        storeId: testStoreId
      }),
    });

    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API Test Failed:');
      console.log(`   Status: ${response.status}`);
      console.log(`   Error: ${errorText}`);
      
      if (response.status === 404) {
        console.log('\n💡 Possible Issues:');
        console.log('   - Admin panel not running');
        console.log('   - Wrong URL or port');
        console.log('   - API endpoint not found');
      } else if (response.status === 500) {
        console.log('\n💡 Possible Issues:');
        console.log('   - Server error in API');
        console.log('   - Database connection issue');
        console.log('   - Code error in endpoint');
      }
      
      return { success: false, error: errorText };
    }

    const result = await response.json();
    console.log('✅ API Test Successful!');
    console.log('\n📋 Response Data:');
    console.log(`   Order ID: ${result.id}`);
    console.log(`   Order Number: ${result.orderNumber}`);
    console.log(`   Customer: ${result.customerName}`);
    console.log(`   Store ID: ${result.storeId}`);
    console.log(`   Store Name: ${result.storeName}`);
    console.log(`   Status: ${result.status}`);
    console.log(`   Total: ₹${result.totalAmount}`);

    console.log('\n🎉 STORE ASSIGNMENT API IS WORKING!');
    console.log('===================================');
    console.log('✅ API endpoint is functional');
    console.log('✅ Store assignment logic is working');
    console.log('✅ Mock system is responding correctly');
    console.log('✅ Response format is correct');

    return { success: true, result };

  } catch (error) {
    console.log('❌ API Test Failed with Exception:');
    console.log(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 SOLUTION:');
      console.log('   ❌ Admin panel is not running');
      console.log('   ✅ Start admin panel: npm run dev');
      console.log('   ✅ Wait for it to fully load');
      console.log('   ✅ Then test again');
    } else {
      console.log('\n💡 TROUBLESHOOTING:');
      console.log('   - Check if admin panel is accessible');
      console.log('   - Verify the URL and port');
      console.log('   - Check for network issues');
    }
    
    return { success: false, error: error.message };
  }
}

// Run the test
testStoreAssignmentAPI().then(result => {
  if (result.success) {
    console.log('\n🎊 SUCCESS! STORE ASSIGNMENT API IS WORKING!');
    console.log('============================================');
    console.log('The admin panel store assignment should now work correctly.');
    console.log('You can proceed to test it in the browser interface.');
  } else {
    console.log('\n❌ API TEST FAILED');
    console.log('==================');
    console.log('Please check the admin panel and try again.');
    console.log('Make sure the admin panel is running on port 3002.');
  }
}).catch(error => {
  console.error('\n💥 TEST CRASHED:', error.message);
});
