const { PrismaClient } = require('@prisma/client');
const nodemailer = require('nodemailer');
require('dotenv').config();

const prisma = new PrismaClient();

// Generate 6-digit OTP
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

async function directOTPTest() {
  try {
    console.log('🧪 DIRECT OTP TEST (Bypassing API)');
    console.log('==================================\n');

    const email = '<EMAIL>';
    console.log('📧 Testing for email:', email);

    // Step 1: Check if user exists
    console.log('\n🔍 Step 1: Checking if customer exists...');
    const user = await prisma.user.findFirst({
      where: {
        email,
        role: 'CUSTOMER'
      },
    });

    if (!user) {
      console.log('❌ Customer not found');
      return;
    }

    console.log('✅ Customer found:', user.name);

    // Step 2: Generate OTP
    console.log('\n🔢 Step 2: Generating OTP...');
    const resetOTP = generateOTP();
    const resetOTPExpiry = new Date(Date.now() + 600000); // 10 minutes

    console.log('✅ Generated OTP:', resetOTP);
    console.log('⏰ Expires at:', resetOTPExpiry.toLocaleString());

    // Step 3: Save OTP to database
    console.log('\n💾 Step 3: Saving OTP to database...');
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetOTP,
        resetOTPExpiry,
      },
    });

    console.log('✅ OTP saved to database');

    // Step 4: Send email
    console.log('\n📧 Step 4: Sending OTP email...');

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Send email
    const result = await transporter.sendMail({
      from: `"Mispri" <${process.env.SMTP_USER}>`,
      to: email,
      subject: 'Your Password Reset Code - Mispri',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
          <div style="background-color: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #ff7700; font-size: 32px; margin-bottom: 10px;">🌸 Mispri</h1>
              <h2 style="color: #333; margin-bottom: 20px;">Password Reset Code</h2>
            </div>

            <p>Hello ${user.name},</p>

            <p>We received a request to reset your password for your Mispri account. Use the verification code below to proceed:</p>

            <div style="background-color: #f8f9fa; border: 2px dashed #ff7700; border-radius: 10px; padding: 30px; text-align: center; margin: 30px 0;">
              <div style="font-size: 14px; color: #666; margin-bottom: 10px;">Your 6-Digit Verification Code</div>
              <div style="font-size: 36px; font-weight: bold; color: #ff7700; letter-spacing: 8px; font-family: 'Courier New', monospace; margin: 10px 0;">${resetOTP}</div>
              <div style="font-size: 12px; color: #999; margin-top: 10px;">Enter this code on the password reset page</div>
            </div>

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <strong>⚠️ Important Security Information:</strong>
              <ul style="margin: 10px 0 0 0;">
                <li>This code will expire in <strong>10 minutes</strong></li>
                <li>Don't share this code with anyone</li>
                <li>If you didn't request this, please ignore this email</li>
                <li>Only use this code on the official Mispri website</li>
              </ul>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 14px; margin: 0;">
                Best regards,<br>The Mispri Team
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
Hello ${user.name},

We received a request to reset your password for your Mispri account.

Your 6-Digit Verification Code: ${resetOTP}

⚠️ Important Security Information:
- This code will expire in 10 minutes
- Don't share this code with anyone
- If you didn't request this, please ignore this email
- Only use this code on the official Mispri website

Best regards,
The Mispri Team
      `.trim()
    });

    console.log('✅ Email sent successfully!');
    console.log('📧 Message ID:', result.messageId);

    console.log('\n🎉 SUCCESS! OTP System Working!');
    console.log('================================');
    console.log('📧 Check Gmail inbox:', email);
    console.log('🔢 OTP Code:', resetOTP);
    console.log('⏰ Valid for: 10 minutes');
    console.log('🌐 Use at: http://localhost:3001/forgot-password');

    console.log('\n📝 Next Steps:');
    console.log('1. Check your Gmail inbox');
    console.log('2. Look for email from Mispri');
    console.log('3. Note the 6-digit code');
    console.log('4. Go to forgot password page');
    console.log('5. Enter your email and the OTP code');

  } catch (error) {
    console.error('❌ Direct OTP test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

directOTPTest();
