const { default: fetch } = require('node-fetch');

async function finalWebsiteTest() {
  console.log('🎯 FINAL WEBSITE FORGOT PASSWORD TEST');
  console.log('=====================================\n');

  try {
    console.log('📧 Testing website forgot <NAME_EMAIL>...');
    console.log('🌐 URL: http://localhost:3001/api/auth/forgot-password\n');

    const startTime = Date.now();
    
    const response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️  Request took: ${duration}ms`);
    console.log(`📊 Response Status: ${response.status}`);
    
    const data = await response.json();
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ SUCCESS! Website forgot password API is working!');
      console.log('📧 Email should have been sent to: <EMAIL>');
      
      console.log('\n📬 CHECK YOUR EMAIL:');
      console.log('===================');
      console.log('1. 📧 Open Gmail: https://gmail.com');
      console.log('2. 🔍 Check Inbox for email from Mispri');
      console.log('3. 📁 Check Spam/Junk folder if not in inbox');
      console.log('4. 🔍 Search for "Mispri" or "Reset Your Password"');
      console.log('5. ⏰ Email should arrive within 1-2 minutes');
      
      console.log('\n📧 EMAIL DETAILS TO LOOK FOR:');
      console.log('============================');
      console.log('📤 From: Mispri <<EMAIL>>');
      console.log('📧 Subject: Reset Your Password - Mispri');
      console.log('🎨 Content: Professional HTML email with reset button');
      console.log('🔗 Contains: Reset password link');
      
      console.log('\n🔧 IF EMAIL NOT RECEIVED:');
      console.log('=========================');
      console.log('1. ⏰ Wait 2-3 minutes (Gmail can be slow)');
      console.log('2. 📁 Check ALL folders (Spam, Promotions, etc.)');
      console.log('3. 🔍 Search Gmail for "mispri" or "password"');
      console.log('4. 📧 <NAME_EMAIL> is the correct address');
      console.log('5. 🔄 Try the test again');
      
    } else {
      console.log('\n❌ FAILED! Website forgot password API error');
      console.log('🔧 This explains why no emails are received');
      
      if (data.error) {
        console.log(`💬 Error message: ${data.error}`);
      }
    }

  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    console.log('\n🔧 Possible issues:');
    console.log('1. 🌐 Website not running (start with: cd website && npm run dev)');
    console.log('2. 🌐 Admin panel not running (start with: npm run dev)');
    console.log('3. 🔌 Network connectivity issues');
  }

  console.log('\n🎯 SUMMARY:');
  console.log('===========');
  console.log('✅ Gmail SMTP is configured and working');
  console.log('✅ Direct email sending works perfectly');
  console.log('✅ Customer account <NAME_EMAIL>');
  console.log('📧 Website forgot password should send emails');
  console.log('\n🔍 Next: Check Gmail inbox for the reset email!');
}

finalWebsiteTest();
