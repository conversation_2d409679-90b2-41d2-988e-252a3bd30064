const { PrismaClient } = require('@prisma/client');

async function fixDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔄 Testing database connection...');
    
    // Test basic connection
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful!');
    
    // Check if tables exist
    const productCount = await prisma.product.count();
    console.log(`📊 Products in database: ${productCount}`);
    
    const categoryCount = await prisma.category.count();
    console.log(`📂 Categories in database: ${categoryCount}`);
    
    // If no categories exist, create default ones
    if (categoryCount === 0) {
      console.log('🔄 Creating default categories...');
      
      const defaultCategories = [
        'Flowers', 'Cakes', 'Birthday', 'Anniversary', 
        'Gifts', 'Personalised', 'Plants', 'Combos'
      ];
      
      for (const categoryName of defaultCategories) {
        await prisma.category.create({
          data: {
            name: categoryName,
            description: `${categoryName} category`,
            isActive: true
          }
        });
      }
      
      console.log('✅ Default categories created!');
    }
    
    // If no products exist, create a sample product
    if (productCount === 0) {
      console.log('🔄 Creating sample product...');
      
      const product = await prisma.product.create({
        data: {
          name: 'Sample Cake',
          description: 'A delicious sample cake',
          category: 'Cakes',
          price: 800,
          discountedPrice: 680,
          costPrice: 400,
          unit: 'piece',
          lowStockThreshold: 10,
          sku: 'SAMPLE-001',
          isActive: true
        }
      });
      
      // Create variants for the sample product
      await prisma.productVariant.createMany({
        data: [
          {
            productId: product.id,
            weight: '0.5 Kg',
            price: 500,
            discountedPrice: 425,
            costPrice: 250,
            isDefault: true,
            isActive: true,
            sortOrder: 1
          },
          {
            productId: product.id,
            weight: '1 Kg',
            price: 800,
            discountedPrice: 680,
            costPrice: 400,
            isDefault: false,
            isActive: true,
            sortOrder: 2
          }
        ]
      });
      
      console.log('✅ Sample product created!');
    }
    
    console.log('🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Database error:', error);
    console.error('Details:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

fixDatabase();
