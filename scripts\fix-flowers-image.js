const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixFlowersImage() {
  console.log('🌸 Fixing Flowers category image...');
  
  try {
    // Update Flowers category metadata
    const result = await prisma.systemSetting.upsert({
      where: {
        category_key: {
          category: 'category_metadata',
          key: 'Flowers'
        }
      },
      update: {
        value: JSON.stringify({
          description: 'Fresh flowers and beautiful arrangements for every occasion',
          imageUrl: '/images/flowers/pink_bouquet.jpg',
          slug: 'flowers',
          displayOrder: 1,
          isActive: true,
          updatedAt: new Date().toISOString(),
        }),
        updatedAt: new Date()
      },
      create: {
        category: 'category_metadata',
        key: 'Flowers',
        value: JSON.stringify({
          description: 'Fresh flowers and beautiful arrangements for every occasion',
          imageUrl: '/images/flowers/pink_bouquet.jpg',
          slug: 'flowers',
          displayOrder: 1,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    
    console.log('✅ Flowers category image fixed!');
    console.log('   🖼️ Image URL: /images/flowers/pink_bouquet.jpg');
    console.log('   📝 Description: Fresh flowers and beautiful arrangements for every occasion');
    
  } catch (error) {
    console.error('❌ Error fixing Flowers image:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixFlowersImage();
