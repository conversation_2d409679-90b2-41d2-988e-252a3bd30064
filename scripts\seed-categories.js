const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const categories = [
  {
    name: "Cakes",
    description: "Delicious cakes for all occasions - birthdays, anniversaries, and celebrations",
    imageUrl: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop",
    slug: "cakes",
    displayOrder: 1,
    isActive: true
  },
  {
    name: "Flowers",
    description: "Fresh flowers and beautiful arrangements for every occasion",
    imageUrl: "https://images.unsplash.com/photo-1518895949257-7621c3c786d7?w=400&h=300&fit=crop",
    slug: "flowers",
    displayOrder: 2,
    isActive: true
  },
  {
    name: "Birthday",
    description: "Special birthday gifts, cakes, and celebration packages",
    imageUrl: "https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400&h=300&fit=crop",
    slug: "birthday",
    displayOrder: 3,
    isActive: true
  },
  {
    name: "Anniversary",
    description: "Romantic gifts and arrangements for anniversary celebrations",
    imageUrl: "https://images.unsplash.com/photo-1606890737304-57a1ca8a5b62?w=400&h=300&fit=crop",
    slug: "anniversary",
    displayOrder: 4,
    isActive: true
  },
  {
    name: "Gifts",
    description: "Perfect gifts for your loved ones on any occasion",
    imageUrl: "https://images.unsplash.com/photo-1549007994-cb92caebd54b?w=400&h=300&fit=crop",
    slug: "gifts",
    displayOrder: 5,
    isActive: true
  },
  {
    name: "Plants",
    description: "Beautiful plants and green arrangements for home and office",
    imageUrl: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop",
    slug: "plants",
    displayOrder: 6,
    isActive: true
  }
];

async function seedCategories() {
  console.log('🏷️ Starting to seed category metadata...');
  
  try {
    for (const category of categories) {
      try {
        // Store category metadata in system settings
        await prisma.systemSetting.upsert({
          where: {
            category_key: {
              category: 'category_metadata',
              key: category.name
            }
          },
          update: {
            value: JSON.stringify({
              description: category.description,
              imageUrl: category.imageUrl,
              slug: category.slug,
              displayOrder: category.displayOrder,
              isActive: category.isActive,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }),
            updatedAt: new Date()
          },
          create: {
            category: 'category_metadata',
            key: category.name,
            value: JSON.stringify({
              description: category.description,
              imageUrl: category.imageUrl,
              slug: category.slug,
              displayOrder: category.displayOrder,
              isActive: category.isActive,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            })
          }
        });
        
        console.log(`✅ Created category metadata: ${category.name}`);
      } catch (error) {
        console.log(`⚠️ Error with category ${category.name}:`, error.message);
      }
    }
    
    console.log('🎉 Category metadata seeding completed!');
    
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedCategories();
