const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const mockProducts = [
  // Cakes Category
  {
    name: "Chocolate Birthday Cake",
    description: "Rich and moist chocolate cake perfect for birthday celebrations. Made with premium cocoa and topped with chocolate ganache.",
    category: "Cakes",
    price: 599,
    costPrice: 350,
    unit: "piece",
    lowStockThreshold: 5,
    sku: "CAKE-CHOC-001",
    imageUrl: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=800&h=600&fit=crop",
    metaTitle: "Chocolate Birthday Cake - Fresh & Delicious",
    metaDescription: "Order fresh chocolate birthday cake online. Perfect for celebrations with rich chocolate flavor.",
    isActive: true,
  },
  {
    name: "Vanilla Sponge Cake",
    description: "Light and fluffy vanilla sponge cake with fresh cream and seasonal fruits. A classic favorite for all occasions.",
    category: "Cakes",
    price: 499,
    costPrice: 300,
    unit: "piece",
    lowStockThreshold: 5,
    sku: "CAKE-VAN-002",
    imageUrl: "https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=800&h=600&fit=crop",
    metaTitle: "Vanilla Sponge Cake - Light & Fluffy",
    metaDescription: "Fresh vanilla sponge cake with cream and fruits. Perfect for any celebration.",
    isActive: true,
  },
  {
    name: "Red Velvet Cake",
    description: "Classic red velvet cake with cream cheese frosting. Moist, tender, and absolutely irresistible.",
    category: "Cakes",
    price: 699,
    costPrice: 400,
    unit: "piece",
    lowStockThreshold: 3,
    sku: "CAKE-RED-003",
    imageUrl: "https://images.unsplash.com/photo-1586985289688-ca3cf47d3e6e?w=800&h=600&fit=crop",
    metaTitle: "Red Velvet Cake - Classic & Elegant",
    metaDescription: "Premium red velvet cake with cream cheese frosting. Order online for special occasions.",
    isActive: true,
  },

  // Flowers Category
  {
    name: "Red Rose Bouquet",
    description: "Beautiful bouquet of 12 fresh red roses, perfect for expressing love and romance. Wrapped in elegant paper.",
    category: "Flowers",
    price: 899,
    costPrice: 500,
    unit: "bouquet",
    lowStockThreshold: 10,
    sku: "FLOW-ROSE-001",
    imageUrl: "https://images.unsplash.com/photo-1518895949257-7621c3c786d7?w=800&h=600&fit=crop",
    metaTitle: "Red Rose Bouquet - Symbol of Love",
    metaDescription: "Fresh red rose bouquet with 12 premium roses. Perfect for romantic occasions.",
    isActive: true,
  },
  {
    name: "Mixed Flower Arrangement",
    description: "Vibrant mixed flower arrangement with roses, lilies, and seasonal blooms. Perfect for any celebration.",
    category: "Flowers",
    price: 1299,
    costPrice: 700,
    unit: "arrangement",
    lowStockThreshold: 8,
    sku: "FLOW-MIX-002",
    imageUrl: "https://images.unsplash.com/photo-1487070183336-b863922373d4?w=800&h=600&fit=crop",
    metaTitle: "Mixed Flower Arrangement - Colorful & Fresh",
    metaDescription: "Beautiful mixed flower arrangement with roses and lilies. Perfect for celebrations.",
    isActive: true,
  },
  {
    name: "White Lily Bouquet",
    description: "Elegant white lily bouquet symbolizing purity and peace. Perfect for sympathy or spiritual occasions.",
    category: "Flowers",
    price: 799,
    costPrice: 450,
    unit: "bouquet",
    lowStockThreshold: 6,
    sku: "FLOW-LILY-003",
    imageUrl: "https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=800&h=600&fit=crop",
    metaTitle: "White Lily Bouquet - Pure & Elegant",
    metaDescription: "Fresh white lily bouquet for special occasions. Symbol of purity and peace.",
    isActive: true,
  },

  // Birthday Category
  {
    name: "Birthday Special Combo",
    description: "Complete birthday package with chocolate cake, balloon bouquet, and greeting card. Make birthdays memorable!",
    category: "Birthday",
    price: 1199,
    costPrice: 700,
    unit: "combo",
    lowStockThreshold: 5,
    sku: "BIRTH-COMBO-001",
    imageUrl: "https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=800&h=600&fit=crop",
    metaTitle: "Birthday Special Combo - Complete Package",
    metaDescription: "Birthday combo with cake, balloons, and card. Perfect birthday celebration package.",
    isActive: true,
  },
  {
    name: "Kids Birthday Cake",
    description: "Colorful and fun birthday cake designed especially for kids. Available in various cartoon themes.",
    category: "Birthday",
    price: 799,
    costPrice: 450,
    unit: "piece",
    lowStockThreshold: 4,
    sku: "BIRTH-KIDS-002",
    imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop",
    metaTitle: "Kids Birthday Cake - Fun & Colorful",
    metaDescription: "Special birthday cake for kids with cartoon themes. Make their day special!",
    isActive: true,
  },

  // Anniversary Category
  {
    name: "Anniversary Rose & Cake Combo",
    description: "Romantic anniversary combo with red roses and heart-shaped cake. Perfect for celebrating love.",
    category: "Anniversary",
    price: 1599,
    costPrice: 900,
    unit: "combo",
    lowStockThreshold: 3,
    sku: "ANNIV-COMBO-001",
    imageUrl: "https://images.unsplash.com/photo-1606890737304-57a1ca8a5b62?w=800&h=600&fit=crop",
    metaTitle: "Anniversary Rose & Cake Combo - Romantic",
    metaDescription: "Anniversary combo with roses and heart cake. Celebrate your love story.",
    isActive: true,
  },

  // Gifts Category
  {
    name: "Chocolate Gift Box",
    description: "Premium chocolate gift box with assorted handmade chocolates. Perfect for any gifting occasion.",
    category: "Gifts",
    price: 999,
    costPrice: 600,
    unit: "box",
    lowStockThreshold: 8,
    sku: "GIFT-CHOC-001",
    imageUrl: "https://images.unsplash.com/photo-1549007994-cb92caebd54b?w=800&h=600&fit=crop",
    metaTitle: "Chocolate Gift Box - Premium Selection",
    metaDescription: "Handmade chocolate gift box with premium chocolates. Perfect for gifting.",
    isActive: true,
  },

  // Plants Category
  {
    name: "Lucky Bamboo Plant",
    description: "Beautiful lucky bamboo plant in decorative pot. Brings good luck and positive energy to your space.",
    category: "Plants",
    price: 399,
    costPrice: 200,
    unit: "plant",
    lowStockThreshold: 12,
    sku: "PLANT-BAMB-001",
    imageUrl: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=800&h=600&fit=crop",
    metaTitle: "Lucky Bamboo Plant - Good Luck Charm",
    metaDescription: "Lucky bamboo plant in decorative pot. Brings positive energy and good luck.",
    isActive: true,
  },
  {
    name: "Succulent Garden",
    description: "Beautiful collection of succulent plants in a decorative planter. Low maintenance and perfect for home decor.",
    category: "Plants",
    price: 699,
    costPrice: 350,
    unit: "arrangement",
    lowStockThreshold: 6,
    sku: "PLANT-SUCC-002",
    imageUrl: "https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=800&h=600&fit=crop",
    metaTitle: "Succulent Garden - Low Maintenance Beauty",
    metaDescription: "Beautiful succulent garden arrangement. Perfect for home and office decor.",
    isActive: true,
  }
];

async function seedData() {
  console.log('🌱 Starting to seed mock data...');
  
  try {
    // Clear existing data
    console.log('🧹 Clearing existing products...');
    await prisma.product.deleteMany({});
    
    // Seed products
    console.log('📦 Creating products...');
    for (const product of mockProducts) {
      try {
        const createdProduct = await prisma.product.create({
          data: product
        });
        console.log(`✅ Created: ${createdProduct.name}`);
      } catch (error) {
        console.log(`⚠️ Skipped: ${product.name} (might already exist)`);
      }
    }
    
    console.log('🎉 Mock data seeding completed successfully!');
    console.log(`📊 Total products created: ${mockProducts.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedData();
