// Seed inventory data to Neon database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedInventory() {
  console.log('📦 SEEDING INVENTORY TO NEON DATABASE');
  console.log('====================================\n');

  try {
    // Check if inventory already exists
    console.log('🔍 Checking existing inventory...');
    const existingWarehouseInventory = await prisma.warehouseInventory.findMany();
    const existingStoreInventory = await prisma.storeInventory.findMany();
    console.log(`📊 Found ${existingWarehouseInventory.length} warehouse inventory items`);
    console.log(`📊 Found ${existingStoreInventory.length} store inventory items`);

    // Get products, warehouses, and stores
    const products = await prisma.product.findMany();
    const warehouses = await prisma.warehouse.findMany();
    const stores = await prisma.store.findMany();

    console.log(`📋 Found ${products.length} products`);
    console.log(`📋 Found ${warehouses.length} warehouses`);
    console.log(`📋 Found ${stores.length} stores`);

    if (products.length === 0) {
      console.log('⚠️ No products found. Please run seed-products.js first');
      return;
    }

    if (warehouses.length === 0) {
      console.log('⚠️ No warehouses found. Please run seed-warehouses.js first');
      return;
    }

    if (stores.length === 0) {
      console.log('⚠️ No stores found. Please run seed-stores.js first');
      return;
    }

    console.log('\n📦 Creating warehouse inventory...');
    
    // Create warehouse inventory for each product
    const warehouseInventoryData = [];
    
    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const warehouse = warehouses[i % warehouses.length]; // Distribute across warehouses
      
      // Check if inventory already exists
      const existingInventory = await prisma.warehouseInventory.findUnique({
        where: {
          warehouseId_productId: {
            warehouseId: warehouse.id,
            productId: product.id
          }
        }
      });

      if (existingInventory) {
        console.log(`✅ Warehouse inventory already exists: ${product.name} in ${warehouse.name}`);
        warehouseInventoryData.push(existingInventory);
      } else {
        // Generate random stock quantity
        const quantity = Math.floor(Math.random() * 50) + 10; // 10-60 items
        
        console.log(`🆕 Creating warehouse inventory: ${product.name} in ${warehouse.name} (${quantity} ${product.unit})`);
        
        const newInventory = await prisma.warehouseInventory.create({
          data: {
            warehouseId: warehouse.id,
            productId: product.id,
            quantity: quantity
          }
        });
        
        warehouseInventoryData.push(newInventory);
        console.log(`✅ Created: ${product.name} - ${quantity} ${product.unit} in ${warehouse.name}`);
      }
    }

    console.log('\n🏪 Creating store inventory...');
    
    // Create store inventory for some products
    const storeInventoryData = [];
    
    for (let i = 0; i < Math.min(products.length, 5); i++) { // Only first 5 products in stores
      const product = products[i];
      const store = stores[i % stores.length]; // Distribute across stores
      
      // Check if inventory already exists
      const existingInventory = await prisma.storeInventory.findUnique({
        where: {
          storeId_productId: {
            storeId: store.id,
            productId: product.id
          }
        }
      });

      if (existingInventory) {
        console.log(`✅ Store inventory already exists: ${product.name} in ${store.name}`);
        storeInventoryData.push(existingInventory);
      } else {
        // Generate random stock quantity (smaller than warehouse)
        const quantity = Math.floor(Math.random() * 20) + 5; // 5-25 items
        
        console.log(`🆕 Creating store inventory: ${product.name} in ${store.name} (${quantity} ${product.unit})`);
        
        const newInventory = await prisma.storeInventory.create({
          data: {
            storeId: store.id,
            productId: product.id,
            quantity: quantity
          }
        });
        
        storeInventoryData.push(newInventory);
        console.log(`✅ Created: ${product.name} - ${quantity} ${product.unit} in ${store.name}`);
      }
    }

    console.log('\n🎉 INVENTORY SEEDING COMPLETED!');
    console.log('===============================');
    
    // Get all inventory after seeding
    const allWarehouseInventory = await prisma.warehouseInventory.findMany({
      include: {
        product: true,
        warehouse: true
      }
    });

    const allStoreInventory = await prisma.storeInventory.findMany({
      include: {
        product: true,
        store: true
      }
    });

    console.log(`\n📊 Total warehouse inventory items: ${allWarehouseInventory.length}`);
    console.log(`📊 Total store inventory items: ${allStoreInventory.length}`);

    console.log('\n📋 Warehouse Inventory Summary:');
    allWarehouseInventory.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.product.name} - ${item.quantity} ${item.product.unit} (${item.warehouse.name})`);
    });

    console.log('\n📋 Store Inventory Summary:');
    allStoreInventory.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.product.name} - ${item.quantity} ${item.product.unit} (${item.store.name})`);
    });

    console.log('\n✅ INVENTORY SUCCESSFULLY ADDED TO NEON DATABASE!');
    console.log('\n🎯 NEXT STEPS:');
    console.log('==============');
    console.log('1. ✅ Inventory is now in your Neon database');
    console.log('2. ✅ Inventory management will now show real data');
    console.log('3. ✅ Product pages will show real stock levels');
    console.log('4. 🎯 Test the inventory management functionality');
    console.log('5. 🎯 Verify inventory appears in the admin panel');

    return {
      warehouseInventory: allWarehouseInventory,
      storeInventory: allStoreInventory
    };

  } catch (error) {
    console.error('\n❌ ERROR SEEDING INVENTORY:', error);
    
    if (error.code === 'P1001') {
      console.log('\n💡 DATABASE CONNECTION ERROR:');
      console.log('===============================');
      console.log('❌ Cannot connect to Neon database');
      console.log('🔧 Possible solutions:');
      console.log('   1. Check your DATABASE_URL in .env file');
      console.log('   2. Verify Neon database is running');
      console.log('   3. Check network connection');
      console.log('   4. Verify database credentials');
    } else if (error.code === 'P2002') {
      console.log('\n💡 UNIQUE CONSTRAINT ERROR:');
      console.log('============================');
      console.log('❌ Inventory with same data already exists');
      console.log('✅ This is normal - inventory is already in database');
    } else {
      console.log('\n💡 TROUBLESHOOTING:');
      console.log('===================');
      console.log('1. Check DATABASE_URL environment variable');
      console.log('2. Run: npx prisma db push');
      console.log('3. Run: npx prisma generate');
      console.log('4. Try running this script again');
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedInventory()
    .then((result) => {
      console.log('\n🎊 INVENTORY SEEDING SUCCESSFUL!');
      console.log(`✅ ${result.warehouseInventory.length} warehouse inventory items`);
      console.log(`✅ ${result.storeInventory.length} store inventory items`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 INVENTORY SEEDING FAILED:', error.message);
      process.exit(1);
    });
}

module.exports = { seedInventory };
