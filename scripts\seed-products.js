// Seed products data to Neon database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedProducts() {
  console.log('🛍️ SEEDING PRODUCTS TO NEON DATABASE');
  console.log('===================================\n');

  try {
    // Check if products already exist
    console.log('🔍 Checking existing products...');
    const existingProducts = await prisma.product.findMany();
    console.log(`📊 Found ${existingProducts.length} existing products`);

    if (existingProducts.length > 0) {
      console.log('\n📋 Existing products:');
      existingProducts.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name} - ${product.category} (₹${product.price})`);
      });
      
      console.log('\n⚠️  For safety, adding new products only if they don\'t exist...');
    }

    // Define the products we want to ensure exist
    const productsToCreate = [
      {
        name: 'Chocolate Cake',
        description: 'Rich and moist chocolate cake perfect for celebrations',
        category: 'Cakes',
        price: 899,
        costPrice: 450,
        unit: 'piece',
        lowStockThreshold: 5,
        sku: 'CAKE-CHOC-001',
        imageUrl: '/images/chocolate-cake.jpg',
        metaTitle: 'Delicious Chocolate Cake',
        metaDescription: 'Fresh baked chocolate cake for special occasions',
        isActive: true
      },
      {
        name: 'Red Roses Bouquet',
        description: 'Beautiful bouquet of 12 fresh red roses',
        category: 'Flowers',
        price: 1299,
        costPrice: 650,
        unit: 'bouquet',
        lowStockThreshold: 10,
        sku: 'FLOWER-ROSE-001',
        imageUrl: '/images/red-roses.jpg',
        metaTitle: 'Red Roses Bouquet',
        metaDescription: 'Fresh red roses perfect for expressing love',
        isActive: true
      },
      {
        name: 'Birthday Gift Hamper',
        description: 'Special birthday hamper with cake, flowers, and chocolates',
        category: 'Birthday',
        price: 2499,
        costPrice: 1250,
        unit: 'hamper',
        lowStockThreshold: 3,
        sku: 'GIFT-BDAY-001',
        imageUrl: '/images/birthday-hamper.jpg',
        metaTitle: 'Birthday Gift Hamper',
        metaDescription: 'Complete birthday celebration package',
        isActive: true
      },
      {
        name: 'Anniversary Special Combo',
        description: 'Romantic combo with roses, cake, and personalized card',
        category: 'Anniversary',
        price: 3299,
        costPrice: 1650,
        unit: 'combo',
        lowStockThreshold: 2,
        sku: 'COMBO-ANNI-001',
        imageUrl: '/images/anniversary-combo.jpg',
        metaTitle: 'Anniversary Special Combo',
        metaDescription: 'Perfect anniversary celebration package',
        isActive: true
      },
      {
        name: 'Personalized Photo Frame',
        description: 'Custom photo frame with your special memories',
        category: 'Personalised',
        price: 799,
        costPrice: 400,
        unit: 'piece',
        lowStockThreshold: 8,
        sku: 'GIFT-FRAME-001',
        imageUrl: '/images/photo-frame.jpg',
        metaTitle: 'Personalized Photo Frame',
        metaDescription: 'Custom photo frame for cherished memories',
        isActive: true
      },
      {
        name: 'Money Plant',
        description: 'Lucky money plant in decorative pot',
        category: 'Plants',
        price: 599,
        costPrice: 300,
        unit: 'plant',
        lowStockThreshold: 15,
        sku: 'PLANT-MONEY-001',
        imageUrl: '/images/money-plant.jpg',
        metaTitle: 'Money Plant',
        metaDescription: 'Bring good luck with this beautiful money plant',
        isActive: true
      }
    ];

    console.log('\n🛍️ Creating/Updating products...');
    
    const createdProducts = [];
    
    for (const productData of productsToCreate) {
      // Check if product with same name and category exists
      const existingProduct = await prisma.product.findFirst({
        where: {
          name: productData.name,
          category: productData.category
        }
      });

      if (existingProduct) {
        console.log(`✅ Product already exists: ${productData.name} - ${productData.category}`);
        createdProducts.push(existingProduct);
      } else {
        console.log(`🆕 Creating new product: ${productData.name} - ${productData.category}`);
        const newProduct = await prisma.product.create({
          data: productData
        });
        createdProducts.push(newProduct);
        console.log(`✅ Created: ${newProduct.name} - ₹${newProduct.price} (ID: ${newProduct.id})`);
      }
    }

    console.log('\n🎉 PRODUCT SEEDING COMPLETED!');
    console.log('==============================');
    
    // Get all products after seeding
    const allProducts = await prisma.product.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`\n📊 Total products in database: ${allProducts.length}`);
    console.log('\n📋 All products:');
    allProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Category: ${product.category}`);
      console.log(`      Price: ₹${product.price}`);
      console.log(`      SKU: ${product.sku}`);
      console.log(`      ID: ${product.id}`);
      console.log('');
    });

    console.log('✅ PRODUCTS SUCCESSFULLY ADDED TO NEON DATABASE!');
    console.log('\n🎯 NEXT STEPS:');
    console.log('==============');
    console.log('1. ✅ Products are now in your Neon database');
    console.log('2. ✅ Admin panel will now show real product data');
    console.log('3. ✅ Product CRUD operations will work with real database');
    console.log('4. 🎯 Test the admin panel product management');
    console.log('5. 🎯 Verify products appear in the products page');

    console.log('\n📋 PRODUCT CATEGORIES:');
    console.log('======================');
    const categories = [...new Set(allProducts.map(p => p.category))];
    categories.forEach(category => {
      const count = allProducts.filter(p => p.category === category).length;
      console.log(`${category}: ${count} products`);
    });

    return allProducts;

  } catch (error) {
    console.error('\n❌ ERROR SEEDING PRODUCTS:', error);
    
    if (error.code === 'P1001') {
      console.log('\n💡 DATABASE CONNECTION ERROR:');
      console.log('===============================');
      console.log('❌ Cannot connect to Neon database');
      console.log('🔧 Possible solutions:');
      console.log('   1. Check your DATABASE_URL in .env file');
      console.log('   2. Verify Neon database is running');
      console.log('   3. Check network connection');
      console.log('   4. Verify database credentials');
    } else if (error.code === 'P2002') {
      console.log('\n💡 UNIQUE CONSTRAINT ERROR:');
      console.log('============================');
      console.log('❌ Product with same data already exists');
      console.log('✅ This is normal - products are already in database');
    } else {
      console.log('\n💡 TROUBLESHOOTING:');
      console.log('===================');
      console.log('1. Check DATABASE_URL environment variable');
      console.log('2. Run: npx prisma db push');
      console.log('3. Run: npx prisma generate');
      console.log('4. Try running this script again');
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedProducts()
    .then((products) => {
      console.log('\n🎊 PRODUCT SEEDING SUCCESSFUL!');
      console.log(`✅ ${products.length} products are now in your Neon database`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 PRODUCT SEEDING FAILED:', error.message);
      process.exit(1);
    });
}

module.exports = { seedProducts };
