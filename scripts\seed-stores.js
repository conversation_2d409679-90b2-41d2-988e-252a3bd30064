// Seed stores data to Neon database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedStores() {
  console.log('🏪 SEEDING STORES TO NEON DATABASE');
  console.log('==================================\n');

  try {
    // Check if stores already exist
    console.log('🔍 Checking existing stores...');
    const existingStores = await prisma.store.findMany();
    console.log(`📊 Found ${existingStores.length} existing stores`);

    if (existingStores.length > 0) {
      console.log('\n📋 Existing stores:');
      existingStores.forEach((store, index) => {
        console.log(`   ${index + 1}. ${store.name} - ${store.location} (ID: ${store.id})`);
      });
      
      console.log('\n❓ Stores already exist. Do you want to:');
      console.log('   1. Keep existing stores');
      console.log('   2. Add new stores (if different)');
      console.log('   3. Replace all stores');
      console.log('\n⚠️  For safety, keeping existing stores and adding missing ones...');
    }

    // Define the stores we want to ensure exist
    const storesToCreate = [
      {
        name: 'Main Store',
        location: 'Bhubaneswar'
      },
      {
        name: 'Branch Store',
        location: 'Cuttack'
      },
      {
        name: 'Express Store',
        location: 'Puri'
      }
    ];

    console.log('\n🏪 Creating/Updating stores...');
    
    const createdStores = [];
    
    for (const storeData of storesToCreate) {
      // Check if store with same name and location exists
      const existingStore = await prisma.store.findFirst({
        where: {
          name: storeData.name,
          location: storeData.location
        }
      });

      if (existingStore) {
        console.log(`✅ Store already exists: ${storeData.name} - ${storeData.location}`);
        createdStores.push(existingStore);
      } else {
        console.log(`🆕 Creating new store: ${storeData.name} - ${storeData.location}`);
        const newStore = await prisma.store.create({
          data: storeData
        });
        createdStores.push(newStore);
        console.log(`✅ Created: ${newStore.name} - ${newStore.location} (ID: ${newStore.id})`);
      }
    }

    console.log('\n🎉 STORE SEEDING COMPLETED!');
    console.log('===========================');
    
    // Get all stores after seeding
    const allStores = await prisma.store.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`\n📊 Total stores in database: ${allStores.length}`);
    console.log('\n📋 All stores:');
    allStores.forEach((store, index) => {
      console.log(`   ${index + 1}. ${store.name} - ${store.location}`);
      console.log(`      ID: ${store.id}`);
      console.log(`      Created: ${store.createdAt.toISOString()}`);
      console.log('');
    });

    console.log('✅ STORES SUCCESSFULLY ADDED TO NEON DATABASE!');
    console.log('\n🎯 NEXT STEPS:');
    console.log('==============');
    console.log('1. ✅ Stores are now in your Neon database');
    console.log('2. ✅ Admin panel will now show real store data');
    console.log('3. ✅ Store assignment will work with real database');
    console.log('4. 🎯 Test the admin panel store assignment');
    console.log('5. 🎯 Verify stores appear in the dropdown');

    console.log('\n📋 STORE IDS FOR REFERENCE:');
    console.log('============================');
    allStores.forEach(store => {
      console.log(`${store.name} (${store.location}): ${store.id}`);
    });

    return allStores;

  } catch (error) {
    console.error('\n❌ ERROR SEEDING STORES:', error);
    
    if (error.code === 'P1001') {
      console.log('\n💡 DATABASE CONNECTION ERROR:');
      console.log('===============================');
      console.log('❌ Cannot connect to Neon database');
      console.log('🔧 Possible solutions:');
      console.log('   1. Check your DATABASE_URL in .env file');
      console.log('   2. Verify Neon database is running');
      console.log('   3. Check network connection');
      console.log('   4. Verify database credentials');
    } else if (error.code === 'P2002') {
      console.log('\n💡 UNIQUE CONSTRAINT ERROR:');
      console.log('============================');
      console.log('❌ Store with same data already exists');
      console.log('✅ This is normal - stores are already in database');
    } else {
      console.log('\n💡 TROUBLESHOOTING:');
      console.log('===================');
      console.log('1. Check DATABASE_URL environment variable');
      console.log('2. Run: npx prisma db push');
      console.log('3. Run: npx prisma generate');
      console.log('4. Try running this script again');
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedStores()
    .then((stores) => {
      console.log('\n🎊 STORE SEEDING SUCCESSFUL!');
      console.log(`✅ ${stores.length} stores are now in your Neon database`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 STORE SEEDING FAILED:', error.message);
      process.exit(1);
    });
}

module.exports = { seedStores };
