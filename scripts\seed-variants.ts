import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedDeliveryLocations() {
  console.log('🚚 Seeding delivery locations...');
  
  const bhubaneswarPincodes = [
    { pincode: '751001', area: 'Bhubaneswar GPO', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751002', area: 'Sachivalaya Marg', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751003', area: 'Kharavel Nagar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751004', area: 'Bapuji Nagar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751005', area: 'Rajmahal Square', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751006', area: 'Jaydev Vihar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751007', area: 'Nayapalli', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751008', area: 'Saheed Nagar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751009', area: 'Old Town', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751010', area: 'Patia', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751011', area: 'Chandrasekharpur', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751012', area: 'Rasulgarh', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751013', area: 'Khandagiri', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751014', area: 'Udayagiri', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751015', area: 'Kalinga Nagar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751016', area: 'Mancheswar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751017', area: 'Bharatpur', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751018', area: 'Niladri Vihar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751019', area: 'Sundarpada', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751020', area: 'Baramunda', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751021', area: 'Infocity', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751022', area: 'Tamando', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751023', area: 'Palasuni', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751024', area: 'Laxmisagar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751025', area: 'Sailashree Vihar', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751026', area: 'Jagamara', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751027', area: 'Damana', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 0 },
    { pincode: '751028', area: 'Jatni', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 50 },
    { pincode: '751029', area: 'Khordha', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 50 },
    { pincode: '751030', area: 'Balianta', city: 'Bhubaneswar', state: 'Odisha', deliveryFee: 50 },
  ];

  for (const location of bhubaneswarPincodes) {
    try {
      await prisma.deliveryLocation.upsert({
        where: { pincode: location.pincode },
        update: location,
        create: location,
      });
    } catch (error) {
      console.error(`Error creating location for pincode ${location.pincode}:`, error);
    }
  }

  console.log('✅ Delivery locations seeded successfully');
}

async function seedProductWithVariants() {
  console.log('🎂 Seeding product with variants...');

  // Get or create a warehouse
  let warehouse = await prisma.warehouse.findFirst();
  if (!warehouse) {
    warehouse = await prisma.warehouse.create({
      data: {
        name: 'Main Warehouse',
        location: 'Bhubaneswar Central'
      }
    });
  }

  // Create a test product with variants
  const product = await prisma.product.create({
    data: {
      name: 'Chocolate Truffle Cake',
      description: 'Rich and decadent chocolate truffle cake made with premium Belgian chocolate. Perfect for celebrations and special occasions.',
      category: 'Cakes',
      price: 595, // Base price (will be overridden by default variant)
      costPrice: 300,
      unit: 'piece',
      sku: 'CAKE-CHOC-001',
      imageUrl: '/images/products/chocolate-truffle-cake.jpg',
      metaTitle: 'Chocolate Truffle Cake - Premium Belgian Chocolate',
      metaDescription: 'Order delicious chocolate truffle cake online. Made with premium Belgian chocolate. Available in multiple sizes.',
      isActive: true,
    }
  });

  // Create variants for different weights
  const variants = [
    { weight: '0.5 Kg', price: 595, costPrice: 300, isDefault: true, sortOrder: 1 },
    { weight: '1 Kg', price: 1045, costPrice: 500, isDefault: false, sortOrder: 2 },
    { weight: '1.5 Kg', price: 1545, costPrice: 750, isDefault: false, sortOrder: 3 },
    { weight: '2 Kg', price: 2045, costPrice: 1000, isDefault: false, sortOrder: 4 },
  ];

  for (const variant of variants) {
    await prisma.productVariant.create({
      data: {
        productId: product.id,
        weight: variant.weight,
        price: variant.price,
        costPrice: variant.costPrice,
        sku: `${product.sku}-${variant.weight.replace(/\s+/g, '')}`,
        isDefault: variant.isDefault,
        isActive: true,
        sortOrder: variant.sortOrder,
      }
    });
  }

  // Create warehouse inventory
  await prisma.warehouseInventory.create({
    data: {
      warehouseId: warehouse.id,
      productId: product.id,
      quantity: 50,
    }
  });

  console.log('✅ Product with variants seeded successfully');
  console.log(`Product ID: ${product.id}`);
}

async function main() {
  try {
    await seedDeliveryLocations();
    await seedProductWithVariants();
    console.log('🎉 All seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
