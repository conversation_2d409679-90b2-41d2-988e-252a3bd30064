const nodemailer = require('nodemailer');
require('dotenv').config();

// Generate 6-digit OTP
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

async function sendRealOTPEmail(email, otp) {
  try {
    console.log(`📧 Sending real OTP email to: ${email}`);
    console.log(`🔢 OTP: ${otp}`);

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Send email
    const result = await transporter.sendMail({
      from: `"Mispri" <${process.env.SMTP_USER}>`,
      to: email,
      subject: 'Your Password Reset Code - Mispri',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
          <div style="background-color: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #ff7700; font-size: 32px; margin-bottom: 10px;">🌸 Mispri</h1>
              <h2 style="color: #333; margin-bottom: 20px;">Password Reset Code</h2>
            </div>
            
            <p>Hello,</p>
            
            <p>We received a request to reset your password for your Mispri account. Use the verification code below to proceed:</p>
            
            <div style="background-color: #f8f9fa; border: 2px dashed #ff7700; border-radius: 10px; padding: 30px; text-align: center; margin: 30px 0;">
              <div style="font-size: 14px; color: #666; margin-bottom: 10px;">Your 6-Digit Verification Code</div>
              <div style="font-size: 36px; font-weight: bold; color: #ff7700; letter-spacing: 8px; font-family: 'Courier New', monospace; margin: 10px 0;">${otp}</div>
              <div style="font-size: 12px; color: #999; margin-top: 10px;">Enter this code on the password reset page</div>
            </div>
            
            <div style="background-color: #e3f2fd; border: 1px solid #bbdefb; color: #0d47a1; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h4 style="margin-top: 0; color: #0d47a1;">📝 How to Reset Your Password:</h4>
              <ol style="margin-bottom: 0;">
                <li>Go to the password reset page</li>
                <li>Enter your email address: <strong>${email}</strong></li>
                <li>Enter the 6-digit code: <strong>${otp}</strong></li>
                <li>Create your new password</li>
                <li>Confirm your new password</li>
              </ol>
            </div>
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <strong>⚠️ Important Security Information:</strong>
              <ul style="margin: 10px 0 0 0;">
                <li>This code will expire in <strong>10 minutes</strong></li>
                <li>Don't share this code with anyone</li>
                <li>If you didn't request this, please ignore this email</li>
                <li>Only use this code on the official Mispri website</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 14px; margin: 0;">
                Best regards,<br>The Mispri Team
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
Hello,

We received a request to reset your password for your Mispri account.

Your 6-Digit Verification Code: ${otp}

How to Reset Your Password:
1. Go to the password reset page
2. Enter your email address: ${email}
3. Enter the 6-digit code: ${otp}
4. Create your new password
5. Confirm your new password

⚠️ Important Security Information:
- This code will expire in 10 minutes
- Don't share this code with anyone
- If you didn't request this, please ignore this email
- Only use this code on the official Mispri website

Best regards,
The Mispri Team
      `.trim()
    });

    console.log('✅ Email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    return { success: true, otp: otp, messageId: result.messageId };

  } catch (error) {
    console.error('❌ Email sending failed:', error);
    return { success: false, error: error.message };
  }
}

// Export for use in other scripts
if (require.main === module) {
  // If run directly, send a test email
  const testEmail = '<EMAIL>';
  const testOTP = generateOTP();
  
  console.log('🧪 SENDING REAL OTP EMAIL');
  console.log('=========================\n');
  
  sendRealOTPEmail(testEmail, testOTP)
    .then(result => {
      if (result.success) {
        console.log('\n🎉 SUCCESS!');
        console.log(`📧 Email sent to: ${testEmail}`);
        console.log(`🔢 OTP: ${result.otp}`);
        console.log(`📧 Message ID: ${result.messageId}`);
        console.log('\n📝 Next steps:');
        console.log('1. Check your Gmail inbox');
        console.log('2. Go to: http://localhost:3001/forgot-password');
        console.log(`3. Enter email: ${testEmail}`);
        console.log(`4. Enter OTP: ${result.otp}`);
        console.log('5. Complete password reset');
      } else {
        console.log('\n❌ FAILED!');
        console.log(`Error: ${result.error}`);
      }
    })
    .catch(error => {
      console.error('\n❌ Script failed:', error);
    });
}

module.exports = { sendRealOTPEmail, generateOTP };
