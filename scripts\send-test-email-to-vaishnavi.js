const nodemailer = require('nodemailer');
require('dotenv').config();

async function sendTestEmailToV<PERSON><PERSON><PERSON>() {
  console.log('📧 Sending direct test <NAME_EMAIL>\n');

  // Create transporter
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  try {
    console.log('📧 Sending test email...');
    const result = await transporter.sendMail({
      from: `"Mispri" <${process.env.SMTP_USER}>`,
      to: '<EMAIL>',
      subject: '🧪 Test Email - Mispri Forgot Password System',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
          <div style="background-color: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #ff7700; font-size: 32px; margin-bottom: 10px;">🌸 Mispri</h1>
              <h2 style="color: #333; margin-bottom: 20px;">Email System Test</h2>
            </div>
            
            <div style="background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="margin-top: 0; color: #155724;">🎉 Success!</h3>
              <p style="margin-bottom: 0;">If you receive this email, the Mispri email system is working correctly!</p>
            </div>
            
            <div style="margin-bottom: 20px;">
              <h4 style="color: #333;">📧 Email Details:</h4>
              <ul style="color: #666;">
                <li><strong>From:</strong> Mispri <<EMAIL>></li>
                <li><strong>To:</strong> <EMAIL></li>
                <li><strong>Time:</strong> ${new Date().toLocaleString()}</li>
                <li><strong>Purpose:</strong> Testing forgot password email delivery</li>
              </ul>
            </div>
            
            <div style="background-color: #e3f2fd; border: 1px solid #bbdefb; color: #0d47a1; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h4 style="margin-top: 0; color: #0d47a1;">🔧 What This Means:</h4>
              <ul style="margin-bottom: 0;">
                <li>✅ Gmail SMTP is working correctly</li>
                <li>✅ Emails can be <NAME_EMAIL></li>
                <li>✅ Emails can be <NAME_EMAIL></li>
                <li>✅ Forgot password functionality should work</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="http://localhost:3001/forgot-password" 
                 style="display: inline-block; background-color: #ff7700; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                Test Forgot Password
              </a>
            </div>
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h4 style="margin-top: 0; color: #856404;">📝 Next Steps:</h4>
              <ol style="margin-bottom: 0;">
                <li>Go to: http://localhost:3001/forgot-password</li>
                <li>Enter: <EMAIL></li>
                <li>Click "Send reset link"</li>
                <li>Check this Gmail inbox for the reset email</li>
                <li>Click the reset link to set a new password</li>
              </ol>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 14px; margin: 0;">
                This is a test email from the Mispri application<br>
                Sent: ${new Date().toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
🌸 Mispri - Email System Test

🎉 Success! If you receive this email, the Mispri email system is working correctly!

📧 Email Details:
- From: Mispri <<EMAIL>>
- To: <EMAIL>
- Time: ${new Date().toLocaleString()}
- Purpose: Testing forgot password email delivery

🔧 What This Means:
✅ Gmail SMTP is working correctly
✅ Emails can be <NAME_EMAIL>
✅ Emails can be <NAME_EMAIL>
✅ Forgot password functionality should work

📝 Next Steps:
1. Go to: http://localhost:3001/forgot-password
2. Enter: <EMAIL>
3. Click "Send reset link"
4. Check this Gmail inbox for the reset email
5. Click the reset link to set a new password

This is a test email from the Mispri application
Sent: ${new Date().toLocaleString()}
      `.trim()
    });

    console.log('✅ Test email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    console.log('📬 Check Gmail inbox: <EMAIL>');
    console.log('\n🎉 If you receive this email, forgot password will work too!');

  } catch (error) {
    console.log('❌ Email test failed:');
    console.error(error.message);
  }
}

sendTestEmailToVaishnavi();
