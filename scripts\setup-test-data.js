const { default: fetch } = require('node-fetch');

async function setupTestData() {
  console.log('🔧 SETTING UP TEST DATA');
  console.log('========================\n');

  const adminURL = 'http://localhost:3002';

  try {
    // Create test products
    console.log('📦 Creating test products...');
    
    const testProducts = [
      {
        name: 'Chocolate Cake',
        description: 'Delicious chocolate cake with rich frosting',
        category: 'Cakes',
        price: 599,
        costPrice: 300,
        unit: 'piece',
        imageUrl: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=800&h=600&fit=crop',
        sku: 'CAKE-CHOC-001',
        metaTitle: 'Chocolate Cake - Rich & Delicious',
        metaDescription: 'Premium chocolate cake perfect for celebrations',
        isActive: true
      },
      {
        name: 'Red Rose Bouquet',
        description: 'Beautiful bouquet of fresh red roses',
        category: 'Flowers',
        price: 899,
        costPrice: 450,
        unit: 'bouquet',
        imageUrl: 'https://images.unsplash.com/photo-1518895949257-7621c3c786d7?w=800&h=600&fit=crop',
        sku: 'FLOWER-ROSE-001',
        metaTitle: 'Red Rose Bouquet - Fresh & Beautiful',
        metaDescription: 'Premium red roses perfect for special occasions',
        isActive: true
      },
      {
        name: 'Birthday Gift Box',
        description: 'Special gift box with assorted treats',
        category: 'Gifts',
        price: 1299,
        costPrice: 650,
        unit: 'box',
        imageUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&h=600&fit=crop',
        sku: 'GIFT-BIRTH-001',
        metaTitle: 'Birthday Gift Box - Special Treats',
        metaDescription: 'Perfect birthday gift box with assorted goodies',
        isActive: true
      }
    ];

    for (const product of testProducts) {
      try {
        const response = await fetch(`${adminURL}/api/products`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(product),
        });

        if (response.ok) {
          const createdProduct = await response.json();
          console.log(`✅ Created product: ${createdProduct.name}`);
        } else {
          const error = await response.text();
          console.log(`❌ Failed to create ${product.name}: ${error}`);
        }
      } catch (error) {
        console.log(`❌ Error creating ${product.name}: ${error.message}`);
      }
    }

    // Create test stores
    console.log('\n🏪 Creating test stores...');
    
    const testStores = [
      {
        name: 'Mispri Main Store',
        location: 'Bhubaneswar Central'
      },
      {
        name: 'Mispri Express',
        location: 'Patia, Bhubaneswar'
      }
    ];

    for (const store of testStores) {
      try {
        const response = await fetch(`${adminURL}/api/stores`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(store),
        });

        if (response.ok) {
          const createdStore = await response.json();
          console.log(`✅ Created store: ${createdStore.name}`);
        } else {
          const error = await response.text();
          console.log(`❌ Failed to create ${store.name}: ${error}`);
        }
      } catch (error) {
        console.log(`❌ Error creating ${store.name}: ${error.message}`);
      }
    }

    console.log('\n🎉 TEST DATA SETUP COMPLETE!');
    console.log('============================');
    console.log('✅ Products created');
    console.log('✅ Stores created');
    console.log('✅ Ready for testing');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
  }
}

setupTestData();
