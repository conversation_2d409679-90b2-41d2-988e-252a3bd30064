const nodemailer = require('nodemailer');
require('dotenv').config();

async function simpleEmailTest() {
  console.log('🧪 Simple Email <NAME_EMAIL>\n');

  // Check environment variables
  console.log('📧 Environment Variables:');
  console.log(`   SMTP_HOST: ${process.env.SMTP_HOST}`);
  console.log(`   SMTP_PORT: ${process.env.SMTP_PORT}`);
  console.log(`   SMTP_USER: ${process.env.SMTP_USER}`);
  console.log(`   SMTP_PASS: ${process.env.SMTP_PASS ? '***configured***' : 'NOT SET'}`);
  console.log('');

  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
    console.log('❌ Missing SMTP configuration in .env file');
    return;
  }

  // Create transporter
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  try {
    console.log('🔍 Testing SMTP connection...');
    await transporter.verify();
    console.log('✅ SMTP connection successful!\n');

    console.log('📧 Sending test email...');
    const result = await transporter.sendMail({
      from: `"Mispri Test" <${process.env.SMTP_USER}>`,
      to: '<EMAIL>',
      subject: '🧪 Direct Email Test - Mispri',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px;">
          <h2 style="color: #ff7700;">🌸 Mispri Email Test</h2>
          <p>This is a direct email test to verify Gmail SMTP is working.</p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
          <p><strong>From:</strong> ${process.env.SMTP_USER}</p>
          <p><strong>To:</strong> <EMAIL></p>
          <div style="background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>✅ If you receive this email, Gmail SMTP is working correctly!</strong></p>
          </div>
          <p>Next step: Test the forgot password functionality on the website.</p>
        </div>
      `,
      text: `
Mispri Email Test

This is a direct email test to verify Gmail SMTP is working.

Time: ${new Date().toLocaleString()}
From: ${process.env.SMTP_USER}
To: <EMAIL>

✅ If you receive this email, Gmail SMTP is working correctly!

Next step: Test the forgot password functionality on the website.
      `.trim()
    });

    console.log('✅ Email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    console.log('📬 Check Gmail inbox: <EMAIL>');
    console.log('\n🎉 Gmail SMTP is working! Now test website forgot password.');

  } catch (error) {
    console.log('❌ Email test failed:');
    console.error(error.message);
    
    if (error.message.includes('Invalid login')) {
      console.log('\n🔧 Fix: Check your Gmail App Password');
      console.log('1. Make sure 2FA is enabled');
      console.log('2. Generate new App Password');
      console.log('3. Update SMTP_PASS in .env file');
    }
  }
}

simpleEmailTest();
