const { default: fetch } = require('node-fetch');

async function simpleOTPTest() {
  console.log('🧪 Simple OTP <NAME_EMAIL>\n');

  try {
    console.log('📧 Testing forgot password API...');
    
    const response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    console.log(`📊 Response Status: ${response.status}`);
    
    const data = await response.json();
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ SUCCESS! OTP system is working!');
      console.log('📧 Check Gmail inbox: <EMAIL>');
      console.log('🔍 Look for: "Your Password Reset Code - Mispri"');
      console.log('🔢 Contains: 6-digit verification code');
    } else {
      console.log('\n❌ FAILED! OTP request failed');
      console.log('🔧 Check admin panel logs for details');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

simpleOTPTest();
