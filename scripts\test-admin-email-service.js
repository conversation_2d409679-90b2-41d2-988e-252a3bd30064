const { emailService } = require('../src/lib/services/nodemailer-service.ts');

async function testAdminEmailService() {
  console.log('🧪 TESTING ADMIN EMAIL SERVICE (Used by Website)');
  console.log('================================================\n');

  try {
    console.log('📧 Testing the exact email service used by the website...');
    
    const email = '<EMAIL>';
    const name = 'Vaishnavi Bhardwaj';
    const otp = '999888'; // Test OTP
    
    console.log(`📧 Sending OTP email to: ${email}`);
    console.log(`🔢 OTP: ${otp}`);
    console.log(`👤 Name: ${name}`);
    
    const result = await emailService.sendPasswordResetOTP(email, name, otp);
    
    if (result) {
      console.log('\n✅ SUCCESS! Admin email service is working!');
      console.log(`📧 Email sent to: ${email}`);
      console.log(`🔢 OTP: ${otp}`);
      console.log('\n📬 Check your Gmail inbox for the email!');
      console.log('📧 Subject: "Your Password Reset Code - Mispri"');
      console.log('📤 From: Mispri <<EMAIL>>');
      
      console.log('\n🎯 This proves the website email system is working!');
      console.log('If you\'re not receiving emails from the website form,');
      console.log('there might be a different issue.');
      
    } else {
      console.log('\n❌ FAILED! Admin email service is not working');
      console.log('This explains why the website form doesn\'t send emails');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('🔧 This explains why the website form emails fail');
  }
}

testAdminEmailService();
