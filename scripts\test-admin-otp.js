const { default: fetch } = require('node-fetch');

async function testAdminOTP() {
  console.log('🧪 Testing Admin Panel OTP API directly\n');

  try {
    console.log('📧 Testing admin panel forgot password API...');
    console.log('🌐 URL: http://localhost:3000/api/auth/forgot-password');
    
    const response = await fetch('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    console.log(`📊 Response Status: ${response.status}`);
    
    const data = await response.json();
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ SUCCESS! Admin panel OTP API is working!');
      console.log('📧 OTP should be sent to: <EMAIL>');
    } else {
      console.log('\n❌ FAILED! Admin panel API error');
      console.log('🔧 Error details above');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('🔧 Make sure admin panel is running on port 3000');
  }
}

testAdminOTP();
