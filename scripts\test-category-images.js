const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testCategoryImages() {
  console.log('🧪 Testing category images and data...');
  
  try {
    // Test 1: Check if category metadata exists
    console.log('\n📋 Test 1: Checking category metadata...');
    const categoryMetadata = await prisma.systemSetting.findMany({
      where: {
        category: 'category_metadata'
      }
    });
    
    console.log(`Found ${categoryMetadata.length} category metadata entries`);
    
    // Test 2: Check each category's data
    console.log('\n📂 Test 2: Category details...');
    for (const meta of categoryMetadata) {
      try {
        const data = JSON.parse(meta.value);
        console.log(`\n✅ Category: ${meta.key}`);
        console.log(`   📝 Description: ${data.description || 'No description'}`);
        console.log(`   🖼️ Image URL: ${data.imageUrl || 'No image'}`);
        console.log(`   🔗 Slug: ${data.slug || 'No slug'}`);
        console.log(`   📊 Display Order: ${data.displayOrder || 'No order'}`);
        console.log(`   ✅ Active: ${data.isActive !== false ? 'Yes' : 'No'}`);
      } catch (error) {
        console.log(`❌ Error parsing data for ${meta.key}:`, error.message);
      }
    }
    
    // Test 3: Check products in categories
    console.log('\n📦 Test 3: Products in categories...');
    const productCategories = await prisma.product.groupBy({
      by: ['category'],
      _count: {
        id: true
      },
      orderBy: {
        category: 'asc'
      }
    });
    
    console.log(`Found products in ${productCategories.length} categories:`);
    for (const cat of productCategories) {
      console.log(`   📂 ${cat.category}: ${cat._count.id} products`);
    }
    
    // Test 4: Simulate API response
    console.log('\n🌐 Test 4: Simulating API response...');
    
    // Get category metadata
    const metadataMap = {};
    for (const meta of categoryMetadata) {
      try {
        metadataMap[meta.key] = JSON.parse(meta.value);
      } catch (error) {
        console.error(`Error parsing metadata for ${meta.key}:`, error);
      }
    }
    
    // Combine with product data
    const apiResponse = productCategories.map(cat => {
      const metadata = metadataMap[cat.category] || {};
      return {
        id: cat.category,
        name: cat.category,
        description: metadata.description || `Products in ${cat.category} category`,
        imageUrl: metadata.imageUrl || `/categories/${cat.category.toLowerCase()}.svg`,
        productCount: cat._count.id,
        isActive: metadata.isActive !== undefined ? metadata.isActive : true,
        slug: metadata.slug || cat.category.toLowerCase().replace(/\s+/g, '-'),
        displayOrder: metadata.displayOrder || 0,
        createdAt: metadata.createdAt || new Date().toISOString(),
        updatedAt: metadata.updatedAt || new Date().toISOString(),
      };
    });
    
    console.log('\n📊 API Response Preview:');
    apiResponse.forEach(cat => {
      console.log(`\n🏷️ ${cat.name}`);
      console.log(`   🆔 ID: ${cat.id}`);
      console.log(`   📝 Description: ${cat.description}`);
      console.log(`   🖼️ Image: ${cat.imageUrl}`);
      console.log(`   📦 Products: ${cat.productCount}`);
      console.log(`   🔗 Slug: ${cat.slug}`);
      console.log(`   📊 Order: ${cat.displayOrder}`);
      console.log(`   ✅ Active: ${cat.isActive}`);
    });
    
    // Test 5: Check for missing images
    console.log('\n🔍 Test 5: Categories without images...');
    const categoriesWithoutImages = apiResponse.filter(cat => 
      !cat.imageUrl || cat.imageUrl.includes('.svg')
    );
    
    if (categoriesWithoutImages.length > 0) {
      console.log(`⚠️ Found ${categoriesWithoutImages.length} categories without proper images:`);
      categoriesWithoutImages.forEach(cat => {
        console.log(`   📂 ${cat.name}: ${cat.imageUrl}`);
      });
    } else {
      console.log('✅ All categories have proper images!');
    }
    
    console.log('\n🎉 Category testing completed!');
    console.log('\n📋 Summary:');
    console.log(`   📂 Total categories with metadata: ${categoryMetadata.length}`);
    console.log(`   📦 Categories with products: ${productCategories.length}`);
    console.log(`   🖼️ Categories with images: ${apiResponse.length - categoriesWithoutImages.length}`);
    console.log(`   ⚠️ Categories needing images: ${categoriesWithoutImages.length}`);
    
  } catch (error) {
    console.error('❌ Error testing categories:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testCategoryImages();
