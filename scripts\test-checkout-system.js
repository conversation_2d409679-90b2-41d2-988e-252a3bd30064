const { default: fetch } = require('node-fetch');

async function testCheckoutSystem() {
  console.log('🛒 TESTING CHECKOUT SYSTEM');
  console.log('==========================\n');

  try {
    // Test data
    const testOrderData = {
      userId: 'test-user-id', // We'll need a real user ID
      items: [
        {
          productId: 'test-product-1',
          quantity: 2,
          unitPrice: 299.99
        },
        {
          productId: 'test-product-2', 
          quantity: 1,
          unitPrice: 199.99
        }
      ],
      shippingAddress: {
        street: '123 Test Street',
        city: 'Bhubaneswar',
        state: 'Odisha',
        pincode: '751001',
        country: 'India',
        firstName: 'Test',
        lastName: 'User',
        phone: '+91 9876543210'
      },
      paymentMethod: 'COD',
      totalAmount: 799.97,
      subtotal: 699.97,
      shipping: 100
    };

    console.log('📦 Test Order Data:', JSON.stringify(testOrderData, null, 2));

    // Test 1: Check if admin panel is running
    console.log('\n🔍 Step 1: Testing admin panel connection...');
    try {
      const adminResponse = await fetch('http://localhost:3002/api/orders', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log(`📡 Admin Panel Status: ${adminResponse.status}`);
      if (adminResponse.ok) {
        console.log('✅ Admin panel is running and accessible');
      } else {
        console.log('❌ Admin panel returned error:', adminResponse.status);
      }
    } catch (adminError) {
      console.log('❌ Admin panel connection failed:', adminError.message);
      console.log('🔧 Make sure admin panel is running on port 3002');
      return;
    }

    // Test 2: Test website API
    console.log('\n🌐 Step 2: Testing website customer-orders API...');
    const websiteResponse = await fetch('http://localhost:3001/api/customer-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testOrderData),
    });

    console.log(`📡 Website API Status: ${websiteResponse.status}`);
    
    if (websiteResponse.ok) {
      const order = await websiteResponse.json();
      console.log('✅ Order created successfully!');
      console.log('📋 Order Details:', JSON.stringify(order, null, 2));
    } else {
      const errorText = await websiteResponse.text();
      console.log('❌ Website API Error:', errorText);
      
      try {
        const errorData = JSON.parse(errorText);
        console.log('📋 Error Details:', errorData);
      } catch (e) {
        console.log('📋 Raw Error:', errorText);
      }
    }

    // Test 3: Check if we need to create a test user
    console.log('\n👤 Step 3: Checking user requirements...');
    console.log('💡 For checkout to work, you need:');
    console.log('   1. A logged-in user with valid ID');
    console.log('   2. Items in the cart');
    console.log('   3. Admin panel running on port 3002');
    console.log('   4. Website running on port 3001');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

testCheckoutSystem();
