// Test database connection after reactivation
const { PrismaClient } = require('@prisma/client');

async function testDatabaseConnection() {
  console.log('🔍 TESTING DATABASE CONNECTION AFTER REACTIVATION');
  console.log('=================================================\n');

  const prisma = new PrismaClient();

  try {
    console.log('📡 Attempting to connect to NeonDB...');
    
    // Test basic connection
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful!');

    // Test orders table
    console.log('\n📋 Testing orders table...');
    const orderCount = await prisma.order.count();
    console.log(`✅ Orders table accessible: ${orderCount} orders found`);

    // Test stores table  
    console.log('\n🏪 Testing stores table...');
    const storeCount = await prisma.store.count();
    console.log(`✅ Stores table accessible: ${storeCount} stores found`);

    // Test customers table
    console.log('\n👥 Testing customers table...');
    const customerCount = await prisma.customer.count();
    console.log(`✅ Customers table accessible: ${customerCount} customers found`);

    console.log('\n🎉 DATABASE IS FULLY OPERATIONAL!');
    console.log('==================================');
    console.log('✅ Connection: WORKING');
    console.log('✅ Orders table: ACCESSIBLE');
    console.log('✅ Stores table: ACCESSIBLE');
    console.log('✅ Customers table: ACCESSIBLE');
    
    console.log('\n📊 WHAT THIS MEANS:');
    console.log('===================');
    console.log('✅ Admin panel will now use REAL data');
    console.log('✅ Order management will persist changes');
    console.log('✅ Store assignments will be saved');
    console.log('✅ Payment status updates will be tracked');
    console.log('✅ All functionality is ready for production');

    return { success: true, orders: orderCount, stores: storeCount, customers: customerCount };

  } catch (error) {
    console.error('\n❌ Database connection failed:', error.message);
    
    if (error.message.includes("Can't reach database server")) {
      console.log('\n💡 DATABASE STILL SLEEPING:');
      console.log('============================');
      console.log('⏳ The database is still waking up');
      console.log('💡 Wait 1-2 more minutes');
      console.log('💡 Try running this test again');
      console.log('💡 Or refresh the admin panel page');
    } else if (error.message.includes('timeout')) {
      console.log('\n💡 CONNECTION TIMEOUT:');
      console.log('======================');
      console.log('⏳ Database is taking longer to wake up');
      console.log('💡 This is normal for paused databases');
      console.log('💡 Try again in a few minutes');
    } else {
      console.log('\n💡 OTHER DATABASE ISSUE:');
      console.log('========================');
      console.log('🔍 Check your DATABASE_URL in .env');
      console.log('🔍 Verify NeonDB project is active');
      console.log('🔍 Check network connectivity');
    }

    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabaseConnection().then(result => {
  if (result.success) {
    console.log('\n🚀 DATABASE IS READY FOR ORDER MANAGEMENT!');
    console.log(`   📋 Orders: ${result.orders}`);
    console.log(`   🏪 Stores: ${result.stores}`);
    console.log(`   👥 Customers: ${result.customers}`);
    console.log('\n🎊 REFRESH THE ADMIN PANEL TO USE REAL DATA! 🎉');
  } else {
    console.log('\n⏳ Database not ready yet. Try again in a few minutes.');
  }
}).catch(error => {
  console.error('\n💥 Test crashed:', error.message);
});
