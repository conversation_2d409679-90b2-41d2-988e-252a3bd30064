const { default: fetch } = require('node-fetch');

async function testDirectOrder() {
  console.log('🛒 TESTING DIRECT ORDER PLACEMENT');
  console.log('==================================\n');

  const baseURL = 'http://localhost:3001';

  try {
    // Step 1: Test customer registration
    console.log('👤 Step 1: Testing customer registration...');
    const customerData = {
      name: 'Test Customer',
      email: `test${Date.now()}@example.com`,
      password: 'password123',
      phone: '+91 9876543210'
    };

    console.log(`📧 Registering: ${customerData.email}`);

    const registerResponse = await fetch(`${baseURL}/api/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(customerData),
    });

    console.log(`📡 Registration response status: ${registerResponse.status}`);

    if (!registerResponse.ok) {
      const error = await registerResponse.text();
      console.log('❌ Registration failed:', error);
      
      // Let's try to get more details
      try {
        const errorJson = JSON.parse(error);
        console.log('   Error details:', errorJson);
      } catch (e) {
        console.log('   Raw error response:', error);
      }
      return;
    }

    const userData = await registerResponse.json();
    console.log('✅ Customer registered successfully');
    console.log(`   User ID: ${userData.user.id}`);
    console.log(`   Email: ${userData.user.email}`);
    
    const userId = userData.user.id;

    // Step 2: Test direct order creation (simulating cart checkout)
    console.log('\n💳 Step 2: Testing direct order creation...');
    
    // Mock order data (as if coming from a cart)
    const orderData = {
      userId: userId,
      items: [
        {
          id: 'mock-product-1',
          productId: 'mock-product-1',
          quantity: 2,
          price: 899,
          unitPrice: 899
        }
      ],
      shippingAddress: {
        firstName: 'Test',
        lastName: 'Customer',
        phone: '+91 9876543210',
        street: '123 Test Street',
        city: 'Bhubaneswar',
        state: 'Odisha',
        pincode: '751001',
        country: 'India'
      },
      paymentMethod: 'COD',
      totalAmount: 1898,
      subtotal: 1798,
      shipping: 100
    };

    console.log('📤 Sending order data...');
    console.log(`   User ID: ${orderData.userId}`);
    console.log(`   Items: ${orderData.items.length}`);
    console.log(`   Total: ₹${orderData.totalAmount}`);

    const orderResponse = await fetch(`${baseURL}/api/customer-orders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(orderData),
    });

    console.log(`📡 Order response status: ${orderResponse.status}`);

    if (!orderResponse.ok) {
      const error = await orderResponse.text();
      console.log('❌ Order creation failed:', error);
      
      try {
        const errorJson = JSON.parse(error);
        console.log('   Error details:', errorJson);
      } catch (e) {
        console.log('   Raw error response:', error);
      }
      return;
    }

    const order = await orderResponse.json();
    console.log('✅ ORDER CREATED SUCCESSFULLY! 🎉');
    console.log(`   Order ID: ${order.id}`);
    console.log(`   Order Number: ${order.orderNumber}`);
    console.log(`   Status: ${order.status}`);
    console.log(`   Total Amount: ₹${order.totalAmount}`);
    console.log(`   Order Type: ${order.orderType}`);

    console.log('\n🎊 ORDER PLACEMENT SYSTEM IS WORKING!');
    console.log('=====================================');
    console.log('✅ Customer registration working');
    console.log('✅ Order creation working');
    console.log('✅ Database integration working');
    console.log('✅ Address creation working');
    console.log('✅ Order items creation working');

    return {
      success: true,
      userId: userId,
      orderId: order.id,
      orderNumber: order.orderNumber
    };

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    return { success: false, error: error.message };
  }
}

// Run the test
testDirectOrder().then(result => {
  if (result.success) {
    console.log('\n✨ Test completed successfully!');
    console.log(`   User: ${result.userId}`);
    console.log(`   Order: ${result.orderNumber}`);
  } else {
    console.log('\n❌ Test failed:', result.error);
  }
}).catch(error => {
  console.error('\n💥 Test crashed:', error.message);
});
