const nodemailer = require('nodemailer');
require('dotenv').config();

async function testEmailConfiguration() {
  console.log('🧪 Testing Email Configuration...\n');

  // Check environment variables
  const requiredVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.log('❌ Missing required environment variables:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n📝 Please check your .env file and add the missing variables.');
    console.log('📖 See docs/EMAIL_SETUP_GUIDE.md for configuration help.\n');
    return;
  }

  console.log('✅ Environment variables found:');
  console.log(`   - SMTP_HOST: ${process.env.SMTP_HOST}`);
  console.log(`   - SMTP_PORT: ${process.env.SMTP_PORT}`);
  console.log(`   - SMTP_SECURE: ${process.env.SMTP_SECURE || 'false'}`);
  console.log(`   - SMTP_USER: ${process.env.SMTP_USER}`);
  console.log(`   - SMTP_PASS: ${'*'.repeat(process.env.SMTP_PASS.length)}`);
  console.log(`   - EMAIL_FROM_NAME: ${process.env.EMAIL_FROM_NAME || 'Not set'}\n`);

  // Create transporter
  const transporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
    debug: true, // Enable debug output
    logger: true, // Log to console
  });

  try {
    console.log('🔍 Testing SMTP connection...');
    await transporter.verify();
    console.log('✅ SMTP connection successful!\n');

    // Send test email
    console.log('📧 Sending test email...');
    const testEmail = {
      from: `"${process.env.EMAIL_FROM_NAME || 'Mispri'}" <${process.env.SMTP_USER}>`,
      to: process.env.SMTP_USER, // Send to self for testing
      subject: '🧪 Test Email from Mispri - Email Configuration Working!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff7700;">🌸 Mispri</h1>
            <h2 style="color: #333;">Email Configuration Test</h2>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="color: #28a745; margin-top: 0;">✅ Success!</h3>
            <p>Your email configuration is working correctly. This test email was sent from your Mispri application.</p>
          </div>
          
          <div style="margin-bottom: 20px;">
            <h4>Configuration Details:</h4>
            <ul>
              <li><strong>SMTP Host:</strong> ${process.env.SMTP_HOST}</li>
              <li><strong>SMTP Port:</strong> ${process.env.SMTP_PORT}</li>
              <li><strong>SMTP Secure:</strong> ${process.env.SMTP_SECURE || 'false'}</li>
              <li><strong>From Name:</strong> ${process.env.EMAIL_FROM_NAME || 'Not set'}</li>
            </ul>
          </div>
          
          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="margin-top: 0; color: #1976d2;">🎉 What's Next?</h4>
            <p>Your email service is ready! You can now:</p>
            <ul>
              <li>Test forgot password functionality</li>
              <li>Receive welcome emails for new registrations</li>
              <li>Send order confirmations and notifications</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 14px;">
              This email was sent at ${new Date().toLocaleString()}
            </p>
          </div>
        </div>
      `,
      text: `
🌸 Mispri - Email Configuration Test

✅ Success! Your email configuration is working correctly.

Configuration Details:
- SMTP Host: ${process.env.SMTP_HOST}
- SMTP Port: ${process.env.SMTP_PORT}
- SMTP Secure: ${process.env.SMTP_SECURE || 'false'}
- From Name: ${process.env.EMAIL_FROM_NAME || 'Not set'}

🎉 What's Next?
Your email service is ready! You can now test forgot password functionality and receive welcome emails.

This email was sent at ${new Date().toLocaleString()}
      `.trim()
    };

    const result = await transporter.sendMail(testEmail);
    console.log('✅ Test email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    console.log(`📬 Check your inbox: ${process.env.SMTP_USER}\n`);

    console.log('🎉 Email configuration is working perfectly!');
    console.log('📖 See docs/EMAIL_SETUP_GUIDE.md for more information.');

  } catch (error) {
    console.log('❌ Email configuration test failed:');
    console.error(error.message);
    
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Check your SMTP credentials');
    console.log('2. Verify SMTP host and port');
    console.log('3. For Gmail: Use App Password, not regular password');
    console.log('4. Check firewall and network settings');
    console.log('5. See docs/EMAIL_SETUP_GUIDE.md for detailed help');
  }
}

testEmailConfiguration();
