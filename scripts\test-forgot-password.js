const { default: fetch } = require('node-fetch');

async function testForgotPassword() {
  try {
    console.log('🧪 Testing forgot password API...');

    const response = await fetch('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    const data = await response.json();

    console.log('Response status:', response.status);
    console.log('Response data:', data);

    if (response.ok) {
      console.log('✅ Forgot password API working correctly!');
      console.log('📧 Check the console logs for the reset link');
    } else {
      console.log('❌ Forgot password API failed');
    }
  } catch (error) {
    console.error('❌ Error testing forgot password:', error);
  }
}

testForgotPassword();
