const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testImageFlow() {
  console.log('🧪 Testing complete image flow: Admin Panel → Database → Website');
  
  try {
    console.log('\n📋 Step 1: Check category metadata in database...');
    
    const categoryMetadata = await prisma.systemSetting.findMany({
      where: {
        category: 'category_metadata'
      }
    });
    
    console.log(`Found ${categoryMetadata.length} categories with metadata:`);
    
    const metadataMap = {};
    for (const meta of categoryMetadata) {
      try {
        const data = JSON.parse(meta.value);
        metadataMap[meta.key] = data;
        console.log(`✅ ${meta.key}: ${data.imageUrl ? '🖼️ HAS IMAGE' : '❌ NO IMAGE'}`);
        if (data.imageUrl) {
          console.log(`   📸 Image URL: ${data.imageUrl}`);
        }
      } catch (error) {
        console.log(`❌ ${meta.key}: Error parsing data`);
      }
    }
    
    console.log('\n📋 Step 2: Check product categories...');
    
    const productCategories = await prisma.product.groupBy({
      by: ['category'],
      _count: {
        id: true
      }
    });
    
    console.log(`Found ${productCategories.length} categories with products:`);
    productCategories.forEach(cat => {
      console.log(`📦 ${cat.category}: ${cat._count.id} products`);
    });
    
    console.log('\n📋 Step 3: Simulate API response...');
    
    // Simulate what the public API returns
    const apiResponse = productCategories.map(cat => {
      const metadata = metadataMap[cat.category] || {};
      return {
        id: cat.category,
        name: cat.category,
        slug: metadata.slug || cat.category.toLowerCase().replace(/\s+/g, '-'),
        description: metadata.description || `Browse our ${cat.category.toLowerCase()} collection`,
        imageUrl: metadata.imageUrl,
        productCount: cat._count.id,
        isActive: metadata.isActive !== undefined ? metadata.isActive : true,
        displayOrder: metadata.displayOrder || 0,
      };
    });
    
    console.log('\n🌐 API Response for website:');
    apiResponse.forEach(cat => {
      console.log(`\n🏷️ ${cat.name}`);
      console.log(`   🆔 ID: ${cat.id}`);
      console.log(`   🔗 Slug: ${cat.slug}`);
      console.log(`   🖼️ Image: ${cat.imageUrl || 'NO IMAGE'}`);
      console.log(`   📦 Products: ${cat.productCount}`);
      console.log(`   ✅ Active: ${cat.isActive}`);
      console.log(`   📊 Order: ${cat.displayOrder}`);
    });
    
    console.log('\n📋 Step 4: Check image accessibility...');
    
    const fs = require('fs');
    const path = require('path');
    
    for (const cat of apiResponse) {
      if (cat.imageUrl && cat.imageUrl.startsWith('/images/')) {
        const imagePath = path.join(process.cwd(), 'public', cat.imageUrl);
        if (fs.existsSync(imagePath)) {
          console.log(`✅ ${cat.name}: Image file exists at ${cat.imageUrl}`);
        } else {
          console.log(`❌ ${cat.name}: Image file missing at ${cat.imageUrl}`);
        }
      }
    }
    
    console.log('\n🎉 Image flow test completed!');
    console.log('\n📊 Summary:');
    console.log(`   📂 Categories with metadata: ${categoryMetadata.length}`);
    console.log(`   📦 Categories with products: ${productCategories.length}`);
    console.log(`   🖼️ Categories with images: ${apiResponse.filter(c => c.imageUrl).length}`);
    console.log(`   ✅ Active categories: ${apiResponse.filter(c => c.isActive).length}`);
    
    console.log('\n🔍 What should happen:');
    console.log('   1. Admin uploads image → Saved to public/images/');
    console.log('   2. Image URL saved to database → SystemSetting table');
    console.log('   3. Website calls /api/public/categories → Gets image URLs');
    console.log('   4. Website displays images → From public/images/');
    
    if (apiResponse.every(c => c.imageUrl)) {
      console.log('\n✅ ALL CATEGORIES HAVE IMAGES - FLOW SHOULD WORK!');
    } else {
      console.log('\n⚠️ Some categories missing images - Upload images in admin panel');
    }
    
  } catch (error) {
    console.error('❌ Error testing image flow:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testImageFlow();
