const { default: fetch } = require('node-fetch');

async function testLogin() {
  try {
    console.log('🧪 Testing login with new password...');
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'newpassword123'
      }),
    });

    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', data);
    
    if (response.ok) {
      console.log('✅ Login with new password successful!');
      console.log('🎉 Forgot password functionality is working end-to-end!');
    } else {
      console.log('❌ Login with new password failed');
    }
  } catch (error) {
    console.error('❌ Error testing login:', error);
  }
}

testLogin();
