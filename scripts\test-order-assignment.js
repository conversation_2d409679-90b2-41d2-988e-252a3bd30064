const { default: fetch } = require('node-fetch');

async function testOrderAssignment() {
  console.log('🏪 TESTING ORDER ASSIGNMENT SYSTEM');
  console.log('==================================\n');

  const adminURL = 'http://localhost:3002';

  try {
    // Step 1: Get all online orders (super admin view)
    console.log('📋 Step 1: Getting online orders (Super Admin view)...');
    const ordersResponse = await fetch(`${adminURL}/api/orders?userRole=ADMIN&orderType=ONLINE`);
    
    if (!ordersResponse.ok) {
      console.log('❌ Failed to get orders');
      return;
    }

    const orders = await ordersResponse.json();
    console.log(`✅ Found ${orders.length} online orders`);

    if (orders.length === 0) {
      console.log('⚠️ No online orders found. Please create an order first using the website.');
      console.log('💡 Go to: http://localhost:3001 and place an order');
      return;
    }

    // Find an unassigned order
    const unassignedOrder = orders.find(order => !order.storeId);
    
    if (!unassignedOrder) {
      console.log('⚠️ All orders are already assigned to stores');
      console.log('📋 Current orders:');
      orders.forEach(order => {
        console.log(`   - ${order.orderNumber}: ${order.storeName || 'Unassigned'}`);
      });
      return;
    }

    console.log(`📦 Found unassigned order: ${unassignedOrder.orderNumber}`);

    // Step 2: Get available stores
    console.log('\n🏪 Step 2: Getting available stores...');
    const storesResponse = await fetch(`${adminURL}/api/stores`);
    
    if (!storesResponse.ok) {
      console.log('❌ Failed to get stores');
      return;
    }

    const stores = await storesResponse.json();
    console.log(`✅ Found ${stores.length} stores`);

    if (stores.length === 0) {
      console.log('⚠️ No stores found. Please create stores first.');
      return;
    }

    const targetStore = stores[0];
    console.log(`🎯 Will assign to store: ${targetStore.name}`);

    // Step 3: Create a super admin user for assignment
    console.log('\n👤 Step 3: Getting super admin user...');
    const usersResponse = await fetch(`${adminURL}/api/users`);
    
    if (!usersResponse.ok) {
      console.log('❌ Failed to get users');
      return;
    }

    const users = await usersResponse.json();
    const superAdmin = users.find(user => user.role === 'ADMIN');

    if (!superAdmin) {
      console.log('⚠️ No super admin found. Creating one...');
      // In a real scenario, you'd create a super admin user
      console.log('💡 Please create a super admin user in the admin panel');
      return;
    }

    console.log(`✅ Found super admin: ${superAdmin.name}`);

    // Step 4: Assign order to store
    console.log('\n🔄 Step 4: Assigning order to store...');
    const assignmentData = {
      orderId: unassignedOrder.id,
      storeId: targetStore.id,
      assignedBy: superAdmin.id
    };

    const assignResponse = await fetch(`${adminURL}/api/orders/assign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(assignmentData),
    });

    if (!assignResponse.ok) {
      const error = await assignResponse.text();
      console.log('❌ Assignment failed:', error);
      return;
    }

    const assignmentResult = await assignResponse.json();
    console.log('✅ Order assigned successfully!');
    console.log(`📦 Order: ${assignmentResult.order.orderNumber}`);
    console.log(`🏪 Store: ${assignmentResult.order.storeName}`);
    console.log(`👤 Assigned by: ${assignmentResult.order.assignedBy.name}`);

    // Step 5: Verify assignment by checking orders again
    console.log('\n🔍 Step 5: Verifying assignment...');
    const verifyResponse = await fetch(`${adminURL}/api/orders?userRole=ADMIN&orderType=ONLINE`);
    
    if (verifyResponse.ok) {
      const updatedOrders = await verifyResponse.json();
      const assignedOrder = updatedOrders.find(order => order.id === unassignedOrder.id);
      
      if (assignedOrder && assignedOrder.storeId) {
        console.log('✅ Assignment verified!');
        console.log(`📦 Order ${assignedOrder.orderNumber} is now assigned to ${assignedOrder.storeName}`);
      } else {
        console.log('❌ Assignment verification failed');
      }
    }

    // Step 6: Test store manager view
    console.log('\n👥 Step 6: Testing store manager view...');
    const storeOrdersResponse = await fetch(`${adminURL}/api/orders?userRole=STORE_MANAGER&storeId=${targetStore.id}`);
    
    if (storeOrdersResponse.ok) {
      const storeOrders = await storeOrdersResponse.json();
      console.log(`✅ Store manager can see ${storeOrders.length} orders for their store`);
      
      const assignedOrderInStore = storeOrders.find(order => order.id === unassignedOrder.id);
      if (assignedOrderInStore) {
        console.log(`✅ Assigned order ${assignedOrderInStore.orderNumber} is visible to store manager`);
      }
    }

    console.log('\n🎉 ORDER ASSIGNMENT SYSTEM TEST SUCCESSFUL!');
    console.log('============================================');
    console.log('✅ Super admin can view all online orders');
    console.log('✅ Super admin can assign orders to stores');
    console.log('✅ Store managers can view their assigned orders');
    console.log('✅ Order assignment workflow is complete');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

testOrderAssignment();
