// Complete test of order management functionality
const { default: fetch } = require('node-fetch');

async function testCompleteOrderManagement() {
  console.log('🎯 COMPLETE ORDER MANAGEMENT FUNCTIONALITY TEST');
  console.log('===============================================\n');

  const adminURL = 'http://localhost:3002';
  let testResults = {
    fetchOrders: false,
    fetchStores: false,
    updateOrderStatus: false,
    updatePaymentStatus: false,
    assignStore: false
  };

  try {
    // Test 1: Fetch Orders
    console.log('📋 Test 1: Fetching Orders...');
    const ordersResponse = await fetch(`${adminURL}/api/orders?userRole=ADMIN&orderType=ONLINE`);
    
    if (!ordersResponse.ok) {
      throw new Error(`Orders API failed: ${ordersResponse.status}`);
    }
    
    const orders = await ordersResponse.json();
    console.log(`✅ Orders fetched successfully: ${orders.length} orders`);
    testResults.fetchOrders = true;
    
    if (orders.length === 0) {
      console.log('⚠️ No orders found for testing');
      return testResults;
    }

    // Test 2: Fetch Stores
    console.log('\n🏪 Test 2: Fetching Stores...');
    const storesResponse = await fetch(`${adminURL}/api/stores`);
    
    if (!storesResponse.ok) {
      throw new Error(`Stores API failed: ${storesResponse.status}`);
    }
    
    const stores = await storesResponse.json();
    console.log(`✅ Stores fetched successfully: ${stores.length} stores`);
    testResults.fetchStores = true;

    // Use first order for testing
    const testOrder = orders[0];
    console.log(`\n🎯 Using test order: ${testOrder.orderNumber} (${testOrder.customerName})`);

    // Test 3: Update Order Status
    console.log('\n📦 Test 3: Testing Order Status Update...');
    const newOrderStatus = testOrder.status === 'PENDING' ? 'PROCESSING' : 'PENDING';
    console.log(`   Changing status from ${testOrder.status} to ${newOrderStatus}`);
    
    const statusUpdateResponse = await fetch(`${adminURL}/api/orders/${testOrder.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: newOrderStatus })
    });

    if (!statusUpdateResponse.ok) {
      const errorText = await statusUpdateResponse.text();
      throw new Error(`Status update failed: ${statusUpdateResponse.status} - ${errorText}`);
    }

    const updatedOrderStatus = await statusUpdateResponse.json();
    console.log(`✅ Order status updated: ${updatedOrderStatus.status}`);
    testResults.updateOrderStatus = true;

    // Test 4: Update Payment Status
    console.log('\n💳 Test 4: Testing Payment Status Update...');
    const newPaymentStatus = testOrder.paymentStatus === 'PENDING' ? 'PAID' : 'PENDING';
    console.log(`   Changing payment status from ${testOrder.paymentStatus} to ${newPaymentStatus}`);
    
    const paymentUpdateResponse = await fetch(`${adminURL}/api/orders/${testOrder.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ paymentStatus: newPaymentStatus })
    });

    if (!paymentUpdateResponse.ok) {
      const errorText = await paymentUpdateResponse.text();
      throw new Error(`Payment update failed: ${paymentUpdateResponse.status} - ${errorText}`);
    }

    const updatedOrderPayment = await paymentUpdateResponse.json();
    console.log(`✅ Payment status updated: ${updatedOrderPayment.paymentStatus}`);
    testResults.updatePaymentStatus = true;

    // Test 5: Store Assignment
    console.log('\n🏪 Test 5: Testing Store Assignment...');
    if (stores.length > 0) {
      const targetStore = stores[0];
      console.log(`   Assigning order to: ${targetStore.name} - ${targetStore.location}`);
      
      const storeAssignResponse = await fetch(`${adminURL}/api/orders/${testOrder.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ storeId: targetStore.id })
      });

      if (!storeAssignResponse.ok) {
        const errorText = await storeAssignResponse.text();
        throw new Error(`Store assignment failed: ${storeAssignResponse.status} - ${errorText}`);
      }

      const updatedOrderStore = await storeAssignResponse.json();
      console.log(`✅ Store assigned: ${updatedOrderStore.storeName || 'Assignment successful'}`);
      testResults.assignStore = true;
    } else {
      console.log('⚠️ No stores available for assignment test');
    }

    // Final verification
    console.log('\n🔍 Final Verification: Fetching updated order...');
    const finalOrderResponse = await fetch(`${adminURL}/api/orders/${testOrder.id}`);
    
    if (finalOrderResponse.ok) {
      const finalOrder = await finalOrderResponse.json();
      console.log('✅ Final order state:');
      console.log(`   Order: ${finalOrder.orderNumber}`);
      console.log(`   Customer: ${finalOrder.customerName}`);
      console.log(`   Status: ${finalOrder.status}`);
      console.log(`   Payment: ${finalOrder.paymentStatus}`);
      console.log(`   Store: ${finalOrder.storeName || 'Not assigned'}`);
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 SOLUTION: Start the admin panel');
      console.log('   Run: npm run dev');
      console.log('   Wait for it to load completely');
    }
  }

  // Test Results Summary
  console.log('\n🎉 TEST RESULTS SUMMARY:');
  console.log('========================');
  console.log(`📋 Fetch Orders: ${testResults.fetchOrders ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🏪 Fetch Stores: ${testResults.fetchStores ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`📦 Update Order Status: ${testResults.updateOrderStatus ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`💳 Update Payment Status: ${testResults.updatePaymentStatus ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🏪 Store Assignment: ${testResults.assignStore ? '✅ PASS' : '❌ FAIL'}`);

  const passedTests = Object.values(testResults).filter(result => result).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n📊 OVERALL SCORE: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('\n🎊 ALL TESTS PASSED! ORDER MANAGEMENT IS FULLY FUNCTIONAL! 🎉');
    console.log('✅ Store assignment works perfectly');
    console.log('✅ Payment status management works perfectly');
    console.log('✅ Order status updates work perfectly');
    console.log('✅ Admin panel is ready for production use');
  } else {
    console.log('\n⚠️ Some tests failed. Check the errors above.');
  }

  return testResults;
}

// Run the complete test
testCompleteOrderManagement().then(results => {
  const allPassed = Object.values(results).every(result => result);
  if (allPassed) {
    console.log('\n🚀 ORDER MANAGEMENT SYSTEM IS WORKING PERFECTLY!');
  } else {
    console.log('\n🔧 Some features need attention. Check the test results above.');
  }
}).catch(error => {
  console.error('\n💥 Test suite crashed:', error.message);
});
