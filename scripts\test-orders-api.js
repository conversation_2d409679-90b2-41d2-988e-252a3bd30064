const { default: fetch } = require('node-fetch');

async function testOrdersAPI() {
  console.log('📋 TESTING ORDERS API ENDPOINT');
  console.log('==============================\n');

  const adminURL = 'http://localhost:3002';

  try {
    // Test the orders API endpoint
    console.log('📡 Testing orders API endpoint...');
    
    const response = await fetch(`${adminURL}/api/orders?userRole=ADMIN&orderType=ONLINE`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`📊 Response status: ${response.status}`);
    console.log(`📊 Response headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API Error Response:', errorText);
      
      if (response.status === 500) {
        console.log('\n🔍 POSSIBLE ISSUES:');
        console.log('==================');
        console.log('1. Database connection failed');
        console.log('2. Prisma client not generated');
        console.log('3. Environment variables missing');
        console.log('4. Admin panel not running');
        
        console.log('\n💡 SOLUTIONS:');
        console.log('=============');
        console.log('1. Check DATABASE_URL in .env.local');
        console.log('2. Run: npx prisma generate');
        console.log('3. Restart admin panel: npm run dev');
        console.log('4. Check if admin panel is running on port 3002');
      }
      
      return { success: false, error: errorText };
    }

    const orders = await response.json();
    console.log('✅ Orders API response successful');
    console.log(`📦 Number of orders: ${orders.length}`);
    
    if (orders.length > 0) {
      console.log('\n📋 Sample Order Data:');
      const sampleOrder = orders[0];
      console.log(`   Order ID: ${sampleOrder.id}`);
      console.log(`   Order Number: ${sampleOrder.orderNumber}`);
      console.log(`   Customer: ${sampleOrder.customerName}`);
      console.log(`   Email: ${sampleOrder.customerEmail}`);
      console.log(`   Status: ${sampleOrder.status}`);
      console.log(`   Payment: ${sampleOrder.paymentStatus}`);
      console.log(`   Amount: ₹${sampleOrder.totalAmount}`);
      console.log(`   Store: ${sampleOrder.storeName || 'Not assigned'}`);
      console.log(`   Items: ${sampleOrder.items?.length || 0}`);
    } else {
      console.log('📋 No orders found (this is normal if no orders exist)');
    }

    console.log('\n🎉 ORDERS API TEST RESULTS:');
    console.log('===========================');
    console.log('✅ API endpoint: WORKING');
    console.log('✅ Database connection: WORKING');
    console.log('✅ Data retrieval: WORKING');
    console.log('✅ Response format: CORRECT');

    console.log('\n📊 WHAT THIS MEANS:');
    console.log('===================');
    console.log('✅ Admin panel orders page will load correctly');
    console.log('✅ "Failed to fetch orders" error is fixed');
    console.log('✅ Order management features will work');
    console.log('✅ Store assignment and payment status management ready');

    return {
      success: true,
      ordersCount: orders.length,
      hasOrders: orders.length > 0,
      sampleOrder: orders.length > 0 ? orders[0] : null
    };

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 CONNECTION ISSUE:');
      console.log('====================');
      console.log('❌ Admin panel is not running');
      console.log('💡 Start admin panel: npm run dev');
      console.log('💡 Wait for it to fully load');
      console.log('💡 Then test again');
    } else if (error.message.includes('fetch')) {
      console.log('\n💡 NETWORK ISSUE:');
      console.log('=================');
      console.log('❌ Network request failed');
      console.log('💡 Check if admin panel is accessible');
      console.log('💡 Try: curl http://localhost:3002/api/orders');
    }
    
    return { success: false, error: error.message };
  }
}

// Run the test
testOrdersAPI().then(result => {
  if (result.success) {
    console.log('\n✨ ORDERS API TEST COMPLETED SUCCESSFULLY!');
    console.log(`   Orders Found: ${result.ordersCount}`);
    if (result.hasOrders) {
      console.log(`   Sample Order: ${result.sampleOrder.orderNumber}`);
      console.log(`   Customer: ${result.sampleOrder.customerName}`);
    }
    console.log('\n🎉 ORDERS API IS NOW WORKING! 🎉');
  } else {
    console.log('\n❌ Test failed:', result.error);
  }
}).catch(error => {
  console.error('\n💥 Test crashed:', error.message);
});
