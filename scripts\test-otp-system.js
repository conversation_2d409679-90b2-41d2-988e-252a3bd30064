const { default: fetch } = require('node-fetch');

async function testOTPSystem() {
  console.log('🎯 TESTING NEW 6-DIGIT OTP PASSWORD RESET SYSTEM');
  console.log('================================================\n');

  const testEmail = '<EMAIL>';

  try {
    // Step 1: Request OTP
    console.log('📧 Step 1: Requesting OTP for', testEmail);
    console.log('🌐 URL: http://localhost:3001/api/auth/forgot-password\n');

    const step1Response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail
      }),
    });

    console.log(`📊 Step 1 Response Status: ${step1Response.status}`);
    const step1Data = await step1Response.json();
    console.log('📋 Step 1 Response Data:', JSON.stringify(step1Data, null, 2));

    if (!step1Response.ok) {
      console.log('\n❌ Step 1 failed - OTP request failed');
      return;
    }

    console.log('\n✅ Step 1 successful - OTP should be sent to email');
    console.log('📧 Check your Gmail inbox for the 6-digit code!');
    
    console.log('\n🔍 What to look for in your email:');
    console.log('📤 From: Mispri <<EMAIL>>');
    console.log('📧 To: <EMAIL>');
    console.log('📧 Subject: Your Password Reset Code - Mispri');
    console.log('🔢 Content: 6-digit verification code (e.g., 123456)');
    
    console.log('\n📝 Next steps to test manually:');
    console.log('1. 📧 Check your Gmail inbox for the OTP email');
    console.log('2. 🌐 Go to: http://localhost:3001/forgot-password');
    console.log('3. 📧 Enter: <EMAIL>');
    console.log('4. 🔢 Enter the 6-digit code from the email');
    console.log('5. 🔑 Set a new password');
    console.log('6. ✅ Login with the new password');

    console.log('\n🎉 OTP System Features:');
    console.log('✅ 6-digit verification codes');
    console.log('✅ 10-minute expiration time');
    console.log('✅ Professional email templates');
    console.log('✅ Multi-step user interface');
    console.log('✅ Progress indicators');
    console.log('✅ Resend code functionality');
    console.log('✅ Email validation');
    console.log('✅ OTP format validation');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n🔧 Possible issues:');
    console.log('1. 🌐 Website not running on port 3001');
    console.log('2. 🌐 Admin panel not running on port 3000');
    console.log('3. 🔌 Network connectivity issues');
    console.log('4. 📧 Email service configuration problems');
  }
}

testOTPSystem();
