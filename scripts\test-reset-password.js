const { PrismaClient } = require('@prisma/client');
const { default: fetch } = require('node-fetch');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function testResetPassword() {
  try {
    console.log('🧪 Testing reset password functionality...');
    
    // Step 1: Generate a reset token for the test user
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Update the test user with reset token
    const user = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: {
        resetToken,
        resetTokenExpiry,
      },
    });

    console.log('✅ Reset token generated for user:', user.email);
    console.log('🔗 Reset token:', resetToken);
    
    // Step 2: Test the reset password API
    const response = await fetch('http://localhost:3000/api/auth/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: resetToken,
        password: 'newpassword123'
      }),
    });

    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', data);
    
    if (response.ok) {
      console.log('✅ Reset password API working correctly!');
      console.log('🔗 Test reset URL: http://localhost:3001/reset-password?token=' + resetToken);
    } else {
      console.log('❌ Reset password API failed');
    }
  } catch (error) {
    console.error('❌ Error testing reset password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testResetPassword();
