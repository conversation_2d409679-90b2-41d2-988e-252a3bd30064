const { default: fetch } = require('node-fetch');

async function testSimplifiedOTP() {
  console.log('🧪 TESTING SIMPLIFIED OTP SYSTEM');
  console.log('=================================\n');

  const email = '<EMAIL>';
  const testOTP = '828219'; // From our direct test

  try {
    // Step 1: Test forgot password
    console.log('📧 Step 1: Testing forgot password...');
    const step1Response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    console.log(`📊 Step 1 Status: ${step1Response.status}`);
    const step1Data = await step1Response.json();
    console.log('📋 Step 1 Response:', JSON.stringify(step1Data, null, 2));

    if (!step1Response.ok) {
      console.log('\n❌ Step 1 failed');
      return;
    }

    console.log('\n✅ Step 1 successful');

    // Step 2: Test OTP verification
    console.log('\n🔢 Step 2: Testing OTP verification...');
    const step2Response = await fetch('http://localhost:3001/api/auth/verify-otp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, otp: testOTP }),
    });

    console.log(`📊 Step 2 Status: ${step2Response.status}`);
    const step2Data = await step2Response.json();
    console.log('📋 Step 2 Response:', JSON.stringify(step2Data, null, 2));

    if (!step2Response.ok) {
      console.log('\n❌ Step 2 failed');
      return;
    }

    console.log('\n✅ Step 2 successful');

    // Step 3: Test password reset
    console.log('\n🔑 Step 3: Testing password reset...');
    const step3Response = await fetch('http://localhost:3001/api/auth/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        email, 
        otp: testOTP, 
        password: 'newpassword123' 
      }),
    });

    console.log(`📊 Step 3 Status: ${step3Response.status}`);
    const step3Data = await step3Response.json();
    console.log('📋 Step 3 Response:', JSON.stringify(step3Data, null, 2));

    if (!step3Response.ok) {
      console.log('\n❌ Step 3 failed');
      return;
    }

    console.log('\n✅ Step 3 successful');

    console.log('\n🎉 ALL STEPS SUCCESSFUL!');
    console.log('========================');
    console.log('✅ Forgot password API working');
    console.log('✅ OTP verification working');
    console.log('✅ Password reset working');
    
    console.log('\n🧪 Now test the UI:');
    console.log('==================');
    console.log('1. Go to: http://localhost:3001/forgot-password');
    console.log('2. Enter: <EMAIL>');
    console.log('3. Click: "Send Verification Code"');
    console.log('4. Enter OTP: 828219');
    console.log('5. Click: "Verify Code"');
    console.log('6. Enter new password');
    console.log('7. Click: "Reset Password"');
    console.log('8. Success!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

testSimplifiedOTP();
