// Test store assignment functionality in admin panel
const { default: fetch } = require('node-fetch');

async function testStoreAssignment() {
  console.log('🏪 TESTING STORE ASSIGNMENT FUNCTIONALITY');
  console.log('=========================================\n');

  const adminURL = 'http://localhost:3002';

  try {
    // Step 1: Test stores API
    console.log('📋 Step 1: Testing stores API...');
    const storesResponse = await fetch(`${adminURL}/api/stores`);
    
    if (!storesResponse.ok) {
      throw new Error(`Stores API failed: ${storesResponse.status}`);
    }
    
    const stores = await storesResponse.json();
    console.log(`✅ Stores API working: ${stores.length} stores found`);
    stores.forEach((store, index) => {
      console.log(`   ${index + 1}. ${store.name} - ${store.location} (ID: ${store.id})`);
    });

    // Step 2: Test orders API
    console.log('\n📦 Step 2: Testing orders API...');
    const ordersResponse = await fetch(`${adminURL}/api/orders?userRole=ADMIN&orderType=ONLINE`);
    
    if (!ordersResponse.ok) {
      throw new Error(`Orders API failed: ${ordersResponse.status}`);
    }
    
    const orders = await ordersResponse.json();
    console.log(`✅ Orders API working: ${orders.length} orders found`);
    
    if (orders.length === 0) {
      console.log('⚠️ No orders found for testing');
      return;
    }

    const testOrder = orders[0];
    console.log(`🎯 Using test order: ${testOrder.orderNumber} (${testOrder.customerName})`);
    console.log(`   Current store: ${testOrder.storeName || 'Not assigned'}`);

    // Step 3: Test store assignment
    console.log('\n🔄 Step 3: Testing store assignment...');
    
    if (stores.length === 0) {
      console.log('❌ No stores available for assignment');
      return;
    }

    const targetStore = stores[0];
    console.log(`   Assigning order to: ${targetStore.name} - ${targetStore.location}`);
    console.log(`   Store ID: ${targetStore.id}`);

    const assignResponse = await fetch(`${adminURL}/api/orders/${testOrder.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        storeId: targetStore.id
      }),
    });

    console.log(`📊 Assignment response status: ${assignResponse.status}`);

    if (!assignResponse.ok) {
      const errorText = await assignResponse.text();
      console.log('❌ Assignment failed:', errorText);
      
      // Try to parse as JSON for better error info
      try {
        const errorJson = JSON.parse(errorText);
        console.log('📋 Error details:', errorJson);
      } catch (e) {
        console.log('📋 Raw error response:', errorText);
      }
      
      return { success: false, error: errorText };
    }

    const updatedOrder = await assignResponse.json();
    console.log('✅ Store assignment successful!');
    console.log(`   Updated order store: ${updatedOrder.storeName || 'Still not assigned'}`);
    console.log(`   Updated store ID: ${updatedOrder.storeId || 'No store ID'}`);

    // Step 4: Verify assignment
    console.log('\n🔍 Step 4: Verifying assignment...');
    
    const verifyResponse = await fetch(`${adminURL}/api/orders/${testOrder.id}`);
    
    if (verifyResponse.ok) {
      const verifiedOrder = await verifyResponse.json();
      console.log('✅ Verification successful:');
      console.log(`   Order: ${verifiedOrder.orderNumber}`);
      console.log(`   Store ID: ${verifiedOrder.storeId}`);
      console.log(`   Store Name: ${verifiedOrder.storeName}`);
      console.log(`   Customer: ${verifiedOrder.customerName}`);
    } else {
      console.log('⚠️ Could not verify assignment');
    }

    console.log('\n🎉 STORE ASSIGNMENT TEST RESULTS:');
    console.log('=================================');
    console.log('✅ Stores API: WORKING');
    console.log('✅ Orders API: WORKING');
    console.log('✅ Store Assignment: WORKING');
    console.log('✅ Data Persistence: WORKING');

    console.log('\n📊 WHAT THIS MEANS:');
    console.log('===================');
    console.log('✅ Admin panel store assignment is functional');
    console.log('✅ Orders can be assigned to stores');
    console.log('✅ Store information is properly updated');
    console.log('✅ The "failed to update order" error should be resolved');

    return {
      success: true,
      orderId: testOrder.id,
      orderNumber: testOrder.orderNumber,
      assignedStore: updatedOrder.storeName,
      storeId: updatedOrder.storeId
    };

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 SOLUTION:');
      console.log('============');
      console.log('❌ Admin panel is not running');
      console.log('💡 Start admin panel: npm run dev');
      console.log('💡 Wait for it to fully load');
      console.log('💡 Then test again');
    } else if (error.message.includes('fetch')) {
      console.log('\n💡 NETWORK ISSUE:');
      console.log('=================');
      console.log('❌ Network request failed');
      console.log('💡 Check if admin panel is accessible');
      console.log('💡 Try: curl http://localhost:3002/api/stores');
    }
    
    return { success: false, error: error.message };
  }
}

// Run the test
testStoreAssignment().then(result => {
  if (result.success) {
    console.log('\n✨ STORE ASSIGNMENT TEST COMPLETED SUCCESSFULLY!');
    console.log(`   Order: ${result.orderNumber}`);
    console.log(`   Assigned to: ${result.assignedStore}`);
    console.log(`   Store ID: ${result.storeId}`);
    console.log('\n🎉 STORE ASSIGNMENT IS WORKING! 🎉');
  } else {
    console.log('\n❌ Test failed:', result.error);
    console.log('\n🔧 TROUBLESHOOTING STEPS:');
    console.log('=========================');
    console.log('1. Make sure admin panel is running');
    console.log('2. Check browser console for errors');
    console.log('3. Verify stores are loaded in admin panel');
    console.log('4. Try refreshing the admin panel page');
  }
}).catch(error => {
  console.error('\n💥 Test crashed:', error.message);
});
