const { default: fetch } = require('node-fetch');

async function testUpdatedOTPSystem() {
  console.log('🧪 TESTING UPDATED OTP SYSTEM');
  console.log('==============================\n');

  const email = '<EMAIL>';

  try {
    // Step 1: Test forgot password
    console.log('📧 Step 1: Testing forgot password API...');
    const response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    console.log(`📊 Response Status: ${response.status}`);
    const data = await response.json();
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.log('\n❌ Step 1 failed');
      return;
    }

    console.log('\n✅ Step 1 successful!');
    
    if (data.debug && data.debug.otp) {
      const otp = data.debug.otp;
      console.log(`🔢 OTP from API: ${otp}`);
      
      // Step 2: Test OTP verification
      console.log('\n🔢 Step 2: Testing OTP verification...');
      const verifyResponse = await fetch('http://localhost:3001/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp }),
      });

      console.log(`📊 Verify Status: ${verifyResponse.status}`);
      const verifyData = await verifyResponse.json();
      console.log('📋 Verify Data:', JSON.stringify(verifyData, null, 2));

      if (!verifyResponse.ok) {
        console.log('\n❌ Step 2 failed');
        return;
      }

      console.log('\n✅ Step 2 successful!');

      // Step 3: Test password reset
      console.log('\n🔑 Step 3: Testing password reset...');
      const resetResponse = await fetch('http://localhost:3001/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          otp, 
          password: 'newpassword123' 
        }),
      });

      console.log(`📊 Reset Status: ${resetResponse.status}`);
      const resetData = await resetResponse.json();
      console.log('📋 Reset Data:', JSON.stringify(resetData, null, 2));

      if (!resetResponse.ok) {
        console.log('\n❌ Step 3 failed');
        return;
      }

      console.log('\n✅ Step 3 successful!');

      console.log('\n🎉 ALL STEPS COMPLETED SUCCESSFULLY!');
      console.log('====================================');
      console.log('✅ Forgot password API working');
      console.log('✅ OTP generation working');
      console.log('✅ OTP verification working');
      console.log('✅ Password reset working');
      console.log('✅ In-memory storage working');
      
      console.log('\n🧪 NOW TEST THE UI:');
      console.log('==================');
      console.log('1. Go to: http://localhost:3001/forgot-password');
      console.log(`2. Enter: ${email}`);
      console.log('3. Click: "Send Verification Code"');
      console.log('4. Check website console logs for OTP');
      console.log('5. Enter the OTP from console');
      console.log('6. Complete password reset');
      console.log('\n📧 BONUS: You should also receive real emails!');
      console.log('Check your Gmail inbox for actual OTP emails.');

    } else {
      console.log('\n⚠️ No OTP in response - check console logs');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

testUpdatedOTPSystem();
