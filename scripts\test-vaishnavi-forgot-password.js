const { default: fetch } = require('node-fetch');

async function testVaishnaviForgotPassword() {
  console.log('🎯 TESTING FORGOT PASSWORD FOR VAISHNAVI');
  console.log('=========================================\n');

  try {
    console.log('📧 Testing website forgot <NAME_EMAIL>...');
    console.log('🌐 URL: http://localhost:3001/api/auth/forgot-password\n');

    const startTime = Date.now();
    
    const response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️  Request took: ${duration}ms`);
    console.log(`📊 Response Status: ${response.status}`);
    
    const data = await response.json();
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ SUCCESS! Forgot password request processed!');
      console.log('📧 Email should be sent to: <EMAIL>');
      
      console.log('\n📬 CHECK YOUR EMAIL:');
      console.log('===================');
      console.log('1. 📧 Open Gmail: https://gmail.com');
      console.log('2. 🔑 Sign in to: <EMAIL>');
      console.log('3. 🔍 Check Inbox for email from Mispri');
      console.log('4. 📁 Check Spam/Junk folder if not in inbox');
      console.log('5. 🔍 Search for "Mispri" or "Reset Your Password"');
      console.log('6. ⏰ Email should arrive within 1-3 minutes');
      
      console.log('\n📧 EMAIL DETAILS TO LOOK FOR:');
      console.log('============================');
      console.log('📤 From: Mispri <<EMAIL>>');
      console.log('📧 To: <EMAIL>');
      console.log('📧 Subject: Reset Your Password - Mispri');
      console.log('🎨 Content: Professional HTML email with reset button');
      console.log('🔗 Contains: Reset password link for website');
      
      console.log('\n🔧 IF EMAIL NOT RECEIVED:');
      console.log('=========================');
      console.log('1. ⏰ Wait 3-5 minutes (email delivery can be delayed)');
      console.log('2. 📁 Check ALL Gmail folders (Inbox, Spam, Promotions, etc.)');
      console.log('3. 🔍 Search Gmail for "mispri", "password", or "reset"');
      console.log('4. 📧 Make sure you\'<NAME_EMAIL>');
      console.log('5. 🔄 Try the forgot password process again');
      console.log('6. 📱 Check if Gmail is working properly');
      
    } else {
      console.log('\n❌ FAILED! Forgot password request error');
      console.log('🔧 This explains why no emails are received');
      
      if (data.error) {
        console.log(`💬 Error message: ${data.error}`);
      }
    }

  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    console.log('\n🔧 Possible issues:');
    console.log('1. 🌐 Website not running on port 3001');
    console.log('2. 🌐 Admin panel not running on port 3000');
    console.log('3. 🔌 Network connectivity issues');
  }

  console.log('\n🎯 SUMMARY:');
  console.log('===========');
  console.log('✅ Customer account <NAME_EMAIL>');
  console.log('✅ Gmail SMTP is configured (<EMAIL>)');
  console.log('✅ Email service is working');
  console.log('📧 Reset emails should be <NAME_EMAIL> TO <EMAIL>');
  console.log('\n🔍 Next: Check <EMAIL> inbox!');
}

testVaishnaviForgotPassword();
