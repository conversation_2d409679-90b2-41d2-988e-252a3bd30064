const nodemailer = require('nodemailer');
require('dotenv').config();

async function testVaishnaviGmail() {
  console.log('🧪 Testing Gmail <NAME_EMAIL>\n');

  // Check if Gmail SMTP is configured
  const expectedUser = '<EMAIL>';

  if (!process.env.SMTP_USER || process.env.SMTP_USER !== expectedUser) {
    console.log('❌ Gmail SMTP not configured correctly');
    console.log(`Expected SMTP_USER: ${expectedUser}`);
    console.log(`Current SMTP_USER: ${process.env.SMTP_USER || 'Not set'}`);
    console.log('\n📝 Please follow these steps:');
    console.log('1. See docs/GMAIL_SETUP_VAISHNAVI.md for setup instructions');
    console.log('2. Enable 2FA on your Gmail account');
    console.log('3. Generate App Password');
    console.log('4. Add SMTP settings to .env file\n');
    return;
  }

  // Check required environment variables
  const requiredVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_PASS'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.log('❌ Missing required environment variables:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n📖 See docs/GMAIL_SETUP_VAISHNAVI.md for configuration help.\n');
    return;
  }

  console.log('✅ Gmail SMTP Configuration Found:');
  console.log(`   - Email: ${process.env.SMTP_USER}`);
  console.log(`   - Host: ${process.env.SMTP_HOST}`);
  console.log(`   - Port: ${process.env.SMTP_PORT}`);
  console.log(`   - App Password: ${'*'.repeat(16)} (${process.env.SMTP_PASS.length} chars)`);
  console.log(`   - From Name: ${process.env.EMAIL_FROM_NAME || 'Mispri'}\n`);

  // Create Gmail transporter
  const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  try {
    console.log('🔍 Testing Gmail SMTP connection...');
    await transporter.verify();
    console.log('✅ Gmail SMTP connection successful!\n');

    // Send test email to Mispri Gmail
    console.log('📧 Sending test <NAME_EMAIL>...');
    const testEmail = {
      from: `"Mispri" <${process.env.SMTP_USER}>`,
      to: '<EMAIL>',
      subject: '🎉 Mispri Email System - Successfully Configured!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background-color: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #ff7700; font-size: 32px; margin-bottom: 10px;">🌸 Mispri</h1>
              <h2 style="color: #333; margin-bottom: 20px;">Email System Successfully Configured!</h2>
            </div>

            <div style="background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="margin-top: 0; color: #155724;">🎉 Congratulations!</h3>
              <p style="margin-bottom: 0;">Your Gmail SMTP configuration is working perfectly. The Mispri application can now send emails!</p>
            </div>

            <div style="margin-bottom: 20px;">
              <h4 style="color: #333;">✅ What's Working:</h4>
              <ul style="color: #666;">
                <li><strong>Forgot Password Emails</strong> - Users can reset their passwords</li>
                <li><strong>Welcome Emails</strong> - New customers receive welcome messages</li>
                <li><strong>Professional Design</strong> - Beautiful, responsive email templates</li>
                <li><strong>Reliable Delivery</strong> - Gmail's infrastructure ensures delivery</li>
              </ul>
            </div>

            <div style="background-color: #e3f2fd; border: 1px solid #bbdefb; color: #0d47a1; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h4 style="margin-top: 0; color: #0d47a1;">📊 Gmail Limits:</h4>
              <ul style="margin-bottom: 0;">
                <li><strong>500 emails per day</strong> - Free forever</li>
                <li><strong>Professional deliverability</strong> - High inbox rates</li>
                <li><strong>No monthly fees</strong> - Completely free service</li>
              </ul>
            </div>

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h4 style="margin-top: 0; color: #856404;">🔒 Security Notes:</h4>
              <ul style="margin-bottom: 0;">
                <li>Using App Password (not your regular Gmail password)</li>
                <li>2-Factor Authentication enabled</li>
                <li>Secure SMTP connection (TLS)</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="http://localhost:3001/forgot-password"
                 style="display: inline-block; background-color: #ff7700; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                Test Forgot Password
              </a>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 14px; margin-bottom: 5px;">
                <strong>Configuration Details:</strong>
              </p>
              <p style="color: #999; font-size: 12px; margin: 0;">
                Email: ${process.env.SMTP_USER}<br>
                Host: ${process.env.SMTP_HOST}:${process.env.SMTP_PORT}<br>
                Sent: ${new Date().toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
🌸 Mispri - Email System Successfully Configured!

🎉 Congratulations!
Your Gmail SMTP configuration is working perfectly. The Mispri application can now send emails!

✅ What's Working:
- Forgot Password Emails - Users can reset their passwords
- Welcome Emails - New customers receive welcome messages
- Professional Design - Beautiful, responsive email templates
- Reliable Delivery - Gmail's infrastructure ensures delivery

📊 Gmail Limits:
- 500 emails per day - Free forever
- Professional deliverability - High inbox rates
- No monthly fees - Completely free service

🔒 Security Notes:
- Using App Password (not your regular Gmail password)
- 2-Factor Authentication enabled
- Secure SMTP connection (TLS)

Configuration Details:
Email: ${process.env.SMTP_USER}
Host: ${process.env.SMTP_HOST}:${process.env.SMTP_PORT}
Sent: ${new Date().toLocaleString()}

Test the forgot password functionality at: http://localhost:3001/forgot-password
      `.trim()
    };

    const result = await transporter.sendMail(testEmail);
    console.log('✅ Test email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    console.log(`📬 Check your Gmail inbox: <EMAIL>\n`);

    console.log('🎉 Gmail SMTP is working perfectly!');
    console.log('🔗 Test forgot password: http://localhost:3001/forgot-password');
    console.log('📖 See docs/GMAIL_SETUP_VAISHNAVI.md for more information.');

  } catch (error) {
    console.log('❌ Gmail SMTP test failed:');
    console.error(error.message);

    console.log('\n🔧 Common solutions:');
    if (error.message.includes('Invalid login')) {
      console.log('1. ❌ Wrong App Password - Generate a new one');
      console.log('2. ❌ Using regular password instead of App Password');
      console.log('3. ❌ 2FA not enabled - Enable 2-Factor Authentication first');
    } else if (error.message.includes('Connection timeout')) {
      console.log('1. ❌ Check internet connection');
      console.log('2. ❌ Try port 465 with SMTP_SECURE=true');
      console.log('3. ❌ Check firewall settings');
    }
    console.log('\n📖 See docs/GMAIL_SETUP_VAISHNAVI.md for detailed help');
  }
}

testVaishnaviGmail();
