const { default: fetch } = require('node-fetch');

async function testWebsiteForgotPassword() {
  try {
    console.log('🧪 Testing WEBSITE forgot password API...');
    console.log('🌐 Testing: http://localhost:3001/api/auth/forgot-password\n');
    
    // Test with the test customer account
    const testEmail = '<EMAIL>';
    
    console.log(`📧 Testing with email: ${testEmail}`);
    
    const response = await fetch('http://localhost:3001/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail
      }),
    });

    console.log(`📊 Response status: ${response.status}`);
    
    const data = await response.json();
    console.log('📋 Response data:', data);
    
    if (response.ok) {
      console.log('\n✅ Website forgot password API working correctly!');
      console.log('📧 Email should be sent (check Gmail inbox: <EMAIL>)');
      
      // Also test with the Mispri Gmail account
      console.log('\n🔄 Testing with Mispri Gmail account...');
      
      const response2 = await fetch('http://localhost:3001/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>'
        }),
      });

      const data2 = await response2.json();
      console.log(`📊 Response status: ${response2.status}`);
      console.log('📋 Response data:', data2);
      
      if (response2.ok) {
        console.log('\n🎉 Both tests successful!');
        console.log('📬 Check Gmail inbox: <EMAIL>');
      }
    } else {
      console.log('\n❌ Website forgot password API failed');
      console.log('🔧 This might be why emails are not being received');
    }
  } catch (error) {
    console.error('\n❌ Error testing website forgot password:', error.message);
    console.log('\n🔧 Possible issues:');
    console.log('1. Website not running on port 3001');
    console.log('2. Admin panel not running on port 3000');
    console.log('3. Network connectivity issues');
    console.log('4. API route configuration problems');
  }
}

testWebsiteForgotPassword();
