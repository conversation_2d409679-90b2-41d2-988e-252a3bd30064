const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verify5Categories() {
  console.log('🔍 Verifying 5 main categories...');
  
  try {
    // Get all category metadata
    const metadata = await prisma.systemSetting.findMany({
      where: {
        category: 'category_metadata'
      },
      orderBy: {
        key: 'asc'
      }
    });
    
    console.log(`\n📊 Found ${metadata.length} categories:`);
    
    for (const meta of metadata) {
      try {
        const data = JSON.parse(meta.value);
        console.log(`\n✅ ${meta.key}`);
        console.log(`   📝 Description: ${data.description}`);
        console.log(`   🖼️ Image: ${data.imageUrl}`);
        console.log(`   📊 Order: ${data.displayOrder}`);
        console.log(`   ✅ Active: ${data.isActive ? 'Yes' : 'No'}`);
      } catch (error) {
        console.log(`❌ ${meta.key}: Error parsing data`);
      }
    }
    
    // Check products in each category
    console.log('\n📦 Products per category:');
    const productCounts = await prisma.product.groupBy({
      by: ['category'],
      _count: {
        id: true
      }
    });
    
    for (const cat of productCounts) {
      console.log(`   📂 ${cat.category}: ${cat._count.id} products`);
    }
    
    console.log('\n🎉 Verification complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verify5Categories();
