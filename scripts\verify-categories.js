const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyCategories() {
  console.log('🔍 Verifying category images...');
  
  try {
    // Check category metadata
    const categoryMetadata = await prisma.systemSetting.findMany({
      where: {
        category: 'category_metadata'
      }
    });
    
    console.log(`\n✅ Found ${categoryMetadata.length} categories with metadata:`);
    
    for (const meta of categoryMetadata) {
      try {
        const data = JSON.parse(meta.value);
        console.log(`📂 ${meta.key}: ${data.imageUrl ? '🖼️ HAS IMAGE' : '❌ NO IMAGE'}`);
      } catch (error) {
        console.log(`📂 ${meta.key}: ❌ ERROR PARSING DATA`);
      }
    }
    
    console.log('\n🎉 Verification complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyCategories();
