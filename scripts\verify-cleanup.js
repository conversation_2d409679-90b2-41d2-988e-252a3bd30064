// Script to verify database cleanup
const { Client } = require('pg');
require('dotenv').config();

async function verifyCleanup() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to NeonDB');

    // Check main tables for remaining data
    const tablesToCheck = [
      'products',
      'users', 
      'warehouses',
      'stores',
      'transactions',
      'orders',
      'customers',
      'carts',
      'coupons'
    ];

    console.log('📊 Checking remaining data...\n');

    for (const table of tablesToCheck) {
      try {
        const result = await client.query(`SELECT COUNT(*) FROM bakery."${table}";`);
        const count = parseInt(result.rows[0].count);
        const status = count === 0 ? '✅' : '📦';
        console.log(`${status} ${table}: ${count} rows`);
      } catch (error) {
        console.log(`❓ ${table}: Table not found or error`);
      }
    }

    console.log('\n🎉 Database cleanup verification completed!');

  } catch (error) {
    console.error('❌ Error verifying cleanup:', error);
  } finally {
    await client.end();
    console.log('🔌 Disconnected from database');
  }
}

verifyCleanup();
