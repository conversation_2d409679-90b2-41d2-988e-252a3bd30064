// Quick verification script for the delivery system
console.log('🚀 Verifying Bhubaneswar Delivery System...\n');

// Test data
const testCases = [
  { pincode: '751001', expected: true, description: 'Central Bhubaneswar' },
  { pincode: '751025', expected: true, description: 'Mid-range pincode' },
  { pincode: '751050', expected: true, description: 'Extended area' },
  { pincode: '751075', expected: true, description: 'Industrial area' },
  { pincode: '751100', expected: true, description: 'Airport area' },
  { pincode: '110001', expected: false, description: 'Delhi (should fail)' },
  { pincode: '400001', expected: false, description: 'Mumbai (should fail)' },
  { pincode: '560001', expected: false, description: 'Bangalore (should fail)' }
];

async function verifyDeliverySystem() {
  console.log('📋 Testing pincode validation...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    try {
      const response = await fetch(`https://mispri24.vercel.app/api/pincode?code=${testCase.pincode}`);
      const data = await response.json();
      
      const isDeliverable = response.ok && data.isDeliverable;
      const testPassed = isDeliverable === testCase.expected;
      
      if (testPassed) {
        console.log(`✅ ${testCase.pincode} (${testCase.description}): ${isDeliverable ? 'Deliverable' : 'Not deliverable'} ✓`);
        passedTests++;
      } else {
        console.log(`❌ ${testCase.pincode} (${testCase.description}): Expected ${testCase.expected}, got ${isDeliverable} ✗`);
      }
      
      if (isDeliverable && data.deliveryInfo) {
        console.log(`   💰 Free delivery above: ₹${data.deliveryInfo.freeDeliveryAbove}`);
        console.log(`   🚚 Delivery charge: ₹${data.deliveryInfo.deliveryCharge}`);
        console.log(`   ⏱️ Estimated time: ${data.deliveryInfo.estimatedTime}`);
      }
      
    } catch (error) {
      console.log(`❌ ${testCase.pincode}: Error - ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }
  
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed\n`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! The delivery system is working correctly.\n');
    
    console.log('✅ System Features Verified:');
    console.log('   📍 All Bhubaneswar pincodes (751001-751100) are supported');
    console.log('   🚫 Non-Bhubaneswar pincodes are correctly rejected');
    console.log('   💰 Delivery pricing information is provided');
    console.log('   ⏱️ Delivery time estimates are included');
    console.log('   🔄 API responses are consistent and reliable');
    
    console.log('\n🚀 Ready for production use!');
  } else {
    console.log('⚠️ Some tests failed. Please check the system configuration.');
  }
}

// Test the list endpoint
async function testListEndpoint() {
  console.log('📋 Testing pincode list endpoint...\n');
  
  try {
    const response = await fetch('https://mispri24.vercel.app/api/pincode?action=list');
    const data = await response.json();
    
    if (response.ok && data.pincodes && data.total) {
      console.log(`✅ Pincode list endpoint working`);
      console.log(`   📊 Total pincodes: ${data.total}`);
      console.log(`   🏙️ City: ${data.city}`);
      console.log(`   🗺️ State: ${data.state}`);
      console.log(`   🔢 Range: ${data.pincodes[0]} - ${data.pincodes[data.pincodes.length - 1]}\n`);
    } else {
      console.log('❌ Pincode list endpoint failed\n');
    }
  } catch (error) {
    console.log(`❌ Error testing list endpoint: ${error.message}\n`);
  }
}

// Run all tests
async function runAllTests() {
  await testListEndpoint();
  await verifyDeliverySystem();
}

// Execute if running in Node.js
if (typeof window === 'undefined') {
  // Node.js environment - need to install node-fetch
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    runAllTests().catch(console.error);
  } catch (error) {
    console.log('Please install node-fetch: npm install node-fetch');
  }
} else {
  // Browser environment
  runAllTests().catch(console.error);
}
