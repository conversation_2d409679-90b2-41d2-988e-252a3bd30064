// This script seeds the database with products in the specified categories
const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function seedCategories() {
  try {
    console.log('Seeding products with specified categories...');

    // Define the categories
    const categories = [
      'Flowers',
      'Cakes',
      'Birthday',
      'Anniversary',
      'Gifts',
      'Personalised',
      'Plants',
      'Combos',
      'International',
      'Occasions',
      'Birthday Gifts',
      'Anniversary Gifts',
      'Gifts for Her'
    ];

    // Create products for each category
    for (const category of categories) {
      const productsInCategory = await createProductsForCategory(category);
      console.log(`Created ${productsInCategory.length} products in category '${category}'`);
    }

    console.log('Category seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding categories:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function createProductsForCategory(category) {
  // Define products for each category
  const productsByCategory = {
    'Flowers': [
      {
        name: '<PERSON> Rose Bouquet',
        description: 'Beautiful bouquet of 12 red roses',
        price: 799,
        costPrice: 400,
        unit: 'bouquet',
        imageUrl: 'https://picsum.photos/seed/redrose/800/600'
      },
      {
        name: 'Mixed Flower Arrangement',
        description: 'Colorful arrangement of seasonal flowers',
        price: 1299,
        costPrice: 650,
        unit: 'arrangement',
        imageUrl: 'https://picsum.photos/seed/mixedflowers/800/600'
      },
      {
        name: 'Lily Bouquet',
        description: 'Elegant bouquet of white lilies',
        price: 999,
        costPrice: 500,
        unit: 'bouquet',
        imageUrl: 'https://picsum.photos/seed/lilies/800/600'
      }
    ],
    'Cakes': [
      {
        name: 'Chocolate Truffle Cake',
        description: 'Rich chocolate cake with truffle frosting',
        price: 899,
        costPrice: 450,
        unit: 'cake',
        imageUrl: 'https://picsum.photos/seed/choctruffle/800/600'
      },
      {
        name: 'Red Velvet Cake',
        description: 'Classic red velvet cake with cream cheese frosting',
        price: 999,
        costPrice: 500,
        unit: 'cake',
        imageUrl: 'https://picsum.photos/seed/redvelvet/800/600'
      },
      {
        name: 'Butterscotch Cake',
        description: 'Sweet butterscotch cake with caramel drizzle',
        price: 849,
        costPrice: 425,
        unit: 'cake',
        imageUrl: 'https://picsum.photos/seed/butterscotch/800/600'
      }
    ],
    'Birthday': [
      {
        name: 'Birthday Surprise Box',
        description: 'Gift box with cake, candles, and party supplies',
        price: 1499,
        costPrice: 750,
        unit: 'box',
        imageUrl: 'https://picsum.photos/seed/birthdaybox/800/600'
      },
      {
        name: 'Birthday Balloon Bouquet',
        description: 'Colorful balloon arrangement for birthday celebrations',
        price: 699,
        costPrice: 350,
        unit: 'set',
        imageUrl: 'https://picsum.photos/seed/balloons/800/600'
      }
    ],
    'Anniversary': [
      {
        name: 'Anniversary Special Cake',
        description: 'Heart-shaped cake for anniversary celebrations',
        price: 1099,
        costPrice: 550,
        unit: 'cake',
        imageUrl: 'https://picsum.photos/seed/annicake/800/600'
      },
      {
        name: 'Silver Anniversary Frame',
        description: 'Silver-plated photo frame for anniversary memories',
        price: 1299,
        costPrice: 650,
        unit: 'piece',
        imageUrl: 'https://picsum.photos/seed/silverframe/800/600'
      }
    ],
    'Gifts': [
      {
        name: 'Premium Chocolate Box',
        description: 'Assorted premium chocolates in an elegant box',
        price: 899,
        costPrice: 450,
        unit: 'box',
        imageUrl: 'https://picsum.photos/seed/chocbox/800/600'
      },
      {
        name: 'Scented Candle Set',
        description: 'Set of 3 aromatic scented candles',
        price: 799,
        costPrice: 400,
        unit: 'set',
        imageUrl: 'https://picsum.photos/seed/candles/800/600'
      }
    ],
    'Personalised': [
      {
        name: 'Personalized Photo Mug',
        description: 'Custom mug with your favorite photo',
        price: 399,
        costPrice: 200,
        unit: 'piece',
        imageUrl: 'https://picsum.photos/seed/photomug/800/600'
      },
      {
        name: 'Custom Name Necklace',
        description: 'Sterling silver necklace with custom name',
        price: 1499,
        costPrice: 750,
        unit: 'piece',
        imageUrl: 'https://picsum.photos/seed/namenecklace/800/600'
      }
    ],
    'Plants': [
      {
        name: 'Money Plant in Ceramic Pot',
        description: 'Lucky money plant in a decorative ceramic pot',
        price: 599,
        costPrice: 300,
        unit: 'plant',
        imageUrl: 'https://picsum.photos/seed/moneyplant/800/600'
      },
      {
        name: 'Bonsai Tree',
        description: 'Miniature bonsai tree in a traditional pot',
        price: 1999,
        costPrice: 1000,
        unit: 'plant',
        imageUrl: 'https://picsum.photos/seed/bonsai/800/600'
      }
    ],
    'Combos': [
      {
        name: 'Cake and Flowers Combo',
        description: 'Chocolate cake with a bouquet of roses',
        price: 1699,
        costPrice: 850,
        unit: 'combo',
        imageUrl: 'https://picsum.photos/seed/cakeflowers/800/600'
      },
      {
        name: 'Chocolate and Teddy Combo',
        description: 'Box of chocolates with a cute teddy bear',
        price: 1299,
        costPrice: 650,
        unit: 'combo',
        imageUrl: 'https://picsum.photos/seed/chocteddy/800/600'
      }
    ],
    'International': [
      {
        name: 'Belgian Chocolate Truffles',
        description: 'Imported Belgian chocolate truffles',
        price: 1499,
        costPrice: 750,
        unit: 'box',
        imageUrl: 'https://picsum.photos/seed/belgiantruffles/800/600'
      },
      {
        name: 'Swiss Watch',
        description: 'Elegant Swiss-made wristwatch',
        price: 4999,
        costPrice: 2500,
        unit: 'piece',
        imageUrl: 'https://picsum.photos/seed/swisswatch/800/600'
      }
    ],
    'Occasions': [
      {
        name: 'Diwali Gift Hamper',
        description: 'Festive hamper with sweets, candles, and decorations',
        price: 1999,
        costPrice: 1000,
        unit: 'hamper',
        imageUrl: 'https://picsum.photos/seed/diwalihamper/800/600'
      },
      {
        name: 'Christmas Gift Box',
        description: 'Christmas-themed gift box with goodies',
        price: 1799,
        costPrice: 900,
        unit: 'box',
        imageUrl: 'https://picsum.photos/seed/christmasbox/800/600'
      }
    ],
    'Birthday Gifts': [
      {
        name: 'Birthday Gift Hamper',
        description: 'Complete birthday gift hamper with cake, chocolates, and more',
        price: 2499,
        costPrice: 1250,
        unit: 'hamper',
        imageUrl: 'https://picsum.photos/seed/birthdayhamper/800/600'
      },
      {
        name: 'Personalized Birthday Frame',
        description: 'Custom photo frame with birthday wishes',
        price: 899,
        costPrice: 450,
        unit: 'piece',
        imageUrl: 'https://picsum.photos/seed/birthdayframe/800/600'
      }
    ],
    'Anniversary Gifts': [
      {
        name: 'Anniversary Gift Basket',
        description: 'Luxury gift basket for anniversary celebrations',
        price: 2999,
        costPrice: 1500,
        unit: 'basket',
        imageUrl: 'https://picsum.photos/seed/annibasket/800/600'
      },
      {
        name: 'Couple Watch Set',
        description: 'Matching watches for couples',
        price: 3999,
        costPrice: 2000,
        unit: 'set',
        imageUrl: 'https://picsum.photos/seed/couplewatches/800/600'
      }
    ],
    'Gifts for Her': [
      {
        name: 'Perfume Gift Set',
        description: 'Luxury perfume set with body lotion',
        price: 1999,
        costPrice: 1000,
        unit: 'set',
        imageUrl: 'https://picsum.photos/seed/perfumeset/800/600'
      },
      {
        name: 'Jewelry Box',
        description: 'Elegant jewelry box with mirror',
        price: 1499,
        costPrice: 750,
        unit: 'piece',
        imageUrl: 'https://picsum.photos/seed/jewelrybox/800/600'
      }
    ]
  };

  // Get products for the specified category
  const productsToCreate = productsByCategory[category] || [];
  
  if (productsToCreate.length === 0) {
    console.log(`No products defined for category '${category}'`);
    return [];
  }

  // Check if products already exist in this category
  const existingProducts = await prisma.product.findMany({
    where: { category }
  });

  if (existingProducts.length > 0) {
    console.log(`${existingProducts.length} products already exist in category '${category}', skipping creation`);
    return existingProducts;
  }

  // Create products for this category
  const createdProducts = [];
  for (const productData of productsToCreate) {
    const product = await prisma.product.create({
      data: {
        ...productData,
        category,
        lowStockThreshold: 5,
        metaTitle: `${productData.name} | Bakery Shop`,
        metaDescription: productData.description,
        isActive: true
      }
    });
    createdProducts.push(product);

    // Create product images
    await prisma.productImage.create({
      data: {
        productId: product.id,
        url: productData.imageUrl,
        alt: productData.name,
        isMain: true,
        sortOrder: 0
      }
    });
  }

  return createdProducts;
}

// Run the function
seedCategories();
