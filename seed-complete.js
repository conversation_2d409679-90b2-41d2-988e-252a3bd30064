// This script seeds the database with comprehensive sample data
const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function seedCompleteData() {
  try {
    console.log('Seeding comprehensive data...');

    // Create or update stores
    const stores = await createStores();
    console.log(`Created/Updated ${stores.length} stores`);

    // Create or update warehouses
    const warehouses = await createWarehouses();
    console.log(`Created/Updated ${warehouses.length} warehouses`);

    // Create or update products with SEO fields
    const products = await updateProducts();
    console.log(`Updated ${products.length} products with SEO fields`);

    // Create raw materials
    const rawMaterials = await createRawMaterials();
    console.log(`Created ${rawMaterials.length} raw materials`);

    // Link products to raw materials
    const productRawMaterials = await createProductRawMaterials(products, rawMaterials);
    console.log(`Created ${productRawMaterials.length} product-raw material links`);

    // Create warehouse inventory
    const warehouseInventory = await createWarehouseInventory(warehouses, products);
    console.log(`Created warehouse inventory entries for ${warehouseInventory.length} products`);

    // Create store inventory
    const storeInventory = await createStoreInventory(stores, products);
    console.log(`Created store inventory entries for ${storeInventory.length} products`);

    // Create recipes
    const recipes = await createRecipes(rawMaterials);
    console.log(`Created ${recipes.length} recipes`);

    // Create bank accounts
    const bankAccounts = await createBankAccounts();
    console.log(`Created ${bankAccounts.length} bank accounts`);

    // Create bank transactions
    const bankTransactions = await createBankTransactions(bankAccounts);
    console.log(`Created ${bankTransactions.length} bank transactions`);

    // Create expenses
    const expenses = await createExpenses(stores);
    console.log(`Created ${expenses.length} expenses`);

    // Create wastage records
    const wastage = await createWastage(products);
    console.log(`Created ${wastage.length} wastage records`);

    // Create API integrations
    const apiIntegrations = await createApiIntegrations();
    console.log(`Created ${apiIntegrations.length} API integrations`);

    // Create system settings
    const systemSettings = await createSystemSettings();
    console.log(`Created ${systemSettings.length} system settings`);

    console.log('Comprehensive data seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding comprehensive data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function createStores() {
  // Check if stores already exist
  const existingStoreCount = await prisma.store.count();

  if (existingStoreCount > 0) {
    console.log(`${existingStoreCount} stores already exist, updating them`);
    const existingStores = await prisma.store.findMany();
    return existingStores;
  }

  const storeData = [
    {
      name: 'Main Bakery',
      location: 'Mumbai, Maharashtra'
    },
    {
      name: 'Downtown Branch',
      location: 'Delhi, Delhi'
    },
    {
      name: 'Suburban Outlet',
      location: 'Bangalore, Karnataka'
    }
  ];

  const stores = [];
  for (const data of storeData) {
    const store = await prisma.store.create({
      data
    });
    stores.push(store);
  }

  return stores;
}

async function createWarehouses() {
  // Check if warehouses already exist
  const existingWarehouseCount = await prisma.warehouse.count();

  if (existingWarehouseCount > 0) {
    console.log(`${existingWarehouseCount} warehouses already exist, updating them`);
    const existingWarehouses = await prisma.warehouse.findMany();
    return existingWarehouses;
  }

  const warehouseData = [
    {
      name: 'Main Warehouse',
      location: 'Mumbai, Maharashtra'
    },
    {
      name: 'North Distribution Center',
      location: 'Delhi, Delhi'
    },
    {
      name: 'South Storage Facility',
      location: 'Bangalore, Karnataka'
    }
  ];

  const warehouses = [];
  for (const data of warehouseData) {
    const warehouse = await prisma.warehouse.create({
      data
    });
    warehouses.push(warehouse);
  }

  return warehouses;
}

async function updateProducts() {
  // Get existing products
  const existingProducts = await prisma.product.findMany();

  if (existingProducts.length === 0) {
    console.log('No products found, creating sample products');

    const productData = [
      {
        name: 'Chocolate Cake',
        description: 'Rich chocolate cake with chocolate ganache',
        category: 'Cakes',
        price: 650,
        costPrice: 350,
        unit: 'cake',
        lowStockThreshold: 5,
        metaTitle: 'Delicious Chocolate Cake | Bakery Shop',
        metaDescription: 'Our rich chocolate cake is made with premium cocoa and topped with smooth chocolate ganache. Perfect for celebrations!',
        isActive: true
      },
      {
        name: 'Whole Wheat Bread',
        description: 'Healthy whole wheat bread loaf',
        category: 'Bread',
        price: 120,
        costPrice: 60,
        unit: 'loaf',
        lowStockThreshold: 10,
        metaTitle: 'Healthy Whole Wheat Bread | Bakery Shop',
        metaDescription: 'Nutritious whole wheat bread made with organic flour. High in fiber and perfect for a healthy breakfast.',
        isActive: true
      },
      {
        name: 'Fruit Tart',
        description: 'Buttery tart shell filled with custard and fresh fruits',
        category: 'Pastries',
        price: 180,
        costPrice: 90,
        unit: 'piece',
        lowStockThreshold: 8,
        metaTitle: 'Fresh Fruit Tart | Bakery Shop',
        metaDescription: 'Delicious fruit tart with buttery crust, smooth custard, and topped with seasonal fresh fruits.',
        isActive: true
      },
      {
        name: 'Almond Croissant',
        description: 'Flaky croissant filled with almond cream',
        category: 'Pastries',
        price: 150,
        costPrice: 75,
        unit: 'piece',
        lowStockThreshold: 8,
        metaTitle: 'Almond Croissant | Bakery Shop',
        metaDescription: 'Flaky, buttery croissant filled with rich almond cream and topped with sliced almonds.',
        isActive: true
      },
      {
        name: 'Chocolate Chip Cookies',
        description: 'Classic cookies with chocolate chips',
        category: 'Cookies',
        price: 30,
        costPrice: 15,
        unit: 'piece',
        lowStockThreshold: 20,
        metaTitle: 'Chocolate Chip Cookies | Bakery Shop',
        metaDescription: 'Classic chocolate chip cookies made with premium chocolate chips. Soft on the inside and crispy on the outside.',
        isActive: true
      }
    ];

    const products = [];
    for (const data of productData) {
      const product = await prisma.product.create({
        data
      });
      products.push(product);
    }

    return products;
  }

  // Update existing products with SEO fields if they don't have them
  const updatedProducts = [];
  for (const product of existingProducts) {
    if (!product.metaTitle || !product.metaDescription) {
      const updatedProduct = await prisma.product.update({
        where: { id: product.id },
        data: {
          metaTitle: product.metaTitle || `${product.name} | Bakery Shop`,
          metaDescription: product.metaDescription || `${product.description || 'Delicious ' + product.name} from our bakery. Made with premium ingredients.`,
          isActive: product.isActive !== undefined ? product.isActive : true
        }
      });
      updatedProducts.push(updatedProduct);
    } else {
      updatedProducts.push(product);
    }
  }

  return updatedProducts;
}

async function createRawMaterials() {
  // Check if raw materials already exist
  const existingRawMaterialCount = await prisma.rawMaterial.count();

  if (existingRawMaterialCount > 0) {
    console.log(`${existingRawMaterialCount} raw materials already exist, skipping creation`);
    return await prisma.rawMaterial.findMany();
  }

  const rawMaterialData = [
    {
      name: 'All-Purpose Flour',
      unit: 'kg',
      costPerUnit: 40,
      currentStock: 100,
      lowStockThreshold: 20
    },
    {
      name: 'Whole Wheat Flour',
      unit: 'kg',
      costPerUnit: 50,
      currentStock: 80,
      lowStockThreshold: 15
    },
    {
      name: 'Sugar',
      unit: 'kg',
      costPerUnit: 45,
      currentStock: 90,
      lowStockThreshold: 20
    },
    {
      name: 'Butter',
      unit: 'kg',
      costPerUnit: 400,
      currentStock: 50,
      lowStockThreshold: 10
    },
    {
      name: 'Eggs',
      unit: 'dozen',
      costPerUnit: 72,
      currentStock: 40,
      lowStockThreshold: 10
    },
    {
      name: 'Milk',
      unit: 'liter',
      costPerUnit: 60,
      currentStock: 60,
      lowStockThreshold: 15
    },
    {
      name: 'Chocolate',
      unit: 'kg',
      costPerUnit: 500,
      currentStock: 30,
      lowStockThreshold: 5
    },
    {
      name: 'Vanilla Extract',
      unit: 'bottle',
      costPerUnit: 150,
      currentStock: 20,
      lowStockThreshold: 5
    },
    {
      name: 'Baking Powder',
      unit: 'kg',
      costPerUnit: 200,
      currentStock: 15,
      lowStockThreshold: 3
    },
    {
      name: 'Yeast',
      unit: 'kg',
      costPerUnit: 300,
      currentStock: 10,
      lowStockThreshold: 2
    }
  ];

  const rawMaterials = [];
  for (const data of rawMaterialData) {
    const rawMaterial = await prisma.rawMaterial.create({
      data
    });
    rawMaterials.push(rawMaterial);
  }

  return rawMaterials;
}

async function createProductRawMaterials(products, rawMaterials) {
  // Check if product raw materials already exist
  const existingCount = await prisma.productRawMaterial.count();

  if (existingCount > 0) {
    console.log(`${existingCount} product raw materials already exist, skipping creation`);
    return await prisma.productRawMaterial.findMany();
  }

  const productRawMaterialLinks = [];

  // Map of product categories to raw materials they typically use
  const categoryToRawMaterials = {
    'Cakes': ['All-Purpose Flour', 'Sugar', 'Butter', 'Eggs', 'Milk', 'Baking Powder', 'Vanilla Extract'],
    'Bread': ['All-Purpose Flour', 'Whole Wheat Flour', 'Yeast', 'Sugar', 'Butter'],
    'Pastries': ['All-Purpose Flour', 'Butter', 'Sugar', 'Eggs'],
    'Cookies': ['All-Purpose Flour', 'Sugar', 'Butter', 'Eggs', 'Chocolate', 'Vanilla Extract']
  };

  // For each product, assign appropriate raw materials
  for (const product of products) {
    const category = product.category;
    const relevantRawMaterials = categoryToRawMaterials[category] || ['All-Purpose Flour', 'Sugar', 'Butter'];

    // Find the raw material objects by name
    const rawMaterialsForProduct = rawMaterials.filter(rm =>
      relevantRawMaterials.includes(rm.name)
    );

    // Create links between product and raw materials
    for (const rawMaterial of rawMaterialsForProduct) {
      try {
        const link = await prisma.productRawMaterial.create({
          data: {
            productId: product.id,
            rawMaterialId: rawMaterial.id,
            quantityRequired: Math.random() * 2 + 0.1 // Random quantity between 0.1 and 2.1
          }
        });
        productRawMaterialLinks.push(link);
      } catch (error) {
        console.log(`Skipping duplicate product-raw material link: ${product.name} - ${rawMaterial.name}`);
      }
    }
  }

  return productRawMaterialLinks;
}

async function createWarehouseInventory(warehouses, products) {
  // Check if warehouse inventory already exists
  const existingCount = await prisma.warehouseInventory.count();

  if (existingCount > 0) {
    console.log(`${existingCount} warehouse inventory entries already exist, skipping creation`);
    return await prisma.warehouseInventory.findMany();
  }

  const inventoryEntries = [];

  // For each warehouse, create inventory for each product
  for (const warehouse of warehouses) {
    for (const product of products) {
      try {
        const entry = await prisma.warehouseInventory.create({
          data: {
            warehouseId: warehouse.id,
            productId: product.id,
            quantity: Math.floor(Math.random() * 100) + 20 // Random quantity between 20 and 119
          }
        });
        inventoryEntries.push(entry);
      } catch (error) {
        console.log(`Skipping duplicate warehouse inventory entry: ${warehouse.name} - ${product.name}`);
      }
    }
  }

  return inventoryEntries;
}

async function createStoreInventory(stores, products) {
  // Check if store inventory already exists
  const existingCount = await prisma.storeInventory.count();

  if (existingCount > 0) {
    console.log(`${existingCount} store inventory entries already exist, skipping creation`);
    return await prisma.storeInventory.findMany();
  }

  const inventoryEntries = [];

  // For each store, create inventory for each product
  for (const store of stores) {
    for (const product of products) {
      try {
        const entry = await prisma.storeInventory.create({
          data: {
            storeId: store.id,
            productId: product.id,
            quantity: Math.floor(Math.random() * 50) + 5 // Random quantity between 5 and 54
          }
        });
        inventoryEntries.push(entry);
      } catch (error) {
        console.log(`Skipping duplicate store inventory entry: ${store.name} - ${product.name}`);
      }
    }
  }

  return inventoryEntries;
}

async function createRecipes(rawMaterials) {
  // Check if recipes already exist
  const existingCount = await prisma.recipe.count();

  if (existingCount > 0) {
    console.log(`${existingCount} recipes already exist, skipping creation`);
    return await prisma.recipe.findMany();
  }

  const recipeData = [
    {
      name: 'Classic Chocolate Cake',
      description: 'Rich and moist chocolate cake with chocolate ganache',
      preparationTime: 30,
      bakingTime: 45,
      restingTime: 60,
      yield: 1,
      yieldUnit: 'cake',
      difficulty: 'Medium',
      category: 'Cakes',
      notes: 'Can be stored in refrigerator for up to 3 days',
      imageUrl: 'https://picsum.photos/seed/choccake/800/600',
      costPerUnit: 350,
      sellingPrice: 650,
      profitMargin: 85.71,
      ingredients: [
        { name: 'All-Purpose Flour', quantity: 2, unit: 'cups' },
        { name: 'Sugar', quantity: 1.5, unit: 'cups' },
        { name: 'Cocoa Powder', quantity: 0.75, unit: 'cup' },
        { name: 'Butter', quantity: 0.5, unit: 'cup' },
        { name: 'Eggs', quantity: 3, unit: 'pieces' },
        { name: 'Milk', quantity: 1, unit: 'cup' },
        { name: 'Vanilla Extract', quantity: 1, unit: 'tsp' },
        { name: 'Baking Powder', quantity: 1.5, unit: 'tsp' }
      ],
      instructions: [
        { stepNumber: 1, text: 'Preheat oven to 350°F (175°C)' },
        { stepNumber: 2, text: 'Grease and flour two 9-inch round cake pans' },
        { stepNumber: 3, text: 'In a large bowl, mix flour, sugar, cocoa powder, and baking powder' },
        { stepNumber: 4, text: 'Add butter, eggs, milk, and vanilla extract' },
        { stepNumber: 5, text: 'Beat for 2 minutes on medium speed' },
        { stepNumber: 6, text: 'Pour batter into prepared pans' },
        { stepNumber: 7, text: 'Bake for 30-35 minutes or until a toothpick inserted comes out clean' },
        { stepNumber: 8, text: 'Cool in pans for 10 minutes, then remove to wire racks' },
        { stepNumber: 9, text: 'Prepare ganache by heating cream and pouring over chocolate' },
        { stepNumber: 10, text: 'Frost cake when completely cool' }
      ],
      tips: [
        { text: 'For extra moisture, add 1/4 cup of sour cream to the batter' },
        { text: 'Use room temperature ingredients for best results' }
      ],
      tags: [
        { name: 'Chocolate' },
        { name: 'Dessert' },
        { name: 'Birthday' }
      ],
      nutritionInfo: {
        calories: 350,
        protein: 5,
        carbs: 45,
        fat: 18,
        fiber: 2,
        sugar: 30
      }
    },
    {
      name: 'Whole Wheat Bread',
      description: 'Healthy and hearty whole wheat bread',
      preparationTime: 20,
      bakingTime: 40,
      restingTime: 120,
      yield: 1,
      yieldUnit: 'loaf',
      difficulty: 'Easy',
      category: 'Bread',
      notes: 'Best consumed within 2 days',
      imageUrl: 'https://picsum.photos/seed/wheatbread/800/600',
      costPerUnit: 60,
      sellingPrice: 120,
      profitMargin: 100,
      ingredients: [
        { name: 'Whole Wheat Flour', quantity: 3, unit: 'cups' },
        { name: 'All-Purpose Flour', quantity: 1, unit: 'cup' },
        { name: 'Yeast', quantity: 2.25, unit: 'tsp' },
        { name: 'Salt', quantity: 1, unit: 'tsp' },
        { name: 'Honey', quantity: 2, unit: 'tbsp' },
        { name: 'Butter', quantity: 2, unit: 'tbsp' },
        { name: 'Warm Water', quantity: 1.5, unit: 'cups' }
      ],
      instructions: [
        { stepNumber: 1, text: 'Dissolve yeast in warm water with honey' },
        { stepNumber: 2, text: 'Let stand until creamy, about 10 minutes' },
        { stepNumber: 3, text: 'Mix in butter, salt, and flours' },
        { stepNumber: 4, text: 'Knead dough on floured surface for 10 minutes' },
        { stepNumber: 5, text: 'Place in greased bowl, cover, and let rise for 1 hour' },
        { stepNumber: 6, text: 'Punch down dough and shape into a loaf' },
        { stepNumber: 7, text: 'Place in greased loaf pan and let rise for 30 minutes' },
        { stepNumber: 8, text: 'Bake at 375°F (190°C) for 30-40 minutes' }
      ],
      tips: [
        { text: 'For a crustier bread, place a pan of water in the oven while baking' },
        { text: 'Brush the top with butter after baking for a softer crust' }
      ],
      tags: [
        { name: 'Whole Wheat' },
        { name: 'Healthy' },
        { name: 'Bread' }
      ],
      nutritionInfo: {
        calories: 120,
        protein: 4,
        carbs: 22,
        fat: 2,
        fiber: 3,
        sugar: 1
      }
    }
  ];

  const recipes = [];
  for (const data of recipeData) {
    const { ingredients, instructions, tips, tags, nutritionInfo, ...recipeData } = data;

    const recipe = await prisma.recipe.create({
      data: {
        ...recipeData,
        ingredients: {
          create: ingredients.map(ing => ({
            name: ing.name,
            quantity: ing.quantity,
            unit: ing.unit,
            cost: rawMaterials.find(rm => rm.name === ing.name)?.costPerUnit || null,
            isOptional: false
          }))
        },
        instructions: {
          create: instructions
        },
        tips: {
          create: tips
        },
        tags: {
          create: tags
        },
        nutritionInfo: {
          create: nutritionInfo
        }
      },
      include: {
        ingredients: true,
        instructions: true,
        tips: true,
        tags: true,
        nutritionInfo: true
      }
    });

    recipes.push(recipe);
  }

  return recipes;
}

async function createBankAccounts() {
  // Check if bank accounts already exist
  const existingCount = await prisma.bankAccount.count();

  if (existingCount > 0) {
    console.log(`${existingCount} bank accounts already exist, skipping creation`);
    return await prisma.bankAccount.findMany();
  }

  const bankAccountData = [
    {
      bankName: 'HDFC Bank',
      accountNumber: 'HDFC12345678',
      balance: 250000
    },
    {
      bankName: 'ICICI Bank',
      accountNumber: 'ICICI87654321',
      balance: 180000
    },
    {
      bankName: 'SBI',
      accountNumber: 'SBI98765432',
      balance: 320000
    }
  ];

  const bankAccounts = [];
  for (const data of bankAccountData) {
    const bankAccount = await prisma.bankAccount.create({
      data
    });
    bankAccounts.push(bankAccount);
  }

  return bankAccounts;
}

async function createBankTransactions(bankAccounts) {
  // Check if bank transactions already exist
  const existingCount = await prisma.bankTransaction.count();

  if (existingCount > 0) {
    console.log(`${existingCount} bank transactions already exist, skipping creation`);
    return await prisma.bankTransaction.findMany();
  }

  const transactions = [];
  const transactionTypes = ['DEPOSIT', 'WITHDRAWAL', 'TRANSFER'];
  const descriptions = [
    'Supplier payment',
    'Sales deposit',
    'Utility bill payment',
    'Rent payment',
    'Salary payment',
    'Equipment purchase',
    'Tax payment',
    'Loan repayment',
    'Insurance premium'
  ];

  // Create 5 transactions for each bank account
  for (const account of bankAccounts) {
    for (let i = 0; i < 5; i++) {
      const type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const amount = Math.floor(Math.random() * 50000) + 1000; // Random amount between 1000 and 51000
      const description = descriptions[Math.floor(Math.random() * descriptions.length)];
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 30)); // Random date in the last 30 days

      const transaction = await prisma.bankTransaction.create({
        data: {
          bankAccountId: account.id,
          type,
          amount,
          description,
          date
        }
      });
      transactions.push(transaction);
    }
  }

  return transactions;
}

async function createExpenses(stores) {
  // Check if expenses already exist
  const existingCount = await prisma.expense.count();

  if (existingCount > 0) {
    console.log(`${existingCount} expenses already exist, skipping creation`);
    return await prisma.expense.findMany();
  }

  // Get a user for the expenses
  const user = await prisma.user.findFirst({
    where: { role: 'ADMIN' }
  });

  if (!user) {
    console.log('No admin user found for expenses');
    return [];
  }

  const expenseCategories = [
    'Rent',
    'Utilities',
    'Salaries',
    'Ingredients',
    'Equipment',
    'Marketing',
    'Maintenance',
    'Insurance',
    'Taxes',
    'Miscellaneous'
  ];

  const expenses = [];

  // Create 3 expenses for each store
  for (const store of stores) {
    for (let i = 0; i < 3; i++) {
      const category = expenseCategories[Math.floor(Math.random() * expenseCategories.length)];
      const amount = Math.floor(Math.random() * 20000) + 1000; // Random amount between 1000 and 21000
      const description = `${category} expense for ${store.name}`;
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 30)); // Random date in the last 30 days

      const expense = await prisma.expense.create({
        data: {
          storeId: store.id,
          userId: user.id,
          category,
          amount,
          description,
          date
        }
      });
      expenses.push(expense);
    }
  }

  return expenses;
}

async function createWastage(products) {
  // Check if wastage records already exist
  const existingCount = await prisma.wastage.count();

  if (existingCount > 0) {
    console.log(`${existingCount} wastage records already exist, skipping creation`);
    return await prisma.wastage.findMany();
  }

  const wastageReasons = [
    'Expired',
    'Damaged during transport',
    'Quality issues',
    'Production error',
    'Customer return',
    'Display item',
    'Unsold at end of day'
  ];

  const wastageRecords = [];

  // Create 1-2 wastage records for each product
  for (const product of products) {
    const recordCount = Math.floor(Math.random() * 2) + 1; // 1 or 2 records

    for (let i = 0; i < recordCount; i++) {
      const reason = wastageReasons[Math.floor(Math.random() * wastageReasons.length)];
      const quantity = Math.floor(Math.random() * 5) + 1; // Random quantity between 1 and 5
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 14)); // Random date in the last 14 days

      const wastage = await prisma.wastage.create({
        data: {
          productId: product.id,
          quantity,
          reason,
          date
        }
      });
      wastageRecords.push(wastage);
    }
  }

  return wastageRecords;
}

async function createApiIntegrations() {
  // Check if API integrations already exist
  const existingCount = await prisma.apiIntegration.count();

  if (existingCount > 0) {
    console.log(`${existingCount} API integrations already exist, skipping creation`);
    return await prisma.apiIntegration.findMany();
  }

  const apiIntegrationData = [
    {
      type: 'PAYMENT_GATEWAY',
      name: 'Razorpay',
      isActive: true,
      credentials: { apiKey: 'rzp_test_sample', apiSecret: 'sample_secret' },
      settings: { webhookUrl: 'https://example.com/webhook/razorpay' }
    },
    {
      type: 'PAYMENT_GATEWAY',
      name: 'Stripe',
      isActive: false,
      credentials: { apiKey: 'sk_test_sample', publishableKey: 'pk_test_sample' },
      settings: { webhookUrl: 'https://example.com/webhook/stripe' }
    },
    {
      type: 'WHATSAPP',
      name: 'WhatsApp Business API',
      isActive: true,
      credentials: { apiKey: 'whatsapp_sample_key' },
      settings: { templateId: 'order_confirmation' }
    },
    {
      type: 'EMAIL_SERVICE',
      name: 'SendGrid',
      isActive: true,
      credentials: { apiKey: 'sendgrid_sample_key' },
      settings: { fromEmail: '<EMAIL>', fromName: 'Bakery Shop' }
    },
    {
      type: 'SMS_SERVICE',
      name: 'Twilio',
      isActive: false,
      credentials: { accountSid: 'twilio_sample_sid', authToken: 'twilio_sample_token' },
      settings: { fromNumber: '+**********' }
    }
  ];

  const apiIntegrations = [];
  for (const data of apiIntegrationData) {
    const apiIntegration = await prisma.apiIntegration.create({
      data
    });
    apiIntegrations.push(apiIntegration);
  }

  return apiIntegrations;
}

async function createSystemSettings() {
  // Check if system settings already exist
  const existingCount = await prisma.systemSetting.count();

  if (existingCount > 0) {
    console.log(`${existingCount} system settings already exist, skipping creation`);
    return await prisma.systemSetting.findMany();
  }

  const systemSettingData = [
    {
      category: 'general',
      key: 'siteName',
      value: 'Bakery Shop'
    },
    {
      category: 'general',
      key: 'siteDescription',
      value: 'Premium bakery products made with love'
    },
    {
      category: 'general',
      key: 'contactEmail',
      value: '<EMAIL>'
    },
    {
      category: 'general',
      key: 'contactPhone',
      value: '+91 9876543210'
    },
    {
      category: 'general',
      key: 'address',
      value: '123 Bakery Street, Mumbai, Maharashtra'
    },
    {
      category: 'ecommerce',
      key: 'currency',
      value: 'INR'
    },
    {
      category: 'ecommerce',
      key: 'taxRate',
      value: '18'
    },
    {
      category: 'ecommerce',
      key: 'freeShippingThreshold',
      value: '500'
    },
    {
      category: 'ecommerce',
      key: 'shippingFee',
      value: '50'
    },
    {
      category: 'social',
      key: 'facebookUrl',
      value: 'https://facebook.com/bakeryshop'
    },
    {
      category: 'social',
      key: 'instagramUrl',
      value: 'https://instagram.com/bakeryshop'
    },
    {
      category: 'social',
      key: 'twitterUrl',
      value: 'https://twitter.com/bakeryshop'
    }
  ];

  const systemSettings = [];
  for (const data of systemSettingData) {
    const systemSetting = await prisma.systemSetting.create({
      data
    });
    systemSettings.push(systemSetting);
  }

  return systemSettings;
}

// Run the function
seedCompleteData();
