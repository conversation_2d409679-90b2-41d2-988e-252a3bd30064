// This script seeds the database with e-commerce sample data
const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function seedEcommerceData() {
  try {
    console.log('Seeding e-commerce data...');

    // Create customer users
    const customerUsers = await createCustomerUsers();
    console.log(`Created ${customerUsers.length} customer users`);

    // Create customer profiles
    const customers = await createCustomerProfiles(customerUsers);
    console.log(`Created ${customers.length} customer profiles`);

    // Create customer addresses
    const addresses = await createCustomerAddresses(customers);
    console.log(`Created ${addresses.length} customer addresses`);

    // Create product images
    const productImages = await createProductImages();
    console.log(`Created ${productImages.length} product images`);

    // Create product relations
    const productRelations = await createProductRelations();
    console.log(`Created ${productRelations.length} product relations`);

    // Create coupons
    const coupons = await createCoupons();
    console.log(`Created ${coupons.length} coupons`);

    // Create carts
    const carts = await createCarts(customerUsers);
    console.log(`Created ${carts.length} carts`);

    // Create orders (transactions)
    const orders = await createOrders(customerUsers, addresses, coupons);
    console.log(`Created ${orders.length} orders`);

    console.log('E-commerce data seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding e-commerce data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function createCustomerUsers() {
  // Check if customer users already exist
  const existingCustomerCount = await prisma.user.count({
    where: { role: 'CUSTOMER' }
  });

  if (existingCustomerCount > 0) {
    console.log(`${existingCustomerCount} customer users already exist, skipping creation`);
    return await prisma.user.findMany({
      where: { role: 'CUSTOMER' }
    });
  }

  // Create 5 customer users
  const customerData = [
    {
      name: 'Rahul Sharma',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      role: 'CUSTOMER'
    },
    {
      name: 'Priya Patel',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      role: 'CUSTOMER'
    },
    {
      name: 'Amit Kumar',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      role: 'CUSTOMER'
    },
    {
      name: 'Neha Singh',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      role: 'CUSTOMER'
    },
    {
      name: 'Vikram Mehta',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      role: 'CUSTOMER'
    }
  ];

  const customers = [];
  for (const data of customerData) {
    const customer = await prisma.user.create({
      data
    });
    customers.push(customer);
  }

  return customers;
}

async function createCustomerProfiles(customerUsers) {
  // Check if customer profiles already exist
  const existingCustomerCount = await prisma.customer.count();

  if (existingCustomerCount > 0) {
    console.log(`${existingCustomerCount} customer profiles already exist, skipping creation`);
    return await prisma.customer.findMany();
  }

  const customers = [];
  for (const user of customerUsers) {
    const nameParts = user.name.split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ');

    const customer = await prisma.customer.create({
      data: {
        userId: user.id,
        firstName,
        lastName,
        phone: `+91${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        isSubscribed: Math.random() > 0.5,
        loyaltyPoints: Math.floor(Math.random() * 1000),
        notes: `Sample customer notes for ${user.name}`,
        customerTags: {
          create: [
            { name: 'Regular' },
            { name: Math.random() > 0.5 ? 'VIP' : 'New Customer' }
          ]
        }
      }
    });
    customers.push(customer);
  }

  return customers;
}

async function createCustomerAddresses(customers) {
  // Check if addresses already exist
  const existingAddressCount = await prisma.address.count();

  if (existingAddressCount > 0) {
    console.log(`${existingAddressCount} addresses already exist, skipping creation`);
    return await prisma.address.findMany();
  }

  const indianCities = ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad', 'Jaipur'];
  const indianStates = ['Maharashtra', 'Delhi', 'Karnataka', 'Telangana', 'Tamil Nadu', 'West Bengal', 'Gujarat', 'Rajasthan'];

  const addresses = [];
  for (const customer of customers) {
    // Create 1-3 addresses for each customer
    const addressCount = Math.floor(Math.random() * 3) + 1;
    
    for (let i = 0; i < addressCount; i++) {
      const city = indianCities[Math.floor(Math.random() * indianCities.length)];
      const state = indianStates[Math.floor(Math.random() * indianStates.length)];
      
      const address = await prisma.address.create({
        data: {
          customerId: customer.id,
          type: i === 0 ? 'BOTH' : (Math.random() > 0.5 ? 'SHIPPING' : 'BILLING'),
          isDefault: i === 0, // First address is default
          street: `${Math.floor(Math.random() * 100) + 1}, ${['Park Street', 'MG Road', 'Gandhi Nagar', 'Nehru Place'][Math.floor(Math.random() * 4)]}`,
          city,
          state,
          postalCode: `${Math.floor(Math.random() * 900000) + 100000}`,
          country: 'India'
        }
      });
      addresses.push(address);
    }
  }

  return addresses;
}

async function createProductImages() {
  // Check if product images already exist
  const existingImageCount = await prisma.productImage.count();

  if (existingImageCount > 0) {
    console.log(`${existingImageCount} product images already exist, skipping creation`);
    return await prisma.productImage.findMany();
  }

  // Get all products
  const products = await prisma.product.findMany();
  
  const productImages = [];
  for (const product of products) {
    // Create 1-4 images for each product
    const imageCount = Math.floor(Math.random() * 4) + 1;
    
    for (let i = 0; i < imageCount; i++) {
      const productImage = await prisma.productImage.create({
        data: {
          productId: product.id,
          url: `https://picsum.photos/seed/${product.id}${i}/800/600`,
          alt: `${product.name} image ${i+1}`,
          isMain: i === 0, // First image is main
          sortOrder: i
        }
      });
      productImages.push(productImage);
    }
  }

  return productImages;
}

async function createProductRelations() {
  // Check if product relations already exist
  const existingRelationCount = await prisma.productRelation.count();

  if (existingRelationCount > 0) {
    console.log(`${existingRelationCount} product relations already exist, skipping creation`);
    return await prisma.productRelation.findMany();
  }

  // Get all products
  const products = await prisma.product.findMany();
  
  const productRelations = [];
  for (const product of products) {
    // Create 0-3 related products for each product
    const relationCount = Math.floor(Math.random() * 4);
    
    for (let i = 0; i < relationCount; i++) {
      // Select a random product that is not the current product
      const relatedProducts = products.filter(p => p.id !== product.id);
      if (relatedProducts.length === 0) continue;
      
      const relatedProduct = relatedProducts[Math.floor(Math.random() * relatedProducts.length)];
      
      try {
        const productRelation = await prisma.productRelation.create({
          data: {
            productId: product.id,
            relatedProductId: relatedProduct.id,
            relationType: Math.random() > 0.7 ? 'upsell' : 'related'
          }
        });
        productRelations.push(productRelation);
      } catch (error) {
        // Skip if relation already exists (unique constraint violation)
        console.log(`Skipping duplicate relation between ${product.id} and ${relatedProduct.id}`);
      }
    }
  }

  return productRelations;
}

async function createCoupons() {
  // Check if coupons already exist
  const existingCouponCount = await prisma.coupon.count();

  if (existingCouponCount > 0) {
    console.log(`${existingCouponCount} coupons already exist, skipping creation`);
    return await prisma.coupon.findMany();
  }

  const couponData = [
    {
      code: 'WELCOME10',
      description: '10% off on your first order',
      discountType: 'percentage',
      discountValue: 10,
      minOrderValue: 500,
      maxUses: 100,
      usedCount: 45,
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
      isActive: true
    },
    {
      code: 'SUMMER25',
      description: '25% off on summer collection',
      discountType: 'percentage',
      discountValue: 25,
      minOrderValue: 1000,
      maxUses: 50,
      usedCount: 12,
      startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
      endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
      isActive: true
    },
    {
      code: 'FLAT200',
      description: 'Flat ₹200 off on orders above ₹1500',
      discountType: 'fixed',
      discountValue: 200,
      minOrderValue: 1500,
      maxUses: 200,
      usedCount: 78,
      startDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
      endDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
      isActive: true
    }
  ];

  const coupons = [];
  for (const data of couponData) {
    const coupon = await prisma.coupon.create({
      data
    });
    coupons.push(coupon);
  }

  return coupons;
}

async function createCarts(customerUsers) {
  // Check if carts already exist
  const existingCartCount = await prisma.cart.count();

  if (existingCartCount > 0) {
    console.log(`${existingCartCount} carts already exist, skipping creation`);
    return await prisma.cart.findMany({
      include: { items: true }
    });
  }

  // Get all products
  const products = await prisma.product.findMany();
  
  const carts = [];
  for (const user of customerUsers) {
    // Create a cart for each customer
    const cart = await prisma.cart.create({
      data: {
        userId: user.id
      }
    });
    
    // Add 1-5 items to the cart
    const itemCount = Math.floor(Math.random() * 5) + 1;
    
    for (let i = 0; i < itemCount; i++) {
      const product = products[Math.floor(Math.random() * products.length)];
      
      await prisma.cartItem.create({
        data: {
          cartId: cart.id,
          productId: product.id,
          quantity: Math.floor(Math.random() * 3) + 1
        }
      });
    }
    
    // Fetch the cart with items
    const cartWithItems = await prisma.cart.findUnique({
      where: { id: cart.id },
      include: { items: true }
    });
    
    carts.push(cartWithItems);
  }

  return carts;
}

async function createOrders(customerUsers, addresses, coupons) {
  // Check if orders (transactions of type SALE) already exist
  const existingOrderCount = await prisma.transaction.count({
    where: { type: 'SALE' }
  });

  if (existingOrderCount > 0) {
    console.log(`${existingOrderCount} orders already exist, skipping creation`);
    return await prisma.transaction.findMany({
      where: { type: 'SALE' },
      include: { items: true }
    });
  }

  // Get all products
  const products = await prisma.product.findMany();
  
  // Get a store for the orders
  const store = await prisma.store.findFirst();
  if (!store) {
    console.log('No store found, creating a default store');
    await prisma.store.create({
      data: {
        name: 'Main Bakery Store',
        location: 'Mumbai, India'
      }
    });
  }
  
  const orders = [];
  for (const user of customerUsers) {
    // Create 1-3 orders for each customer
    const orderCount = Math.floor(Math.random() * 3) + 1;
    
    // Get customer addresses
    const customerAddresses = addresses.filter(addr => addr.customerId === user.customer.id);
    if (customerAddresses.length === 0) continue;
    
    for (let i = 0; i < orderCount; i++) {
      // Select random addresses for shipping and billing
      const shippingAddress = customerAddresses[Math.floor(Math.random() * customerAddresses.length)];
      const billingAddress = Math.random() > 0.7 ? 
        customerAddresses[Math.floor(Math.random() * customerAddresses.length)] : 
        shippingAddress;
      
      // Select a random coupon or no coupon
      const coupon = Math.random() > 0.7 ? coupons[Math.floor(Math.random() * coupons.length)] : null;
      
      // Create 1-5 order items
      const itemCount = Math.floor(Math.random() * 5) + 1;
      const items = [];
      let subtotal = 0;
      
      for (let j = 0; j < itemCount; j++) {
        const product = products[Math.floor(Math.random() * products.length)];
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = product.price;
        const totalPrice = unitPrice * quantity;
        
        items.push({
          productId: product.id,
          quantity,
          unitPrice,
          totalPrice
        });
        
        subtotal += totalPrice;
      }
      
      // Calculate discount
      const discount = coupon ? 
        (coupon.discountType === 'percentage' ? 
          (subtotal * coupon.discountValue / 100) : 
          coupon.discountValue) : 
        0;
      
      // Calculate total
      const total = subtotal - discount;
      
      // Create the order
      const order = await prisma.transaction.create({
        data: {
          type: 'SALE',
          storeId: store?.id,
          userId: user.id,
          partyName: user.name,
          partyContact: user.email,
          totalAmount: total,
          discount,
          paymentMethod: ['CARD', 'ONLINE', 'RAZORPAY', 'CASH'][Math.floor(Math.random() * 4)],
          status: 'COMPLETED',
          orderNumber: `ORD-${Date.now().toString().slice(-6)}-${Math.floor(Math.random() * 1000)}`,
          shippingAddressId: shippingAddress.id,
          billingAddressId: billingAddress.id,
          trackingNumber: Math.random() > 0.5 ? `TRK${Math.floor(Math.random() * 1000000)}` : null,
          paymentStatus: ['PENDING', 'PAID'][Math.floor(Math.random() * 2)],
          orderStatus: ['PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED'][Math.floor(Math.random() * 4)],
          couponId: coupon?.id,
          notes: Math.random() > 0.7 ? `Sample order notes for ${user.name}` : null,
          items: {
            create: items
          }
        }
      });
      
      orders.push(order);
    }
  }

  return orders;
}

// Run the function
seedEcommerceData();
