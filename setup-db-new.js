// This script sets up the database with the updated connection string
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function setupDatabase() {
  console.log('Setting up the database with the updated connection string...');

  try {
    // 1. Update the .env file if needed
    const envPath = path.join(__dirname, '.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    if (!envContent.includes('schema=bakery')) {
      console.log('\n1. Updating .env file to include schema=bakery...');
      envContent = envContent.replace(
        /DATABASE_URL="([^"]+)"/,
        (match, url) => {
          // Add schema=bakery if it doesn't exist
          const newUrl = url.includes('?') 
            ? url.replace('?', '?schema=bakery&') 
            : `${url}?schema=bakery`;
          return `DATABASE_URL="${newUrl}"`;
        }
      );
      
      fs.writeFileSync(envPath, envContent);
      console.log('Updated .env file successfully.');
    } else {
      console.log('\n1. .env file already includes schema=bakery.');
    }

    // 2. Generate Prisma client
    console.log('\n2. Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });

    // 3. Push the schema to the database
    console.log('\n3. Creating database schema...');
    execSync('npx prisma db push', { stdio: 'inherit' });

    // 4. Create admin user
    console.log('\n4. Creating admin user...');
    execSync('node create-admin-sql.js', { stdio: 'inherit' });

    console.log('\nDatabase setup completed successfully!');
    console.log('\nYou can now run the application with:');
    console.log('npm run dev');
  } catch (error) {
    console.error('Error setting up the database:', error);
    process.exit(1);
  }
}

// Run the function
setupDatabase();
