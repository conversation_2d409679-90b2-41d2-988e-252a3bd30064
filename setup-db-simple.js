const { execSync } = require('child_process');

async function setupDatabase() {
  console.log('Setting up the database...');

  try {
    // 1. Generate Prisma client
    console.log('\n1. Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });

    // 2. Push the schema to the database (this creates the tables without migrations)
    console.log('\n2. Creating database schema...');
    execSync('npx prisma db push', { stdio: 'inherit' });

    // 3. Create admin user directly
    console.log('\n3. Creating admin user...');
    execSync('node create-admin.js', { stdio: 'inherit' });

    console.log('\nDatabase setup completed successfully!');
    console.log('\nYou can now run the application with:');
    console.log('npm run dev');
  } catch (error) {
    console.error('Error setting up the database:', error);
    process.exit(1);
  }
}

setupDatabase();
