const { execSync } = require('child_process');

console.log('Setting up the database...');

try {
  // Generate Prisma client
  console.log('\n1. Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  // Reset database (drop, create, apply migrations)
  console.log('\n2. Resetting database...');
  execSync('npx prisma migrate reset --force', { stdio: 'inherit' });

  // Note: The reset command already runs the seed script automatically

  console.log('\nDatabase setup completed successfully!');
  console.log('\nYou can now run the application with:');
  console.log('npm run dev');
} catch (error) {
  console.error('Error setting up the database:', error.message);
  process.exit(1);
}
