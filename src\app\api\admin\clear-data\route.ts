import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const { type, confirm } = await request.json();
    
    if (!confirm || confirm !== 'YES_DELETE_ALL_DATA') {
      return NextResponse.json(
        { error: 'Confirmation required. Send { "confirm": "YES_DELETE_ALL_DATA" }' },
        { status: 400 }
      );
    }

    console.log('🗑️  Starting database cleanup via API...');
    
    if (type === 'all') {
      // Delete ALL data
      console.log('Deleting ALL data...');
      
      // Delete in correct order to avoid foreign key constraints
      await prisma.transactionItem.deleteMany({});
      await prisma.transaction.deleteMany({});
      await prisma.transferItem.deleteMany({});
      await prisma.transfer.deleteMany({});
      await prisma.inventoryTransfer.deleteMany({});
      await prisma.warehouseInventory.deleteMany({});
      await prisma.storeInventory.deleteMany({});
      await prisma.rawMaterialConsumption.deleteMany({});
      await prisma.production.deleteMany({});
      await prisma.productRawMaterial.deleteMany({});
      await prisma.rawMaterial.deleteMany({});
      await prisma.wastage.deleteMany({});
      await prisma.product_reviews.deleteMany({});
      await prisma.wishlist_items.deleteMany({});
      await prisma.recipeNutrition.deleteMany({});
      await prisma.recipeTip.deleteMany({});
      await prisma.recipeInstruction.deleteMany({});
      await prisma.recipeIngredient.deleteMany({});
      await prisma.recipeTag.deleteMany({});
      await prisma.recipe.deleteMany({});
      await prisma.customerTag.deleteMany({});
      await prisma.address.deleteMany({});
      await prisma.customer.deleteMany({});
      await prisma.bankTransaction.deleteMany({});
      await prisma.bankAccount.deleteMany({});
      await prisma.expense.deleteMany({});
      await prisma.customerVisit.deleteMany({});
      await prisma.product.deleteMany({});
      await prisma.category.deleteMany({});
      await prisma.store.deleteMany({});
      await prisma.warehouse.deleteMany({});
      await prisma.user.deleteMany({});
      await prisma.apiIntegration.deleteMany({});
      await prisma.systemSetting.deleteMany({});
      
      console.log('✅ ALL data deleted successfully!');
      
    } else {
      // Delete only main data (preserve users, warehouses, stores, categories)
      console.log('Deleting main data only...');
      
      await prisma.transactionItem.deleteMany({});
      await prisma.transaction.deleteMany({});
      await prisma.transferItem.deleteMany({});
      await prisma.transfer.deleteMany({});
      await prisma.inventoryTransfer.deleteMany({});
      await prisma.warehouseInventory.deleteMany({});
      await prisma.storeInventory.deleteMany({});
      await prisma.rawMaterialConsumption.deleteMany({});
      await prisma.production.deleteMany({});
      await prisma.productRawMaterial.deleteMany({});
      await prisma.rawMaterial.deleteMany({});
      await prisma.wastage.deleteMany({});
      await prisma.product_reviews.deleteMany({});
      await prisma.wishlist_items.deleteMany({});
      await prisma.recipeNutrition.deleteMany({});
      await prisma.recipeTip.deleteMany({});
      await prisma.recipeInstruction.deleteMany({});
      await prisma.recipeIngredient.deleteMany({});
      await prisma.recipeTag.deleteMany({});
      await prisma.recipe.deleteMany({});
      await prisma.customerTag.deleteMany({});
      await prisma.address.deleteMany({});
      await prisma.customer.deleteMany({});
      await prisma.bankTransaction.deleteMany({});
      await prisma.bankAccount.deleteMany({});
      await prisma.expense.deleteMany({});
      await prisma.customerVisit.deleteMany({});
      await prisma.product.deleteMany({});
      
      console.log('✅ Main data deleted successfully!');
    }

    return NextResponse.json({ 
      success: true, 
      message: type === 'all' ? 'All data deleted successfully' : 'Main data deleted successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error clearing database:', error);
    return NextResponse.json(
      { error: 'Failed to clear database', details: error.message },
      { status: 500 }
    );
  }
}
