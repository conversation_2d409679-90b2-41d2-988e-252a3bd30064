import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const footerPages = [
  {
    slug: 'about-us',
    title: 'About Us',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Welcome to <PERSON>spri</h2>
        <p class="text-lg text-gray-700">
          At Mispri, we believe that every special moment deserves to be celebrated with beauty, love, and the finest quality products.
          We are your trusted partner in making life's precious moments unforgettable.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">Our Story</h3>
        <p class="text-gray-700">
          Founded with a passion for bringing joy and beauty into people's lives, <PERSON><PERSON><PERSON> has grown from a small local business
          into a trusted name in flowers, cakes, gifts, and personalized items. Our journey began with a simple belief:
          that every celebration, big or small, deserves the perfect touch.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">What We Offer</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li><strong>Fresh Flowers:</strong> Handpicked, vibrant blooms for every occasion</li>
          <li><strong>Delicious Cakes:</strong> Freshly baked, customizable cakes that taste as good as they look</li>
          <li><strong>Thoughtful Gifts:</strong> Curated selection of gifts for birthdays, anniversaries, and special moments</li>
          <li><strong>Personalized Items:</strong> Custom-made products that add a personal touch to your celebrations</li>
          <li><strong>Plants:</strong> Beautiful indoor and outdoor plants to brighten your space</li>
          <li><strong>Combo Packages:</strong> Perfectly paired combinations for maximum impact</li>
        </ul>

        <h3 class="text-xl font-semibold text-gray-900">Our Promise</h3>
        <p class="text-gray-700">
          We are committed to delivering excellence in every product and service. From the freshness of our flowers
          to the taste of our cakes, from the quality of our gifts to the reliability of our delivery,
          we ensure that your experience with Mispri is nothing short of exceptional.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">Serving Bhubaneswar</h3>
        <p class="text-gray-700">
          Currently, we proudly serve the beautiful city of Bhubaneswar and its surrounding areas.
          Our local presence allows us to maintain the highest standards of freshness and provide
          timely delivery for all your special occasions.
        </p>
      </div>
    `
  },
  {
    slug: 'privacy-policy',
    title: 'Privacy Policy',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Privacy Policy</h2>
        <p class="text-sm text-gray-600">Last updated: ${new Date().toLocaleDateString()}</p>

        <h3 class="text-xl font-semibold text-gray-900">Information We Collect</h3>
        <p class="text-gray-700">
          We collect information you provide directly to us, such as when you create an account,
          make a purchase, or contact us for support.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">How We Use Your Information</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>To process and fulfill your orders</li>
          <li>To communicate with you about your orders and our services</li>
          <li>To improve our products and services</li>
          <li>To send you promotional materials (with your consent)</li>
        </ul>

        <h3 class="text-xl font-semibold text-gray-900">Information Sharing</h3>
        <p class="text-gray-700">
          We do not sell, trade, or otherwise transfer your personal information to third parties
          without your consent, except as described in this policy.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">Contact Us</h3>
        <p class="text-gray-700">
          If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>
        </p>
      </div>
    `
  },
  {
    slug: 'terms-conditions',
    title: 'Terms and Conditions',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Terms and Conditions</h2>
        <p class="text-sm text-gray-600">Last updated: ${new Date().toLocaleDateString()}</p>

        <h3 class="text-xl font-semibold text-gray-900">Acceptance of Terms</h3>
        <p class="text-gray-700">
          By accessing and using this website, you accept and agree to be bound by the terms
          and provision of this agreement.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">Products and Services</h3>
        <p class="text-gray-700">
          All products and services are subject to availability. We reserve the right to
          discontinue any product or service without notice.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">Delivery</h3>
        <p class="text-gray-700">
          We currently deliver within Bhubaneswar city limits. Delivery times are estimates
          and may vary based on location and availability.
        </p>

        <h3 class="text-xl font-semibold text-gray-900">Payment</h3>
        <p class="text-gray-700">
          We accept various payment methods as displayed during checkout. All prices are
          in Indian Rupees (INR) and include applicable taxes.
        </p>
      </div>
    `
  },
  {
    slug: 'cancellation-refund',
    title: 'Cancellation & Refund Policy',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Cancellation & Refund Policy</h2>

        <h3 class="text-xl font-semibold text-gray-900">Cancellation Policy</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>Orders can be cancelled up to 2 hours before the scheduled delivery time</li>
          <li>Same-day delivery orders cannot be cancelled once confirmed</li>
          <li>Custom/personalized items cannot be cancelled once production has started</li>
        </ul>

        <h3 class="text-xl font-semibold text-gray-900">Refund Policy</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>Full refund for orders cancelled within the allowed timeframe</li>
          <li>Refunds for damaged or incorrect items delivered</li>
          <li>Refunds will be processed within 5-7 business days</li>
          <li>Refunds will be credited to the original payment method</li>
        </ul>

        <h3 class="text-xl font-semibold text-gray-900">How to Request Cancellation/Refund</h3>
        <p class="text-gray-700">
          Contact our customer support <NAME_EMAIL> or call us at +91 98765 43210
          with your order details.
        </p>
      </div>
    `
  },
  {
    slug: 'coupons-deals',
    title: 'Coupons & Deals',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Coupons & Deals</h2>

        <h3 class="text-xl font-semibold text-gray-900">Current Offers</h3>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 class="font-semibold text-green-800">Welcome Offer</h4>
          <p class="text-green-700">Get 10% off on your first order! Use code: WELCOME10</p>
        </div>

        <h3 class="text-xl font-semibold text-gray-900">How to Use Coupons</h3>
        <ol class="list-decimal list-inside text-gray-700 space-y-2">
          <li>Add items to your cart</li>
          <li>Proceed to checkout</li>
          <li>Enter the coupon code in the "Promo Code" field</li>
          <li>Click "Apply" to see the discount</li>
        </ol>

        <h3 class="text-xl font-semibold text-gray-900">Terms & Conditions</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>Coupons cannot be combined with other offers</li>
          <li>Minimum order value may apply</li>
          <li>Valid for limited time only</li>
          <li>One coupon per customer per order</li>
        </ul>
      </div>
    `
  },
  {
    slug: 'faq',
    title: 'FAQ',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Frequently Asked Questions</h2>

        <div class="space-y-4">
          <div class="border-b border-gray-200 pb-4">
            <h3 class="text-lg font-semibold text-gray-900">What areas do you deliver to?</h3>
            <p class="text-gray-700 mt-2">We currently deliver within Bhubaneswar city limits and surrounding areas.</p>
          </div>

          <div class="border-b border-gray-200 pb-4">
            <h3 class="text-lg font-semibold text-gray-900">What are your delivery timings?</h3>
            <p class="text-gray-700 mt-2">We deliver from 9:00 AM to 9:00 PM, 7 days a week. Same-day delivery is available for orders placed before 2:00 PM.</p>
          </div>

          <div class="border-b border-gray-200 pb-4">
            <h3 class="text-lg font-semibold text-gray-900">How fresh are your flowers?</h3>
            <p class="text-gray-700 mt-2">All our flowers are sourced fresh daily and arranged just before delivery to ensure maximum freshness and longevity.</p>
          </div>

          <div class="border-b border-gray-200 pb-4">
            <h3 class="text-lg font-semibold text-gray-900">Can I customize my cake?</h3>
            <p class="text-gray-700 mt-2">Yes! We offer custom cakes with your choice of flavors, designs, and messages. Please place custom orders at least 24 hours in advance.</p>
          </div>

          <div class="border-b border-gray-200 pb-4">
            <h3 class="text-lg font-semibold text-gray-900">What payment methods do you accept?</h3>
            <p class="text-gray-700 mt-2">We accept cash on delivery, UPI, credit/debit cards, and net banking.</p>
          </div>
        </div>
      </div>
    `
  },
  {
    slug: 'contact-us',
    title: 'Contact Us',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Contact Us</h2>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Get in Touch</h3>
            <div class="space-y-3">
              <div class="flex items-center">
                <span class="font-semibold text-gray-700 w-20">Phone:</span>
                <span class="text-gray-700">+91 98765 43210</span>
              </div>
              <div class="flex items-center">
                <span class="font-semibold text-gray-700 w-20">Email:</span>
                <span class="text-gray-700"><EMAIL></span>
              </div>
              <div class="flex items-start">
                <span class="font-semibold text-gray-700 w-20">Address:</span>
                <span class="text-gray-700">123 Main Street, Bhubaneswar, Odisha 751001</span>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Business Hours</h3>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-700">Monday - Sunday:</span>
                <span class="text-gray-700">9:00 AM - 9:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-700">Customer Support:</span>
                <span class="text-gray-700">24/7</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-semibold text-blue-800 mb-2">Quick Support</h4>
          <p class="text-blue-700">For immediate assistance, you can also reach us via WhatsApp at +91 98765 43210</p>
        </div>
      </div>
    `
  },
  {
    slug: 'media',
    title: 'Media',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Media Gallery</h2>

        <p class="text-gray-700">
          Explore our beautiful collection of flowers, cakes, gifts, and memorable moments we've helped create.
        </p>

        <div class="grid md:grid-cols-3 gap-6">
          <div class="bg-gray-100 rounded-lg p-6 text-center">
            <h3 class="font-semibold text-gray-900 mb-2">Flower Arrangements</h3>
            <p class="text-gray-600 text-sm">Beautiful bouquets and arrangements for every occasion</p>
          </div>

          <div class="bg-gray-100 rounded-lg p-6 text-center">
            <h3 class="font-semibold text-gray-900 mb-2">Custom Cakes</h3>
            <p class="text-gray-600 text-sm">Delicious and beautifully designed cakes</p>
          </div>

          <div class="bg-gray-100 rounded-lg p-6 text-center">
            <h3 class="font-semibold text-gray-900 mb-2">Gift Collections</h3>
            <p class="text-gray-600 text-sm">Thoughtfully curated gifts for special moments</p>
          </div>
        </div>

        <p class="text-gray-600 text-sm">
          Follow us on social media to see more of our latest creations and customer celebrations!
        </p>
      </div>
    `
  },
  {
    slug: 'reviews',
    title: 'Reviews',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Customer Reviews</h2>

        <div class="space-y-6">
          <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-3">
              <div class="text-yellow-400">★★★★★</div>
              <span class="ml-2 font-semibold text-gray-900">Priya S.</span>
            </div>
            <p class="text-gray-700">
              "Amazing service! The flowers were fresh and beautiful. Delivered right on time for my anniversary.
              Highly recommend Mispri for all your special occasions."
            </p>
          </div>

          <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-3">
              <div class="text-yellow-400">★★★★★</div>
              <span class="ml-2 font-semibold text-gray-900">Rajesh K.</span>
            </div>
            <p class="text-gray-700">
              "The custom cake for my daughter's birthday was absolutely perfect! Great taste and beautiful design.
              The team really understood what we wanted."
            </p>
          </div>

          <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-3">
              <div class="text-yellow-400">★★★★★</div>
              <span class="ml-2 font-semibold text-gray-900">Anita M.</span>
            </div>
            <p class="text-gray-700">
              "Excellent quality products and very professional service. The gift combo I ordered was beautifully
              packaged and my friend loved it!"
            </p>
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-semibold text-blue-800 mb-2">Share Your Experience</h4>
          <p class="text-blue-700">We'd love to hear about your experience with Mispri! Contact us to share your review.</p>
        </div>
      </div>
    `
  },
  {
    slug: 'blog',
    title: 'Blog',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Mispri Blog</h2>

        <p class="text-gray-700">
          Welcome to our blog! Here you'll find tips, ideas, and inspiration for making your celebrations even more special.
        </p>

        <div class="space-y-6">
          <article class="border-b border-gray-200 pb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              5 Tips for Choosing the Perfect Birthday Flowers
            </h3>
            <p class="text-gray-600 text-sm mb-3">Published on ${new Date().toLocaleDateString()}</p>
            <p class="text-gray-700">
              Selecting the right flowers for a birthday celebration can make the day extra special.
              Here are our top tips for choosing flowers that will bring joy and beauty to any birthday...
            </p>
          </article>

          <article class="border-b border-gray-200 pb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              How to Care for Your Fresh Flower Arrangements
            </h3>
            <p class="text-gray-600 text-sm mb-3">Published on ${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
            <p class="text-gray-700">
              Make your beautiful flower arrangements last longer with these simple care tips.
              From proper watering to ideal placement, we'll help you keep your flowers fresh...
            </p>
          </article>

          <article class="border-b border-gray-200 pb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              Anniversary Gift Ideas That Show You Care
            </h3>
            <p class="text-gray-600 text-sm mb-3">Published on ${new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
            <p class="text-gray-700">
              Celebrating an anniversary? Discover thoughtful gift ideas that will make your special someone
              feel loved and appreciated on your special day...
            </p>
          </article>
        </div>
      </div>
    `
  },
  {
    slug: 'sitemap',
    title: 'Sitemap',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Sitemap</h2>

        <p class="text-gray-700">
          Find everything you need on our website with this comprehensive sitemap.
        </p>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Shop Categories</h3>
            <ul class="space-y-2 text-gray-700">
              <li><a href="/category/flowers" class="hover:text-primary-600">Flowers</a></li>
              <li><a href="/category/cakes" class="hover:text-primary-600">Cakes</a></li>
              <li><a href="/category/birthday" class="hover:text-primary-600">Birthday</a></li>
              <li><a href="/category/anniversary" class="hover:text-primary-600">Anniversary</a></li>
              <li><a href="/category/gifts" class="hover:text-primary-600">Gifts</a></li>
              <li><a href="/category/personalised" class="hover:text-primary-600">Personalised</a></li>
              <li><a href="/category/plants" class="hover:text-primary-600">Plants</a></li>
              <li><a href="/category/combos" class="hover:text-primary-600">Combos</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Information Pages</h3>
            <ul class="space-y-2 text-gray-700">
              <li><a href="/pages/about-us" class="hover:text-primary-600">About Us</a></li>
              <li><a href="/pages/contact-us" class="hover:text-primary-600">Contact Us</a></li>
              <li><a href="/pages/faq" class="hover:text-primary-600">FAQ</a></li>
              <li><a href="/pages/blog" class="hover:text-primary-600">Blog</a></li>
              <li><a href="/pages/media" class="hover:text-primary-600">Media</a></li>
              <li><a href="/pages/reviews" class="hover:text-primary-600">Reviews</a></li>
              <li><a href="/pages/quotes" class="hover:text-primary-600">Quotes</a></li>
              <li><a href="/pages/corporate-gifts" class="hover:text-primary-600">Corporate Gifts</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Policies & Terms</h3>
            <ul class="space-y-2 text-gray-700">
              <li><a href="/pages/privacy-policy" class="hover:text-primary-600">Privacy Policy</a></li>
              <li><a href="/pages/terms-conditions" class="hover:text-primary-600">Terms & Conditions</a></li>
              <li><a href="/pages/cancellation-refund" class="hover:text-primary-600">Cancellation & Refund</a></li>
              <li><a href="/pages/coupons-deals" class="hover:text-primary-600">Coupons & Deals</a></li>
            </ul>
          </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Links</h3>
          <div class="grid md:grid-cols-2 gap-4">
            <ul class="space-y-2 text-gray-700">
              <li><a href="/" class="hover:text-primary-600">Home</a></li>
              <li><a href="/cart" class="hover:text-primary-600">Shopping Cart</a></li>
              <li><a href="/account" class="hover:text-primary-600">My Account</a></li>
              <li><a href="/orders" class="hover:text-primary-600">Order History</a></li>
            </ul>
            <ul class="space-y-2 text-gray-700">
              <li><a href="/wishlist" class="hover:text-primary-600">Wishlist</a></li>
              <li><a href="/track-order" class="hover:text-primary-600">Track Order</a></li>
              <li><a href="/delivery-info" class="hover:text-primary-600">Delivery Information</a></li>
              <li><a href="/customer-support" class="hover:text-primary-600">Customer Support</a></li>
            </ul>
          </div>
        </div>
      </div>
    `
  },
  {
    slug: 'quotes',
    title: 'Quotes',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Inspirational Quotes</h2>

        <p class="text-gray-700">
          Beautiful words for beautiful moments. Find the perfect quote to accompany your gifts and celebrations.
        </p>

        <div class="space-y-8">
          <div class="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Love & Romance</h3>
            <div class="space-y-4">
              <blockquote class="italic text-gray-700 border-l-4 border-pink-400 pl-4">
                "In all the world, there is no heart for me like yours. In all the world, there is no love for you like mine."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-pink-400 pl-4">
                "You are my today and all of my tomorrows."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-pink-400 pl-4">
                "Every love story is beautiful, but ours is my favorite."
              </blockquote>
            </div>
          </div>

          <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Birthday Wishes</h3>
            <div class="space-y-4">
              <blockquote class="italic text-gray-700 border-l-4 border-yellow-400 pl-4">
                "May your birthday be filled with sunshine and smiles, laughter, love, and cheer."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-yellow-400 pl-4">
                "Another year older, another year wiser, another year more wonderful."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-yellow-400 pl-4">
                "Age is merely the number of years the world has been enjoying you."
              </blockquote>
            </div>
          </div>

          <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Friendship</h3>
            <div class="space-y-4">
              <blockquote class="italic text-gray-700 border-l-4 border-green-400 pl-4">
                "A friend is someone who knows all about you and still loves you."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-green-400 pl-4">
                "Good friends are like stars. You don't always see them, but you know they're always there."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-green-400 pl-4">
                "Friendship is the only cement that will ever hold the world together."
              </blockquote>
            </div>
          </div>

          <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Celebration & Joy</h3>
            <div class="space-y-4">
              <blockquote class="italic text-gray-700 border-l-4 border-purple-400 pl-4">
                "Life is a celebration. Make every moment count."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-purple-400 pl-4">
                "Happiness is not something ready-made. It comes from your own actions."
              </blockquote>
              <blockquote class="italic text-gray-700 border-l-4 border-purple-400 pl-4">
                "The best way to make your dreams come true is to wake up and celebrate life."
              </blockquote>
            </div>
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-semibold text-blue-800 mb-2">Custom Messages</h4>
          <p class="text-blue-700">Need a personalized message for your gift? Our team can help you craft the perfect words for any occasion.</p>
        </div>
      </div>
    `
  },
  {
    slug: 'corporate-gifts',
    title: 'Corporate Gifts',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Corporate Gifts</h2>

        <p class="text-lg text-gray-700">
          Make a lasting impression with thoughtful corporate gifts from Mispri. Perfect for client appreciation,
          employee recognition, and business celebrations.
        </p>

        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Our Corporate Services</h3>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                <span><strong>Bulk Orders:</strong> Special pricing for large quantity orders</span>
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                <span><strong>Custom Branding:</strong> Add your company logo to gift items</span>
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                <span><strong>Scheduled Delivery:</strong> Plan deliveries for special dates</span>
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                <span><strong>Corporate Accounts:</strong> Streamlined billing and payment</span>
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                <span><strong>Dedicated Support:</strong> Personal account manager</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Popular Corporate Gifts</h3>
            <div class="space-y-4">
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900">Executive Gift Hampers</h4>
                <p class="text-gray-600 text-sm">Premium gift baskets with gourmet treats and flowers</p>
              </div>
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900">Branded Plant Gifts</h4>
                <p class="text-gray-600 text-sm">Elegant plants with custom branded planters</p>
              </div>
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900">Corporate Cake Orders</h4>
                <p class="text-gray-600 text-sm">Custom cakes for office celebrations and events</p>
              </div>
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900">Seasonal Arrangements</h4>
                <p class="text-gray-600 text-sm">Festival and holiday-themed corporate gifts</p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-blue-800 mb-4">Why Choose Mispri for Corporate Gifts?</h3>
          <div class="grid md:grid-cols-2 gap-4">
            <ul class="space-y-2 text-blue-700">
              <li>• Professional presentation and packaging</li>
              <li>• Reliable delivery across Bhubaneswar</li>
              <li>• Competitive corporate pricing</li>
              <li>• Quality assurance guarantee</li>
            </ul>
            <ul class="space-y-2 text-blue-700">
              <li>• Flexible payment terms</li>
              <li>• Custom gift solutions</li>
              <li>• Experienced corporate team</li>
              <li>• Long-term partnership approach</li>
            </ul>
          </div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-green-800 mb-4">Get Started with Corporate Gifts</h3>
          <p class="text-green-700 mb-4">
            Ready to elevate your corporate gifting? Contact our corporate team to discuss your requirements
            and get a customized quote.
          </p>
          <div class="space-y-2 text-green-700">
            <p><strong>Corporate Sales:</strong> <EMAIL></p>
            <p><strong>Phone:</strong> +91 98765 43210 (Ext. 2)</p>
            <p><strong>Minimum Order:</strong> ₹5,000 for corporate pricing</p>
          </div>
        </div>

        <div class="text-center">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Occasions We Serve</h3>
          <div class="flex flex-wrap justify-center gap-3">
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Employee Appreciation</span>
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Client Meetings</span>
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Office Celebrations</span>
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Product Launches</span>
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Festival Gifting</span>
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Conference Gifts</span>
            <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">Thank You Gifts</span>
          </div>
        </div>
      </div>
    `
  }
];

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Seeding footer pages via API...');

    for (const page of footerPages) {
      const pageData = {
        title: page.title,
        slug: page.slug,
        content: page.content,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Check if page already exists
      const existingPage = await prisma.systemSetting.findFirst({
        where: {
          category: 'static_pages',
          key: page.slug
        }
      });

      if (existingPage) {
        console.log(`📄 Updating existing page: ${page.title}`);
        await prisma.systemSetting.update({
          where: { id: existingPage.id },
          data: {
            value: JSON.stringify(pageData),
            updatedAt: new Date()
          }
        });
      } else {
        console.log(`📄 Creating new page: ${page.title}`);
        await prisma.systemSetting.create({
          data: {
            category: 'static_pages',
            key: page.slug,
            value: JSON.stringify(pageData),
            description: `Static page: ${page.title}`
          }
        });
      }
    }

    console.log('✅ Footer pages seeded successfully!');

    return NextResponse.json({
      success: true,
      message: 'Footer pages seeded successfully',
      pages: footerPages.map(p => ({ title: p.title, slug: p.slug }))
    });
  } catch (error) {
    console.error('❌ Error seeding footer pages:', error);
    return NextResponse.json(
      { error: 'Failed to seed footer pages' },
      { status: 500 }
    );
  }
}
