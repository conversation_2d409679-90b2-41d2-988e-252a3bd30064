import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    console.log('🏪 ADMIN: Seeding stores to Neon database...');

    // Check if DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      console.log('⚠️ DATABASE_URL not found, cannot seed stores');
      return NextResponse.json(
        { 
          error: 'Database not configured',
          message: 'DATABASE_URL environment variable is missing',
          success: false
        },
        { status: 500 }
      );
    }

    // Test database connection
    try {
      await prisma.$queryRaw`SELECT 1`;
      console.log('✅ Database connection successful');
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      return NextResponse.json(
        { 
          error: 'Database connection failed',
          message: 'Cannot connect to Neon database',
          success: false
        },
        { status: 500 }
      );
    }

    // Check existing stores
    console.log('🔍 Checking existing stores...');
    const existingStores = await prisma.store.findMany();
    console.log(`📊 Found ${existingStores.length} existing stores`);

    // Define stores to create
    const storesToCreate = [
      {
        name: 'Main Store',
        location: 'Bhubaneswar'
      },
      {
        name: 'Branch Store',
        location: 'Cuttack'
      },
      {
        name: 'Express Store',
        location: 'Puri'
      }
    ];

    console.log('🏪 Creating/Updating stores...');
    
    const results = [];
    
    for (const storeData of storesToCreate) {
      // Check if store with same name and location exists
      const existingStore = await prisma.store.findFirst({
        where: {
          name: storeData.name,
          location: storeData.location
        }
      });

      if (existingStore) {
        console.log(`✅ Store already exists: ${storeData.name} - ${storeData.location}`);
        results.push({
          action: 'exists',
          store: existingStore
        });
      } else {
        console.log(`🆕 Creating new store: ${storeData.name} - ${storeData.location}`);
        const newStore = await prisma.store.create({
          data: storeData
        });
        results.push({
          action: 'created',
          store: newStore
        });
        console.log(`✅ Created: ${newStore.name} - ${newStore.location} (ID: ${newStore.id})`);
      }
    }

    // Get all stores after seeding
    const allStores = await prisma.store.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`✅ Store seeding completed! Total stores: ${allStores.length}`);

    return NextResponse.json({
      success: true,
      message: 'Stores seeded successfully',
      data: {
        totalStores: allStores.length,
        results: results,
        allStores: allStores
      }
    });

  } catch (error) {
    console.error('❌ Error seeding stores:', error);
    
    let errorMessage = 'Failed to seed stores';
    let errorCode = 500;
    
    if (error.code === 'P1001') {
      errorMessage = 'Cannot connect to database - check DATABASE_URL';
    } else if (error.code === 'P2002') {
      errorMessage = 'Store already exists (unique constraint)';
      errorCode = 409;
    }

    return NextResponse.json(
      { 
        error: errorMessage,
        details: error.message,
        success: false
      },
      { status: errorCode }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 ADMIN: Fetching stores from Neon database...');

    // Check if DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      console.log('⚠️ DATABASE_URL not found, returning mock stores');
      return NextResponse.json({
        success: false,
        message: 'Database not configured - using mock data',
        data: {
          stores: [
            { id: 'mock-1', name: 'Main Store', location: 'Bhubaneswar', createdAt: new Date() },
            { id: 'mock-2', name: 'Branch Store', location: 'Cuttack', createdAt: new Date() },
            { id: 'mock-3', name: 'Express Store', location: 'Puri', createdAt: new Date() }
          ],
          totalStores: 3,
          source: 'mock'
        }
      });
    }

    // Get all stores from database
    const stores = await prisma.store.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`✅ Found ${stores.length} stores in database`);

    return NextResponse.json({
      success: true,
      message: 'Stores fetched successfully',
      data: {
        stores: stores,
        totalStores: stores.length,
        source: 'database'
      }
    });

  } catch (error) {
    console.error('❌ Error fetching stores:', error);
    
    // Return mock data as fallback
    return NextResponse.json({
      success: false,
      message: 'Database error - using mock data',
      error: error.message,
      data: {
        stores: [
          { id: 'mock-1', name: 'Main Store', location: 'Bhubaneswar', createdAt: new Date() },
          { id: 'mock-2', name: 'Branch Store', location: 'Cuttack', createdAt: new Date() },
          { id: 'mock-3', name: 'Express Store', location: 'Puri', createdAt: new Date() }
        ],
        totalStores: 3,
        source: 'mock'
      }
    });
  } finally {
    await prisma.$disconnect();
  }
}
