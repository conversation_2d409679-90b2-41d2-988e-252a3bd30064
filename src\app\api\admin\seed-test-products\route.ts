import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const testProducts = [
  // Flowers
  {
    name: 'Red Rose Bouquet',
    description: 'Beautiful red roses arranged in an elegant bouquet',
    category: 'Flowers',
    price: 599,
    costPrice: 350,
    unit: 'bouquet',
    sku: 'FLOW-001',
    imageUrl: '/images/products/red-roses.jpg',
    isActive: true,
    isFeatured: true
  },
  {
    name: 'Mixed Flower Arrangement',
    description: 'Colorful mixed flowers in a beautiful arrangement',
    category: 'Flowers',
    price: 799,
    costPrice: 450,
    unit: 'arrangement',
    sku: 'FLOW-002',
    imageUrl: '/images/products/mixed-flowers.jpg',
    isActive: true,
    isFeatured: false
  },
  
  // Cakes
  {
    name: 'Chocolate Birthday Cake',
    description: 'Rich chocolate cake perfect for birthdays',
    category: 'Cakes',
    price: 899,
    costPrice: 500,
    unit: 'cake',
    sku: 'CAKE-001',
    imageUrl: '/images/products/chocolate-cake.jpg',
    isActive: true,
    isFeatured: true
  },
  {
    name: 'Vanilla Sponge Cake',
    description: 'Light and fluffy vanilla sponge cake',
    category: 'Cakes',
    price: 699,
    costPrice: 400,
    unit: 'cake',
    sku: 'CAKE-002',
    imageUrl: '/images/products/vanilla-cake.jpg',
    isActive: true,
    isFeatured: false
  },
  
  // Birthday
  {
    name: 'Birthday Special Combo',
    description: 'Flowers and cake combo for birthday celebrations',
    category: 'Birthday',
    price: 1299,
    costPrice: 750,
    unit: 'combo',
    sku: 'BDAY-001',
    imageUrl: '/images/products/birthday-combo.jpg',
    isActive: true,
    isFeatured: true
  },
  {
    name: 'Birthday Balloon Bouquet',
    description: 'Colorful balloons perfect for birthday parties',
    category: 'Birthday',
    price: 399,
    costPrice: 200,
    unit: 'bouquet',
    sku: 'BDAY-002',
    imageUrl: '/images/products/birthday-balloons.jpg',
    isActive: true,
    isFeatured: false
  },
  
  // Anniversary
  {
    name: 'Anniversary Rose & Cake Combo',
    description: 'Romantic roses with anniversary cake',
    category: 'Anniversary',
    price: 1599,
    costPrice: 900,
    unit: 'combo',
    sku: 'ANNI-001',
    imageUrl: '/images/products/anniversary-combo.jpg',
    isActive: true,
    isFeatured: true
  },
  
  // Gifts
  {
    name: 'Gift Hamper Deluxe',
    description: 'Premium gift hamper with assorted treats',
    category: 'Gifts',
    price: 1999,
    costPrice: 1200,
    unit: 'hamper',
    sku: 'GIFT-001',
    imageUrl: '/images/products/gift-hamper.jpg',
    isActive: true,
    isFeatured: true
  },
  {
    name: 'Chocolate Gift Box',
    description: 'Assorted chocolates in a beautiful gift box',
    category: 'Gifts',
    price: 799,
    costPrice: 450,
    unit: 'box',
    sku: 'GIFT-002',
    imageUrl: '/images/products/chocolate-box.jpg',
    isActive: true,
    isFeatured: false
  },
  
  // Plants
  {
    name: 'Lucky Bamboo Plant',
    description: 'Bring good luck with this beautiful bamboo plant',
    category: 'Plants',
    price: 499,
    costPrice: 250,
    unit: 'plant',
    sku: 'PLANT-001',
    imageUrl: '/images/products/bamboo-plant.jpg',
    isActive: true,
    isFeatured: false
  },
  {
    name: 'Money Plant in Decorative Pot',
    description: 'Beautiful money plant in an elegant decorative pot',
    category: 'Plants',
    price: 699,
    costPrice: 350,
    unit: 'plant',
    sku: 'PLANT-002',
    imageUrl: '/images/products/money-plant.jpg',
    isActive: true,
    isFeatured: false
  }
];

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Seeding test products for category testing...');

    // Get the first warehouse (or create a default one)
    let warehouse = await prisma.warehouse.findFirst();
    
    if (!warehouse) {
      console.log('🏭 Creating default warehouse for test products');
      warehouse = await prisma.warehouse.create({
        data: {
          name: 'Main Warehouse',
          location: 'Bhubaneswar Central'
        }
      });
    }

    let createdCount = 0;
    let updatedCount = 0;

    for (const productData of testProducts) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: { sku: productData.sku }
      });

      if (existingProduct) {
        console.log(`📦 Updating existing product: ${productData.name}`);
        await prisma.product.update({
          where: { id: existingProduct.id },
          data: productData
        });
        updatedCount++;
      } else {
        console.log(`📦 Creating new product: ${productData.name}`);
        const product = await prisma.product.create({
          data: productData
        });

        // Create warehouse inventory for the product
        await prisma.warehouseInventory.create({
          data: {
            warehouseId: warehouse.id,
            productId: product.id,
            quantity: 50 // Default stock
          }
        });

        createdCount++;
      }
    }

    console.log('✅ Test products seeded successfully!');

    return NextResponse.json({
      success: true,
      message: 'Test products seeded successfully',
      created: createdCount,
      updated: updatedCount,
      total: testProducts.length,
      categories: [...new Set(testProducts.map(p => p.category))]
    });
  } catch (error) {
    console.error('❌ Error seeding test products:', error);
    return NextResponse.json(
      { error: 'Failed to seed test products' },
      { status: 500 }
    );
  }
}
