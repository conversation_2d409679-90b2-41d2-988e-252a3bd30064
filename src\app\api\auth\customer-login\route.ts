import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import * as bcrypt from 'bcrypt';

// POST /api/auth/customer-login - Authenticate a customer
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    console.log('Customer login attempt:', { email });

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // TEMPORARY: Demo customer login - Create if doesn't exist
    if (email === '<EMAIL>' && password === 'customer123') {
      try {
        // Try to find or create demo customer in database
        let user = await prisma.user.findUnique({
          where: { email: '<EMAIL>' },
          include: { customer: true }
        });

        if (!user) {
          console.log('Creating demo customer in database...');
          user = await prisma.user.create({
            data: {
              id: 'customer-user-id',
              name: 'Demo Customer',
              email: '<EMAIL>',
              password: 'customer123',
              role: 'CUSTOMER',
              customer: {
                create: {
                  firstName: 'Demo',
                  lastName: 'Customer',
                  phone: '+91 9876543210',
                  isSubscribed: false,
                  loyaltyPoints: 100,
                }
              }
            },
            include: { customer: true }
          });
          console.log('Demo customer created in database');
        }

        const customerData = {
          id: user.id,
          name: user.name,
          firstName: user.customer?.firstName || 'Demo',
          lastName: user.customer?.lastName || 'Customer',
          email: user.email,
          phone: user.customer?.phone || '+91 9876543210',
          isSubscribed: user.customer?.isSubscribed || false,
          loyaltyPoints: user.customer?.loyaltyPoints || 100,
          createdAt: user.createdAt.toISOString(),
        };

        console.log('Demo customer login successful');
        return NextResponse.json(customerData);
      } catch (dbError) {
        console.error('Database error for demo customer:', dbError);
        // Fallback to hardcoded data
        const customerData = {
          id: 'customer-user-id',
          name: 'Demo Customer',
          firstName: 'Demo',
          lastName: 'Customer',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          isSubscribed: false,
          loyaltyPoints: 100,
          createdAt: new Date().toISOString(),
        };

        console.log('Demo customer login successful (fallback)');
        return NextResponse.json(customerData);
      }
    }

    // Try to find user with CUSTOMER role in the database
    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          name: true,
          email: true,
          password: true,
          role: true,
          createdAt: true,
          customer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phone: true,
              isSubscribed: true,
              loyaltyPoints: true,
            }
          }
        },
      });

      if (!user) {
        console.log('User not found:', email);
        return NextResponse.json(
          { error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      // Check if user has CUSTOMER role or if they have a customer profile
      if (user.role !== 'CUSTOMER' && !user.customer) {
        console.log('User is not a customer:', email);
        return NextResponse.json(
          { error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      // Verify password
      let passwordMatch = false;

      // First try direct comparison for non-hashed passwords
      if (password === user.password) {
        passwordMatch = true;
      } else {
        // Then try bcrypt comparison for hashed passwords
        try {
          passwordMatch = await bcrypt.compare(password, user.password);
        } catch (err) {
          console.error('Password comparison error:', err);
          passwordMatch = false;
        }
      }

      if (!passwordMatch) {
        console.log('Password mismatch for user:', email);
        return NextResponse.json(
          { error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      // Return customer data (excluding password)
      const { password: _, ...userData } = user;

      const responseData = {
        id: userData.id,
        name: userData.customer ? `${userData.customer.firstName} ${userData.customer.lastName}`.trim() : userData.name,
        firstName: userData.customer?.firstName || userData.name.split(' ')[0] || '',
        lastName: userData.customer?.lastName || userData.name.split(' ').slice(1).join(' ') || '',
        email: userData.email,
        phone: userData.customer?.phone || null,
        isSubscribed: userData.customer?.isSubscribed || false,
        loyaltyPoints: userData.customer?.loyaltyPoints || 0,
        createdAt: userData.createdAt,
      };

      console.log('Customer login successful:', email);
      return NextResponse.json(responseData);
    } catch (dbError) {
      console.error('Database error during customer login:', dbError);

      // If database fails, return a generic error
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Customer login error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
