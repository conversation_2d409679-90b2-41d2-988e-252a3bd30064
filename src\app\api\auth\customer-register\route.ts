import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import * as bcrypt from 'bcrypt';
import emailService from '@/lib/services/nodemailer-service';

// POST /api/auth/customer-register - Register a new customer
export async function POST(request: NextRequest) {
  try {
    const { firstName, lastName, email, password, phone } = await request.json();

    console.log('🔐 Customer registration attempt:', { firstName, lastName, email, phone });

    // Test database connection first
    try {
      await prisma.$queryRaw`SELECT 1`;
      console.log('✅ Database connection successful for registration');
    } catch (dbConnectionError) {
      console.error('❌ Database connection failed for registration:', dbConnectionError);
      return NextResponse.json(
        { error: 'Service temporarily unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    // Validate required fields
    if (!firstName || !email || !password) {
      return NextResponse.json(
        { error: 'First name, email, and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    // Validate password length
    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: 'An account with this email already exists' },
          { status: 409 }
        );
      }

      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create new user with CUSTOMER role
      const newUser = await prisma.user.create({
        data: {
          name: `${firstName} ${lastName || ''}`.trim(),
          email,
          password: hashedPassword,
          role: 'CUSTOMER',
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
        },
      });

      // Create customer profile and cart in a transaction
      let result;
      try {
        result = await prisma.$transaction(async (tx) => {
          // Create customer profile linked to the user
          const newCustomer = await tx.customer.create({
            data: {
              userId: newUser.id,
              firstName,
              lastName: lastName || '',
              phone: phone || null,
              isSubscribed: false,
              loyaltyPoints: 0,
            },
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phone: true,
              isSubscribed: true,
              loyaltyPoints: true,
            },
          });

          // Create cart for the user
          const cart = await tx.cart.create({
            data: {
              userId: newUser.id,
            },
          });

          return { customer: newCustomer, cart };
        });
        console.log('✅ Customer profile and cart created successfully');
      } catch (customerError) {
        console.error('❌ Error creating customer profile:', customerError);

        // If customer creation fails, still return the user data
        // The user account was created successfully
        const responseData = {
          id: newUser.id,
          name: newUser.name,
          firstName: firstName,
          lastName: lastName || '',
          email: newUser.email,
          phone: phone || null,
          isSubscribed: false,
          loyaltyPoints: 0,
          createdAt: newUser.createdAt,
        };

        console.log('⚠️ Registration completed with basic user account (customer profile creation failed)');
        return NextResponse.json(responseData, { status: 201 });
      }

      const newCustomer = result.customer;

      // Return customer data
      const responseData = {
        id: newUser.id,
        name: newUser.name,
        firstName: newCustomer.firstName,
        lastName: newCustomer.lastName,
        email: newUser.email,
        phone: newCustomer.phone,
        isSubscribed: newCustomer.isSubscribed,
        loyaltyPoints: newCustomer.loyaltyPoints,
        createdAt: newUser.createdAt,
      };

      // Send welcome email (don't fail registration if email fails)
      try {
        const emailSent = await emailService.sendWelcomeEmail(email, newUser.name);
        if (emailSent) {
          console.log('✅ Welcome email sent successfully to:', email);
        } else {
          console.log('⚠️ Failed to send welcome email, but registration successful');
        }
      } catch (emailError) {
        console.error('❌ Error sending welcome email:', emailError);
        // Don't fail registration if email fails
      }

      console.log('Customer registration successful:', email);
      return NextResponse.json(responseData, { status: 201 });
    } catch (dbError) {
      console.error('Database error during customer registration:', dbError);

      // Check if it's a unique constraint violation
      if (dbError instanceof Error && dbError.message.includes('Unique constraint')) {
        return NextResponse.json(
          { error: 'An account with this email already exists' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: 'Registration failed. Please try again.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Customer registration error:', error);
    return NextResponse.json(
      { error: 'Registration failed' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
