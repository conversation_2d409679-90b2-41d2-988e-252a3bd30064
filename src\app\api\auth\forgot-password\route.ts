import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import crypto from 'crypto';
import emailService from '@/lib/services/nodemailer-service';

// Generate 6-digit OTP
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// POST /api/auth/forgot-password - Send password reset email
export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    console.log('Forgot password request for:', email);

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    try {
      // Check if user exists (look for customers with role CUSTOMER)
      const user = await prisma.user.findFirst({
        where: {
          email,
          role: 'CUSTOMER'
        },
      });

      // Always return success to prevent email enumeration attacks
      // But only send email if user actually exists
      if (user) {
        // Generate 6-digit OTP
        const resetOTP = generateOTP();
        const resetOTPExpiry = new Date(Date.now() + 600000); // 10 minutes from now

        console.log('🔢 Generated OTP for', email, ':', resetOTP);

        // Save OTP to database
        await prisma.user.update({
          where: { id: user.id },
          data: {
            resetOTP,
            resetOTPExpiry,
          },
        });

        // Send OTP email
        try {
          const emailSent = await emailService.sendPasswordResetOTP(
            email,
            user.name,
            resetOTP
          );

          if (emailSent) {
            console.log('✅ Password reset OTP email sent successfully to:', email);
          } else {
            console.log('⚠️ Failed to send password reset OTP email, but continuing...');
          }
        } catch (emailError) {
          console.error('❌ Error sending password reset OTP email:', emailError);
          // Don't fail the request if email fails - continue with success response
        }
      } else {
        console.log('User not found for email:', email);
      }

      // Always return success response
      return NextResponse.json({
        success: true,
        message: 'If an account with that email exists, we have sent a password reset link.',
      });

    } catch (dbError) {
      console.error('Database error during forgot password:', dbError);
      return NextResponse.json(
        { error: 'Failed to process request' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
