import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import * as bcrypt from 'bcrypt';

// POST /api/auth/login - Authenticate a user
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    console.log('Login attempt:', { email, password });

    // TEMPORARY: Bypass database check for admin login
    if (email === '<EMAIL>' && (password === 'admin123' || password === 'admin@123')) {
      // Return hardcoded admin user data
      const userData = {
        id: 'admin-user-id',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'ADMIN',
        storeId: null,
        store: null
      };

      console.log('Login successful for admin');
      return NextResponse.json(userData);
    }

    // TEMPORARY: Demo customer login
    if (email === '<EMAIL>' && password === 'customer123') {
      const userData = {
        id: 'customer-user-id',
        name: 'Demo Customer',
        email: '<EMAIL>',
        role: 'CUSTOMER',
        storeId: null,
        store: null
      };

      console.log('Login successful for demo customer');
      return NextResponse.json(userData);
    }

    // For non-admin users, try database (this will likely fail without DB setup)
    try {
      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          name: true,
          email: true,
          password: true,
          role: true,
          storeId: true,
          store: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!user) {
        console.log('User not found:', email);
        return NextResponse.json(
          { error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      // Verify password
      // Try both direct comparison and bcrypt comparison
      let passwordMatch = false;

      // First try direct comparison for non-hashed passwords
      if (password === user.password) {
        passwordMatch = true;
      } else {
        // Then try bcrypt comparison for hashed passwords
        try {
          passwordMatch = await bcrypt.compare(password, user.password);
        } catch (err) {
          console.error('Password comparison error:', err);
          // If bcrypt fails, it might be because the password isn't hashed
          passwordMatch = false;
        }
      }

      if (!passwordMatch) {
        console.log('Password mismatch for:', email);
        return NextResponse.json(
          { error: 'Invalid email or password' },
          { status: 401 }
        );
      }

      // Return user data (excluding password)
      const { password: _, ...userData } = user;
      console.log('Login successful for:', email);
      return NextResponse.json(userData);
    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}
