import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import * as bcrypt from 'bcrypt';

// POST /api/auth/reset-password - Reset password with OTP
export async function POST(request: NextRequest) {
  try {
    const { email, otp, password } = await request.json();

    console.log('Reset password request for:', email, 'with OTP:', otp);

    if (!email || !otp || !password) {
      return NextResponse.json(
        { error: 'Email, OTP, and password are required' },
        { status: 400 }
      );
    }

    // Validate OTP format (6 digits)
    if (!/^\d{6}$/.test(otp)) {
      return NextResponse.json(
        { error: 'OTP must be exactly 6 digits' },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    try {
      // Find user with valid OTP
      const user = await prisma.user.findFirst({
        where: {
          email,
          resetOTP: otp,
          resetOTPExpiry: {
            gt: new Date(), // OTP must not be expired
          },
          role: 'CUSTOMER', // Only allow customers to reset password
        },
      });

      if (!user) {
        console.log('Invalid or expired OTP for email:', email);
        return NextResponse.json(
          { error: 'Invalid or expired OTP code' },
          { status: 400 }
        );
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Update user password and clear OTP
      await prisma.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          resetOTP: null,
          resetOTPExpiry: null,
        },
      });

      console.log('Password reset successful for user:', user.email);

      return NextResponse.json({
        success: true,
        message: 'Password has been reset successfully',
      });

    } catch (dbError) {
      console.error('Database error during password reset:', dbError);
      return NextResponse.json(
        { error: 'Failed to reset password' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
