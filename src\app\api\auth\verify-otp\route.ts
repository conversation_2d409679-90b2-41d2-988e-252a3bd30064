import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/auth/verify-otp - Verify OTP code
export async function POST(request: NextRequest) {
  try {
    const { email, otp } = await request.json();

    console.log('🔍 OTP verification request for:', email, 'with OTP:', otp);

    if (!email || !otp) {
      return NextResponse.json(
        { error: '<PERSON>ail and OTP are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    // Validate OTP format (6 digits)
    if (!/^\d{6}$/.test(otp)) {
      return NextResponse.json(
        { error: 'OTP must be exactly 6 digits' },
        { status: 400 }
      );
    }

    try {
      // Find user with valid OTP
      const user = await prisma.user.findFirst({
        where: {
          email,
          resetOTP: otp,
          resetOTPExpiry: {
            gt: new Date(), // OTP must not be expired
          },
          role: 'CUSTOMER', // Only allow customers to verify OTP
        },
      });

      if (!user) {
        console.log('❌ Invalid or expired OTP for email:', email);
        return NextResponse.json(
          { error: 'Invalid or expired OTP code' },
          { status: 400 }
        );
      }

      console.log('✅ OTP verified successfully for user:', user.email);

      // Return success with user info (but don't clear OTP yet - wait for password reset)
      return NextResponse.json({
        success: true,
        message: 'OTP verified successfully',
        userId: user.id,
        email: user.email,
        name: user.name,
      });

    } catch (dbError) {
      console.error('Database error during OTP verification:', dbError);
      return NextResponse.json(
        { error: 'Failed to verify OTP' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('OTP verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
