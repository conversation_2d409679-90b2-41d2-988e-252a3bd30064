import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/categories/[id] - Get a specific category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Find products with this category
    const products = await prisma.product.findMany({
      where: {
        category: id,
      },
    });

    // Count products in this category
    const productCount = products.length;

    // Even if no products exist, we can still return the category
    return NextResponse.json({
      id: id,
      name: id,
      productCount,
      products: products.map(product => ({
        id: product.id,
        name: product.name,
        price: product.price,
      })),
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    );
  }
}

// PUT /api/categories/[id] - Update a category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { getAuthenticatedUser } = await import('@/lib/auth/api-auth');
    const { CategoryService } = await import('@/lib/services/category-service');

    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const data = await request.json();
    console.log('Updating category:', id, data);

    // Update category using service layer
    const updatedCategory = await CategoryService.updateCategory(id, {
      name: data.name,
      description: data.description,
      imageUrl: data.imageUrl,
      slug: data.slug,
      displayOrder: data.displayOrder,
      isActive: data.isActive,
    });

    // If category name changed, update all products with the old category name
    if (data.name && data.name !== id) {
      const updatedProducts = await prisma.product.updateMany({
        where: {
          category: id,
        },
        data: {
          category: data.name,
        },
      });

      console.log(`Category renamed from '${id}' to '${data.name}'. ${updatedProducts.count} products updated.`);
    }

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    );
  }
}

// DELETE /api/categories/[id] - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { getAuthenticatedUser } = await import('@/lib/auth/api-auth');
    const { CategoryService } = await import('@/lib/services/category-service');

    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('🔍 User attempting to delete category:', user.email);
    console.log('🔑 User permissions:', user.permissions);
    console.log('🎯 Required permission: categories.delete');
    console.log('✅ Has permission:', user.permissions.includes('categories.delete'));

    // Check permissions for category deletion
    if (!user.permissions.includes('categories.delete')) {
      console.log('❌ Permission denied for user:', user.email);
      return NextResponse.json(
        { error: `Insufficient permissions to delete categories. User ${user.email} has permissions: ${user.permissions.join(', ')}` },
        { status: 403 }
      );
    }

    // Await params to fix Next.js warning
    const { id } = await params;
    console.log('🗑️ DELETE Category API called for category ID:', id);

    // Delete category using service layer (moves products to Uncategorized)
    const result = await CategoryService.deleteCategory(id);

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 404 }
      );
    }

    console.log(`✅ Category '${id}' deleted successfully. ${result.productsAffected} products affected.`);

    return NextResponse.json({
      success: true,
      message: result.message,
      productsAffected: result.productsAffected
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    );
  }
}
