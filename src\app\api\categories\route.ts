import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { CategoryService } from '@/lib/services/category-service';
import { testDatabaseConnection } from '@/lib/db-utils';

// GET /api/categories - Get all categories
export async function GET(request: NextRequest) {
  try {
    // Test database connection first
    const connectionError = await testDatabaseConnection();
    if (connectionError) {
      return connectionError;
    }

    console.log('📂 Fetching categories from database...');

    // Get categories from database using service layer
    const categories = await CategoryService.getAllCategories();

    console.log(`✅ Successfully fetched ${categories.length} categories`);
    console.log('Categories:', categories.map(c => c.name));

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create a new category
export async function POST(request: NextRequest) {
  try {
    // Test database connection first
    const connectionError = await testDatabaseConnection();
    if (connectionError) {
      return connectionError;
    }

    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const data = await request.json();
    console.log('Creating category:', data);

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      );
    }

    // Check if category already exists
    const existingCategories = await CategoryService.getAllCategories();
    const categoryExists = existingCategories.some(cat =>
      cat.name.toLowerCase() === data.name.toLowerCase()
    );

    if (categoryExists) {
      return NextResponse.json(
        { error: 'Category already exists' },
        { status: 400 }
      );
    }

    // Create category using service layer
    const categoryData = await CategoryService.createCategory({
      name: data.name,
      description: data.description,
      imageUrl: data.imageUrl,
      slug: data.slug,
      displayOrder: data.displayOrder,
      isActive: data.isActive,
    });

    console.log('Category created successfully:', categoryData.name);
    return NextResponse.json(categoryData, { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
