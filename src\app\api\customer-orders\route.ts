import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { EmailService } from '@/lib/services/email-service';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// GET /api/customer-orders - Get user's orders
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Admin API: Fetching orders for userId:', userId);

    // Find customer
    const customer = await prisma.customer.findUnique({
      where: { userId },
    });

    if (!customer) {
      console.log('❌ Customer not found for userId:', userId);
      return NextResponse.json([]);
    }

    console.log('✅ Customer found:', customer.id);

    // Get orders for this customer
    const orders = await prisma.order.findMany({
      where: { customerId: customer.id },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        address: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    console.log(`📋 Found ${orders.length} orders for customer:`, customer.id);
    return NextResponse.json(orders);
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST /api/customer-orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();
    console.log('📦 Received order data:', JSON.stringify(orderData, null, 2));

    const { userId, items, shippingAddress, paymentMethod, totalAmount, subtotal, shipping, ...otherData } = orderData;

    if (!userId || !items || !Array.isArray(items) || items.length === 0) {
      console.error('❌ Validation failed:', { userId, items: items?.length });
      return NextResponse.json(
        { error: 'User ID and items are required' },
        { status: 400 }
      );
    }

    // Find or create customer
    console.log('🔍 Looking for customer with userId:', userId);
    let customer;

    try {
      customer = await prisma.customer.findUnique({
        where: { userId },
      });
    } catch (dbError) {
      console.error('❌ Database error finding customer:', dbError);
      throw new Error(`Database error: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`);
    }

    if (!customer) {
      console.log('❌ Customer not found, looking for user...');
      let user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        console.error('❌ User not found with ID:', userId);
        console.log('🔄 Attempting to find user by email from order data...');

        // Try to find user by email if available in shipping address
        if (shippingAddress?.email) {
          user = await prisma.user.findUnique({
            where: { email: shippingAddress.email },
          });

          if (user) {
            console.log('✅ Found user by email:', user.email);
          }
        }

        // If still no user found, create a new customer user
        if (!user) {
          console.log('🔄 Creating new customer user...');
          const customerEmail = shippingAddress?.email || `customer-${Date.now()}@example.com`;
          const customerName = shippingAddress ?
            `${shippingAddress.firstName || ''} ${shippingAddress.lastName || ''}`.trim() :
            'Customer';

          try {
            user = await prisma.user.create({
              data: {
                name: customerName || 'Customer',
                email: customerEmail,
                password: 'temp-password', // Temporary password
                role: 'CUSTOMER',
              },
            });
            console.log('✅ Created new user:', user.email);
          } catch (userCreateError) {
            console.error('❌ Error creating user:', userCreateError);
            throw new Error(`Failed to create user: ${userCreateError instanceof Error ? userCreateError.message : 'Unknown error'}`);
          }
        }
      } else {
        console.log('✅ User found:', user.email);
      }

      console.log('🔄 Creating customer profile for user...');
      try {
        customer = await prisma.customer.create({
          data: {
            userId: user.id, // Use the actual user ID
            firstName: shippingAddress?.firstName || user.name.split(' ')[0] || '',
            lastName: shippingAddress?.lastName || user.name.split(' ').slice(1).join(' ') || '',
            phone: shippingAddress?.phone || null,
          },
        });
        console.log('✅ Customer profile created:', customer.id);
      } catch (customerCreateError) {
        console.error('❌ Error creating customer profile:', customerCreateError);
        throw new Error(`Failed to create customer profile: ${customerCreateError instanceof Error ? customerCreateError.message : 'Unknown error'}`);
      }
    } else {
      console.log('✅ Customer found:', customer.id);
    }

    // Create address if provided
    let addressId = null;
    if (shippingAddress) {
      const address = await prisma.address.create({
        data: {
          customerId: customer.id,
          street: shippingAddress.street || '',
          city: shippingAddress.city || '',
          state: shippingAddress.state || '',
          postalCode: shippingAddress.pincode || shippingAddress.postalCode || '', // Map pincode to postalCode
          country: shippingAddress.country || 'India',
          isDefault: false,
        },
      });
      addressId = address.id;
    }

    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
    console.log('🔢 Generated order number:', orderNumber);

    // Create order
    console.log('🔄 Creating order with data:', {
      customerId: customer.id,
      addressId,
      orderNumber,
      totalAmount: totalAmount || 0,
      subtotal: subtotal || 0,
      shipping: shipping || 0,
      status: 'PENDING_ASSIGNMENT',
      paymentMethod: paymentMethod || 'COD',
      paymentStatus: 'PENDING',
      orderType: 'ONLINE',
    });

    let order;
    try {
      order = await prisma.order.create({
        data: {
          customerId: customer.id,
          addressId,
          orderNumber,
          totalAmount: totalAmount || 0,
          subtotal: subtotal || 0,
          shipping: shipping || 0,
          status: 'PENDING_ASSIGNMENT', // New orders start as pending assignment
          paymentMethod: paymentMethod || 'COD',
          paymentStatus: 'PENDING',
          orderType: 'ONLINE', // All website orders are online orders
          ...otherData,
        },
      });

      console.log('✅ Order created:', order.id, order.orderNumber);
    } catch (orderCreateError) {
      console.error('❌ Error creating order:', orderCreateError);
      throw new Error(`Failed to create order: ${orderCreateError instanceof Error ? orderCreateError.message : 'Unknown error'}`);
    }

    // Create order items
    let orderItems;
    try {
      orderItems = await Promise.all(
        items.map((item: any) =>
          prisma.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId || item.id,
              quantity: item.quantity,
              unitPrice: item.price || item.unitPrice || 0,
            },
          })
        )
      );
      console.log('✅ Order items created:', orderItems.length);
    } catch (orderItemsError) {
      console.error('❌ Error creating order items:', orderItemsError);
      // Clean up the order if items creation fails
      try {
        await prisma.order.delete({ where: { id: order.id } });
        console.log('🧹 Cleaned up order after items creation failure');
      } catch (cleanupError) {
        console.error('❌ Failed to cleanup order:', cleanupError);
      }
      throw new Error(`Failed to create order items: ${orderItemsError instanceof Error ? orderItemsError.message : 'Unknown error'}`);
    }

    // Clear user's cart after successful order
    const cart = await prisma.cart.findUnique({
      where: { userId },
    });

    if (cart) {
      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id },
      });
    }

    // Return order with items
    const completeOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        address: true,
        customer: {
          include: {
            user: true,
          },
        },
      },
    });

    // Send order confirmation email
    if (completeOrder && completeOrder.customer.user) {
      try {
        const emailData = {
          orderNumber: completeOrder.id,
          customerName: `${completeOrder.customer.firstName} ${completeOrder.customer.lastName}`.trim() || completeOrder.customer.user.name,
          customerEmail: completeOrder.customer.user.email,
          items: completeOrder.orderItems.map(item => ({
            name: item.product.name,
            quantity: item.quantity,
            price: item.unitPrice,
            total: item.quantity * item.unitPrice,
          })),
          subtotal: completeOrder.totalAmount - (completeOrder.totalAmount > 1000 ? 0 : 100),
          shipping: completeOrder.totalAmount > 1000 ? 0 : 100,
          total: completeOrder.totalAmount,
          shippingAddress: {
            firstName: completeOrder.address?.firstName || completeOrder.customer.firstName,
            lastName: completeOrder.address?.lastName || completeOrder.customer.lastName,
            street: completeOrder.address?.street || '',
            city: completeOrder.address?.city || '',
            state: completeOrder.address?.state || '',
            pincode: completeOrder.address?.postalCode || '', // Use postalCode from database
            phone: completeOrder.address?.phone || '',
          },
          paymentMethod: completeOrder.paymentMethod,
          estimatedDelivery: EmailService.getEstimatedDelivery(completeOrder.address?.city || 'Bhubaneswar'),
        };

        // Send email asynchronously (don't wait for it to complete)
        EmailService.sendOrderConfirmation(emailData).catch(error => {
          console.error('Failed to send order confirmation email:', error);
        });
      } catch (emailError) {
        console.error('Error preparing order confirmation email:', emailError);
        // Don't fail the order creation if email fails
      }
    }

    return NextResponse.json(completeOrder);
  } catch (error) {
    console.error('❌ Error creating customer order:', error);
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      name: error instanceof Error ? error.name : 'Unknown',
    });

    return NextResponse.json(
      {
        error: 'Failed to create order',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
