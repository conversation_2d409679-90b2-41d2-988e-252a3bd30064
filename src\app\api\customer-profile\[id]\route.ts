import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import bcrypt from 'bcryptjs';

// GET /api/customer-profile/[id] - Get customer profile for website users
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const customer = await prisma.customer.findUnique({
      where: { userId: id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
        addresses: true,
        customerTags: true,
      },
    });

    if (!customer) {
      // If customer profile doesn't exist, create one
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true,
        },
      });

      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      const newCustomer = await prisma.customer.create({
        data: {
          userId: id,
          firstName: user.name.split(' ')[0] || '',
          lastName: user.name.split(' ').slice(1).join(' ') || '',
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              createdAt: true,
            },
          },
          addresses: true,
          customerTags: true,
        },
      });

      return NextResponse.json(newCustomer);
    }

    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error fetching customer profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer profile' },
      { status: 500 }
    );
  }
}

// PUT /api/customer-profile/[id] - Update customer profile
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const data = await request.json();

    // Separate user data from customer data
    const { name, email, firstName, lastName, phone, birthdate, ...otherData } = data;

    // Update user information
    if (name || email) {
      await prisma.user.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(email && { email }),
        },
      });
    }

    // Find or create customer profile
    let customer = await prisma.customer.findUnique({
      where: { userId: id },
    });

    if (!customer) {
      // Create customer profile if it doesn't exist
      customer = await prisma.customer.create({
        data: {
          userId: id,
          firstName: firstName || name?.split(' ')[0] || '',
          lastName: lastName || name?.split(' ').slice(1).join(' ') || '',
          phone,
          birthdate: birthdate ? new Date(birthdate) : null,
          ...otherData,
        },
      });
    } else {
      // Update existing customer profile
      customer = await prisma.customer.update({
        where: { userId: id },
        data: {
          ...(firstName && { firstName }),
          ...(lastName && { lastName }),
          ...(phone && { phone }),
          ...(birthdate && { birthdate: new Date(birthdate) }),
          ...otherData,
        },
      });
    }

    // Return updated customer with user data
    const updatedCustomer = await prisma.customer.findUnique({
      where: { userId: id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
        addresses: true,
        customerTags: true,
      },
    });

    return NextResponse.json(updatedCustomer);
  } catch (error) {
    console.error('Error updating customer profile:', error);
    return NextResponse.json(
      { error: 'Failed to update customer profile' },
      { status: 500 }
    );
  }
}

// PATCH /api/customer-profile/[id] - Update password
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { currentPassword, newPassword } = await request.json();

    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: 'Current password and new password are required' },
        { status: 400 }
      );
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, user.password);
    if (!isValidPassword) {
      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 400 }
      );
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    await prisma.user.update({
      where: { id },
      data: { password: hashedPassword },
    });

    return NextResponse.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error updating password:', error);
    return NextResponse.json(
      { error: 'Failed to update password' },
      { status: 500 }
    );
  }
}
