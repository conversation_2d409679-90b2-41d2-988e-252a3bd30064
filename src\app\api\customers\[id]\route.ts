import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/customers/[id] - Get a specific customer
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Find the customer visit
    const customerVisit = await prisma.customerVisit.findUnique({
      where: { id: params.id },
      include: {
        store: true,
        user: true,
      },
    });

    if (!customerVisit) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Find all visits by this customer (by name)
    const allVisits = await prisma.customerVisit.findMany({
      where: { customerName: customerVisit.customerName },
      orderBy: { date: 'desc' },
      include: {
        store: true,
        user: true,
      },
    });

    // Find transactions that might be associated with this customer
    const transactions = await prisma.transaction.findMany({
      where: {
        partyName: customerVisit.customerName,
        type: 'SALE' // Only include sales transactions
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Calculate total spent and favorite products
    const totalSpent = transactions.reduce((sum, t) => sum + t.totalAmount, 0);

    // Count product occurrences to find favorites
    const productCounts = new Map();
    transactions.forEach(transaction => {
      // Type assertion to access items property
      const transactionWithItems = transaction as unknown as {
        items: Array<{
          product?: { name: string };
          quantity: number;
        }>;
      };

      transactionWithItems.items?.forEach(item => {
        const productName = item.product?.name || 'Unknown Product';
        productCounts.set(
          productName,
          (productCounts.get(productName) || 0) + item.quantity
        );
      });
    });

    // Get top 3 products
    const favoriteProducts = Array.from(productCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(entry => entry[0]);

    // Calculate average order value
    const avgOrderValue = transactions.length > 0 ? totalSpent / transactions.length : 0;

    // Calculate days since last order
    const daysSinceLastOrder = transactions.length > 0
      ? Math.floor((new Date().getTime() - new Date(transactions[0].createdAt).getTime()) / (1000 * 60 * 60 * 24))
      : null;

    // Calculate purchase frequency (average days between orders)
    let purchaseFrequency = null;
    if (transactions.length > 1) {
      const sortedDates = transactions
        .map(t => new Date(t.createdAt).getTime())
        .sort((a, b) => b - a); // Sort descending

      let totalDaysBetween = 0;
      for (let i = 0; i < sortedDates.length - 1; i++) {
        totalDaysBetween += Math.floor((sortedDates[i] - sortedDates[i + 1]) / (1000 * 60 * 60 * 24));
      }

      purchaseFrequency = Math.floor(totalDaysBetween / (sortedDates.length - 1));
    }

    // Get first order date
    const firstOrderDate = transactions.length > 0
      ? transactions.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0].createdAt
      : null;

    // Calculate customer lifetime value
    const customerLifetimeValue = totalSpent;

    // Format the customer data
    const customer = {
      id: customerVisit.id,
      firstName: customerVisit.customerName.split(' ')[0] || '',
      lastName: customerVisit.customerName.split(' ').slice(1).join(' ') || '',
      email: customerVisit.customerContact || '',
      phone: '',
      addresses: [],
      notes: customerVisit.purpose || '',
      tags: [],
      isSubscribedToNewsletter: false,
      createdAt: customerVisit.createdAt,
      updatedAt: customerVisit.updatedAt,
      totalOrders: transactions.length,
      totalSpent,
      avgOrderValue,
      lastOrderDate: transactions.length > 0 ? transactions[0].createdAt : null,
      firstOrderDate,
      daysSinceLastOrder,
      purchaseFrequency,
      customerLifetimeValue,
      favoriteProducts,
      visits: allVisits.map(visit => ({
        id: visit.id,
        date: visit.date,
        store: visit.store?.name || '',
        purpose: visit.purpose || '',
        handledBy: visit.user?.name || '',
      })),
      transactions: transactions.map(t => {
        // Type assertion to access items property
        const transactionWithItems = t as unknown as {
          id: string;
          createdAt: Date;
          totalAmount: number;
          status: string;
          items: Array<{
            product?: { name: string };
            quantity: number;
            unitPrice: number;
          }>;
        };

        return {
          id: t.id,
          date: t.createdAt,
          amount: t.totalAmount,
          status: t.status,
          items: transactionWithItems.items?.map(item => ({
            product: item.product?.name || 'Unknown Product',
            quantity: item.quantity,
            price: item.unitPrice,
          })) || [],
        };
      }),
    };

    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    );
  }
}

// PUT /api/customers/[id] - Update a customer
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Find the customer visit
    const customerVisit = await prisma.customerVisit.findUnique({
      where: { id: params.id },
    });

    if (!customerVisit) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Update the customer visit
    const updatedVisit = await prisma.customerVisit.update({
      where: { id: params.id },
      data: {
        customerName: `${data.firstName} ${data.lastName}`.trim(),
        customerContact: data.email || null,
        purpose: data.notes || null,
      },
    });

    // Update all other visits with the same customer name
    if (customerVisit.customerName !== `${data.firstName} ${data.lastName}`.trim()) {
      await prisma.customerVisit.updateMany({
        where: { customerName: customerVisit.customerName },
        data: {
          customerName: `${data.firstName} ${data.lastName}`.trim(),
          customerContact: data.email || null,
        },
      });

      // Also update transactions with this customer name
      await prisma.transaction.updateMany({
        where: {
          partyName: customerVisit.customerName,
          type: 'SALE' // Only update sales transactions
        },
        data: {
          partyName: `${data.firstName} ${data.lastName}`.trim(),
          partyContact: data.email || null,
        },
      });
    }

    return NextResponse.json({
      id: updatedVisit.id,
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email || '',
      notes: data.notes || '',
      updatedAt: updatedVisit.updatedAt,
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    return NextResponse.json(
      { error: 'Failed to update customer' },
      { status: 500 }
    );
  }
}

// DELETE /api/customers/[id] - Delete a customer
export async function DELETE(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    // Find the customer visit
    const customerVisit = await prisma.customerVisit.findUnique({
      where: { id: params.id },
    });

    if (!customerVisit) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Delete all visits with this customer name
    await prisma.customerVisit.deleteMany({
      where: { customerName: customerVisit.customerName },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting customer:', error);
    return NextResponse.json(
      { error: 'Failed to delete customer' },
      { status: 500 }
    );
  }
}
