import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/customers/analytics - Get customer analytics
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || 'all';

    // Calculate date range based on timeRange
    let dateFilter: { gte?: Date } = {};
    const now = new Date();

    if (timeRange !== 'all') {
      const start = new Date();

      switch (timeRange) {
        case 'week':
          start.setDate(now.getDate() - 7);
          break;
        case 'month':
          start.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          start.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          start.setFullYear(now.getFullYear() - 1);
          break;
      }

      dateFilter = { gte: start };
    }

    // Get all customer visits
    const customerVisits = await prisma.customerVisit.findMany({
      where: {
        createdAt: dateFilter,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get all transactions
    const transactions = await prisma.transaction.findMany({
      where: {
        type: 'SALE',
        partyName: {
          not: null,
        },
        createdAt: dateFilter,
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Extract unique customers from visits
    const uniqueCustomers = new Set();
    customerVisits.forEach(visit => {
      uniqueCustomers.add(visit.customerName);
    });

    // Group transactions by customer
    const customerTransactions = new Map();
    transactions.forEach(transaction => {
      if (transaction.partyName) {
        if (!customerTransactions.has(transaction.partyName)) {
          customerTransactions.set(transaction.partyName, []);
        }
        customerTransactions.get(transaction.partyName).push(transaction);
      }
    });

    // Calculate customer metrics
    const totalCustomers = uniqueCustomers.size;
    const customersWithOrders = customerTransactions.size;
    const totalOrders = transactions.length;
    const totalRevenue = transactions.reduce((sum, t) => sum + t.totalAmount, 0);

    // Calculate average metrics
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const avgOrdersPerCustomer = customersWithOrders > 0 ? totalOrders / customersWithOrders : 0;
    const avgRevenuePerCustomer = customersWithOrders > 0 ? totalRevenue / customersWithOrders : 0;

    // Calculate customer segments
    const customerSegments = [
      { name: 'One-time', count: 0, revenue: 0 },
      { name: 'Occasional', count: 0, revenue: 0 },
      { name: 'Regular', count: 0, revenue: 0 },
      { name: 'Loyal', count: 0, revenue: 0 },
    ];

    // Calculate purchase frequency distribution
    const purchaseFrequency = [
      { frequency: '1', customers: 0, revenue: 0 },
      { frequency: '2-3', customers: 0, revenue: 0 },
      { frequency: '4-6', customers: 0, revenue: 0 },
      { frequency: '7+', customers: 0, revenue: 0 },
    ];

    // Calculate customer retention
    const retentionData = [];

    // Process customer transactions for segments and frequency
    customerTransactions.forEach((txns, customerName) => {
      const customerRevenue = txns.reduce((sum: number, t: any) => sum + t.totalAmount, 0);

      // Segment based on order count
      if (txns.length === 1) {
        customerSegments[0].count++;
        customerSegments[0].revenue += customerRevenue;
        purchaseFrequency[0].customers++;
        purchaseFrequency[0].revenue += customerRevenue;
      } else if (txns.length >= 2 && txns.length <= 3) {
        customerSegments[1].count++;
        customerSegments[1].revenue += customerRevenue;
        purchaseFrequency[1].customers++;
        purchaseFrequency[1].revenue += customerRevenue;
      } else if (txns.length >= 4 && txns.length <= 6) {
        customerSegments[2].count++;
        customerSegments[2].revenue += customerRevenue;
        purchaseFrequency[2].customers++;
        purchaseFrequency[2].revenue += customerRevenue;
      } else {
        customerSegments[3].count++;
        customerSegments[3].revenue += customerRevenue;
        purchaseFrequency[3].customers++;
        purchaseFrequency[3].revenue += customerRevenue;
      }
    });

    // Calculate retention by month (for the last 6 months)
    const months = [];
    for (let i = 0; i < 6; i++) {
      const month = new Date();
      month.setMonth(month.getMonth() - i);
      month.setDate(1);
      month.setHours(0, 0, 0, 0);
      months.push(month);
    }

    for (let i = 0; i < months.length; i++) {
      const monthStart = months[i];
      const monthEnd = new Date(monthStart);
      monthEnd.setMonth(monthEnd.getMonth() + 1);

      // Get transactions for this month
      const monthTransactions = transactions.filter(t =>
        t.createdAt >= monthStart && t.createdAt < monthEnd
      );

      // Get unique customers for this month
      const monthCustomers = new Set();
      monthTransactions.forEach(t => {
        if (t.partyName) {
          monthCustomers.add(t.partyName);
        }
      });

      // Get new vs returning customers
      let newCustomers = 0;
      let returningCustomers = 0;

      monthCustomers.forEach(customer => {
        // Check if customer had transactions before this month
        const customerTxns = customerTransactions.get(customer as string) || [];
        const hadPriorTransactions = customerTxns.some((t: any) => t.createdAt < monthStart);

        if (hadPriorTransactions) {
          returningCustomers++;
        } else {
          newCustomers++;
        }
      });

      // Calculate churn rate (approximate)
      const prevMonth = i < months.length - 1 ? months[i + 1] : null;
      let churnRate = 0;

      if (prevMonth) {
        const prevMonthEnd = new Date(prevMonth);
        prevMonthEnd.setMonth(prevMonthEnd.getMonth() + 1);

        // Get transactions for previous month
        const prevMonthTransactions = transactions.filter(t =>
          t.createdAt >= prevMonth && t.createdAt < prevMonthEnd
        );

        // Get unique customers for previous month
        const prevMonthCustomers = new Set();
        prevMonthTransactions.forEach(t => {
          if (t.partyName) {
            prevMonthCustomers.add(t.partyName);
          }
        });

        // Count customers who didn't return
        let churnedCustomers = 0;
        prevMonthCustomers.forEach(customer => {
          if (!monthCustomers.has(customer)) {
            churnedCustomers++;
          }
        });

        // Calculate churn rate
        churnRate = prevMonthCustomers.size > 0
          ? (churnedCustomers / prevMonthCustomers.size) * 100
          : 0;
      }

      // Add to retention data
      retentionData.push({
        month: monthStart.toLocaleString('default', { month: 'short' }),
        newCustomers,
        returningCustomers,
        totalCustomers: monthCustomers.size,
        churnRate,
      });
    }

    // Format customer segments for chart
    const formattedSegments = customerSegments.map((segment, index) => {
      const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042'];
      return {
        name: segment.name,
        value: segment.count,
        revenue: segment.revenue,
        color: colors[index % colors.length],
      };
    });

    return NextResponse.json({
      summary: {
        totalCustomers,
        customersWithOrders,
        totalOrders,
        totalRevenue,
        avgOrderValue,
        avgOrdersPerCustomer,
        avgRevenuePerCustomer,
      },
      segments: formattedSegments,
      frequency: purchaseFrequency,
      retention: retentionData.reverse(), // Reverse to get chronological order
    });
  } catch (error) {
    console.error('Error generating customer analytics:', error);
    return NextResponse.json(
      { error: 'Failed to generate customer analytics' },
      { status: 500 }
    );
  }
}
