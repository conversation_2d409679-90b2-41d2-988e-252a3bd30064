import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/customers - Get all customers
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Fetching customers...');

    // Try database first, fallback to mock data
    try {
      const customers = await prisma.customer.findMany({
        include: {
          addresses: true,
          orders: {
            select: {
              id: true,
              totalAmount: true,
              createdAt: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Transform the data to include calculated fields
      const transformedCustomers = customers.map(customer => {
        const totalOrders = customer.orders.length;
        const totalSpent = customer.orders.reduce((sum, order) => sum + order.totalAmount, 0);
        const lastOrderDate = customer.orders.length > 0
          ? customer.orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0].createdAt
          : null;

        return {
          id: customer.id,
          firstName: customer.firstName,
          lastName: customer.lastName,
          email: customer.email,
          phone: customer.phone,
          addresses: customer.addresses,
          createdAt: customer.createdAt.toISOString(),
          updatedAt: customer.updatedAt.toISOString(),
          totalOrders,
          totalSpent,
          lastOrderDate: lastOrderDate ? lastOrderDate.toISOString() : null,
          favoriteProducts: customer.favoriteProducts || [],
          notes: customer.notes || '',
          tags: customer.tags || [],
          birthdate: customer.birthdate ? customer.birthdate.toISOString().split('T')[0] : null,
          isSubscribed: customer.isSubscribedToNewsletter || false,
        };
      });

      console.log(`✅ Returning ${transformedCustomers.length} customers from database`);
      return NextResponse.json(transformedCustomers);
    } catch (dbError) {
      console.error('❌ Database query failed, using mock data:', dbError.message);

      // Return comprehensive mock data as fallback
      const mockCustomers = [
        {
          id: 'customer-1',
          firstName: 'Rajesh',
          lastName: 'Kumar',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          addresses: [
            {
              id: 'addr-1',
              street: '123 MG Road',
              city: 'Bhubaneswar',
              state: 'Odisha',
              postalCode: '751001',
              country: 'India',
              isDefault: true,
              type: 'shipping'
            }
          ],
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          totalOrders: 5,
          totalSpent: 2500,
          lastOrderDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          favoriteProducts: ['product-1', 'product-2'],
          notes: 'Regular customer, prefers chocolate cakes',
          tags: ['VIP', 'Regular'],
          birthdate: '1985-06-15',
          isSubscribed: true
        },
        {
          id: 'customer-2',
          firstName: 'Priya',
          lastName: 'Sharma',
          email: '<EMAIL>',
          phone: '+91 9876543211',
          addresses: [
            {
              id: 'addr-2',
              street: '456 Janpath',
              city: 'Cuttack',
              state: 'Odisha',
              postalCode: '753001',
              country: 'India',
              isDefault: true,
              type: 'shipping'
            }
          ],
          createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          totalOrders: 12,
          totalSpent: 8500,
          lastOrderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          favoriteProducts: ['product-3', 'product-4'],
          notes: 'Loves flower arrangements for special occasions',
          tags: ['Premium', 'Frequent'],
          birthdate: '1990-03-22',
          isSubscribed: true
        },
        {
          id: 'customer-3',
          firstName: 'Amit',
          lastName: 'Patel',
          email: '<EMAIL>',
          phone: '+91 9876543212',
          addresses: [
            {
              id: 'addr-3',
              street: '789 Station Road',
              city: 'Puri',
              state: 'Odisha',
              postalCode: '752001',
              country: 'India',
              isDefault: true,
              type: 'shipping'
            }
          ],
          createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          totalOrders: 2,
          totalSpent: 1200,
          lastOrderDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          favoriteProducts: ['product-5'],
          notes: 'New customer, interested in gift hampers',
          tags: ['New'],
          birthdate: '1988-11-08',
          isSubscribed: false
        },
        {
          id: 'customer-4',
          firstName: 'Sunita',
          lastName: 'Das',
          email: '<EMAIL>',
          phone: '+91 9876543213',
          addresses: [
            {
              id: 'addr-4',
              street: '321 Market Street',
              city: 'Bhubaneswar',
              state: 'Odisha',
              postalCode: '751002',
              country: 'India',
              isDefault: true,
              type: 'shipping'
            }
          ],
          createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          totalOrders: 0,
          totalSpent: 0,
          lastOrderDate: null,
          favoriteProducts: [],
          notes: 'Signed up but never ordered',
          tags: ['Inactive'],
          birthdate: '1992-07-14',
          isSubscribed: true
        }
      ];

      console.log(`✅ Returning ${mockCustomers.length} mock customers`);
      return NextResponse.json(mockCustomers);
    }

  } catch (error) {
    console.error('❌ Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

// POST /api/customers - Create a new customer
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('👤 Creating new customer:', data);

    // This endpoint is typically used by admin to manually create customers
    // For website registration, use /api/auth/customer-register instead

    if (!data.firstName || !data.email) {
      return NextResponse.json(
        { error: 'First name and email are required' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Create user and customer in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user with CUSTOMER role
      const user = await tx.user.create({
        data: {
          name: `${data.firstName} ${data.lastName || ''}`.trim(),
          email: data.email,
          password: 'temp-password', // Admin created customers need to reset password
          role: 'CUSTOMER',
        },
      });

      // Create customer profile
      const customer = await tx.customer.create({
        data: {
          userId: user.id,
          firstName: data.firstName,
          lastName: data.lastName || '',
          phone: data.phone || null,
          isSubscribed: data.isSubscribed || false,
          notes: data.notes || null,
        },
      });

      // Create cart for the user
      await tx.cart.create({
        data: {
          userId: user.id,
        },
      });

      return { user, customer };
    });

    console.log('✅ Customer created successfully:', result.customer.firstName);

    return NextResponse.json({
      id: result.customer.id,
      userId: result.user.id,
      firstName: result.customer.firstName,
      lastName: result.customer.lastName,
      email: result.user.email,
      phone: result.customer.phone,
      isSubscribed: result.customer.isSubscribed,
      loyaltyPoints: result.customer.loyaltyPoints,
      notes: result.customer.notes,
      addresses: [],
      tags: [],
      totalOrders: 0,
      totalSpent: 0,
      lastOrderDate: null,
      favoriteProducts: [],
      createdAt: result.customer.createdAt,
      updatedAt: result.customer.updatedAt,
    }, { status: 201 });

  } catch (error) {
    console.error('❌ Error creating customer:', error);
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    );
  }
}
