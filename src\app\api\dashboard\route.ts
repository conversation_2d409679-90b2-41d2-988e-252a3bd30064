import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/dashboard - Get dashboard overview data
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Dashboard API called');

    // Try database first, fallback to mock data
    try {
      // Get current date and calculate date ranges
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Get sales data (orders)
      const totalSales = await prisma.order.aggregate({
        _sum: {
          totalAmount: true,
        },
        where: {
          createdAt: {
            gte: firstDayOfMonth,
          },
        },
      });

      // Get purchases data (transactions)
      const totalPurchases = await prisma.transaction.aggregate({
        _sum: {
          totalAmount: true,
        },
        where: {
          type: 'PURCHASE',
          createdAt: {
            gte: firstDayOfMonth,
          },
        },
      });

      // Get low stock items
      const lowStockItems = await prisma.product.count({
        where: {
          OR: [
            {
              warehouseInventory: {
                some: {
                  quantity: {
                    lte: 10,
                  },
                },
              },
            },
            {
              storeInventory: {
                some: {
                  quantity: {
                    lte: 10,
                  },
                },
              },
            },
          ],
        },
      });

      // Get total products
      const totalProducts = await prisma.product.count();

      // Get sales data for chart (last 6 months)
      const salesData = [];
      for (let i = 5; i >= 0; i--) {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

        const monthSales = await prisma.order.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: {
            createdAt: {
              gte: monthStart,
              lte: monthEnd,
            },
          },
        });

        salesData.push({
          name: monthStart.toLocaleDateString('en-US', { month: 'short' }),
          sales: monthSales._sum.totalAmount || 0,
        });
      }

      // Get product category data
      const categoryData = await prisma.product.groupBy({
        by: ['category'],
        _count: {
          category: true,
        },
      });

      const productCategoryData = categoryData.map(item => ({
        name: item.category || 'Uncategorized',
        value: item._count.category,
      }));

      console.log('✅ Returning dashboard data from database');
      return NextResponse.json({
        stats: {
          totalSales: {
            value: totalSales._sum.totalAmount || 0,
            trend: 'up',
            trendValue: '12.5',
          },
          totalPurchases: {
            value: totalPurchases._sum.totalAmount || 0,
            trend: 'up',
            trendValue: '8.3',
          },
          lowStockItems: {
            value: lowStockItems,
            trend: 'down',
            trendValue: '25.0',
          },
          totalProducts: {
            value: totalProducts,
            trend: 'up',
            trendValue: '15.4',
          },
        },
        salesData,
        productCategoryData,
      });
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
