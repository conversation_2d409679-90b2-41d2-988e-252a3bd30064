import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// GET /api/debug-orders - Debug orders in database
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Admin API: Checking orders in database...');

    // Get all orders
    const orders = await prisma.order.findMany({
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        customer: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10 // Limit to last 10 orders
    });

    // Get order count
    const orderCount = await prisma.order.count();

    // Get customer count
    const customerCount = await prisma.customer.count();

    // Get user count
    const userCount = await prisma.user.count();

    console.log(`✅ Admin API: Found ${orders.length} orders in database`);
    
    return NextResponse.json({
      success: true,
      counts: {
        orders: orderCount,
        customers: customerCount,
        users: userCount,
      },
      recentOrders: orders.map(order => ({
        id: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        totalAmount: order.totalAmount,
        createdAt: order.createdAt,
        customerName: order.customer ? `${order.customer.firstName} ${order.customer.lastName}` : 'Unknown',
        itemCount: order.orderItems?.length || 0,
      })),
      environment: 'admin-panel'
    });

  } catch (error) {
    console.error('❌ Admin API: Debug orders error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Database error',
      environment: 'admin-panel'
    }, { status: 500 });
  }
}
