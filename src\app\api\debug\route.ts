import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/debug - Debug database and order creation
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Starting debug check...');

    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connected');

    // Count records
    const [userCount, customerCount, productCount, orderCount] = await Promise.all([
      prisma.user.count(),
      prisma.customer.count(),
      prisma.product.count(),
      prisma.order.count(),
    ]);

    console.log('📊 Record counts:', { userCount, customerCount, productCount, orderCount });

    // Check demo customer
    const demoUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    console.log('👤 Demo user:', demoUser ? 'exists' : 'not found');

    // Get sample products
    const sampleProducts = await prisma.product.findMany({
      take: 3,
      select: {
        id: true,
        name: true,
        price: true,
        isActive: true,
      }
    });

    console.log('📦 Sample products:', sampleProducts.length);

    // Get recent orders
    const recentOrders = await prisma.order.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        totalAmount: true,
        createdAt: true,
      }
    });

    console.log('📋 Recent orders:', recentOrders.length);

    return NextResponse.json({
      status: 'success',
      database: 'connected',
      counts: {
        users: userCount,
        customers: customerCount,
        products: productCount,
        orders: orderCount,
      },
      demoUser: demoUser ? {
        id: demoUser.id,
        email: demoUser.email,
        hasCustomerProfile: !!demoUser.customer,
        customerId: demoUser.customer?.id,
      } : null,
      sampleProducts,
      recentOrders,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ Debug check failed:', error);
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    }, { status: 500 });
  }
}

// POST /api/debug - Create demo customer and test order
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Creating demo customer and test order...');

    // Step 1: Create or find demo customer
    let user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    if (!user) {
      console.log('Creating demo user...');
      user = await prisma.user.create({
        data: {
          name: 'Demo Customer',
          email: '<EMAIL>',
          password: 'customer123',
          role: 'CUSTOMER',
          customer: {
            create: {
              firstName: 'Demo',
              lastName: 'Customer',
              phone: '+91 9876543210',
              isSubscribed: false,
              loyaltyPoints: 100,
            }
          }
        },
        include: { customer: true }
      });
      console.log('✅ Demo user created');
    } else {
      console.log('✅ Demo user exists');
    }

    // Step 2: Get or create a product
    let product = await prisma.product.findFirst({
      where: { isActive: true }
    });

    if (!product) {
      console.log('Creating demo product...');
      product = await prisma.product.create({
        data: {
          name: 'Demo Chocolate Cake',
          description: 'A delicious chocolate cake for testing',
          price: 299.99,
          costPrice: 150.00,
          sku: 'DEMO-CHOC-001',
          unit: 'piece',
          isActive: true,
        }
      });
      console.log('✅ Demo product created');
    } else {
      console.log('✅ Using existing product:', product.name);
    }

    // Step 3: Create address
    const address = await prisma.address.create({
      data: {
        customerId: user.customer!.id,
        street: '123 Test Street',
        city: 'Bhubaneswar',
        state: 'Odisha',
        postalCode: '751001',
        country: 'India',
        isDefault: false,
      }
    });
    console.log('✅ Address created');

    // Step 4: Create order
    const orderNumber = `DEBUG-${Date.now()}`;
    const order = await prisma.order.create({
      data: {
        customerId: user.customer!.id,
        addressId: address.id,
        orderNumber,
        totalAmount: product.price,
        subtotal: product.price,
        shipping: 0,
        status: 'PENDING_ASSIGNMENT',
        paymentMethod: 'COD',
        paymentStatus: 'PENDING',
        orderType: 'ONLINE',
      }
    });
    console.log('✅ Order created');

    // Step 5: Create order item
    const orderItem = await prisma.orderItem.create({
      data: {
        orderId: order.id,
        productId: product.id,
        quantity: 1,
        unitPrice: product.price,
      }
    });
    console.log('✅ Order item created');

    // Step 6: Fetch complete order
    const completeOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        orderItems: {
          include: {
            product: true,
          }
        },
        address: true,
        customer: {
          include: {
            user: true,
          }
        }
      }
    });

    return NextResponse.json({
      status: 'success',
      message: 'Demo customer and test order created successfully',
      user: {
        id: user.id,
        email: user.email,
        customerId: user.customer!.id,
      },
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
      },
      order: completeOrder,
    });

  } catch (error) {
    console.error('❌ Demo creation failed:', error);
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    }, { status: 500 });
  }
}
