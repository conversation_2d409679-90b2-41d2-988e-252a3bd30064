import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/expenses/[id] - Get a specific expense
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const expense = await prisma.expense.findUnique({
      where: { id: params.id },
      include: {
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Format the response
    const formattedExpense = {
      id: expense.id,
      date: expense.date.toISOString().split('T')[0],
      storeId: expense.storeId || '',
      storeName: expense.store?.name || 'N/A',
      category: expense.category,
      amount: expense.amount,
      description: expense.description || '',
      userId: expense.userId,
      userName: expense.user?.name || 'Unknown',
      createdAt: expense.createdAt,
      updatedAt: expense.updatedAt,
    };

    return NextResponse.json(formattedExpense);
  } catch (error) {
    console.error('Error fetching expense:', error);
    return NextResponse.json(
      { error: 'Failed to fetch expense' },
      { status: 500 }
    );
  }
}

// PUT /api/expenses/[id] - Update an expense
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Validate required fields
    if (data.date === undefined) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    if (data.category === undefined) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      );
    }

    if (data.amount === undefined || isNaN(parseFloat(data.amount)) || parseFloat(data.amount) <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    // Check if the expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id: params.id },
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Update the expense
    const expense = await prisma.expense.update({
      where: { id: params.id },
      data: {
        date: new Date(data.date),
        storeId: data.storeId || null,
        category: data.category,
        amount: parseFloat(data.amount),
        description: data.description || null,
      },
      include: {
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Format the response
    const formattedExpense = {
      id: expense.id,
      date: expense.date.toISOString().split('T')[0],
      storeId: expense.storeId || '',
      storeName: expense.store?.name || 'N/A',
      category: expense.category,
      amount: expense.amount,
      description: expense.description || '',
      userId: expense.userId,
      userName: expense.user?.name || 'Unknown',
      createdAt: expense.createdAt,
      updatedAt: expense.updatedAt,
    };

    return NextResponse.json(formattedExpense);
  } catch (error) {
    console.error('Error updating expense:', error);
    return NextResponse.json(
      { error: 'Failed to update expense' },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/[id] - Delete an expense
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id: params.id },
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Delete the expense
    await prisma.expense.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting expense:', error);
    return NextResponse.json(
      { error: 'Failed to delete expense' },
      { status: 500 }
    );
  }
}
