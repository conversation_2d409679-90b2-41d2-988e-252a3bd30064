import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/expenses - Get all expenses (NeonDB preferred, mock fallback)
export async function GET(request: NextRequest) {
  try {
    console.log('💸 Expenses API called - trying NeonDB first');

    // Try NeonDB first
    try {
    // Get query parameters for filtering
    const url = new URL(request.url);
    const storeId = url.searchParams.get('storeId');
    const category = url.searchParams.get('category');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Build the where clause for filtering
    const where: any = {};

    if (storeId && storeId !== 'all') {
      where.storeId = storeId;
    }

    if (category && category !== 'all') {
      where.category = category;
    }

    if (startDate || endDate) {
      where.date = {};

      if (startDate) {
        where.date.gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        where.date.lt = endDateObj;
      }
    }

    const expenses = await prisma.expense.findMany({
      where,
      include: {
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    // Format the response
    const formattedExpenses = expenses.map(expense => ({
      id: expense.id,
      date: expense.date.toISOString().split('T')[0],
      storeId: expense.storeId || '',
      storeName: expense.store?.name || 'N/A',
      category: expense.category,
      amount: expense.amount,
      description: expense.description || '',
      userId: expense.userId,
      userName: expense.user?.name || 'Unknown',
      createdAt: expense.createdAt,
      updatedAt: expense.updatedAt,
    }));

      return NextResponse.json(formattedExpenses);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error in expenses API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch expenses' },
      { status: 500 }
    );
  }
}

// POST /api/expenses - Create a new expense
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.date) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    if (!data.category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      );
    }

    if (!data.amount || isNaN(parseFloat(data.amount)) || parseFloat(data.amount) <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    // Get the first user for demo purposes if userId is not provided
    let userId = data.userId;
    if (!userId) {
      const user = await prisma.user.findFirst();
      if (!user) {
        return NextResponse.json(
          { error: 'No user found' },
          { status: 400 }
        );
      }
      userId = user.id;
    }

    // Create the expense
    const expense = await prisma.expense.create({
      data: {
        date: new Date(data.date),
        storeId: data.storeId || null,
        userId,
        category: data.category,
        amount: parseFloat(data.amount),
        description: data.description || null,
      },
      include: {
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Format the response
    const formattedExpense = {
      id: expense.id,
      date: expense.date.toISOString().split('T')[0],
      storeId: expense.storeId || '',
      storeName: expense.store?.name || 'N/A',
      category: expense.category,
      amount: expense.amount,
      description: expense.description || '',
      userId: expense.userId,
      userName: expense.user?.name || 'Unknown',
      createdAt: expense.createdAt,
      updatedAt: expense.updatedAt,
    };

    return NextResponse.json(formattedExpense, { status: 201 });
  } catch (error) {
    console.error('Error creating expense:', error);
    return NextResponse.json(
      { error: 'Failed to create expense' },
      { status: 500 }
    );
  }
}
