import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import bcrypt from 'bcryptjs';

// POST /api/init-db - Initialize database with default data
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Initializing database with default data...');

    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      return NextResponse.json({
        message: 'Database already initialized',
        admin: existingAdmin.email
      });
    }

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const adminUser = await prisma.user.create({
      data: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    console.log('✅ Created default admin user:', adminUser.email);

    // Create default warehouses
    const warehouses = await Promise.all([
      prisma.warehouse.create({
        data: {
          name: 'Main Warehouse',
          location: 'Bhubaneswar Central',
        }
      }),
      prisma.warehouse.create({
        data: {
          name: 'Secondary Warehouse',
          location: 'Cuttack Industrial Area',
        }
      }),
      prisma.warehouse.create({
        data: {
          name: 'Cold Storage Warehouse',
          location: 'Puri Storage Complex',
        }
      })
    ]);

    console.log(`✅ Created ${warehouses.length} default warehouses`);

    // Create default stores
    const stores = await Promise.all([
      prisma.store.create({
        data: {
          name: 'Main Store',
          location: 'Bhubaneswar Central Plaza',
        }
      }),
      prisma.store.create({
        data: {
          name: 'Branch Store',
          location: 'Cuttack Market Square',
        }
      }),
      prisma.store.create({
        data: {
          name: 'Express Store',
          location: 'Puri Beach Road',
        }
      })
    ]);

    console.log(`✅ Created ${stores.length} default stores`);

    return NextResponse.json({
      success: true,
      message: 'Database initialized successfully',
      data: {
        admin: adminUser.email,
        warehouses: warehouses.length,
        stores: stores.length
      }
    });

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    return NextResponse.json(
      { error: 'Failed to initialize database', details: error.message },
      { status: 500 }
    );
  }
}

// GET /api/init-db - Check database status
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking database status...');

    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Check if tables exist and have data
    const userCount = await prisma.user.count();
    const warehouseCount = await prisma.warehouse.count();
    const storeCount = await prisma.store.count();

    return NextResponse.json({
      connected: true,
      status: 'healthy',
      data: {
        users: userCount,
        warehouses: warehouseCount,
        stores: storeCount
      }
    });

  } catch (error) {
    console.error('❌ Database status check failed:', error);
    return NextResponse.json(
      { 
        connected: false, 
        status: 'error', 
        error: error.message 
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
