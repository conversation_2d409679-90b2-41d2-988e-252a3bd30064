import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/inventory-items/[id] - Get a specific inventory item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔍 Inventory Item API called for ID: ${params.id} - fetching from database`);

    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Inventory item not found' },
        { status: 404 }
      );
    }

    // Calculate total stock across all warehouses
    const warehouseStock = product.warehouseInventory.reduce(
      (sum, item) => sum + item.quantity,
      0
    );

    // Calculate total stock across all stores
    const storeStock = product.storeInventory.reduce(
      (sum, item) => sum + item.quantity,
      0
    );

    // Get the primary location (first warehouse with stock)
    const primaryLocation = product.warehouseInventory.length > 0
      ? product.warehouseInventory[0].warehouse.name
      : 'No location';

    // Get the last restocked date (most recent inventory update)
    const allInventory = [
      ...product.warehouseInventory,
      ...product.storeInventory,
    ];

    const lastRestocked = allInventory.length > 0
      ? new Date(Math.max(...allInventory.map(item => item.updatedAt.getTime())))
          .toISOString().split('T')[0]
      : null;

    // Get transactions related to this product
    const transactions = await prisma.transaction.findMany({
      where: {
        items: {
          some: {
            productId: params.id
          }
        }
      },
      include: {
        items: {
          where: {
            productId: params.id
          },
          include: {
            product: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format transactions
    const formattedTransactions = transactions.map(transaction => {
      const item = transaction.items[0];

      return {
        id: transaction.id,
        itemId: params.id,
        date: transaction.createdAt.toISOString().split('T')[0],
        type: transaction.type.toLowerCase(),
        quantity: item ? item.quantity : 0,
        unitCost: item ? item.unitPrice : 0,
        totalCost: item ? item.totalPrice : 0,
        reference: `${transaction.type}-${transaction.id.substring(0, 8)}`,
        notes: null,
      };
    });

    const inventoryItem = {
      id: product.id,
      name: product.name,
      sku: product.sku || `PROD-${product.id.substring(0, 8)}`,
      category: product.category || 'Uncategorized',
      description: product.description || '',
      unitOfMeasure: product.unit || 'piece',
      costPerUnit: product.costPrice || 0,
      currentStock: warehouseStock + storeStock,
      reorderPoint: product.lowStockThreshold || 10,
      reorderQuantity: product.lowStockThreshold ? product.lowStockThreshold * 2 : 20,
      location: primaryLocation,
      supplier: null, // We don't have direct supplier info in the schema
      lastRestocked,
      imageUrl: product.imageUrl || null,
      transactions: formattedTransactions,
      warehouseInventory: product.warehouseInventory.map(item => ({
        warehouseId: item.warehouseId,
        warehouseName: item.warehouse.name,
        quantity: item.quantity,
      })),
      storeInventory: product.storeInventory.map(item => ({
        storeId: item.storeId,
        storeName: item.store.name,
        quantity: item.quantity,
      })),
    };

    console.log(`✅ Returning inventory item from database: ${inventoryItem.name}`);
    return NextResponse.json(inventoryItem);
  } catch (error) {
    console.error('Error fetching inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory item' },
      { status: 500 }
    );
  }
}

// PUT /api/inventory-items/[id] - Update an inventory item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Use the same type assertion as in the GET function
    type ProductWithInventory = {
      id: string;
      name: string;
      description: string | null;
      category: string;
      price: number;
      costPrice: number;
      unit: string;
      lowStockThreshold: number;
      sku: string | null;
      imageUrl: string | null;
      createdAt: Date;
      updatedAt: Date;
      warehouseInventory: Array<{
        id: string;
        warehouseId: string;
        warehouse: {
          id: string;
          name: string;
          location: string;
          createdAt: Date;
          updatedAt: Date;
        };
        productId: string;
        quantity: number;
        createdAt: Date;
        updatedAt: Date;
      }>;
      storeInventory: Array<{
        id: string;
        storeId: string;
        store: {
          id: string;
          name: string;
          location: string;
          createdAt: Date;
          updatedAt: Date;
        };
        productId: string;
        quantity: number;
        createdAt: Date;
        updatedAt: Date;
      }>;
    };

    // Update the product
    // We need to use a workaround for the sku and imageUrl fields
    // since they're not recognized by the TypeScript types yet
    const updateData: any = {
      name: data.name,
      description: data.description || '',
      category: data.category || 'Uncategorized',
      unit: data.unitOfMeasure || 'piece',
      costPrice: data.costPerUnit || 0,
      lowStockThreshold: data.reorderPoint || 10,
    };

    // Add the new fields
    updateData.sku = data.sku || null;
    updateData.imageUrl = data.imageUrl || null;

    const product = await prisma.product.update({
      where: { id: params.id },
      data: updateData,
      include: {
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    }) as ProductWithInventory;

    // Calculate total stock across all warehouses
    const warehouseStock = product.warehouseInventory.reduce(
      (sum: number, item: { quantity: number }) => sum + item.quantity,
      0
    );

    // Calculate total stock across all stores
    const storeStock = product.storeInventory.reduce(
      (sum: number, item: { quantity: number }) => sum + item.quantity,
      0
    );

    // Get the primary location (first warehouse with stock)
    const primaryLocation = product.warehouseInventory.length > 0
      ? product.warehouseInventory[0].warehouse.name
      : 'No location';

    // Get the last restocked date (most recent inventory update)
    const allInventory = [
      ...product.warehouseInventory,
      ...product.storeInventory,
    ];

    const lastRestocked = allInventory.length > 0
      ? new Date(Math.max(...allInventory.map(item => item.updatedAt.getTime())))
          .toISOString().split('T')[0]
      : null;

    return NextResponse.json({
      id: product.id,
      name: product.name,
      sku: product.sku || `PROD-${product.id.substring(0, 8)}`,
      category: product.category || 'Uncategorized',
      description: product.description || '',
      unitOfMeasure: product.unit || 'piece',
      costPerUnit: product.costPrice || 0,
      currentStock: warehouseStock + storeStock,
      reorderPoint: product.lowStockThreshold || 10,
      reorderQuantity: product.lowStockThreshold ? product.lowStockThreshold * 2 : 20,
      location: primaryLocation,
      supplier: null, // We don't have direct supplier info in the schema
      lastRestocked,
      imageUrl: product.imageUrl || null,
    });
  } catch (error) {
    console.error('Error updating inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to update inventory item' },
      { status: 500 }
    );
  }
}

// DELETE /api/inventory-items/[id] - Delete an inventory item
export async function DELETE(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    // Delete all warehouse inventory for this product
    await prisma.warehouseInventory.deleteMany({
      where: { productId: params.id },
    });

    // Delete all store inventory for this product
    await prisma.storeInventory.deleteMany({
      where: { productId: params.id },
    });

    // Delete the product
    await prisma.product.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to delete inventory item' },
      { status: 500 }
    );
  }
}
