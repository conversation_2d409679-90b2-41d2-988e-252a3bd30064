import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/inventory-items - Get all inventory items
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Inventory Items API called');

    // Try database first, fallback to mock data
    try {
      // Get all products with their inventory information
      const products = await prisma.product.findMany({
        include: {
          warehouseInventory: {
            include: {
              warehouse: true,
            },
          },
          storeInventory: {
            include: {
              store: true,
            },
          },
        },
      });

      // Transform the data to match the expected format
      const inventoryItems = products.map(product => {
        // Calculate total stock across all warehouses
        const warehouseStock = product.warehouseInventory.reduce(
          (sum, item) => sum + item.quantity,
          0
        );

        // Calculate total stock across all stores
        const storeStock = product.storeInventory.reduce(
          (sum, item) => sum + item.quantity,
          0
        );

        // Get the primary location (first warehouse with stock)
        const primaryLocation = product.warehouseInventory.length > 0
          ? product.warehouseInventory[0].warehouse.name
          : 'No location';

        // Get the last restocked date (most recent inventory update)
        const allInventory = [
          ...product.warehouseInventory,
          ...product.storeInventory,
        ];

        const lastRestocked = allInventory.length > 0
          ? new Date(Math.max(...allInventory.map(item => item.updatedAt.getTime())))
              .toISOString().split('T')[0]
          : null;

        return {
          id: product.id,
          name: product.name,
          sku: product.sku || `PROD-${product.id.substring(0, 8)}`,
          category: product.category || 'Uncategorized',
          description: product.description || '',
          unitOfMeasure: product.unit || 'piece',
          costPerUnit: product.costPrice || 0,
          currentStock: warehouseStock + storeStock,
          reorderPoint: product.lowStockThreshold || 10,
          reorderQuantity: product.lowStockThreshold ? product.lowStockThreshold * 2 : 20,
          location: primaryLocation,
          supplier: null, // We don't have direct supplier info in the schema
          lastRestocked,
          imageUrl: product.imageUrl || null,
        };
      });

      console.log(`✅ Returning ${inventoryItems.length} inventory items from database`);
      return NextResponse.json(inventoryItems);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory items' },
      { status: 500 }
    );
  }
}

// POST /api/inventory-items - Create a new inventory item
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Use type assertion for the product
    type ProductWithExtras = {
      id: string;
      name: string;
      description: string | null;
      category: string;
      price: number;
      costPrice: number;
      unit: string;
      lowStockThreshold: number;
      sku: string | null;
      imageUrl: string | null;
      createdAt: Date;
      updatedAt: Date;
    };

    // Create a new product
    // We need to use a workaround for the sku and imageUrl fields
    // since they're not recognized by the TypeScript types yet
    const createData: any = {
      name: data.name,
      description: data.description || '',
      category: data.category || 'Uncategorized',
      unit: data.unitOfMeasure || 'piece',
      price: 0, // Default price
      costPrice: data.costPerUnit || 0,
      lowStockThreshold: data.reorderPoint || 10,
    };

    // Add the new fields
    createData.sku = data.sku || null;
    createData.imageUrl = data.imageUrl || null;

    const product = await prisma.product.create({
      data: createData,
    }) as ProductWithExtras;

    // If initial stock is provided, create warehouse inventory
    if (data.currentStock && data.currentStock > 0 && data.location) {
      // Find the warehouse by name
      const warehouse = await prisma.warehouse.findFirst({
        where: {
          name: data.location,
        },
      });

      if (warehouse) {
        await prisma.warehouseInventory.create({
          data: {
            productId: product.id,
            warehouseId: warehouse.id,
            quantity: data.currentStock,
          },
        });
      }
    }

    return NextResponse.json({
      id: product.id,
      name: product.name,
      sku: product.sku || `PROD-${product.id.substring(0, 8)}`,
      category: product.category || 'Uncategorized',
      description: product.description || '',
      unitOfMeasure: product.unit || 'piece',
      costPerUnit: product.costPrice || 0,
      currentStock: data.currentStock || 0,
      reorderPoint: product.lowStockThreshold || 10,
      reorderQuantity: product.lowStockThreshold ? product.lowStockThreshold * 2 : 20,
      location: data.location || null,
      supplier: data.supplier || null,
      lastRestocked: new Date().toISOString().split('T')[0],
      imageUrl: product.imageUrl || null,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to create inventory item' },
      { status: 500 }
    );
  }
}
