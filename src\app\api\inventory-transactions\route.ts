import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/inventory-transactions - Get all inventory transactions
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Inventory Transactions API called - fetching from database');

    // Get all transactions with their items and products
    const transactions = await prisma.transaction.findMany({
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform transactions to inventory transaction format
    const inventoryTransactions = [];

    for (const transaction of transactions) {
      for (const item of transaction.items) {
        inventoryTransactions.push({
          id: `${transaction.id}-${item.id}`,
          itemId: item.productId,
          itemName: item.product?.name || 'Unknown Product',
          date: transaction.createdAt.toISOString().split('T')[0],
          type: transaction.type === 'PURCHASE' ? 'purchase' : 'sale',
          quantity: transaction.type === 'PURCHASE' ? item.quantity : -item.quantity,
          unitCost: item.unitPrice,
          totalCost: transaction.type === 'PURCHASE' ? item.totalPrice : -item.totalPrice,
          reference: transaction.partyName || `Order #${transaction.id.substring(0, 8)}`,
          notes: null,
        });
      }
    }

    console.log(`✅ Returning ${inventoryTransactions.length} inventory transactions from database`);
    return NextResponse.json(inventoryTransactions);
  } catch (error) {
    console.error('❌ Error fetching inventory transactions from database:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory transactions from database' },
      { status: 500 }
    );
  }
}

// POST /api/inventory-transactions - Create a new inventory transaction
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.itemId) {
      return NextResponse.json(
        { error: 'Item ID is required' },
        { status: 400 }
      );
    }

    if (!data.type) {
      return NextResponse.json(
        { error: 'Transaction type is required' },
        { status: 400 }
      );
    }

    if (!data.quantity || parseFloat(data.quantity) <= 0) {
      return NextResponse.json(
        { error: 'Quantity must be greater than 0' },
        { status: 400 }
      );
    }

    // Find the product
    const product = await prisma.product.findUnique({
      where: { id: data.itemId },
      include: {
        warehouseInventory: true,
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    const quantity = parseFloat(data.quantity);
    const unitCost = parseFloat(data.unitCost) || product.costPrice || 0;

    // Since we don't have a dedicated InventoryTransaction model,
    // we'll create a Transaction with a single item
    const transaction = await prisma.transaction.create({
      data: {
        type: 'PURCHASE',
        totalAmount: quantity * unitCost,
        discount: 0,
        status: 'COMPLETED',
        paymentMethod: 'CASH',
        partyName: data.reference || 'Inventory Purchase',
        userId: 'system', // You might want to get this from authentication
        items: {
          create: [
            {
              productId: data.itemId,
              quantity,
              unitPrice: unitCost,
              totalPrice: quantity * unitCost,
            },
          ],
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Update inventory based on transaction type
    if (data.type.toLowerCase() === 'purchase') {
      // Add to inventory
      if (product.warehouseInventory.length > 0) {
        // Update existing inventory in the first warehouse
        await prisma.warehouseInventory.update({
          where: { id: product.warehouseInventory[0].id },
          data: {
            quantity: product.warehouseInventory[0].quantity + quantity,
          },
        });
      } else {
        // Find a warehouse to add inventory to
        const warehouse = await prisma.warehouse.findFirst();

        if (warehouse) {
          await prisma.warehouseInventory.create({
            data: {
              productId: data.itemId,
              warehouseId: warehouse.id,
              quantity,
            },
          });
        }
      }

      // Update product cost price if different
      if (unitCost !== product.costPrice) {
        await prisma.product.update({
          where: { id: data.itemId },
          data: {
            costPrice: unitCost,
          },
        });
      }
    } else if (data.type.toLowerCase() === 'production' || data.type.toLowerCase() === 'sale') {
      // Remove from inventory
      if (product.warehouseInventory.length > 0) {
        // Check if there's enough inventory
        const totalInventory = product.warehouseInventory.reduce(
          (sum, item) => sum + item.quantity,
          0
        );

        if (totalInventory < quantity) {
          return NextResponse.json(
            { error: 'Not enough inventory available' },
            { status: 400 }
          );
        }

        // Remove from the first warehouse with enough inventory
        let remainingQuantity = quantity;

        for (const inventory of product.warehouseInventory) {
          if (remainingQuantity <= 0) break;

          if (inventory.quantity >= remainingQuantity) {
            // This warehouse has enough inventory
            await prisma.warehouseInventory.update({
              where: { id: inventory.id },
              data: {
                quantity: inventory.quantity - remainingQuantity,
              },
            });
            remainingQuantity = 0;
          } else {
            // Use all inventory from this warehouse and continue
            await prisma.warehouseInventory.update({
              where: { id: inventory.id },
              data: {
                quantity: 0,
              },
            });
            remainingQuantity -= inventory.quantity;
          }
        }
      }
    }

    // Get the first item from the transaction
    const item = transaction.items[0];

    return NextResponse.json({
      id: `${transaction.id}-${item.id}`,
      itemId: item.productId,
      itemName: item.product?.name || 'Unknown Product',
      date: transaction.createdAt.toISOString().split('T')[0],
      type: 'purchase',
      quantity: item.quantity,
      unitCost: item.unitPrice,
      totalCost: item.totalPrice,
      reference: data.reference || `Order #${transaction.id.substring(0, 8)}`,
      notes: data.notes || null,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating inventory transaction:', error);
    return NextResponse.json(
      { error: 'Failed to create inventory transaction' },
      { status: 500 }
    );
  }
}
