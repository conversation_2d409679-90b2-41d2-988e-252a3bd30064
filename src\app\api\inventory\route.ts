import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/inventory - Get all inventory items
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Inventory API called - fetching from database');

    // Get all products with their inventory information
    const products = await prisma.product.findMany({
      include: {
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    });

    // Transform the data to match the expected format
    const inventoryItems = [];

    for (const product of products) {
      // Add warehouse inventory items
      for (const warehouseItem of product.warehouseInventory) {
        inventoryItems.push({
          id: `wh-${warehouseItem.id}`,
          productId: product.id,
          productName: product.name,
          category: product.category || 'Uncategorized',
          warehouseId: warehouseItem.warehouseId,
          warehouseName: warehouseItem.warehouse.name,
          quantity: warehouseItem.quantity,
          lowStockThreshold: product.lowStockThreshold,
          unit: product.unit || 'piece',
          price: product.price,
          type: 'warehouse'
        });
      }

      // Add store inventory items
      for (const storeItem of product.storeInventory) {
        inventoryItems.push({
          id: `st-${storeItem.id}`,
          productId: product.id,
          productName: product.name,
          category: product.category || 'Uncategorized',
          storeId: storeItem.storeId,
          storeName: storeItem.store.name,
          quantity: storeItem.quantity,
          lowStockThreshold: product.lowStockThreshold,
          unit: product.unit || 'piece',
          price: product.price,
          type: 'store'
        });
      }
    }

    console.log(`✅ Returning ${inventoryItems.length} real inventory items from database`);
    return NextResponse.json(inventoryItems);
  } catch (error) {
    console.error('❌ Error fetching inventory from database:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory from database' },
      { status: 500 }
    );
  }
}

// POST /api/inventory - Create or update inventory item
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    if ((!data.warehouseId && !data.storeId) || (data.warehouseId && data.storeId)) {
      return NextResponse.json(
        { error: 'Either warehouse ID or store ID is required, but not both' },
        { status: 400 }
      );
    }

    let inventoryItem;

    if (data.warehouseId) {
      // Check if the warehouse inventory item already exists
      const existingItem = await prisma.warehouseInventory.findFirst({
        where: {
          productId: data.productId,
          warehouseId: data.warehouseId,
        },
      });

      if (existingItem) {
        // Update existing warehouse inventory item
        inventoryItem = await prisma.warehouseInventory.update({
          where: { id: existingItem.id },
          data: {
            quantity: data.quantity,
          },
          include: {
            product: true,
            warehouse: true,
          },
        });
      } else {
        // Create new warehouse inventory item
        inventoryItem = await prisma.warehouseInventory.create({
          data: {
            productId: data.productId,
            warehouseId: data.warehouseId,
            quantity: data.quantity,
          },
          include: {
            product: true,
            warehouse: true,
          },
        });
      }

      return NextResponse.json({
        id: inventoryItem.id,
        productId: inventoryItem.productId,
        productName: inventoryItem.product?.name || 'Unknown Product',
        category: inventoryItem.product?.category || 'Uncategorized',
        warehouseId: inventoryItem.warehouseId,
        warehouseName: inventoryItem.warehouse?.name || 'Unknown Warehouse',
        quantity: inventoryItem.quantity,
        lowStockThreshold: inventoryItem.product?.lowStockThreshold || 10,
        unit: inventoryItem.product?.unit || 'piece',
        price: inventoryItem.product?.price || 0,
      });
    } else {
      // Check if the store inventory item already exists
      const existingItem = await prisma.storeInventory.findFirst({
        where: {
          productId: data.productId,
          storeId: data.storeId,
        },
      });

      if (existingItem) {
        // Update existing store inventory item
        inventoryItem = await prisma.storeInventory.update({
          where: { id: existingItem.id },
          data: {
            quantity: data.quantity,
          },
          include: {
            product: true,
            store: true,
          },
        });
      } else {
        // Create new store inventory item
        inventoryItem = await prisma.storeInventory.create({
          data: {
            productId: data.productId,
            storeId: data.storeId,
            quantity: data.quantity,
          },
          include: {
            product: true,
            store: true,
          },
        });
      }

      return NextResponse.json({
        id: inventoryItem.id,
        productId: inventoryItem.productId,
        productName: inventoryItem.product?.name || 'Unknown Product',
        category: inventoryItem.product?.category || 'Uncategorized',
        storeId: inventoryItem.storeId,
        storeName: inventoryItem.store?.name || 'Unknown Store',
        quantity: inventoryItem.quantity,
        lowStockThreshold: inventoryItem.product?.lowStockThreshold || 10,
        unit: inventoryItem.product?.unit || 'piece',
        price: inventoryItem.product?.price || 0,
      });
    }
  } catch (error) {
    console.error('Error creating/updating inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to create/update inventory item' },
      { status: 500 }
    );
  }
}
