import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/inventory/transfer - Transfer inventory between locations
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }
    
    if (!data.sourceWarehouseId) {
      return NextResponse.json(
        { error: 'Source warehouse ID is required' },
        { status: 400 }
      );
    }
    
    if (!data.destinationType || !data.destinationId) {
      return NextResponse.json(
        { error: 'Destination type and ID are required' },
        { status: 400 }
      );
    }
    
    if (!data.quantity || parseFloat(data.quantity) <= 0) {
      return NextResponse.json(
        { error: 'Quantity must be greater than 0' },
        { status: 400 }
      );
    }
    
    // Find the source inventory item
    const sourceInventory = await prisma.warehouseInventory.findFirst({
      where: {
        productId: data.productId,
        warehouseId: data.sourceWarehouseId,
      },
      include: {
        product: true,
        warehouse: true,
      },
    });
    
    if (!sourceInventory) {
      return NextResponse.json(
        { error: 'Source inventory not found' },
        { status: 404 }
      );
    }
    
    // Check if there's enough quantity to transfer
    const transferQuantity = parseFloat(data.quantity);
    if (sourceInventory.quantity < transferQuantity) {
      return NextResponse.json(
        { error: 'Not enough quantity available to transfer' },
        { status: 400 }
      );
    }
    
    // Update the source inventory
    await prisma.warehouseInventory.update({
      where: { id: sourceInventory.id },
      data: {
        quantity: sourceInventory.quantity - transferQuantity,
      },
    });
    
    // Update or create the destination inventory
    if (data.destinationType === 'warehouse') {
      // Transfer to another warehouse
      const destinationInventory = await prisma.warehouseInventory.findFirst({
        where: {
          productId: data.productId,
          warehouseId: data.destinationId,
        },
      });
      
      if (destinationInventory) {
        // Update existing destination inventory
        await prisma.warehouseInventory.update({
          where: { id: destinationInventory.id },
          data: {
            quantity: destinationInventory.quantity + transferQuantity,
          },
        });
      } else {
        // Create new destination inventory
        await prisma.warehouseInventory.create({
          data: {
            productId: data.productId,
            warehouseId: data.destinationId,
            quantity: transferQuantity,
          },
        });
      }
    } else if (data.destinationType === 'store') {
      // Transfer to a store
      const destinationInventory = await prisma.storeInventory.findFirst({
        where: {
          productId: data.productId,
          storeId: data.destinationId,
        },
      });
      
      if (destinationInventory) {
        // Update existing destination inventory
        await prisma.storeInventory.update({
          where: { id: destinationInventory.id },
          data: {
            quantity: destinationInventory.quantity + transferQuantity,
          },
        });
      } else {
        // Create new destination inventory
        await prisma.storeInventory.create({
          data: {
            productId: data.productId,
            storeId: data.destinationId,
            quantity: transferQuantity,
          },
        });
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid destination type' },
        { status: 400 }
      );
    }
    
    // Create a transfer record
    const transfer = await prisma.inventoryTransfer.create({
      data: {
        productId: data.productId,
        sourceWarehouseId: data.sourceWarehouseId,
        destinationType: data.destinationType,
        destinationId: data.destinationId,
        quantity: transferQuantity,
        notes: data.notes || null,
      },
    });
    
    return NextResponse.json({
      success: true,
      transfer,
      message: `Successfully transferred ${transferQuantity} ${sourceInventory.product?.unit || 'units'} of ${sourceInventory.product?.name || 'product'} from ${sourceInventory.warehouse?.name || 'warehouse'} to ${data.destinationType} ${data.destinationId}`,
    });
  } catch (error) {
    console.error('Error transferring inventory:', error);
    return NextResponse.json(
      { error: 'Failed to transfer inventory' },
      { status: 500 }
    );
  }
}
