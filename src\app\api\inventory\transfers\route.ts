import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/inventory/transfers - Get all inventory transfers
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const productId = url.searchParams.get('productId');
    const warehouseId = url.searchParams.get('warehouseId');
    const storeId = url.searchParams.get('storeId');
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit')!) : undefined;

    // Build the query filter
    const filter: any = {};

    if (productId) {
      filter.productId = productId;
    }

    if (warehouseId) {
      filter.OR = [
        { sourceWarehouseId: warehouseId },
        {
          AND: [
            { destinationType: 'WAREHOUSE' },
            { destinationId: warehouseId }
          ]
        }
      ];
    }

    if (storeId) {
      filter.OR = [
        ...(filter.OR || []),
        {
          AND: [
            { destinationType: 'STORE' },
            { destinationId: storeId }
          ]
        }
      ];
    }

    // Check if the InventoryTransfer table exists
    try {
      // Get transfers with related data
      const transfers = await prisma.inventoryTransfer.findMany({
        where: filter,
        include: {
          product: true,
          sourceWarehouse: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      // If no transfers found, return an empty array
      if (transfers.length === 0) {
        return NextResponse.json([]);
      }

      // Get destination names (warehouse or store)
      const enhancedTransfers = await Promise.all(transfers.map(async (transfer) => {
        let destinationName = '';

        if (transfer.destinationType === 'WAREHOUSE') {
          const warehouse = await prisma.warehouse.findUnique({
            where: { id: transfer.destinationId },
          });
          destinationName = warehouse?.name || 'Unknown Warehouse';
        } else if (transfer.destinationType === 'STORE') {
          const store = await prisma.store.findUnique({
            where: { id: transfer.destinationId },
          });
          destinationName = store?.name || 'Unknown Store';
        }

        return {
          ...transfer,
          destinationName,
          productName: transfer.product?.name || 'Unknown Product',
          sourceWarehouseName: transfer.sourceWarehouse?.name || 'Unknown Warehouse',
        };
      }));

      return NextResponse.json(enhancedTransfers);
    } catch (err) {
      // If the table doesn't exist yet, return an empty array
      console.error('Error accessing inventory transfers:', err);
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('Error fetching inventory transfers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory transfers' },
      { status: 500 }
    );
  }
}
