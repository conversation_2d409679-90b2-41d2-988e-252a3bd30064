import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/orders/[id] - Get a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('🔍 Fetching order:', id);

    // Get order from database

    const order = await prisma.order.findUnique({
      where: { id: id },
      include: {
        customer: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        address: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Transform order to the expected format
    const transformedOrder = {
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      customerName: `${order.customer.firstName} ${order.customer.lastName}`.trim(),
      customerEmail: order.customer.user.email,
      customerPhone: order.customer.phone,
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      orderType: order.orderType,
      deliveryMethod: order.deliveryMethod,
      shippingAddress: order.address,
      items: order.orderItems.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
      subtotal: order.subtotal,
      tax: order.tax,
      discount: order.discount,
      totalAmount: order.totalAmount,
      notes: order.notes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      storeId: order.storeId,
      storeName: order.store?.name || null,
      assignedBy: order.assignedByUser,
    };

    return NextResponse.json(transformedOrder);
  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    );
  }
}

// PUT /api/orders/[id] - Update an order
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Validate order ID
    if (!id || id.trim() === '') {
      console.error('❌ Invalid order ID provided');
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      );
    }

    let data;
    try {
      data = await request.json();
    } catch (parseError) {
      console.error('❌ Failed to parse request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    console.log('🔄 Admin Panel: Updating order:', id, 'with data:', data);
    console.log('📋 Request details:', {
      method: request.method,
      url: request.url,
      orderId: id,
      updateData: data
    });

    // Database update
    const existingOrder = await prisma.order.findUnique({
      where: { id: id },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (data.status) {
      updateData.status = data.status;
    }

    if (data.paymentStatus) {
      updateData.paymentStatus = data.paymentStatus;
    }

    if (data.storeId) {
      updateData.storeId = data.storeId;
    }

    if (data.notes) {
      updateData.notes = data.notes;
    }

    // Update the order
    const updatedOrder = await prisma.order.update({
      where: { id: id },
      data: updateData,
      include: {
        customer: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        address: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Transform to the expected format
    const transformedOrder = {
      id: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      customerId: updatedOrder.customerId,
      customerName: `${updatedOrder.customer.firstName} ${updatedOrder.customer.lastName}`.trim(),
      customerEmail: updatedOrder.customer.user.email,
      customerPhone: updatedOrder.customer.phone,
      status: updatedOrder.status,
      paymentStatus: updatedOrder.paymentStatus,
      paymentMethod: updatedOrder.paymentMethod,
      orderType: updatedOrder.orderType,
      deliveryMethod: updatedOrder.deliveryMethod,
      shippingAddress: updatedOrder.address,
      items: updatedOrder.orderItems.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
      subtotal: updatedOrder.subtotal,
      tax: updatedOrder.tax,
      discount: updatedOrder.discount,
      totalAmount: updatedOrder.totalAmount,
      notes: updatedOrder.notes,
      createdAt: updatedOrder.createdAt,
      updatedAt: updatedOrder.updatedAt,
      storeId: updatedOrder.storeId,
      storeName: updatedOrder.store?.name || null,
      assignedBy: updatedOrder.assignedByUser,
    };

    console.log('✅ Order updated successfully:', transformedOrder.orderNumber);

    return NextResponse.json(transformedOrder);
  } catch (error) {
    console.error('❌ Error updating order:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

// DELETE /api/orders/[id] - Delete an order
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Find the order
    const order = await prisma.order.findUnique({
      where: { id: id },
      include: {
        orderItems: true,
      },
    });

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    console.log('🗑️ Deleting order:', order.orderNumber);

    // Delete order items first
    await prisma.orderItem.deleteMany({
      where: { orderId: id },
    });

    // Delete the order
    await prisma.order.delete({
      where: { id: id },
    });

    console.log('✅ Order deleted successfully');

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('❌ Error deleting order:', error);
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    );
  }
}
