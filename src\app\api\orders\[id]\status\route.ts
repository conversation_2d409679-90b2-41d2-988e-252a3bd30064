import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// PUT /api/orders/[id]/status - Update order status (Store managers only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { status, userRole, storeId } = await request.json();

    console.log('🔄 Updating order status:', { orderId: id, status, userRole, storeId });

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Verify the order exists
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        store: true,
      },
    });

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Role-based access control
    if (userRole === 'STORE_MANAGER' || userRole === 'STAFF') {
      // Store managers and staff can only update orders assigned to their store
      if (!order.storeId || order.storeId !== storeId) {
        return NextResponse.json(
          { error: 'You can only update orders assigned to your store' },
          { status: 403 }
        );
      }

      // Store managers cannot change status back to PENDING_ASSIGNMENT
      if (status === 'PENDING_ASSIGNMENT') {
        return NextResponse.json(
          { error: 'Cannot change status back to pending assignment' },
          { status: 400 }
        );
      }
    } else if (userRole !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Validate status transitions
    const validStatuses = ['ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'DELIVERED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Update the order status
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: {
        status: status,
        updatedAt: new Date(),
      },
      include: {
        customer: {
          include: {
            user: true,
          },
        },
        address: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        store: true,
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Order status updated successfully:', {
      orderId: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      newStatus: updatedOrder.status,
      storeName: updatedOrder.store?.name,
    });

    return NextResponse.json({
      success: true,
      message: 'Order status updated successfully',
      order: updatedOrder,
    });

  } catch (error) {
    console.error('❌ Error updating order status:', error);
    return NextResponse.json(
      { error: 'Failed to update order status' },
      { status: 500 }
    );
  }
}
