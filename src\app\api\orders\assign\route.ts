import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/orders/assign - Assign order to store (Super Admin only)
export async function POST(request: NextRequest) {
  try {
    const { orderId, storeId, assignedBy } = await request.json();

    console.log('🏪 Assigning order to store:', { orderId, storeId, assignedBy });

    if (!orderId || !storeId || !assignedBy) {
      return NextResponse.json(
        { error: 'Order ID, Store ID, and Assigned By are required' },
        { status: 400 }
      );
    }

    // Verify the order exists and is an online order
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        customer: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    if (order.orderType !== 'ONLINE') {
      return NextResponse.json(
        { error: 'Only online orders can be assigned to stores' },
        { status: 400 }
      );
    }

    if (order.storeId) {
      return NextResponse.json(
        { error: 'Order is already assigned to a store' },
        { status: 400 }
      );
    }

    // Verify the store exists
    const store = await prisma.store.findUnique({
      where: { id: storeId },
    });

    if (!store) {
      return NextResponse.json(
        { error: 'Store not found' },
        { status: 404 }
      );
    }

    // Verify the assigning user exists and is an admin
    const assigningUser = await prisma.user.findUnique({
      where: { id: assignedBy },
    });

    if (!assigningUser || assigningUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Only super admins can assign orders to stores' },
        { status: 403 }
      );
    }

    // Update the order with store assignment
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        storeId: storeId,
        assignedBy: assignedBy,
        status: 'ASSIGNED', // Update status to assigned when assigned to store
        updatedAt: new Date(),
      },
      include: {
        customer: {
          include: {
            user: true,
          },
        },
        address: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        store: true,
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Order assigned successfully:', {
      orderId: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      storeName: updatedOrder.store?.name,
      assignedBy: updatedOrder.assignedByUser?.name,
    });

    // Transform the response
    const response = {
      id: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      customerId: updatedOrder.customerId,
      customerName: `${updatedOrder.customer.firstName} ${updatedOrder.customer.lastName}`.trim(),
      customerEmail: updatedOrder.customer.user.email,
      status: updatedOrder.status,
      orderType: updatedOrder.orderType,
      storeId: updatedOrder.storeId,
      storeName: updatedOrder.store?.name,
      assignedBy: updatedOrder.assignedByUser ? {
        id: updatedOrder.assignedByUser.id,
        name: updatedOrder.assignedByUser.name,
        email: updatedOrder.assignedByUser.email,
      } : null,
      totalAmount: updatedOrder.totalAmount,
      createdAt: updatedOrder.createdAt,
      updatedAt: updatedOrder.updatedAt,
    };

    return NextResponse.json({
      success: true,
      message: `Order ${updatedOrder.orderNumber} assigned to ${updatedOrder.store?.name}`,
      order: response,
    });

  } catch (error) {
    console.error('❌ Error assigning order to store:', error);
    return NextResponse.json(
      { error: 'Failed to assign order to store' },
      { status: 500 }
    );
  }
}

// PUT /api/orders/assign - Update order assignment
export async function PUT(request: NextRequest) {
  try {
    const { orderId, storeId, assignedBy } = await request.json();

    console.log('🔄 Updating order assignment:', { orderId, storeId, assignedBy });

    if (!orderId || !assignedBy) {
      return NextResponse.json(
        { error: 'Order ID and Assigned By are required' },
        { status: 400 }
      );
    }

    // If storeId is null, we're unassigning the order
    const updateData: any = {
      assignedBy: assignedBy,
      updatedAt: new Date(),
    };

    if (storeId === null) {
      updateData.storeId = null;
      updateData.status = 'PENDING_ASSIGNMENT'; // Reset status when unassigned
    } else {
      // Verify the store exists
      const store = await prisma.store.findUnique({
        where: { id: storeId },
      });

      if (!store) {
        return NextResponse.json(
          { error: 'Store not found' },
          { status: 404 }
        );
      }

      updateData.storeId = storeId;
      updateData.status = 'ASSIGNED';
    }

    // Update the order
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: updateData,
      include: {
        store: true,
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const message = storeId === null
      ? `Order ${updatedOrder.orderNumber} unassigned from store`
      : `Order ${updatedOrder.orderNumber} reassigned to ${updatedOrder.store?.name}`;

    console.log('✅ Order assignment updated:', message);

    return NextResponse.json({
      success: true,
      message: message,
      order: {
        id: updatedOrder.id,
        orderNumber: updatedOrder.orderNumber,
        status: updatedOrder.status,
        storeId: updatedOrder.storeId,
        storeName: updatedOrder.store?.name,
        assignedBy: updatedOrder.assignedByUser ? {
          id: updatedOrder.assignedByUser.id,
          name: updatedOrder.assignedByUser.name,
          email: updatedOrder.assignedByUser.email,
        } : null,
        updatedAt: updatedOrder.updatedAt,
      },
    });

  } catch (error) {
    console.error('❌ Error updating order assignment:', error);
    return NextResponse.json(
      { error: 'Failed to update order assignment' },
      { status: 500 }
    );
  }
}
