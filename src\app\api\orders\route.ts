import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

// Helper function to generate order number
function generateOrderNumber() {
  const prefix = 'ORD';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
}

// GET /api/orders - Get all orders with role-based filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userRole = searchParams.get('userRole');
    const storeId = searchParams.get('storeId');
    const orderType = searchParams.get('orderType');

    console.log('🔍 Admin Panel: Fetching orders from database with filters:', { userRole, storeId, orderType });

    // Build where clause based on role and filters
    const whereClause: any = {};

    // Role-based filtering
    if (userRole === 'ADMIN') {
      // Admin sees all online orders
      whereClause.orderType = 'ONLINE';
    } else if (userRole === 'STORE_MANAGER' || userRole === 'STAFF') {
      // Store managers and staff see only orders assigned to their store
      if (storeId) {
        whereClause.storeId = storeId;
        whereClause.status = { not: 'PENDING_ASSIGNMENT' };
      } else {
        // No store ID provided, return empty array
        console.log('🔍 No store ID provided for store user, returning empty array');
        return NextResponse.json([]);
      }
    }

    // Additional filters
    if (orderType) {
      whereClause.orderType = orderType;
    }

    // Get orders from database
    const orders = await prisma.order.findMany({
      where: whereClause,
      include: {
        customer: {
          include: {
            user: true,
          },
        },
        orderItems: {
          include: {
            product: true,
          },
        },
        store: true,
        address: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform orders to match expected format
    const transformedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      customerName: order.customer?.user?.name || order.customer?.name || 'Unknown Customer',
      customerEmail: order.customer?.user?.email || order.customer?.email || '',
      customerPhone: order.customer?.phone || '',
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      orderType: order.orderType,
      storeId: order.storeId,
      storeName: order.store?.name || null,
      totalAmount: order.totalAmount,
      createdAt: order.createdAt.toISOString(),
      items: order.orderItems.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.totalPrice,
      })),
    }));

    console.log(`✅ Returning ${transformedOrders.length} orders from database`);
    return NextResponse.json(transformedOrders);

  } catch (error) {
    console.error('❌ Error fetching orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create a new order
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('orders.create')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: 'Order items are required' },
        { status: 400 }
      );
    }

    // Calculate totals
    const subtotal = data.items.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0);
    const discount = data.discount || 0;
    const totalAmount = subtotal - discount;

    // Create order in database
    const order = await prisma.order.create({
      data: {
        orderNumber: generateOrderNumber(),
        customerId: data.customerId,
        status: data.status || 'PENDING_ASSIGNMENT',
        paymentStatus: data.paymentStatus || 'PENDING',
        paymentMethod: data.paymentMethod || 'CASH',
        orderType: data.orderType || 'ONLINE',
        storeId: data.storeId || null,
        totalAmount: totalAmount,
        discount: discount,
        notes: data.notes || '',
        orderItems: {
          create: data.items.map((item: any) => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.quantity * item.unitPrice,
          })),
        },
      },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        customer: {
          include: {
            user: true,
          },
        },
        store: true,
      },
    });

    // Transform response to match expected format
    const transformedOrder = {
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      customerName: order.customer?.user?.name || order.customer?.name || 'Unknown Customer',
      customerEmail: order.customer?.user?.email || order.customer?.email || '',
      customerPhone: order.customer?.phone || '',
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      orderType: order.orderType,
      storeId: order.storeId,
      storeName: order.store?.name || null,
      totalAmount: order.totalAmount,
      discount: order.discount,
      notes: order.notes,
      items: order.orderItems.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.totalPrice,
      })),
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
    };

    console.log(`✅ Created order in database: ${order.orderNumber}`);
    return NextResponse.json(transformedOrder, { status: 201 });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
