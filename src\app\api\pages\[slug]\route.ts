import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - Fetch single page by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    const page = await prisma.systemSetting.findFirst({
      where: {
        category: 'static_pages',
        key: slug
      }
    });

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      );
    }

    const data = JSON.parse(page.value);

    return NextResponse.json({
      id: page.key,
      title: data.title,
      slug: data.slug,
      content: data.content,
      isActive: data.isActive,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt
    });
  } catch (error) {
    console.error('Error fetching page:', error);
    return NextResponse.json(
      { error: 'Failed to fetch page' },
      { status: 500 }
    );
  }
}

// PUT - Update page
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const { title, content, isActive } = await request.json();

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    const existingPage = await prisma.systemSetting.findFirst({
      where: {
        category: 'static_pages',
        key: slug
      }
    });

    if (!existingPage) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      );
    }

    const existingData = JSON.parse(existingPage.value);
    const updatedData = {
      ...existingData,
      title,
      content,
      isActive: isActive ?? true,
      updatedAt: new Date().toISOString()
    };

    const updatedPage = await prisma.systemSetting.update({
      where: {
        id: existingPage.id
      },
      data: {
        value: JSON.stringify(updatedData),
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      id: updatedPage.key,
      ...updatedData
    });
  } catch (error) {
    console.error('Error updating page:', error);
    return NextResponse.json(
      { error: 'Failed to update page' },
      { status: 500 }
    );
  }
}

// DELETE - Delete page
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    const existingPage = await prisma.systemSetting.findFirst({
      where: {
        category: 'static_pages',
        key: slug
      }
    });

    if (!existingPage) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      );
    }

    await prisma.systemSetting.delete({
      where: {
        id: existingPage.id
      }
    });

    return NextResponse.json({ message: 'Page deleted successfully' });
  } catch (error) {
    console.error('Error deleting page:', error);
    return NextResponse.json(
      { error: 'Failed to delete page' },
      { status: 500 }
    );
  }
}
