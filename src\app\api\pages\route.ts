import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - Fetch all pages
export async function GET() {
  try {
    const pages = await prisma.systemSetting.findMany({
      where: {
        category: 'static_pages'
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    const formattedPages = pages.map(page => {
      const data = JSON.parse(page.value);
      return {
        id: page.key,
        title: data.title,
        slug: data.slug,
        content: data.content,
        isActive: data.isActive,
        createdAt: page.createdAt,
        updatedAt: page.updatedAt
      };
    });

    return NextResponse.json(formattedPages);
  } catch (error) {
    console.error('Error fetching pages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pages' },
      { status: 500 }
    );
  }
}

// POST - Create new page
export async function POST(request: NextRequest) {
  try {
    const { title, slug, content, isActive } = await request.json();

    if (!title || !slug || !content) {
      return NextResponse.json(
        { error: 'Title, slug, and content are required' },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const existingPage = await prisma.systemSetting.findFirst({
      where: {
        category: 'static_pages',
        key: slug
      }
    });

    if (existingPage) {
      return NextResponse.json(
        { error: 'A page with this slug already exists' },
        { status: 400 }
      );
    }

    const pageData = {
      title,
      slug,
      content,
      isActive: isActive ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const page = await prisma.systemSetting.create({
      data: {
        category: 'static_pages',
        key: slug,
        value: JSON.stringify(pageData)
      }
    });

    return NextResponse.json({
      id: page.key,
      ...pageData
    });
  } catch (error) {
    console.error('Error creating page:', error);
    return NextResponse.json(
      { error: 'Failed to create page' },
      { status: 500 }
    );
  }
}
