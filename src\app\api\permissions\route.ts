import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

// In-memory storage for custom permissions (replace with database in production)
let userPermissions: Record<string, string[]> = {
  '<EMAIL>': [
    'dashboard.view', 'products.view', 'products.create', 'products.edit', 'products.delete',
    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
    'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.view_all_stores',
    'customers.view', 'customers.create', 'customers.edit', 'customers.delete', 'customers.view_all_stores',
    'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles',
    'stores.view', 'stores.create', 'stores.edit', 'stores.delete', 'stores.manage_access',
    'inventory.view', 'inventory.edit', 'inventory.transfer', 'inventory.view_all_stores',
    'raw_materials.view', 'raw_materials.create', 'raw_materials.edit', 'raw_materials.delete',
    'recipes.view', 'recipes.create', 'recipes.edit', 'recipes.delete',
    'production.view', 'production.create', 'production.edit', 'production.delete',
    'sales.view', 'sales.create', 'sales.process',
    'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
    'expenses.view', 'expenses.create', 'expenses.edit', 'expenses.delete',
    'wastage.view', 'wastage.create', 'wastage.edit', 'wastage.delete',
    'reports.view', 'reports.sales', 'reports.inventory', 'reports.financial', 'reports.production', 'reports.all_stores',
    'settings.view', 'settings.edit', 'settings.system'
  ]
};

// GET /api/permissions - Get user permissions
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('users.manage_roles')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get user email from query params
    const url = new URL(request.url);
    const userEmail = url.searchParams.get('userEmail');

    if (userEmail) {
      // Return permissions for specific user
      const permissions = userPermissions[userEmail] || [];
      return NextResponse.json({ userEmail, permissions });
    } else {
      // Return all user permissions
      return NextResponse.json(userPermissions);
    }
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    );
  }
}

// POST /api/permissions - Update user permissions
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('users.manage_roles')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const data = await request.json();
    const { userEmail, permissions } = data;

    if (!userEmail || !Array.isArray(permissions)) {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      );
    }

    // Update user permissions
    userPermissions[userEmail] = permissions;

    // In production, save to database:
    // await prisma.userPermission.deleteMany({ where: { userEmail } });
    // await prisma.userPermission.createMany({
    //   data: permissions.map(permission => ({ userEmail, permission }))
    // });

    return NextResponse.json({ 
      success: true, 
      userEmail, 
      permissions: userPermissions[userEmail] 
    });
  } catch (error) {
    console.error('Error updating permissions:', error);
    return NextResponse.json(
      { error: 'Failed to update permissions' },
      { status: 500 }
    );
  }
}

// PUT /api/permissions - Grant or revoke specific permission
export async function PUT(request: NextRequest) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('users.manage_roles')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const data = await request.json();
    const { userEmail, permission, grant } = data;

    if (!userEmail || !permission || typeof grant !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      );
    }

    // Initialize user permissions if not exists
    if (!userPermissions[userEmail]) {
      userPermissions[userEmail] = [];
    }

    if (grant) {
      // Grant permission
      if (!userPermissions[userEmail].includes(permission)) {
        userPermissions[userEmail].push(permission);
      }
    } else {
      // Revoke permission
      userPermissions[userEmail] = userPermissions[userEmail].filter(p => p !== permission);
    }

    // In production, update database:
    // if (grant) {
    //   await prisma.userPermission.upsert({
    //     where: { userEmail_permission: { userEmail, permission } },
    //     create: { userEmail, permission },
    //     update: {}
    //   });
    // } else {
    //   await prisma.userPermission.deleteMany({
    //     where: { userEmail, permission }
    //   });
    // }

    return NextResponse.json({ 
      success: true, 
      userEmail, 
      permission,
      granted: grant,
      totalPermissions: userPermissions[userEmail].length
    });
  } catch (error) {
    console.error('Error updating permission:', error);
    return NextResponse.json(
      { error: 'Failed to update permission' },
      { status: 500 }
    );
  }
}
