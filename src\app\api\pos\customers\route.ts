import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/pos/customers - Get all customers for POS
export async function GET(request: NextRequest) {
  try {
    console.log('👥 POS: Fetching customers from database...');

    // Try to get customers from CustomerVisit table
    let customers = [];

    try {
      const customerVisits = await prisma.customerVisit.findMany({
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Extract unique customers from visits
      const customerMap = new Map();

      customerVisits.forEach(visit => {
        if (!customerMap.has(visit.customerName)) {
          customerMap.set(visit.customerName, {
            id: visit.id,
            name: visit.customerName,
            email: visit.customerContact || '',
            phone: '',
            loyaltyPoints: 0, // Default loyalty points
          });
        }
      });

      customers = Array.from(customerMap.values());
      console.log(`✅ Retrieved ${customers.length} customers from database for POS`);
    } catch (customerError) {
      console.log('⚠️ No customer visits found, creating default walk-in customer');
      // If no customers exist, create a default walk-in customer
      customers = [
        {
          id: 'walk-in-customer',
          name: 'Walk-in Customer',
          email: '',
          phone: '',
          loyaltyPoints: 0
        }
      ];
    }

    // Try to get transactions to calculate loyalty points (only if we have database customers)
    if (customers.length > 0 && customers[0].id !== 'pos-customer-1') {
      try {
        const customerNames = customers.map(c => c.name);
        const transactions = await prisma.transaction.findMany({
          where: {
            type: 'SALE',
            partyName: {
              in: customerNames,
            },
          },
          select: {
            partyName: true,
            totalAmount: true,
          },
        });

        // Calculate loyalty points and total spent
        transactions.forEach(transaction => {
          if (transaction.partyName) {
            const customer = customers.find(c => c.name === transaction.partyName);
            if (customer) {
              // Add 1 point for every $10 spent
              customer.loyaltyPoints += Math.floor(transaction.totalAmount / 10);
            }
          }
        });
      } catch (loyaltyError) {
        console.log('⚠️ Could not calculate loyalty points:', loyaltyError.message);
      }
    }

    console.log(`✅ Returning ${customers.length} customers for POS`);
    return NextResponse.json(customers);
  } catch (error) {
    console.error('❌ Critical error in POS customers API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

// POST /api/pos/customers - Create a new customer for POS
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Get the first store and user for demo purposes
    const store = await prisma.store.findFirst();
    const user = await prisma.user.findFirst();

    if (!store || !user) {
      return NextResponse.json(
        { error: 'No store or user found' },
        { status: 400 }
      );
    }

    const customerVisit = await prisma.customerVisit.create({
      data: {
        storeId: store.id,
        userId: user.id,
        customerName: data.name,
        customerContact: data.email || data.phone,
        purpose: 'New customer registration via POS',
        date: new Date(),
      },
    });

    return NextResponse.json({
      id: customerVisit.id,
      name: customerVisit.customerName,
      email: data.email || '',
      phone: data.phone || '',
      loyaltyPoints: 0,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating POS customer:', error);
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    );
  }
}
