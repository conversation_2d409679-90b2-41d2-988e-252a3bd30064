import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/pos/products - Get all products for POS
export async function GET(request: NextRequest) {
  try {
    console.log('🛒 POS: Fetching products from database...');

    // Get all active products from database
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
      },
      include: {
        warehouseInventory: true,
        storeInventory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform products for POS format
    const posProducts = products.map(product => {
      // Calculate total stock
      const warehouseStock = product.warehouseInventory.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const storeStock = product.storeInventory.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const totalStock = warehouseStock + storeStock;

      return {
        id: product.id,
        name: product.name,
        price: product.price,
        category: product.category || 'Uncategorized',
        description: product.description || '',
        image: product.imageUrl || 'https://source.unsplash.com/300x300/?product',
        inStock: totalStock > 0,
        stock: totalStock,
        unit: product.unit || 'piece',
      };
    });

    console.log(`✅ Returning ${posProducts.length} products for POS from database`);
    return NextResponse.json(posProducts);
  } catch (error) {
    console.error('❌ Error fetching POS products from database:', error);
    return NextResponse.json(
      { error: 'Failed to fetch POS products from database' },
      { status: 500 }
    );
  }
}
