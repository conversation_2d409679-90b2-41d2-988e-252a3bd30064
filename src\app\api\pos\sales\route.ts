import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/pos/sales - Create a new sale from POS
export async function POST(request: NextRequest) {
  try {
    console.log('💰 POS: Creating sale...');
    const data = await request.json();

    // Validate required fields
    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: 'At least one item is required' },
        { status: 400 }
      );
    }

    // Get store and user from database
    const store = await prisma.store.findFirst();
    const user = await prisma.user.findFirst();

    if (!store || !user) {
      return NextResponse.json(
        { error: 'No store or user found in database. Please set up stores and users first.' },
        { status: 400 }
      );
    }

    console.log(`✅ Using store: ${store.name} and user: ${user.name}`);

    // Calculate subtotal and total
    const subtotal = data.items.reduce(
      (sum: number, item: { price: number; quantity: number }) =>
        sum + (item.price * item.quantity),
      0
    );

    const discount = data.discount || 0;
    const tax = data.tax || 0;
    const total = subtotal - discount + tax;

    // Start a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create the transaction
      const transaction = await tx.transaction.create({
        data: {
          type: 'SALE',
          storeId: store.id,
          userId: user.id,
          partyName: data.customer?.name || null,
          partyContact: data.customer?.email || data.customer?.phone || null,
          totalAmount: total,
          discount: discount,
          paymentMethod: mapPaymentMethod(data.paymentDetails?.method),
          status: 'COMPLETED',
          notes: 'POS_SALE', // Mark this as a POS sale
          items: {
            create: data.items.map((item: any) => ({
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.price,
              totalPrice: item.price * item.quantity,
            })),
          },
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      // Update inventory for each product
      for (const item of data.items) {
        // Find store inventory for this product
        const storeInventory = await tx.storeInventory.findFirst({
          where: {
            storeId: store.id,
            productId: item.productId,
          },
        });

        if (storeInventory) {
          // Update existing inventory
          await tx.storeInventory.update({
            where: { id: storeInventory.id },
            data: {
              quantity: storeInventory.quantity - item.quantity,
            },
          });
        } else {
          // Create new inventory with negative quantity (to track what's been sold)
          await tx.storeInventory.create({
            data: {
              storeId: store.id,
              productId: item.productId,
              quantity: -item.quantity,
            },
          });
        }
      }

      return transaction;
    });

    // Generate receipt number
    const receiptNumber = `INV-${result.id.substring(0, 8)}`;
    console.log('✅ Created POS sale:', receiptNumber);

    // Format the response
    const formattedSale = {
      id: result.id,
      receiptNumber,
      date: result.createdAt.toISOString(),
      customer: data.customer || null,
      items: result.items.map(item => ({
        id: item.id,
        productId: item.productId,
        name: item.product.name,
        price: item.unitPrice,
        quantity: item.quantity,
        total: item.totalPrice,
      })),
      subtotal,
      discount,
      tax,
      total,
      paymentDetails: data.paymentDetails,
    };

    return NextResponse.json(formattedSale, { status: 201 });
  } catch (error) {
    console.error('Error creating POS sale:', error);
    return NextResponse.json(
      { error: 'Failed to create sale' },
      { status: 500 }
    );
  }
}

// Helper function to map payment method
function mapPaymentMethod(method: string): 'CASH' | 'CARD' | 'ONLINE' | 'BANK_TRANSFER' {
  switch (method?.toLowerCase()) {
    case 'cash':
      return 'CASH';
    case 'card':
    case 'credit/debit card':
    case 'credit_card':
      return 'CARD';
    case 'mobile':
    case 'mobile payment':
    case 'mobile_payment':
      return 'ONLINE';
    default:
      return 'CASH'; // Default to cash
  }
}
