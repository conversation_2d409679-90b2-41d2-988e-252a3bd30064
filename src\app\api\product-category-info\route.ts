import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/product-category-info - Get category-specific product information
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    if (!category) {
      return NextResponse.json(
        { error: 'Category parameter is required' },
        { status: 400 }
      );
    }

    // Get category-specific information from system settings
    const categoryInfo = await prisma.systemSetting.findFirst({
      where: {
        category: 'product_category_info',
        key: category.toLowerCase()
      }
    });

    if (!categoryInfo) {
      // Return default information if not found
      return NextResponse.json({
        category: category,
        productContains: [],
        careInstructions: [],
        badges: ['PREMIUM', 'QUALITY'],
        showNameField: false,
        weightLabel: 'Size'
      });
    }

    const data = JSON.parse(categoryInfo.value);
    return NextResponse.json({
      category: category,
      ...data
    });
  } catch (error) {
    console.error('Error fetching category info:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category information' },
      { status: 500 }
    );
  }
}

// POST /api/product-category-info - Create or update category-specific information
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { category, productContains, careInstructions, badges, showNameField, weightLabel } = data;

    if (!category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      );
    }

    const categoryData = {
      productContains: productContains || [],
      careInstructions: careInstructions || [],
      badges: badges || ['PREMIUM', 'QUALITY'],
      showNameField: showNameField || false,
      weightLabel: weightLabel || 'Size'
    };

    // Upsert category information in system settings
    await prisma.systemSetting.upsert({
      where: {
        category_key: {
          category: 'product_category_info',
          key: category.toLowerCase()
        }
      },
      update: {
        value: JSON.stringify(categoryData),
        updatedAt: new Date()
      },
      create: {
        category: 'product_category_info',
        key: category.toLowerCase(),
        value: JSON.stringify(categoryData)
      }
    });

    return NextResponse.json({
      success: true,
      category: category,
      data: categoryData
    });
  } catch (error) {
    console.error('Error saving category info:', error);
    return NextResponse.json(
      { error: 'Failed to save category information' },
      { status: 500 }
    );
  }
}

// PUT /api/product-category-info - Update existing category information
export async function PUT(request: NextRequest) {
  return POST(request); // Same logic as POST for upsert
}

// DELETE /api/product-category-info - Delete category information
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    if (!category) {
      return NextResponse.json(
        { error: 'Category parameter is required' },
        { status: 400 }
      );
    }

    await prisma.systemSetting.deleteMany({
      where: {
        category: 'product_category_info',
        key: category.toLowerCase()
      }
    });

    return NextResponse.json({
      success: true,
      message: `Category information for ${category} deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting category info:', error);
    return NextResponse.json(
      { error: 'Failed to delete category information' },
      { status: 500 }
    );
  }
}
