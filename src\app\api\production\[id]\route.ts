import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/production/[id] - Get a specific production record
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const production = await prisma.production.findUnique({
      where: { id: params.id },
      include: {
        consumption: {
          include: {
            rawMaterial: true,
          },
        },
      },
    });
    
    if (!production) {
      return NextResponse.json(
        { error: 'Production record not found' },
        { status: 404 }
      );
    }
    
    // Get product name
    const product = await prisma.product.findUnique({
      where: { id: production.productId },
      select: { name: true },
    });
    
    // Determine status based on creation date (mock status since it's not in the model)
    // In a real implementation, you would add a status field to the Production model
    const creationDate = new Date(production.createdAt);
    const now = new Date();
    const daysDifference = Math.floor((now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Assign a status based on creation date (just for demonstration)
    let status = 'PENDING';
    if (daysDifference > 2) {
      status = 'COMPLETED';
    } else if (daysDifference > 5 && Math.random() > 0.8) {
      status = 'CANCELLED';
    }
    
    // Format the response
    const formattedProduction = {
      id: production.id,
      productionNumber: `PRD-${production.id.substring(0, 8)}`,
      date: production.date.toISOString().split('T')[0],
      productId: production.productId,
      productName: product?.name || 'Unknown Product',
      quantity: production.quantity,
      status,
      rawMaterials: production.consumption.map(consumption => ({
        id: consumption.rawMaterialId,
        name: consumption.rawMaterial.name,
        quantity: consumption.quantity,
        unit: consumption.rawMaterial.unit,
      })),
      createdAt: production.createdAt,
      updatedAt: production.updatedAt,
    };
    
    return NextResponse.json(formattedProduction);
  } catch (error) {
    console.error('Error fetching production record:', error);
    return NextResponse.json(
      { error: 'Failed to fetch production record' },
      { status: 500 }
    );
  }
}

// PATCH /api/production/[id] - Update a production record's status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();
    
    // Validate status
    if (!data.status || !['COMPLETED', 'CANCELLED'].includes(data.status)) {
      return NextResponse.json(
        { error: 'Valid status is required (COMPLETED, CANCELLED)' },
        { status: 400 }
      );
    }
    
    // Find the production record
    const production = await prisma.production.findUnique({
      where: { id: params.id },
      include: {
        consumption: true,
      },
    });
    
    if (!production) {
      return NextResponse.json(
        { error: 'Production record not found' },
        { status: 404 }
      );
    }
    
    // In a real implementation, you would update the status field in the Production model
    // Since we don't have a status field, we'll just return the production record with the updated status
    
    // If the status is CANCELLED and the production was PENDING, restore the raw materials
    if (data.status === 'CANCELLED') {
      // Start a transaction to ensure data consistency
      await prisma.$transaction(async (tx) => {
        // Restore raw materials
        for (const consumption of production.consumption) {
          // Get current raw material stock
          const rawMaterial = await tx.rawMaterial.findUnique({
            where: { id: consumption.rawMaterialId },
          });
          
          if (rawMaterial) {
            // Update raw material stock
            await tx.rawMaterial.update({
              where: { id: consumption.rawMaterialId },
              data: {
                currentStock: rawMaterial.currentStock + consumption.quantity,
              },
            });
          }
        }
      });
    }
    
    // Get product name
    const product = await prisma.product.findUnique({
      where: { id: production.productId },
      select: { name: true },
    });
    
    // Get raw materials with their names
    const rawMaterials = await Promise.all(production.consumption.map(async (consumption) => {
      const rawMaterial = await prisma.rawMaterial.findUnique({
        where: { id: consumption.rawMaterialId },
        select: { name: true, unit: true },
      });
      
      return {
        id: consumption.rawMaterialId,
        name: rawMaterial?.name || 'Unknown Material',
        quantity: consumption.quantity,
        unit: rawMaterial?.unit || 'unit',
      };
    }));
    
    // Format the response
    const formattedProduction = {
      id: production.id,
      productionNumber: `PRD-${production.id.substring(0, 8)}`,
      date: production.date.toISOString().split('T')[0],
      productId: production.productId,
      productName: product?.name || 'Unknown Product',
      quantity: production.quantity,
      status: data.status,
      rawMaterials,
      createdAt: production.createdAt,
      updatedAt: production.updatedAt,
    };
    
    return NextResponse.json(formattedProduction);
  } catch (error) {
    console.error('Error updating production record:', error);
    return NextResponse.json(
      { error: 'Failed to update production record' },
      { status: 500 }
    );
  }
}

// DELETE /api/production/[id] - Delete a production record
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Find the production record
    const production = await prisma.production.findUnique({
      where: { id: params.id },
      include: {
        consumption: true,
      },
    });
    
    if (!production) {
      return NextResponse.json(
        { error: 'Production record not found' },
        { status: 404 }
      );
    }
    
    // Start a transaction to ensure data consistency
    await prisma.$transaction(async (tx) => {
      // Restore raw materials (assuming the production is being cancelled)
      for (const consumption of production.consumption) {
        // Get current raw material stock
        const rawMaterial = await tx.rawMaterial.findUnique({
          where: { id: consumption.rawMaterialId },
        });
        
        if (rawMaterial) {
          // Update raw material stock
          await tx.rawMaterial.update({
            where: { id: consumption.rawMaterialId },
            data: {
              currentStock: rawMaterial.currentStock + consumption.quantity,
            },
          });
        }
      }
      
      // Delete consumption records
      await tx.rawMaterialConsumption.deleteMany({
        where: { productionId: params.id },
      });
      
      // Delete the production record
      await tx.production.delete({
        where: { id: params.id },
      });
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting production record:', error);
    return NextResponse.json(
      { error: 'Failed to delete production record' },
      { status: 500 }
    );
  }
}
