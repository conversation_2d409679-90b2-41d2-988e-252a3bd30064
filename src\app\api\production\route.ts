import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// Helper function to generate production number
function generateProductionNumber() {
  const prefix = 'PRD';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
}

// GET /api/production - Get all production records
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const status = url.searchParams.get('status');

    // Build the query filter
    const filter: any = {};

    if (status && ['PENDING', 'COMPLETED', 'CANCELLED'].includes(status)) {
      // We'll need to filter in memory since status is not in the database model
    }

    // Get all production records with related data
    const productionRecords = await prisma.production.findMany({
      include: {
        consumption: {
          include: {
            rawMaterial: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    // Get product names for each production record
    const formattedRecords = await Promise.all(productionRecords.map(async (record) => {
      const product = await prisma.product.findUnique({
        where: { id: record.productId },
        select: { name: true },
      });

      // Determine status based on creation date (mock status since it's not in the model)
      // In a real implementation, you would add a status field to the Production model
      const creationDate = new Date(record.createdAt);
      const now = new Date();
      const daysDifference = Math.floor((now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));

      // Assign a status based on creation date (just for demonstration)
      let recordStatus = 'PENDING';
      if (daysDifference > 2) {
        recordStatus = 'COMPLETED';
      } else if (daysDifference > 5 && Math.random() > 0.8) {
        recordStatus = 'CANCELLED';
      }

      return {
        id: record.id,
        productionNumber: `PRD-${record.id.substring(0, 8)}`,
        date: record.date.toISOString().split('T')[0],
        productId: record.productId,
        productName: product?.name || 'Unknown Product',
        quantity: record.quantity,
        status: recordStatus,
        rawMaterials: record.consumption.map(consumption => ({
          id: consumption.rawMaterialId,
          name: consumption.rawMaterial.name,
          quantity: consumption.quantity,
          unit: consumption.rawMaterial.unit,
        })),
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      };
    }));

    // Apply status filter if provided
    const filteredRecords = status
      ? formattedRecords.filter(record => record.status === status)
      : formattedRecords;

    return NextResponse.json(filteredRecords);
  } catch (error) {
    console.error('Error fetching production records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch production records' },
      { status: 500 }
    );
  }
}

// POST /api/production - Create a new production record
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    if (!data.quantity || isNaN(parseFloat(data.quantity)) || parseFloat(data.quantity) <= 0) {
      return NextResponse.json(
        { error: 'Quantity must be a positive number' },
        { status: 400 }
      );
    }

    if (!data.date) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    if (!data.rawMaterials || !Array.isArray(data.rawMaterials) || data.rawMaterials.length === 0) {
      return NextResponse.json(
        { error: 'At least one raw material is required' },
        { status: 400 }
      );
    }

    // Start a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create the production record
      const production = await tx.production.create({
        data: {
          productId: data.productId,
          quantity: parseFloat(data.quantity),
          date: new Date(data.date),
        },
      });

      // Create raw material consumption records
      for (const material of data.rawMaterials) {
        // Validate material data
        if (!material.id || !material.quantity || isNaN(parseFloat(material.quantity))) {
          throw new Error(`Invalid material data: ${JSON.stringify(material)}`);
        }

        // Get current raw material stock
        const rawMaterial = await tx.rawMaterial.findUnique({
          where: { id: material.id },
        });

        if (!rawMaterial) {
          throw new Error(`Raw material not found: ${material.id}`);
        }

        // Check if there's enough stock
        const requiredQuantity = parseFloat(material.quantity);
        if (rawMaterial.currentStock < requiredQuantity) {
          throw new Error(`Not enough stock for ${rawMaterial.name}: Need ${requiredQuantity} ${rawMaterial.unit}, but only have ${rawMaterial.currentStock} ${rawMaterial.unit}`);
        }

        // Create consumption record
        await tx.rawMaterialConsumption.create({
          data: {
            productionId: production.id,
            rawMaterialId: material.id,
            quantity: requiredQuantity,
          },
        });

        // Update raw material stock
        await tx.rawMaterial.update({
          where: { id: material.id },
          data: {
            currentStock: rawMaterial.currentStock - requiredQuantity,
          },
        });
      }

      // Get the complete production record with related data
      return await tx.production.findUnique({
        where: { id: production.id },
        include: {
          consumption: {
            include: {
              rawMaterial: true,
            },
          },
        },
      });
    });

    // Get product name
    if (!result) {
      throw new Error('Failed to create production record');
    }

    const product = await prisma.product.findUnique({
      where: { id: result.productId },
      select: { name: true },
    });

    // Format the response
    const formattedProduction = {
      id: result.id,
      productionNumber: `PRD-${result.id.substring(0, 8)}`,
      date: result.date.toISOString().split('T')[0],
      productId: result.productId,
      productName: product?.name || 'Unknown Product',
      quantity: result.quantity,
      status: 'PENDING',
      rawMaterials: result.consumption.map(consumption => ({
        id: consumption.rawMaterialId,
        name: consumption.rawMaterial.name,
        quantity: consumption.quantity,
        unit: consumption.rawMaterial.unit,
      })),
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };

    return NextResponse.json(formattedProduction, { status: 201 });
  } catch (error) {
    console.error('Error creating production record:', error);
    return NextResponse.json(
      { error: (error as Error).message || 'Failed to create production record' },
      { status: 500 }
    );
  }
}
