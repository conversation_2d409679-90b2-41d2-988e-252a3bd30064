import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const productId = params.id;

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Get product with variants and images
    const product = await prisma.product.findUnique({
      where: {
        id: productId,
        isActive: true,
      },
      include: {
        variants: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        productImages: {
          orderBy: { sortOrder: 'asc' }
        },
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(product);
  } catch (error) {
    console.error('Error fetching product with variants:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const productId = params.id;
    const data = await request.json();

    console.log('📝 PUT request for product:', productId);
    console.log('👤 Authenticated user:', user.email);
    console.log('📦 Update data:', {
      name: data.name,
      category: data.category,
      variantsCount: data.variants?.length || 0
    });

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Validate variants if provided
    if (data.variants && data.variants.length > 0) {
      const hasDefault = data.variants.some((v: any) => v.isDefault);
      if (!hasDefault) {
        return NextResponse.json(
          { error: 'One variant must be set as default' },
          { status: 400 }
        );
      }
    }

    // Update product with variants in a transaction
    const result = await prisma.$transaction(async (tx) => {
      console.log('🔄 Starting transaction for product update');

      // Parse numeric values safely
      const defaultVariant = data.variants?.find((v: any) => v.isDefault);
      const basePrice = defaultVariant ? parseFloat(defaultVariant.price) : parseFloat(data.price || '0');
      const baseCostPrice = defaultVariant ? parseFloat(defaultVariant.costPrice || '0') : parseFloat(data.costPrice || '0');

      console.log('💰 Parsed prices:', { basePrice, baseCostPrice });

      // Update the main product
      const product = await tx.product.update({
        where: { id: productId },
        data: {
          name: data.name,
          description: data.description || '',
          category: data.category,
          price: basePrice,
          costPrice: baseCostPrice,
          unit: data.unit || 'piece',
          lowStockThreshold: parseInt(data.lowStockThreshold || '10'),
          sku: data.sku,
          imageUrl: data.imageUrl || null,
          metaTitle: data.metaTitle || null,
          metaDescription: data.metaDescription || null,
          isActive: data.isActive !== undefined ? data.isActive : true,
        }
      });

      console.log('✅ Product updated:', product.id);

      // Update variants if provided
      if (data.variants && data.variants.length > 0) {
        console.log('🔄 Updating variants, count:', data.variants.length);

        // Delete existing variants
        await tx.productVariant.deleteMany({
          where: { productId: productId }
        });

        console.log('🗑️ Deleted existing variants');

        // Create new variants with better error handling
        const variants = [];
        for (let index = 0; index < data.variants.length; index++) {
          const variant = data.variants[index];

          try {
            console.log(`📦 Creating variant ${index + 1}:`, {
              weight: variant.weight,
              price: variant.price,
              costPrice: variant.costPrice,
              isDefault: variant.isDefault
            });

            const createdVariant = await tx.productVariant.create({
              data: {
                productId: productId,
                weight: variant.weight || '',
                price: parseFloat(variant.price) || 0,
                costPrice: parseFloat(variant.costPrice || '0'),
                sku: variant.sku || `${product.sku}-${variant.weight?.replace(/\s+/g, '') || index}`,
                isDefault: variant.isDefault || false,
                isActive: variant.isActive !== undefined ? variant.isActive : true,
                sortOrder: variant.sortOrder || index + 1,
              }
            });

            variants.push(createdVariant);
            console.log(`✅ Created variant ${index + 1}:`, createdVariant.id);
          } catch (variantError) {
            console.error(`❌ Error creating variant ${index + 1}:`, variantError);
            throw new Error(`Failed to create variant ${index + 1}: ${variantError instanceof Error ? variantError.message : 'Unknown error'}`);
          }
        }

        console.log('✅ All variants created successfully');
        return { product, variants };
      }

      return { product, variants: [] };
    });

    // Return the updated product with variants
    const updatedProduct = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        variants: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        productImages: {
          orderBy: { sortOrder: 'asc' }
        },
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    });

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product with variants:', error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const productId = params.id;

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Delete product and its variants (cascade will handle variants)
    await prisma.product.delete({
      where: { id: productId }
    });

    return NextResponse.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
