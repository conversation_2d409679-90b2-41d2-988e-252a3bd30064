import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    console.log('📦 Creating product with variants:', {
      name: data.name,
      category: data.category,
      variantsCount: data.variants?.length || 0,
      hasImageUrl: !!data.imageUrl,
      warehouseId: data.warehouseId
    });

    // Validate required fields
    if (!data.name || !data.category || !data.warehouseId) {
      return NextResponse.json(
        { error: 'Name, category, and warehouse are required' },
        { status: 400 }
      );
    }

    // Validate variants
    if (!data.variants || data.variants.length === 0) {
      return NextResponse.json(
        { error: 'At least one product variant is required' },
        { status: 400 }
      );
    }

    const hasDefault = data.variants.some((v: any) => v.isDefault);
    if (!hasDefault) {
      return NextResponse.json(
        { error: 'One variant must be set as default' },
        { status: 400 }
      );
    }

    // Create product with variants in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the main product
      const defaultVariant = data.variants.find((v: any) => v.isDefault);
      const product = await tx.product.create({
        data: {
          name: data.name,
          description: data.description || '',
          category: data.category,
          price: parseFloat(defaultVariant?.price || '0'),
          discountedPrice: defaultVariant?.discountedPrice ? parseFloat(defaultVariant.discountedPrice) : null,
          costPrice: parseFloat(defaultVariant?.costPrice || '0'),
          unit: data.unit || 'piece',
          lowStockThreshold: parseInt(data.lowStockThreshold || '10'),
          sku: data.sku || `SKU-${Date.now()}`,
          imageUrl: data.imageUrl || null,
          metaTitle: data.metaTitle || null,
          metaDescription: data.metaDescription || null,
          isActive: data.isActive !== undefined ? data.isActive : true,
        }
      });

      // Create product variants
      const variants = await Promise.all(
        data.variants.map((variant: any, index: number) =>
          tx.productVariant.create({
            data: {
              productId: product.id,
              weight: variant.weight,
              price: parseFloat(variant.price),
              discountedPrice: variant.discountedPrice ? parseFloat(variant.discountedPrice) : null,
              costPrice: parseFloat(variant.costPrice || '0'),
              sku: variant.sku || `${product.sku}-${variant.weight.replace(/\s+/g, '')}`,
              isDefault: variant.isDefault || false,
              isActive: variant.isActive !== undefined ? variant.isActive : true,
              sortOrder: variant.sortOrder || index + 1,
            }
          })
        )
      );

      // Create warehouse inventory if initial stock is provided
      if (data.initialStock && parseFloat(data.initialStock) > 0) {
        await tx.warehouseInventory.create({
          data: {
            warehouseId: data.warehouseId,
            productId: product.id,
            quantity: parseFloat(data.initialStock),
          }
        });
      }

      return { product, variants };
    });

    console.log('✅ Product created with variants:', {
      productId: result.product.id,
      variantsCreated: result.variants.length
    });

    // Return the complete product with variants
    const productWithVariants = await prisma.product.findUnique({
      where: { id: result.product.id },
      include: {
        variants: {
          orderBy: { sortOrder: 'asc' }
        },
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    });

    return NextResponse.json(productWithVariants, { status: 201 });
  } catch (error) {
    console.error('Error creating product with variants:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '1000');
    const skip = (page - 1) * limit;

    console.log('📦 Fetching products with variants - Page:', page, 'Limit:', limit);

    // Get total count
    const total = await prisma.product.count();

    // Get products with all related data
    const products = await prisma.product.findMany({
      skip,
      take: limit,
      include: {
        variants: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        productImages: {
          orderBy: { sortOrder: 'asc' }
        },
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log('✅ Successfully fetched', products.length, 'products with variants');

    const pages = Math.ceil(total / limit);

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    });
  } catch (error) {
    console.error('Error fetching products with variants:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
