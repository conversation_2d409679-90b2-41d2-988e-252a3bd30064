import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/products/[id]/raw-materials - Get raw materials for a specific product
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const productRawMaterials = await prisma.productRawMaterial.findMany({
      where: { productId: params.id },
      include: {
        rawMaterial: true,
      },
    });
    
    if (productRawMaterials.length === 0) {
      return NextResponse.json([]);
    }
    
    // Format the response
    const formattedRawMaterials = productRawMaterials.map(material => ({
      id: material.rawMaterial.id,
      name: material.rawMaterial.name,
      quantityRequired: material.quantityRequired,
      unit: material.rawMaterial.unit,
      currentStock: material.rawMaterial.currentStock,
    }));
    
    return NextResponse.json(formattedRawMaterials);
  } catch (error) {
    console.error('Error fetching product raw materials:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product raw materials' },
      { status: 500 }
    );
  }
}
