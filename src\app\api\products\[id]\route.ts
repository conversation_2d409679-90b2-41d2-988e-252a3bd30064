import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { ProductService } from '@/lib/services/product-service';

// GET /api/products/[id] - Get a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('products.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = await params;
    console.log('🔍 Fetching product from database:', id);

    // Get product from database
    const product = await ProductService.getProductById(id);

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Add mock inventory data for compatibility
    const productWithInventory = {
      ...product,
      warehouseInventory: [],
      storeInventory: [],
      categoryName: product.category,
    };

    console.log('✅ Product fetched successfully:', product.name);
    return NextResponse.json(productWithInventory);
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

// PUT /api/products/[id] - Update a product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('products.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const data = await request.json();
    console.log('🔄 Updating product in database:', id, data);

    // Validate required fields
    if (data.name && !data.name.trim()) {
      return NextResponse.json(
        { error: 'Product name cannot be empty' },
        { status: 400 }
      );
    }

    if (data.price && (isNaN(parseFloat(data.price)) || parseFloat(data.price) < 0)) {
      return NextResponse.json(
        { error: 'Price must be a valid positive number' },
        { status: 400 }
      );
    }

    // Update product in database
    const updateData = {
      ...(data.name && { name: data.name }),
      ...(data.description !== undefined && { description: data.description }),
      ...(data.category && { category: data.category }),
      ...(data.price !== undefined && { price: parseFloat(data.price) }),
      ...(data.costPrice !== undefined && { costPrice: parseFloat(data.costPrice || '0') }),
      ...(data.unit && { unit: data.unit }),
      ...(data.lowStockThreshold !== undefined && { lowStockThreshold: parseInt(data.lowStockThreshold || '10') }),
      ...(data.sku && { sku: data.sku }),
      ...(data.imageUrl !== undefined && { imageUrl: data.imageUrl }),
      ...(data.metaTitle !== undefined && { metaTitle: data.metaTitle }),
      ...(data.metaDescription !== undefined && { metaDescription: data.metaDescription }),
      ...(data.isActive !== undefined && { isActive: data.isActive }),
    };

    const product = await ProductService.updateProduct(id, updateData);

    // Add mock inventory data for compatibility
    const productWithInventory = {
      ...product,
      warehouseInventory: [],
      storeInventory: [],
      categoryName: product.category,
    };

    console.log('✅ Product updated successfully:', product.name);
    return NextResponse.json(productWithInventory);
  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id] - Delete a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('products.delete')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Await params to fix Next.js warning
    const { id } = await params;
    console.log('🗑️ DELETE API called for product ID:', id);

    // Check if product exists before deleting
    const existingProduct = await ProductService.getProductById(id);
    if (!existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Delete product from database
    await ProductService.deleteProduct(id);

    console.log('✅ Product deleted successfully from database:', existingProduct.name);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting product:', error);

    // Check if it's a foreign key constraint error
    if (error instanceof Error && (
      error.message.includes('Foreign key constraint') ||
      error.message.includes('foreign key constraint') ||
      error.message.includes('violates foreign key constraint') ||
      error.message.includes('FOREIGN KEY constraint failed')
    )) {
      return NextResponse.json(
        { error: 'Cannot delete product: it has related records that must be removed first' },
        { status: 400 }
      );
    }

    // Check if it's a record not found error
    if (error instanceof Error && (
      error.message.includes('Record to delete does not exist') ||
      error.message.includes('No Product found')
    )) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Generic error
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json(
      { error: `Failed to delete product: ${errorMessage}` },
      { status: 500 }
    );
  }
}
