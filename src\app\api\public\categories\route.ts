import { NextRequest, NextResponse } from 'next/server';
import { CategoryService } from '@/lib/services/category-service';

// GET /api/public/categories - Get all categories for website (no authentication required)
export async function GET(request: NextRequest) {
  try {
    console.log('Public Categories API called');

    // Get categories from database using service layer
    const categories = await CategoryService.getAllCategories();

    // Format categories for website consumption
    const websiteCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      slug: category.name.toLowerCase().replace(/\s+/g, '-'),
      description: category.description || `Browse our ${category.name.toLowerCase()} collection`,
      imageUrl: category.imageUrl,
      productCount: category.productCount,
      isActive: category.isActive,
    }));

    console.log(`Returning ${websiteCategories.length} categories for website`);
    return NextResponse.json(websiteCategories);
  } catch (error) {
    console.error('Error fetching public categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// Helper function to get category images
function getCategoryImage(category: string): string {
  const categoryLower = category.toLowerCase();

  if (categoryLower.includes('cake')) {
    return '/categories/cakes.svg';
  } else if (categoryLower.includes('flower')) {
    return '/categories/flowers.svg';
  } else if (categoryLower.includes('gift')) {
    return '/categories/gifts.svg';
  } else if (categoryLower.includes('plant')) {
    return '/categories/plants.svg';
  } else if (categoryLower.includes('combo')) {
    return '/categories/combos.svg';
  } else if (categoryLower.includes('occasion')) {
    return '/categories/occasions.svg';
  } else {
    return '/categories/gifts.svg'; // Default fallback
  }
}
