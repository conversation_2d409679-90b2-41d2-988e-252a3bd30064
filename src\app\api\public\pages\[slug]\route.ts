import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/public/pages/[slug] - Get single page by slug for website (no authentication required)
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    console.log('Public Page API called for slug:', slug);

    const page = await prisma.systemSetting.findFirst({
      where: {
        category: 'static_pages',
        key: slug
      }
    });

    if (!page) {
      console.log('Page not found for slug:', slug);
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      );
    }

    let data;
    try {
      data = JSON.parse(page.value);
    } catch (error) {
      console.error('Error parsing page data:', error);
      return NextResponse.json(
        { error: 'Invalid page data' },
        { status: 500 }
      );
    }

    // Only return active pages to the public
    if (!data.isActive) {
      console.log('Page is inactive for slug:', slug);
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      );
    }

    const pageData = {
      id: page.key,
      title: data.title,
      slug: data.slug,
      content: data.content,
      isActive: data.isActive,
      createdAt: page.createdAt,
      updatedAt: page.updatedAt
    };

    console.log('Public Page API: Returning page:', pageData.title);
    
    return NextResponse.json(pageData);
  } catch (error) {
    console.error('Error fetching public page:', error);
    return NextResponse.json(
      { error: 'Failed to fetch page' },
      { status: 500 }
    );
  }
}
