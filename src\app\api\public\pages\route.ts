import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/public/pages - Get all active pages for website (no authentication required)
export async function GET(request: NextRequest) {
  try {
    console.log('Public Pages API called');

    const pages = await prisma.systemSetting.findMany({
      where: {
        category: 'static_pages'
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    const formattedPages = pages
      .map(page => {
        try {
          const data = JSON.parse(page.value);
          return {
            id: page.key,
            title: data.title,
            slug: data.slug,
            content: data.content,
            isActive: data.isActive,
            createdAt: page.createdAt,
            updatedAt: page.updatedAt
          };
        } catch (error) {
          console.error('Error parsing page data:', error);
          return null;
        }
      })
      .filter(page => page !== null && page.isActive); // Only return active pages

    console.log(`Public Pages API: Returning ${formattedPages.length} active pages`);
    
    return NextResponse.json(formattedPages);
  } catch (error) {
    console.error('Error fetching public pages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pages' },
      { status: 500 }
    );
  }
}
