import { NextRequest, NextResponse } from 'next/server';
import { ProductService } from '@/lib/services/product-service';

// GET /api/public/products/[id] - Get single product for website (no authentication required)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Public Product Detail API called for ID:', params.id);

    // Get product from database
    const product = await ProductService.getProductById(params.id);

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Check if product is active (only show active products on website)
    if (!product.isActive) {
      return NextResponse.json(
        { error: 'Product not available' },
        { status: 404 }
      );
    }

    // Format product for website consumption
    const websiteProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      category: product.category,
      price: product.price,
      unit: product.unit,
      sku: product.sku,
      weight: product.weight,
      dimensions: product.dimensions,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      imageUrl: product.imageUrl,
      tags: product.tags,
      metaTitle: product.metaTitle,
      metaDescription: product.metaDescription,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      // Add website-specific fields
      productImages: product.imageUrl ? [{ url: product.imageUrl, isMain: true }] : [],
      gallery: product.imageUrl ? [product.imageUrl] : [],
      // Add related products (same category)
      relatedProducts: await getRelatedProducts(product.id, product.category),
    };

    console.log(`Returning product details for: ${product.name}`);
    return NextResponse.json(websiteProduct);
  } catch (error) {
    console.error('Error fetching public product:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

// Helper function to get related products
async function getRelatedProducts(currentProductId: string, category: string) {
  try {
    const allProducts = await ProductService.getProductsByCategory(category);
    const relatedProducts = allProducts
      .filter(product =>
        product.id !== currentProductId &&
        product.isActive
      )
      .slice(0, 4) // Limit to 4 related products
      .map(product => ({
        id: product.id,
        name: product.name,
        price: product.price,
        imageUrl: product.imageUrl,
        productImages: product.imageUrl ? [{ url: product.imageUrl, isMain: true }] : [],
      }));

    return relatedProducts;
  } catch (error) {
    console.error('Error fetching related products:', error);
    return [];
  }
}
