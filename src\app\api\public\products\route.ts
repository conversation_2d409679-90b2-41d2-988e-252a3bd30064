import { NextRequest, NextResponse } from 'next/server';
import { ProductService } from '@/lib/services/product-service';

// GET /api/public/products - Get all active products for website (no authentication required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const search = searchParams.get('search');

    console.log('Public Products API called with params:', { categoryId, search });

    // Get all products from database
    const allProducts = await ProductService.getAllProducts();

    // Filter only active products for public website
    let activeProducts = allProducts.filter(product => product.isActive);

    // Filter by category if specified
    if (categoryId) {
      console.log(`Filtering products by category: ${categoryId}`);
      activeProducts = activeProducts.filter(product => {
        // Try exact match first
        if (product.category === categoryId) return true;

        // Try case-insensitive match
        if (product.category.toLowerCase() === categoryId.toLowerCase()) return true;

        // Try with spaces replaced by hyphens (for URL slugs)
        const categorySlug = product.category.toLowerCase().replace(/\s+/g, '-');
        if (categorySlug === categoryId.toLowerCase()) return true;

        // Try with hyphens replaced by spaces
        const categoryFromSlug = categoryId.toLowerCase().replace(/-/g, ' ');
        if (product.category.toLowerCase() === categoryFromSlug) return true;

        return false;
      });
      console.log(`Found ${activeProducts.length} products in category: ${categoryId}`);
    }

    // Filter by search term if specified
    if (search) {
      console.log(`Filtering products by search term: ${search}`);
      const searchLower = search.toLowerCase();
      activeProducts = activeProducts.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower) ||
        product.category.toLowerCase().includes(searchLower) ||
        (product.tags && product.tags.toLowerCase().includes(searchLower))
      );
      console.log(`Found ${activeProducts.length} products matching search: ${search}`);
    }

    // Format products for website consumption
    const websiteProducts = activeProducts.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      category: product.category,
      price: product.price,
      unit: product.unit,
      sku: product.sku,
      weight: product.weight,
      dimensions: product.dimensions,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      imageUrl: product.imageUrl,
      tags: product.tags,
      metaTitle: product.metaTitle,
      metaDescription: product.metaDescription,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      // Add website-specific fields
      productImages: product.imageUrl ? [{ url: product.imageUrl, isMain: true }] : [],
      gallery: product.imageUrl ? [product.imageUrl] : [],
    }));

    console.log(`Returning ${websiteProducts.length} active products for website`);
    return NextResponse.json(websiteProducts);
  } catch (error) {
    console.error('Error fetching public products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
