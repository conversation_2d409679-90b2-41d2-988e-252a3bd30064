import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/purchase-orders/[id] - Get a specific purchase order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Purchase order not found' },
        { status: 404 }
      );
    }

    // Transform transaction to purchase order format
    const purchaseOrder = {
      id: transaction.id,
      orderNumber: `PO-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt,
      supplierId: transaction.id, // Using the transaction ID as supplier ID
      supplierName: transaction.partyName || 'Unknown Supplier',
      status: transaction.status.toLowerCase(),
      total: transaction.totalAmount,
      paymentStatus: transaction.status === 'COMPLETED' ? 'paid' : 'unpaid',
      expectedDeliveryDate: transaction.updatedAt, // Using updatedAt as a placeholder
      items: transaction.items.map(item => ({
        id: item.id,
        productId: item.productId || '',
        productName: item.product?.name || 'Unknown Product', // Use product relation if available
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
      notes: null, // No notes field in schema
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      storeId: transaction.storeId,
      storeName: transaction.store?.name || null,
      staffId: transaction.userId,
      staffName: transaction.user?.name || null,
    };

    return NextResponse.json(purchaseOrder);
  } catch (error) {
    console.error('Error fetching purchase order:', error);
    return NextResponse.json(
      { error: 'Failed to fetch purchase order' },
      { status: 500 }
    );
  }
}

// PUT /api/purchase-orders/[id] - Update a purchase order
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Find the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Purchase order not found' },
        { status: 404 }
      );
    }

    // Update the transaction
    const updatedTransaction = await prisma.transaction.update({
      where: { id: params.id },
      data: {
        status: data.status === 'delivered' ? 'COMPLETED' :
                data.status === 'pending' ? 'PENDING' : 'CANCELLED',
        // notes field doesn't exist in the schema
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Transform to purchase order format
    const purchaseOrder = {
      id: updatedTransaction.id,
      orderNumber: `PO-${updatedTransaction.id.substring(0, 8)}`,
      date: updatedTransaction.createdAt,
      supplierId: data.supplierId,
      supplierName: updatedTransaction.partyName || 'Unknown Supplier',
      status: updatedTransaction.status.toLowerCase(),
      total: updatedTransaction.totalAmount,
      paymentStatus: updatedTransaction.status === 'COMPLETED' ? 'paid' : 'unpaid',
      expectedDeliveryDate: data.expectedDeliveryDate || updatedTransaction.updatedAt,
      items: updatedTransaction.items.map((item: { id: string; productId: string; product?: { name: string }; quantity: number; unitPrice: number }) => ({
        id: item.id,
        productId: item.productId || '',
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
      notes: null, // No notes field in schema
      createdAt: updatedTransaction.createdAt,
      updatedAt: updatedTransaction.updatedAt,
      storeId: updatedTransaction.storeId,
      storeName: updatedTransaction.store?.name || null,
      staffId: updatedTransaction.userId,
      staffName: updatedTransaction.user?.name || null,
    };

    return NextResponse.json(purchaseOrder);
  } catch (error) {
    console.error('Error updating purchase order:', error);
    return NextResponse.json(
      { error: 'Failed to update purchase order' },
      { status: 500 }
    );
  }
}

// DELETE /api/purchase-orders/[id] - Delete a purchase order
export async function DELETE(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    // Find the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: true,
      },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Purchase order not found' },
        { status: 404 }
      );
    }

    // Delete transaction items
    await prisma.transactionItem.deleteMany({
      where: { transactionId: params.id },
    });

    // Delete the transaction
    await prisma.transaction.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting purchase order:', error);
    return NextResponse.json(
      { error: 'Failed to delete purchase order' },
      { status: 500 }
    );
  }
}
