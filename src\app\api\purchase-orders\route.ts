import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/purchase-orders - Get all purchase orders
export async function GET(request: NextRequest) {
  try {
    const transactions = await prisma.transaction.findMany({
      where: {
        type: 'PURCHASE',
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Transform transactions to purchase orders format
    const purchaseOrders = transactions.map(transaction => ({
      id: transaction.id,
      orderNumber: `PO-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt,
      supplierId: transaction.id,
      supplierName: transaction.partyName || 'Unknown Supplier',
      status: transaction.status.toLowerCase(),
      total: transaction.totalAmount,
      paymentStatus: transaction.status === 'COMPLETED' ? 'paid' : 'unpaid',
      expectedDeliveryDate: transaction.updatedAt, // Using updatedAt as a placeholder
      items: transaction.items.map((item: { id: string; productId: string; product?: { name: string }; quantity: number; unitPrice: number }) => ({
        id: item.id,
        productId: item.productId || '',
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
      notes: null, // No notes field in schema
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      storeId: transaction.storeId,
      storeName: transaction.store?.name || null,
      staffId: transaction.userId,
      staffName: transaction.user?.name || null,
    }));

    return NextResponse.json(purchaseOrders);
  } catch (error) {
    console.error('Error fetching purchase orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch purchase orders' },
      { status: 500 }
    );
  }
}

// POST /api/purchase-orders - Create a new purchase order
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.supplierId) {
      return NextResponse.json(
        { error: 'Supplier is required' },
        { status: 400 }
      );
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: 'At least one item is required' },
        { status: 400 }
      );
    }

    // Find the supplier transaction to get supplier name
    const supplierTransaction = await prisma.transaction.findUnique({
      where: { id: data.supplierId },
    });

    if (!supplierTransaction) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    // Calculate total amount
    let total = 0;
    for (const item of data.items) {
      total += parseFloat(item.quantity) * parseFloat(item.unitPrice);
    }

    // Create the transaction
    const transaction = await prisma.transaction.create({
      data: {
        type: 'PURCHASE',
        storeId: data.storeId,
        userId: data.staffId,
        partyName: supplierTransaction.partyName,
        partyContact: supplierTransaction.partyContact,
        totalAmount: total,
        status: data.paymentStatus === 'paid' ? 'COMPLETED' : 'PENDING',
        // notes field doesn't exist in the schema
        items: {
          create: data.items.map((item: { productId: string; productName: string; quantity: string; unitPrice: string }) => ({
            productId: item.productId || null,
            // We can't set productName directly, but we'll use the product relation
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            totalPrice: parseFloat(item.quantity) * parseFloat(item.unitPrice),
          })),
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Format the response
    const purchaseOrder = {
      id: transaction.id,
      orderNumber: `PO-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt,
      supplierId: data.supplierId,
      supplierName: transaction.partyName || 'Unknown Supplier',
      status: transaction.status.toLowerCase(),
      total: transaction.totalAmount,
      paymentStatus: transaction.status === 'COMPLETED' ? 'paid' : 'unpaid',
      expectedDeliveryDate: data.expectedDeliveryDate || transaction.updatedAt,
      items: transaction.items.map((item: { id: string; productId: string; product?: { name: string }; quantity: number; unitPrice: number }) => ({
        id: item.id,
        productId: item.productId || '',
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
      notes: null, // No notes field in schema
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      storeId: transaction.storeId,
      storeName: transaction.store?.name || null,
      staffId: transaction.userId,
      staffName: transaction.user?.name || null,
    };

    return NextResponse.json(purchaseOrder, { status: 201 });
  } catch (error) {
    console.error('Error creating purchase order:', error);
    return NextResponse.json(
      { error: 'Failed to create purchase order' },
      { status: 500 }
    );
  }
}
