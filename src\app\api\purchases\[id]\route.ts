import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/purchases/[id] - Get a specific purchase
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Purchase not found' },
        { status: 404 }
      );
    }

    // Format the response
    const purchase = {
      id: transaction.id,
      invoiceNumber: `PUR-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt.toISOString().split('T')[0],
      warehouseId: transaction.storeId || '',
      warehouseName: transaction.store?.name || 'Unknown Warehouse',
      supplierName: transaction.partyName || '',
      supplierContact: transaction.partyContact || '',
      totalAmount: transaction.totalAmount,
      paymentMethod: transaction.paymentMethod,
      status: transaction.status,
      items: transaction.items.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
      })),
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };

    return NextResponse.json(purchase);
  } catch (error) {
    console.error('Error fetching purchase:', error);
    return NextResponse.json(
      { error: 'Failed to fetch purchase' },
      { status: 500 }
    );
  }
}

// DELETE /api/purchases/[id] - Delete a purchase
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Find the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: true,
      },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Purchase not found' },
        { status: 404 }
      );
    }

    // Start a transaction to ensure data consistency
    await prisma.$transaction(async (tx) => {
      // Revert warehouse inventory for each product
      for (const item of transaction.items) {
        // Find warehouse inventory for this product
        const warehouseInventory = await tx.warehouseInventory.findFirst({
          where: {
            warehouseId: transaction.storeId || '',
            productId: item.productId,
          },
        });

        if (warehouseInventory) {
          // Update existing inventory
          await tx.warehouseInventory.update({
            where: { id: warehouseInventory.id },
            data: {
              quantity: Math.max(0, warehouseInventory.quantity - item.quantity),
            },
          });
        }
      }

      // Delete transaction items
      await tx.transactionItem.deleteMany({
        where: { transactionId: params.id },
      });

      // Delete the transaction
      await tx.transaction.delete({
        where: { id: params.id },
      });
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting purchase:', error);
    return NextResponse.json(
      { error: 'Failed to delete purchase' },
      { status: 500 }
    );
  }
}
