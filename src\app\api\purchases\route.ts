import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/purchases - Get all purchases (NeonDB preferred, mock fallback)
export async function GET(request: NextRequest) {
  try {
    console.log('🛒 Purchases API called - trying NeonDB first');

    // Try NeonDB first
    try {
      const transactions = await prisma.transaction.findMany({
      where: {
        type: 'PURCHASE',
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format the response
    const purchases = transactions.map(transaction => {
      return {
        id: transaction.id,
        invoiceNumber: `PUR-${transaction.id.substring(0, 8)}`,
        date: transaction.createdAt.toISOString().split('T')[0],
        warehouseId: transaction.storeId || '',
        warehouseName: transaction.store?.name || 'Unknown Warehouse',
        supplierName: transaction.partyName || '',
        supplierContact: transaction.partyContact || '',
        totalAmount: transaction.totalAmount,
        paymentMethod: transaction.paymentMethod,
        status: transaction.status,
        items: transaction.items.map(item => ({
          id: item.id,
          productId: item.productId,
          productName: item.product?.name || 'Unknown Product',
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
        })),
      };
    });

      return NextResponse.json(purchases);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error in purchases API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch purchases' },
      { status: 500 }
    );
  }
}

// POST /api/purchases - Create a new purchase
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.warehouseId) {
      return NextResponse.json(
        { error: 'Warehouse is required' },
        { status: 400 }
      );
    }

    if (!data.supplierName) {
      return NextResponse.json(
        { error: 'Supplier name is required' },
        { status: 400 }
      );
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: 'At least one item is required' },
        { status: 400 }
      );
    }

    // Get the first user for demo purposes
    const user = await prisma.user.findFirst();

    if (!user) {
      return NextResponse.json(
        { error: 'No user found' },
        { status: 400 }
      );
    }

    // Calculate total amount
    let totalAmount = 0;
    for (const item of data.items) {
      totalAmount += parseFloat(item.totalPrice);
    }

    // Start a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create the transaction
      const transaction = await tx.transaction.create({
        data: {
          type: 'PURCHASE',
          storeId: data.warehouseId,
          userId: user.id,
          partyName: data.supplierName,
          partyContact: data.supplierContact || null,
          totalAmount,
          paymentMethod: data.paymentMethod,
          status: 'COMPLETED',
          items: {
            create: data.items.map((item: any) => ({
              productId: item.productId,
              quantity: parseFloat(item.quantity),
              unitPrice: parseFloat(item.unitPrice),
              totalPrice: parseFloat(item.totalPrice),
            })),
          },
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          store: true,
        },
      });

      // Update warehouse inventory for each product
      for (const item of data.items) {
        // Find warehouse inventory for this product
        const warehouseInventory = await tx.warehouseInventory.findFirst({
          where: {
            warehouseId: data.warehouseId,
            productId: item.productId,
          },
        });

        if (warehouseInventory) {
          // Update existing inventory
          await tx.warehouseInventory.update({
            where: { id: warehouseInventory.id },
            data: {
              quantity: warehouseInventory.quantity + parseFloat(item.quantity),
            },
          });
        } else {
          // Create new inventory
          await tx.warehouseInventory.create({
            data: {
              warehouseId: data.warehouseId,
              productId: item.productId,
              quantity: parseFloat(item.quantity),
            },
          });
        }
      }

      return transaction;
    });

    // Format the response
    const purchase = {
      id: result.id,
      invoiceNumber: `PUR-${result.id.substring(0, 8)}`,
      date: result.createdAt.toISOString().split('T')[0],
      warehouseId: result.storeId || '',
      warehouseName: result.store?.name || 'Unknown Warehouse',
      supplierName: result.partyName || '',
      supplierContact: result.partyContact || '',
      totalAmount: result.totalAmount,
      paymentMethod: result.paymentMethod,
      status: result.status,
      items: result.items.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
      })),
    };

    return NextResponse.json(purchase, { status: 201 });
  } catch (error) {
    console.error('Error creating purchase:', error);
    return NextResponse.json(
      { error: 'Failed to create purchase' },
      { status: 500 }
    );
  }
}
