import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/raw-materials/[id]/add-stock - Add stock to a raw material
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();
    
    // Validate quantity
    if (!data.quantity || isNaN(parseFloat(data.quantity)) || parseFloat(data.quantity) <= 0) {
      return NextResponse.json(
        { error: 'Quantity must be a positive number' },
        { status: 400 }
      );
    }
    
    // Find the raw material
    const rawMaterial = await prisma.rawMaterial.findUnique({
      where: { id: params.id },
    });
    
    if (!rawMaterial) {
      return NextResponse.json(
        { error: 'Raw material not found' },
        { status: 404 }
      );
    }
    
    // Add stock to the raw material
    const updatedRawMaterial = await prisma.rawMaterial.update({
      where: { id: params.id },
      data: {
        currentStock: rawMaterial.currentStock + parseFloat(data.quantity),
      },
    });
    
    return NextResponse.json(updatedRawMaterial);
  } catch (error) {
    console.error('Error adding stock to raw material:', error);
    return NextResponse.json(
      { error: 'Failed to add stock to raw material' },
      { status: 500 }
    );
  }
}
