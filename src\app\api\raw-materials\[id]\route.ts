import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/raw-materials/[id] - Get a specific raw material
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const rawMaterial = await prisma.rawMaterial.findUnique({
      where: { id: params.id },
    });
    
    if (!rawMaterial) {
      return NextResponse.json(
        { error: 'Raw material not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(rawMaterial);
  } catch (error) {
    console.error('Error fetching raw material:', error);
    return NextResponse.json(
      { error: 'Failed to fetch raw material' },
      { status: 500 }
    );
  }
}

// PUT /api/raw-materials/[id] - Update a raw material
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }
    
    if (!data.unit) {
      return NextResponse.json(
        { error: 'Unit is required' },
        { status: 400 }
      );
    }
    
    if (!data.costPerUnit || isNaN(parseFloat(data.costPerUnit)) || parseFloat(data.costPerUnit) <= 0) {
      return NextResponse.json(
        { error: 'Cost per unit must be a positive number' },
        { status: 400 }
      );
    }
    
    if (!data.currentStock || isNaN(parseFloat(data.currentStock)) || parseFloat(data.currentStock) < 0) {
      return NextResponse.json(
        { error: 'Current stock must be a non-negative number' },
        { status: 400 }
      );
    }
    
    // Update the raw material
    const rawMaterial = await prisma.rawMaterial.update({
      where: { id: params.id },
      data: {
        name: data.name,
        unit: data.unit,
        costPerUnit: parseFloat(data.costPerUnit),
        currentStock: parseFloat(data.currentStock),
        lowStockThreshold: data.lowStockThreshold ? parseInt(data.lowStockThreshold) : 10,
      },
    });
    
    return NextResponse.json(rawMaterial);
  } catch (error) {
    console.error('Error updating raw material:', error);
    return NextResponse.json(
      { error: 'Failed to update raw material' },
      { status: 500 }
    );
  }
}

// DELETE /api/raw-materials/[id] - Delete a raw material
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the raw material exists
    const rawMaterial = await prisma.rawMaterial.findUnique({
      where: { id: params.id },
      include: {
        products: true,
        consumption: true,
      },
    });
    
    if (!rawMaterial) {
      return NextResponse.json(
        { error: 'Raw material not found' },
        { status: 404 }
      );
    }
    
    // Check if the raw material is used in any products or consumption records
    if (rawMaterial.products.length > 0 || rawMaterial.consumption.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete raw material that is in use' },
        { status: 400 }
      );
    }
    
    // Delete the raw material
    await prisma.rawMaterial.delete({
      where: { id: params.id },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting raw material:', error);
    return NextResponse.json(
      { error: 'Failed to delete raw material' },
      { status: 500 }
    );
  }
}
