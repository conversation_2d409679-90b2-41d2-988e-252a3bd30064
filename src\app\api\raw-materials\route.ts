import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/raw-materials - Get all raw materials
export async function GET(request: NextRequest) {
  try {
    const rawMaterials = await prisma.rawMaterial.findMany({
      orderBy: {
        name: 'asc',
      },
    });
    
    return NextResponse.json(rawMaterials);
  } catch (error) {
    console.error('Error fetching raw materials:', error);
    return NextResponse.json(
      { error: 'Failed to fetch raw materials' },
      { status: 500 }
    );
  }
}

// POST /api/raw-materials - Create a new raw material
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }
    
    if (!data.unit) {
      return NextResponse.json(
        { error: 'Unit is required' },
        { status: 400 }
      );
    }
    
    if (!data.costPerUnit || isNaN(parseFloat(data.costPerUnit)) || parseFloat(data.costPerUnit) <= 0) {
      return NextResponse.json(
        { error: 'Cost per unit must be a positive number' },
        { status: 400 }
      );
    }
    
    if (!data.currentStock || isNaN(parseFloat(data.currentStock)) || parseFloat(data.currentStock) < 0) {
      return NextResponse.json(
        { error: 'Current stock must be a non-negative number' },
        { status: 400 }
      );
    }
    
    // Create the raw material
    const rawMaterial = await prisma.rawMaterial.create({
      data: {
        name: data.name,
        unit: data.unit,
        costPerUnit: parseFloat(data.costPerUnit),
        currentStock: parseFloat(data.currentStock),
        lowStockThreshold: data.lowStockThreshold ? parseInt(data.lowStockThreshold) : 10,
      },
    });
    
    return NextResponse.json(rawMaterial, { status: 201 });
  } catch (error) {
    console.error('Error creating raw material:', error);
    return NextResponse.json(
      { error: 'Failed to create raw material' },
      { status: 500 }
    );
  }
}
