import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/recipes/[id] - Get a specific recipe
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const recipe = await prisma.recipe.findUnique({
      where: { id: params.id },
      include: {
        tags: true,
        ingredients: true,
        instructions: {
          orderBy: {
            stepNumber: 'asc',
          },
        },
        tips: true,
        nutritionInfo: true,
      },
    });

    if (!recipe) {
      return NextResponse.json(
        { error: 'Recipe not found' },
        { status: 404 }
      );
    }

    // Transform the data to match the expected format in the frontend
    const formattedRecipe = {
      id: recipe.id,
      name: recipe.name,
      description: recipe.description,
      preparationTime: recipe.preparationTime,
      bakingTime: recipe.bakingTime,
      restingTime: recipe.restingTime,
      totalTime: recipe.totalTime,
      yield: recipe.yield,
      yieldUnit: recipe.yieldUnit,
      difficulty: recipe.difficulty,
      category: recipe.category,
      tags: recipe.tags.map(tag => tag.name),
      ingredients: recipe.ingredients.map(ingredient => ({
        id: ingredient.id,
        name: ingredient.name,
        quantity: ingredient.quantity,
        unit: ingredient.unit,
        cost: ingredient.cost,
        category: ingredient.category,
        isOptional: ingredient.isOptional,
        substitutes: ingredient.substitutes,
      })),
      instructions: recipe.instructions.map(instruction => instruction.text),
      notes: recipe.notes,
      tips: recipe.tips.map(tip => tip.text),
      nutritionInfo: recipe.nutritionInfo ? {
        calories: recipe.nutritionInfo.calories,
        protein: recipe.nutritionInfo.protein,
        carbs: recipe.nutritionInfo.carbs,
        fat: recipe.nutritionInfo.fat,
        fiber: recipe.nutritionInfo.fiber,
        sugar: recipe.nutritionInfo.sugar,
      } : undefined,
      costPerUnit: recipe.costPerUnit,
      sellingPrice: recipe.sellingPrice,
      profitMargin: recipe.profitMargin,
      imageUrl: recipe.imageUrl,
      isActive: recipe.isActive,
      createdAt: recipe.createdAt.toISOString(),
      updatedAt: recipe.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedRecipe);
  } catch (error) {
    console.error('Error fetching recipe:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recipe' },
      { status: 500 }
    );
  }
}

// PUT /api/recipes/[id] - Update a recipe
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Check if the recipe exists
    const existingRecipe = await prisma.recipe.findUnique({
      where: { id: params.id },
    });

    if (!existingRecipe) {
      return NextResponse.json(
        { error: 'Recipe not found' },
        { status: 404 }
      );
    }

    // Update the recipe and related data in a transaction
    await prisma.$transaction(async (tx) => {
      // Update the recipe
      await tx.recipe.update({
        where: { id: params.id },
        data: {
          name: data.name,
          description: data.description,
          preparationTime: data.preparationTime || 0,
          bakingTime: data.bakingTime || 0,
          restingTime: data.restingTime,
          totalTime: data.totalTime,
          yield: data.yield || 1,
          yieldUnit: data.yieldUnit || 'piece',
          difficulty: data.difficulty || 'Medium',
          category: data.category || 'Other',
          notes: data.notes,
          imageUrl: data.imageUrl,
          costPerUnit: data.costPerUnit,
          sellingPrice: data.sellingPrice,
          profitMargin: data.profitMargin,
          isActive: data.isActive !== undefined ? data.isActive : true,
        },
      });

      // Delete existing tags and create new ones
      await tx.recipeTag.deleteMany({
        where: { recipeId: params.id },
      });

      if (data.tags && Array.isArray(data.tags)) {
        for (const tag of data.tags) {
          await tx.recipeTag.create({
            data: {
              recipeId: params.id,
              name: tag,
            },
          });
        }
      }

      // Delete existing ingredients and create new ones
      await tx.recipeIngredient.deleteMany({
        where: { recipeId: params.id },
      });

      if (data.ingredients && Array.isArray(data.ingredients)) {
        for (const ingredient of data.ingredients) {
          await tx.recipeIngredient.create({
            data: {
              recipeId: params.id,
              name: ingredient.name,
              quantity: ingredient.quantity,
              unit: ingredient.unit,
              cost: ingredient.cost,
              category: ingredient.category,
              isOptional: ingredient.isOptional || false,
              substitutes: ingredient.substitutes,
            },
          });
        }
      }

      // Delete existing instructions and create new ones
      await tx.recipeInstruction.deleteMany({
        where: { recipeId: params.id },
      });

      if (data.instructions && Array.isArray(data.instructions)) {
        for (let i = 0; i < data.instructions.length; i++) {
          await tx.recipeInstruction.create({
            data: {
              recipeId: params.id,
              stepNumber: i + 1,
              text: data.instructions[i],
            },
          });
        }
      }

      // Delete existing tips and create new ones
      await tx.recipeTip.deleteMany({
        where: { recipeId: params.id },
      });

      if (data.tips && Array.isArray(data.tips)) {
        for (const tip of data.tips) {
          await tx.recipeTip.create({
            data: {
              recipeId: params.id,
              text: tip,
            },
          });
        }
      }

      // Update or create nutrition info
      await tx.recipeNutrition.deleteMany({
        where: { recipeId: params.id },
      });

      if (data.nutritionInfo) {
        await tx.recipeNutrition.create({
          data: {
            recipeId: params.id,
            calories: data.nutritionInfo.calories,
            protein: data.nutritionInfo.protein,
            carbs: data.nutritionInfo.carbs,
            fat: data.nutritionInfo.fat,
            fiber: data.nutritionInfo.fiber,
            sugar: data.nutritionInfo.sugar,
          },
        });
      }
    });

    // Fetch the updated recipe with all related data
    const updatedRecipe = await prisma.recipe.findUnique({
      where: { id: params.id },
      include: {
        tags: true,
        ingredients: true,
        instructions: {
          orderBy: {
            stepNumber: 'asc',
          },
        },
        tips: true,
        nutritionInfo: true,
      },
    });

    if (!updatedRecipe) {
      return NextResponse.json(
        { error: 'Failed to retrieve updated recipe' },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format in the frontend
    const formattedRecipe = {
      id: updatedRecipe.id,
      name: updatedRecipe.name,
      description: updatedRecipe.description,
      preparationTime: updatedRecipe.preparationTime,
      bakingTime: updatedRecipe.bakingTime,
      restingTime: updatedRecipe.restingTime,
      totalTime: updatedRecipe.totalTime,
      yield: updatedRecipe.yield,
      yieldUnit: updatedRecipe.yieldUnit,
      difficulty: updatedRecipe.difficulty,
      category: updatedRecipe.category,
      tags: updatedRecipe.tags.map(tag => tag.name),
      ingredients: updatedRecipe.ingredients.map(ingredient => ({
        id: ingredient.id,
        name: ingredient.name,
        quantity: ingredient.quantity,
        unit: ingredient.unit,
        cost: ingredient.cost,
        category: ingredient.category,
        isOptional: ingredient.isOptional,
        substitutes: ingredient.substitutes,
      })),
      instructions: updatedRecipe.instructions.map(instruction => instruction.text),
      notes: updatedRecipe.notes,
      tips: updatedRecipe.tips.map(tip => tip.text),
      nutritionInfo: updatedRecipe.nutritionInfo ? {
        calories: updatedRecipe.nutritionInfo.calories,
        protein: updatedRecipe.nutritionInfo.protein,
        carbs: updatedRecipe.nutritionInfo.carbs,
        fat: updatedRecipe.nutritionInfo.fat,
        fiber: updatedRecipe.nutritionInfo.fiber,
        sugar: updatedRecipe.nutritionInfo.sugar,
      } : undefined,
      costPerUnit: updatedRecipe.costPerUnit,
      sellingPrice: updatedRecipe.sellingPrice,
      profitMargin: updatedRecipe.profitMargin,
      imageUrl: updatedRecipe.imageUrl,
      isActive: updatedRecipe.isActive,
      createdAt: updatedRecipe.createdAt.toISOString(),
      updatedAt: updatedRecipe.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedRecipe);
  } catch (error) {
    console.error('Error updating recipe:', error);
    return NextResponse.json(
      { error: 'Failed to update recipe' },
      { status: 500 }
    );
  }
}

// DELETE /api/recipes/[id] - Delete a recipe
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the recipe exists
    const existingRecipe = await prisma.recipe.findUnique({
      where: { id: params.id },
    });

    if (!existingRecipe) {
      return NextResponse.json(
        { error: 'Recipe not found' },
        { status: 404 }
      );
    }

    // Delete the recipe (cascade will delete related records)
    await prisma.recipe.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting recipe:', error);
    return NextResponse.json(
      { error: 'Failed to delete recipe' },
      { status: 500 }
    );
  }
}
