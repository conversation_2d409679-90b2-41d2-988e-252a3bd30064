import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/recipes - Get all recipes
export async function GET(request: NextRequest) {
  try {
    const recipes = await prisma.recipe.findMany({
      include: {
        tags: true,
        ingredients: true,
        instructions: {
          orderBy: {
            stepNumber: 'asc',
          },
        },
        tips: true,
        nutritionInfo: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform the data to match the expected format in the frontend
    const formattedRecipes = recipes.map(recipe => ({
      id: recipe.id,
      name: recipe.name,
      description: recipe.description,
      preparationTime: recipe.preparationTime,
      bakingTime: recipe.bakingTime,
      restingTime: recipe.restingTime,
      totalTime: recipe.totalTime,
      yield: recipe.yield,
      yieldUnit: recipe.yieldUnit,
      difficulty: recipe.difficulty,
      category: recipe.category,
      tags: recipe.tags.map(tag => tag.name),
      ingredients: recipe.ingredients.map(ingredient => ({
        id: ingredient.id,
        name: ingredient.name,
        quantity: ingredient.quantity,
        unit: ingredient.unit,
        cost: ingredient.cost,
        category: ingredient.category,
        isOptional: ingredient.isOptional,
        substitutes: ingredient.substitutes,
      })),
      instructions: recipe.instructions.map(instruction => instruction.text),
      notes: recipe.notes,
      tips: recipe.tips.map(tip => tip.text),
      nutritionInfo: recipe.nutritionInfo ? {
        calories: recipe.nutritionInfo.calories,
        protein: recipe.nutritionInfo.protein,
        carbs: recipe.nutritionInfo.carbs,
        fat: recipe.nutritionInfo.fat,
        fiber: recipe.nutritionInfo.fiber,
        sugar: recipe.nutritionInfo.sugar,
      } : undefined,
      costPerUnit: recipe.costPerUnit,
      sellingPrice: recipe.sellingPrice,
      profitMargin: recipe.profitMargin,
      imageUrl: recipe.imageUrl,
      isActive: recipe.isActive,
      createdAt: recipe.createdAt.toISOString(),
      updatedAt: recipe.updatedAt.toISOString(),
    }));

    return NextResponse.json(formattedRecipes);
  } catch (error) {
    console.error('Error fetching recipes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recipes' },
      { status: 500 }
    );
  }
}

// POST /api/recipes - Create a new recipe
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    if (!data.description) {
      return NextResponse.json(
        { error: 'Description is required' },
        { status: 400 }
      );
    }

    // Create the recipe and related data in a transaction
    const recipe = await prisma.$transaction(async (tx) => {
      // Create the recipe
      const newRecipe = await tx.recipe.create({
        data: {
          name: data.name,
          description: data.description,
          preparationTime: data.preparationTime || 0,
          bakingTime: data.bakingTime || 0,
          restingTime: data.restingTime,
          totalTime: data.totalTime,
          yield: data.yield || 1,
          yieldUnit: data.yieldUnit || 'piece',
          difficulty: data.difficulty || 'Medium',
          category: data.category || 'Other',
          notes: data.notes,
          imageUrl: data.imageUrl,
          costPerUnit: data.costPerUnit,
          sellingPrice: data.sellingPrice,
          profitMargin: data.profitMargin,
          isActive: data.isActive !== undefined ? data.isActive : true,
        },
      });

      // Create tags
      if (data.tags && Array.isArray(data.tags)) {
        for (const tag of data.tags) {
          await tx.recipeTag.create({
            data: {
              recipeId: newRecipe.id,
              name: tag,
            },
          });
        }
      }

      // Create ingredients
      if (data.ingredients && Array.isArray(data.ingredients)) {
        for (const ingredient of data.ingredients) {
          await tx.recipeIngredient.create({
            data: {
              recipeId: newRecipe.id,
              name: ingredient.name,
              quantity: ingredient.quantity,
              unit: ingredient.unit,
              cost: ingredient.cost,
              category: ingredient.category,
              isOptional: ingredient.isOptional || false,
              substitutes: ingredient.substitutes,
            },
          });
        }
      }

      // Create instructions
      if (data.instructions && Array.isArray(data.instructions)) {
        for (let i = 0; i < data.instructions.length; i++) {
          await tx.recipeInstruction.create({
            data: {
              recipeId: newRecipe.id,
              stepNumber: i + 1,
              text: data.instructions[i],
            },
          });
        }
      }

      // Create tips
      if (data.tips && Array.isArray(data.tips)) {
        for (const tip of data.tips) {
          await tx.recipeTip.create({
            data: {
              recipeId: newRecipe.id,
              text: tip,
            },
          });
        }
      }

      // Create nutrition info
      if (data.nutritionInfo) {
        await tx.recipeNutrition.create({
          data: {
            recipeId: newRecipe.id,
            calories: data.nutritionInfo.calories,
            protein: data.nutritionInfo.protein,
            carbs: data.nutritionInfo.carbs,
            fat: data.nutritionInfo.fat,
            fiber: data.nutritionInfo.fiber,
            sugar: data.nutritionInfo.sugar,
          },
        });
      }

      return newRecipe;
    });

    // Fetch the complete recipe with all related data
    const completeRecipe = await prisma.recipe.findUnique({
      where: { id: recipe.id },
      include: {
        tags: true,
        ingredients: true,
        instructions: {
          orderBy: {
            stepNumber: 'asc',
          },
        },
        tips: true,
        nutritionInfo: true,
      },
    });

    if (!completeRecipe) {
      return NextResponse.json(
        { error: 'Failed to retrieve created recipe' },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format in the frontend
    const formattedRecipe = {
      id: completeRecipe.id,
      name: completeRecipe.name,
      description: completeRecipe.description,
      preparationTime: completeRecipe.preparationTime,
      bakingTime: completeRecipe.bakingTime,
      restingTime: completeRecipe.restingTime,
      totalTime: completeRecipe.totalTime,
      yield: completeRecipe.yield,
      yieldUnit: completeRecipe.yieldUnit,
      difficulty: completeRecipe.difficulty,
      category: completeRecipe.category,
      tags: completeRecipe.tags.map(tag => tag.name),
      ingredients: completeRecipe.ingredients.map(ingredient => ({
        id: ingredient.id,
        name: ingredient.name,
        quantity: ingredient.quantity,
        unit: ingredient.unit,
        cost: ingredient.cost,
        category: ingredient.category,
        isOptional: ingredient.isOptional,
        substitutes: ingredient.substitutes,
      })),
      instructions: completeRecipe.instructions.map(instruction => instruction.text),
      notes: completeRecipe.notes,
      tips: completeRecipe.tips.map(tip => tip.text),
      nutritionInfo: completeRecipe.nutritionInfo ? {
        calories: completeRecipe.nutritionInfo.calories,
        protein: completeRecipe.nutritionInfo.protein,
        carbs: completeRecipe.nutritionInfo.carbs,
        fat: completeRecipe.nutritionInfo.fat,
        fiber: completeRecipe.nutritionInfo.fiber,
        sugar: completeRecipe.nutritionInfo.sugar,
      } : undefined,
      costPerUnit: completeRecipe.costPerUnit,
      sellingPrice: completeRecipe.sellingPrice,
      profitMargin: completeRecipe.profitMargin,
      imageUrl: completeRecipe.imageUrl,
      isActive: completeRecipe.isActive,
      createdAt: completeRecipe.createdAt.toISOString(),
      updatedAt: completeRecipe.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedRecipe, { status: 201 });
  } catch (error) {
    console.error('Error creating recipe:', error);
    return NextResponse.json(
      { error: 'Failed to create recipe' },
      { status: 500 }
    );
  }
}
