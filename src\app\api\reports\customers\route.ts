import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/reports/customers - Get customer insights report data
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || 'month';
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Calculate date range based on timeRange or explicit dates
    let dateFilter: { gte: Date; lte: Date };

    if (startDate && endDate) {
      dateFilter = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else {
      const now = new Date();
      let start = new Date();

      switch (timeRange) {
        case 'week':
          start.setDate(now.getDate() - 7);
          break;
        case 'month':
          start.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          start.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          start.setFullYear(now.getFullYear() - 1);
          break;
        default:
          start.setMonth(now.getMonth() - 6); // Default to 6 months
      }

      dateFilter = {
        gte: start,
        lte: now,
      };
    }

    // Get all sales transactions with customer information
    const transactions = await prisma.transaction.findMany({
      where: {
        type: 'SALE',
        createdAt: dateFilter,
        partyName: {
          not: null,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Group transactions by customer
    const customerMap = new Map();

    transactions.forEach(transaction => {
      const customerName = transaction.partyName || 'Unknown';

      if (!customerMap.has(customerName)) {
        customerMap.set(customerName, {
          name: customerName,
          transactions: [],
          totalSpent: 0,
          firstPurchase: transaction.createdAt,
          lastPurchase: transaction.createdAt,
        });
      }

      const customer = customerMap.get(customerName);
      customer.transactions.push(transaction);
      customer.totalSpent += transaction.totalAmount;

      // Update first and last purchase dates
      if (transaction.createdAt < customer.firstPurchase) {
        customer.firstPurchase = transaction.createdAt;
      }
      if (transaction.createdAt > customer.lastPurchase) {
        customer.lastPurchase = transaction.createdAt;
      }
    });

    // Calculate customer segments based on purchase frequency and amount
    const segments = [
      { name: 'New Customers', value: 0, count: 0, color: '#8884d8' },
      { name: 'Occasional', value: 0, count: 0, color: '#82ca9d' },
      { name: 'Regular', value: 0, count: 0, color: '#ffc658' },
      { name: 'Loyal', value: 0, count: 0, color: '#ff8042' },
    ];

    customerMap.forEach(customer => {
      const daysSinceFirstPurchase = Math.floor(
        (new Date().getTime() - new Date(customer.firstPurchase).getTime()) / (1000 * 60 * 60 * 24)
      );

      const purchaseFrequency = customer.transactions.length / (daysSinceFirstPurchase || 1) * 30; // Purchases per month

      // Segment customers
      if (daysSinceFirstPurchase <= 30) {
        // New customers (first purchase within 30 days)
        segments[0].value += customer.totalSpent;
        segments[0].count += 1;
      } else if (purchaseFrequency < 1) {
        // Occasional customers (less than 1 purchase per month)
        segments[1].value += customer.totalSpent;
        segments[1].count += 1;
      } else if (purchaseFrequency < 4) {
        // Regular customers (1-4 purchases per month)
        segments[2].value += customer.totalSpent;
        segments[2].count += 1;
      } else {
        // Loyal customers (more than 4 purchases per month)
        segments[3].value += customer.totalSpent;
        segments[3].count += 1;
      }
    });

    // Calculate retention data by month
    const retentionByMonth = new Map();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Initialize months
    for (let i = 0; i <= 6; i++) {
      const date = new Date(sixMonthsAgo);
      date.setMonth(date.getMonth() + i);
      const month = date.toISOString().slice(0, 7); // YYYY-MM format

      retentionByMonth.set(month, {
        month: new Date(date).toLocaleString('default', { month: 'short' }),
        newCustomers: 0,
        returningCustomers: 0,
        churnRate: 0,
      });
    }

    // Group customers by first purchase month
    customerMap.forEach(customer => {
      const firstPurchaseMonth = new Date(customer.firstPurchase).toISOString().slice(0, 7);

      // Only consider months in our range
      if (retentionByMonth.has(firstPurchaseMonth)) {
        retentionByMonth.get(firstPurchaseMonth).newCustomers += 1;
      }

      // Count returning customers
      customer.transactions.forEach((transaction: any) => {
        if (transaction.createdAt > customer.firstPurchase) {
          const transactionMonth = new Date(transaction.createdAt).toISOString().slice(0, 7);

          if (retentionByMonth.has(transactionMonth)) {
            retentionByMonth.get(transactionMonth).returningCustomers += 1;
          }
        }
      });
    });

    // Calculate churn rate (simplified)
    let previousActiveCustomers = 0;

    retentionByMonth.forEach((data, month) => {
      const activeCustomers = data.newCustomers + data.returningCustomers;

      if (previousActiveCustomers > 0) {
        // Churn rate = (previous active - returning) / previous active
        const churn = Math.max(0, previousActiveCustomers - data.returningCustomers);
        data.churnRate = (churn / previousActiveCustomers) * 100;
      }

      previousActiveCustomers = activeCustomers;
    });

    // Calculate purchase frequency distribution
    const frequencyData = [
      { frequency: 'One-time', customers: 0, revenue: 0 },
      { frequency: '2-3 times', customers: 0, revenue: 0 },
      { frequency: '4-6 times', customers: 0, revenue: 0 },
      { frequency: '7-12 times', customers: 0, revenue: 0 },
      { frequency: '12+ times', customers: 0, revenue: 0 },
    ];

    customerMap.forEach(customer => {
      const purchaseCount = customer.transactions.length;

      if (purchaseCount === 1) {
        frequencyData[0].customers += 1;
        frequencyData[0].revenue += customer.totalSpent;
      } else if (purchaseCount >= 2 && purchaseCount <= 3) {
        frequencyData[1].customers += 1;
        frequencyData[1].revenue += customer.totalSpent;
      } else if (purchaseCount >= 4 && purchaseCount <= 6) {
        frequencyData[2].customers += 1;
        frequencyData[2].revenue += customer.totalSpent;
      } else if (purchaseCount >= 7 && purchaseCount <= 12) {
        frequencyData[3].customers += 1;
        frequencyData[3].revenue += customer.totalSpent;
      } else {
        frequencyData[4].customers += 1;
        frequencyData[4].revenue += customer.totalSpent;
      }
    });

    // Convert retention data to array and sort by month
    const retentionData = Array.from(retentionByMonth.values());

    return NextResponse.json({
      segments,
      retention: retentionData,
      frequency: frequencyData,
      summary: {
        totalCustomers: customerMap.size,
        totalRevenue: transactions.reduce((sum, t) => sum + t.totalAmount, 0),
        averageCustomerValue: customerMap.size > 0
          ? transactions.reduce((sum, t) => sum + t.totalAmount, 0) / customerMap.size
          : 0,
      },
    });
  } catch (error) {
    console.error('Database error, using mock data:', error);

    // Return mock data as fallback
    return NextResponse.json({
      segments: [
        { segment: 'VIP', customers: 25, revenue: 75000, averageValue: 3000 },
        { segment: 'Regular', customers: 85, revenue: 85000, averageValue: 1000 },
        { segment: 'New', customers: 45, revenue: 22500, averageValue: 500 }
      ],
      retention: [
        { month: '2024-01', newCustomers: 15, returningCustomers: 35 },
        { month: '2024-02', newCustomers: 18, returningCustomers: 42 },
        { month: '2024-03', newCustomers: 22, returningCustomers: 48 },
        { month: '2024-04', newCustomers: 20, returningCustomers: 55 }
      ],
      frequency: [
        { frequency: '1 order', customers: 45 },
        { frequency: '2-3 orders', customers: 65 },
        { frequency: '4-5 orders', customers: 30 },
        { frequency: '6+ orders', customers: 15 }
      ],
      summary: {
        totalCustomers: 155,
        totalRevenue: 182500,
        averageCustomerValue: 1177,
      },
    });
  }
}
