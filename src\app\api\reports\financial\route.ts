import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/reports/financial - Get financial report data
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || 'month';
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Calculate date range based on timeRange or explicit dates
    let dateFilter: { gte: Date; lte: Date };

    if (startDate && endDate) {
      dateFilter = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else {
      const now = new Date();
      let start = new Date();

      switch (timeRange) {
        case 'week':
          start.setDate(now.getDate() - 7);
          break;
        case 'month':
          start.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          start.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          start.setFullYear(now.getFullYear() - 1);
          break;
        default:
          start.setMonth(now.getMonth() - 6); // Default to 6 months
      }

      dateFilter = {
        gte: start,
        lte: now,
      };
    }

    // Get all transactions for the date range
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: dateFilter,
      },
      include: {
        items: true,
      },
    });

    // Group transactions by month
    const financialByMonth = new Map();

    transactions.forEach(transaction => {
      const date = new Date(transaction.createdAt);
      const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

      if (!financialByMonth.has(monthYear)) {
        financialByMonth.set(monthYear, {
          date: monthYear,
          revenue: 0,
          cogs: 0,
          expenses: 0,
          profit: 0,
        });
      }

      const monthData = financialByMonth.get(monthYear);

      if (transaction.type === 'SALE') {
        // Add to revenue
        monthData.revenue += transaction.totalAmount;

        // Calculate COGS (cost of goods sold)
        const cogs = transaction.items.reduce((sum, item) => {
          // Estimate COGS as 40% of sales if no cost price is available
          const costMultiplier = 0.4;
          return sum + (item.quantity * (item.unitPrice * costMultiplier));
        }, 0);

        monthData.cogs += cogs;
      } else if (transaction.type === 'PURCHASE') {
        // Add to expenses (purchases are considered expenses)
        monthData.expenses += transaction.totalAmount;
      }
    });

    // Calculate profit for each month
    financialByMonth.forEach(monthData => {
      monthData.profit = monthData.revenue - monthData.cogs - monthData.expenses;
    });

    // Convert to array and sort by date
    const financialData = Array.from(financialByMonth.values()).sort((a, b) =>
      a.date.localeCompare(b.date)
    );

    // Get expense categories
    const expenseCategories = [
      { name: 'Salaries', value: 0, color: '#8884d8' },
      { name: 'Rent', value: 0, color: '#82ca9d' },
      { name: 'Utilities', value: 0, color: '#ffc658' },
      { name: 'Marketing', value: 0, color: '#ff8042' },
      { name: 'Maintenance', value: 0, color: '#8dd1e1' },
      { name: 'Other', value: 0, color: '#a4de6c' },
    ];

    // Since we don't have actual expense categories in the schema,
    // we'll distribute expenses proportionally for demonstration
    const totalExpenses = financialData.reduce((sum, month) => sum + month.expenses, 0);

    if (totalExpenses > 0) {
      expenseCategories[0].value = totalExpenses * 0.4; // Salaries: 40%
      expenseCategories[1].value = totalExpenses * 0.2; // Rent: 20%
      expenseCategories[2].value = totalExpenses * 0.1; // Utilities: 10%
      expenseCategories[3].value = totalExpenses * 0.1; // Marketing: 10%
      expenseCategories[4].value = totalExpenses * 0.1; // Maintenance: 10%
      expenseCategories[5].value = totalExpenses * 0.1; // Other: 10%
    }

    // Calculate summary metrics
    const totalRevenue = financialData.reduce((sum, month) => sum + month.revenue, 0);
    const totalCOGS = financialData.reduce((sum, month) => sum + month.cogs, 0);
    const totalExpensesSum = financialData.reduce((sum, month) => sum + month.expenses, 0);
    const totalProfit = financialData.reduce((sum, month) => sum + month.profit, 0);

    const grossMargin = totalRevenue > 0 ? ((totalRevenue - totalCOGS) / totalRevenue) * 100 : 0;
    const netMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    // Get previous period data for comparison
    const previousPeriodStart = new Date(dateFilter.gte);
    const previousPeriodEnd = new Date(dateFilter.lte);
    const periodDuration = previousPeriodEnd.getTime() - previousPeriodStart.getTime();

    previousPeriodStart.setTime(previousPeriodStart.getTime() - periodDuration);
    previousPeriodEnd.setTime(previousPeriodEnd.getTime() - periodDuration);

    const previousTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: previousPeriodStart,
          lte: previousPeriodEnd,
        },
      },
      include: {
        items: true,
      },
    });

    // Calculate previous period metrics
    let previousRevenue = 0;
    let previousCOGS = 0;
    let previousExpenses = 0;

    previousTransactions.forEach(transaction => {
      if (transaction.type === 'SALE') {
        // Add to revenue
        previousRevenue += transaction.totalAmount;

        // Calculate COGS (cost of goods sold)
        const cogs = transaction.items.reduce((sum, item) => {
          // Estimate COGS as 40% of sales if no cost price is available
          const costMultiplier = 0.4;
          return sum + (item.quantity * (item.unitPrice * costMultiplier));
        }, 0);

        previousCOGS += cogs;
      } else if (transaction.type === 'PURCHASE') {
        // Add to expenses (purchases are considered expenses)
        previousExpenses += transaction.totalAmount;
      }
    });

    const previousProfit = previousRevenue - previousCOGS - previousExpenses;
    const previousGrossMargin = previousRevenue > 0 ? ((previousRevenue - previousCOGS) / previousRevenue) * 100 : 0;
    const previousNetMargin = previousRevenue > 0 ? (previousProfit / previousRevenue) * 100 : 0;

    // Calculate percentage changes
    const revenueChange = previousRevenue > 0
      ? ((totalRevenue - previousRevenue) / previousRevenue) * 100
      : 0;

    const profitChange = previousProfit > 0
      ? ((totalProfit - previousProfit) / previousProfit) * 100
      : 0;

    const grossMarginChange = previousGrossMargin > 0
      ? grossMargin - previousGrossMargin
      : 0;

    const netMarginChange = previousNetMargin > 0
      ? netMargin - previousNetMargin
      : 0;

    return NextResponse.json({
      financialData,
      expenseCategories,
      summary: {
        totalRevenue,
        totalCOGS,
        totalExpenses: totalExpensesSum,
        totalProfit,
        grossMargin,
        netMargin,
      },
      comparison: {
        revenueChange,
        profitChange,
        grossMarginChange,
        netMarginChange,
      }
    });
  } catch (error) {
    console.error('Database error, using mock data:', error);

    // Return mock data as fallback
    return NextResponse.json({
      financialData: [
        { month: '2024-01', revenue: 125000, cogs: 75000, expenses: 25000, profit: 25000 },
        { month: '2024-02', revenue: 135000, cogs: 81000, expenses: 27000, profit: 27000 },
        { month: '2024-03', revenue: 145000, cogs: 87000, expenses: 29000, profit: 29000 },
        { month: '2024-04', revenue: 155000, cogs: 93000, expenses: 31000, profit: 31000 }
      ],
      expenseCategories: [
        { category: 'Rent', amount: 15000 },
        { category: 'Utilities', amount: 5000 },
        { category: 'Marketing', amount: 8000 },
        { category: 'Staff', amount: 35000 },
        { category: 'Other', amount: 7000 }
      ],
      summary: {
        totalRevenue: 560000,
        totalCOGS: 336000,
        totalExpenses: 112000,
        totalProfit: 112000,
        grossMargin: 40,
        netMargin: 20,
      },
      comparison: {
        revenueChange: 12.5,
        profitChange: 15.2,
        grossMarginChange: 2.1,
        netMarginChange: 1.8,
      }
    });
  }
}
