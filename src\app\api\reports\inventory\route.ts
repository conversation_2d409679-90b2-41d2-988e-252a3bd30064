import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/reports/inventory - Get inventory report data
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const reportType = url.searchParams.get('type') || 'all';

    // Get all products with their inventory information
    const products = await prisma.product.findMany({
      include: {
        warehouseInventory: {
          include: {
            warehouse: true,
          },
        },
        storeInventory: {
          include: {
            store: true,
          },
        },
      },
    });

    // Transform the data based on report type
    // Define the type for inventory items
    type InventoryItem = {
      id: string;
      name: string;
      category: string;
      sku: string;
      totalStock: number;
      lowStockThreshold: number;
      unit: string;
      isLowStock: boolean;
      warehouseStock: number;
      storeStock: number;
      locations: Array<{
        type: 'warehouse' | 'store';
        name: string;
        quantity: number;
      }>;
    };

    let reportData: InventoryItem[] = [];

    if (reportType === 'lowStock' || reportType === 'all') {
      // Calculate total stock for each product
      const inventoryItems = products.map(product => {
        const warehouseStock = product.warehouseInventory.reduce(
          (sum, item) => sum + item.quantity,
          0
        );

        const storeStock = product.storeInventory.reduce(
          (sum, item) => sum + item.quantity,
          0
        );

        const totalStock = warehouseStock + storeStock;
        const lowStockThreshold = product.lowStockThreshold || 10;

        return {
          id: product.id,
          name: product.name,
          category: product.category || 'Uncategorized',
          sku: product.sku || `PROD-${product.id.substring(0, 8)}`,
          totalStock,
          lowStockThreshold,
          unit: product.unit || 'piece',
          isLowStock: totalStock <= lowStockThreshold,
          warehouseStock,
          storeStock,
          locations: [
            ...product.warehouseInventory.map(item => ({
              type: 'warehouse' as const,
              name: item.warehouse.name,
              quantity: item.quantity,
            })),
            ...product.storeInventory.map(item => ({
              type: 'store' as const,
              name: item.store.name,
              quantity: item.quantity,
            })),
          ],
        };
      });

      if (reportType === 'lowStock') {
        // Filter to only low stock items
        reportData = inventoryItems.filter(item => item.isLowStock);
      } else {
        reportData = inventoryItems;
      }
    }

    // Calculate inventory value
    const totalInventoryValue = products.reduce((sum, product) => {
      const totalQuantity =
        product.warehouseInventory.reduce((sum, item) => sum + item.quantity, 0) +
        product.storeInventory.reduce((sum, item) => sum + item.quantity, 0);

      return sum + (totalQuantity * (product.costPrice || 0));
    }, 0);

    // Count low stock items
    const lowStockCount = products.filter(product => {
      const totalStock =
        product.warehouseInventory.reduce((sum, item) => sum + item.quantity, 0) +
        product.storeInventory.reduce((sum, item) => sum + item.quantity, 0);

      return totalStock <= (product.lowStockThreshold || 10);
    }).length;

    // Count out of stock items
    const outOfStockCount = products.filter(product => {
      const totalStock =
        product.warehouseInventory.reduce((sum, item) => sum + item.quantity, 0) +
        product.storeInventory.reduce((sum, item) => sum + item.quantity, 0);

      return totalStock === 0;
    }).length;

    // Calculate inventory by category
    const categoryMap = new Map();

    products.forEach(product => {
      const category = product.category || 'Uncategorized';
      const totalQuantity =
        product.warehouseInventory.reduce((sum, item) => sum + item.quantity, 0) +
        product.storeInventory.reduce((sum, item) => sum + item.quantity, 0);

      if (!categoryMap.has(category)) {
        categoryMap.set(category, { name: category, count: 0, value: 0 });
      }

      const categoryData = categoryMap.get(category);
      categoryData.count += 1;
      categoryData.value += totalQuantity * (product.costPrice || 0);
    });

    // Convert to array
    const inventoryByCategory = Array.from(categoryMap.values());

    // Calculate inventory by location
    const locationMap = new Map();

    products.forEach(product => {
      // Add warehouse inventory
      product.warehouseInventory.forEach(item => {
        const locationName = item.warehouse.name;

        if (!locationMap.has(locationName)) {
          locationMap.set(locationName, {
            name: locationName,
            type: 'warehouse',
            count: 0,
            value: 0
          });
        }

        const locationData = locationMap.get(locationName);
        locationData.count += 1;
        locationData.value += item.quantity * (product.costPrice || 0);
      });

      // Add store inventory
      product.storeInventory.forEach(item => {
        const locationName = item.store.name;

        if (!locationMap.has(locationName)) {
          locationMap.set(locationName, {
            name: locationName,
            type: 'store',
            count: 0,
            value: 0
          });
        }

        const locationData = locationMap.get(locationName);
        locationData.count += 1;
        locationData.value += item.quantity * (product.costPrice || 0);
      });
    });

    // Convert to array
    const inventoryByLocation = Array.from(locationMap.values());

    return NextResponse.json({
      reportData,
      summary: {
        totalProducts: products.length,
        totalInventoryValue,
        lowStockCount,
        outOfStockCount,
      },
      inventoryByCategory,
      inventoryByLocation,
    });
  } catch (error) {
    console.error('Database error, using mock data:', error);

    return NextResponse.json(
      { error: 'Failed to fetch inventory report from database' },
      { status: 500 }
    );
  }
}
