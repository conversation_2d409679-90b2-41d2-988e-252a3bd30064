import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/reports/low-stock - Get low stock report data
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const threshold = parseInt(url.searchParams.get('threshold') || '10', 10);
    const storeId = url.searchParams.get('storeId');
    const includeZero = url.searchParams.get('includeZero') === 'true';

    // Build the where clause for store inventory
    const storeInventoryWhere: any = {};

    if (storeId) {
      storeInventoryWhere.storeId = storeId;
    }

    if (includeZero) {
      storeInventoryWhere.quantity = {
        lte: threshold,
      };
    } else {
      storeInventoryWhere.quantity = {
        gt: 0,
        lte: threshold,
      };
    }

    // Get all products with their inventory information
    const products = await prisma.product.findMany({
      include: {
        storeInventory: {
          where: storeInventoryWhere,
          include: {
            store: true,
          },
        },
      },
    });

    // Filter products with low stock
    const lowStockProducts = products.filter(product =>
      product.storeInventory.length > 0
    );

    // Format low stock products for the response
    const formattedLowStockProducts = lowStockProducts.map(product => {
      const inventoryItems = product.storeInventory.map(item => ({
        storeId: item.storeId,
        storeName: item.store.name,
        quantity: item.quantity,
        threshold: product.lowStockThreshold || threshold,
        status: item.quantity === 0 ? 'Out of Stock' : 'Low Stock',
      }));

      return {
        id: product.id,
        name: product.name,
        category: product.category || 'Uncategorized',
        price: product.price || 0,
        unit: product.unit || 'unit',
        minStockLevel: product.lowStockThreshold || threshold,
        inventory: inventoryItems,
      };
    });

    // Group by store
    const storeMap = new Map<string, {
      storeId: string;
      storeName: string;
      lowStockCount: number;
      outOfStockCount: number;
      products: Array<{
        productId: string;
        productName: string;
        quantity: number;
        threshold: number;
        status: string;
      }>;
    }>();

    lowStockProducts.forEach(product => {
      product.storeInventory.forEach(item => {
        const storeId = item.storeId;
        const storeName = item.store.name;
        const status = item.quantity === 0 ? 'Out of Stock' : 'Low Stock';

        if (!storeMap.has(storeId)) {
          storeMap.set(storeId, {
            storeId,
            storeName,
            lowStockCount: 0,
            outOfStockCount: 0,
            products: [],
          });
        }

        const storeData = storeMap.get(storeId);

        if (storeData) {
          if (status === 'Out of Stock') {
            storeData.outOfStockCount += 1;
          } else {
            storeData.lowStockCount += 1;
          }

          storeData.products.push({
            productId: product.id,
            productName: product.name,
            quantity: item.quantity,
            threshold: product.lowStockThreshold || threshold,
            status,
          });
        }
      });
    });

    // Convert to array and sort
    const storeData = Array.from(storeMap.values())
      .sort((a, b) => (b.outOfStockCount + b.lowStockCount) - (a.outOfStockCount + a.lowStockCount));

    // Calculate summary
    const totalLowStockProducts = lowStockProducts.length;
    const totalLowStockItems = lowStockProducts.reduce(
      (sum, product) => sum + product.storeInventory.length,
      0
    );
    const outOfStockCount = lowStockProducts.reduce(
      (sum, product) => sum + product.storeInventory.filter(item => item.quantity === 0).length,
      0
    );
    const lowStockCount = totalLowStockItems - outOfStockCount;

    return NextResponse.json({
      products: formattedLowStockProducts,
      stores: storeData,
      summary: {
        totalLowStockProducts,
        totalLowStockItems,
        outOfStockCount,
        lowStockCount,
      },
    });
  } catch (error) {
    console.error('Error generating low stock report:', error);
    return NextResponse.json(
      { error: 'Failed to generate low stock report' },
      { status: 500 }
    );
  }
}
