import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/reports/purchases - Get purchase report data
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || 'month';
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const storeId = url.searchParams.get('storeId');

    // Calculate date range based on timeRange or explicit dates
    let dateFilter: { gte: Date; lte: Date };

    if (startDate && endDate) {
      dateFilter = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else {
      const now = new Date();
      let start = new Date();

      switch (timeRange) {
        case 'week':
          start.setDate(now.getDate() - 7);
          break;
        case 'month':
          start.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          start.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          start.setFullYear(now.getFullYear() - 1);
          break;
        default:
          start.setMonth(now.getMonth() - 1);
      }

      dateFilter = {
        gte: start,
        lte: now,
      };
    }

    // Build the where clause
    const where: any = {
      type: 'PURCHASE',
      createdAt: dateFilter,
    };

    // Add store filter if provided
    if (storeId) {
      where.storeId = storeId;
    }

    // Get all purchases within the date range
    const purchases = await prisma.transaction.findMany({
      where,
      include: {
        store: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate total purchase amount
    const totalPurchaseAmount = purchases.reduce(
      (sum, purchase) => sum + purchase.totalAmount,
      0
    );

    // Group purchases by supplier
    const purchasesBySupplier = new Map<string, {
      supplierName: string;
      amount: number;
      count: number;
      percentage: number;
    }>();

    purchases.forEach(purchase => {
      const supplierName = purchase.partyName || 'Unknown';

      if (!purchasesBySupplier.has(supplierName)) {
        purchasesBySupplier.set(supplierName, {
          supplierName,
          amount: 0,
          count: 0,
          percentage: 0,
        });
      }

      const supplierData = purchasesBySupplier.get(supplierName);
      supplierData.amount += purchase.totalAmount;
      supplierData.count += 1;
    });

    // Calculate percentages
    purchasesBySupplier.forEach(supplier => {
      supplier.percentage = (supplier.amount / totalPurchaseAmount) * 100;
    });

    // Group purchases by store
    const purchasesByStore = new Map<string, {
      storeId: string;
      storeName: string;
      amount: number;
      count: number;
      percentage: number;
    }>();

    purchases.forEach(purchase => {
      if (!purchase.storeId) return;

      const storeId = purchase.storeId;
      const storeName = purchase.store?.name || 'Unknown';

      if (!purchasesByStore.has(storeId)) {
        purchasesByStore.set(storeId, {
          storeId,
          storeName,
          amount: 0,
          count: 0,
          percentage: 0,
        });
      }

      const storeData = purchasesByStore.get(storeId);
      storeData.amount += purchase.totalAmount;
      storeData.count += 1;
    });

    // Calculate percentages
    purchasesByStore.forEach(store => {
      store.percentage = (store.amount / totalPurchaseAmount) * 100;
    });

    // Group purchases by date
    const purchasesByDate = new Map<string, {
      date: string;
      amount: number;
      count: number;
    }>();

    purchases.forEach(purchase => {
      const date = purchase.createdAt.toISOString().split('T')[0];

      if (!purchasesByDate.has(date)) {
        purchasesByDate.set(date, { date, amount: 0, count: 0 });
      }

      const dateData = purchasesByDate.get(date);
      dateData.amount += purchase.totalAmount;
      dateData.count += 1;
    });

    // Group purchases by product
    const purchasesByProduct = new Map<string, {
      productId: string;
      productName: string;
      quantity: number;
      amount: number;
    }>();

    purchases.forEach(purchase => {
      purchase.items.forEach(item => {
        const productId = item.productId;
        const productName = item.product?.name || 'Unknown';

        if (!purchasesByProduct.has(productId)) {
          purchasesByProduct.set(productId, {
            productId,
            productName,
            quantity: 0,
            amount: 0,
          });
        }

        const productData = purchasesByProduct.get(productId);
        productData.quantity += item.quantity;
        productData.amount += item.totalPrice;
      });
    });

    // Convert to arrays and sort
    const purchasesBySupplierArray = Array.from(purchasesBySupplier.values())
      .sort((a, b) => b.amount - a.amount);

    const purchasesByStoreArray = Array.from(purchasesByStore.values())
      .sort((a, b) => b.amount - a.amount);

    const purchasesByDateArray = Array.from(purchasesByDate.values())
      .sort((a, b) => a.date.localeCompare(b.date));

    const purchasesByProductArray = Array.from(purchasesByProduct.values())
      .sort((a, b) => b.amount - a.amount);

    // Format purchases for the table
    const formattedPurchases = purchases.map(purchase => ({
      id: purchase.id,
      date: purchase.createdAt.toISOString().split('T')[0],
      storeId: purchase.storeId || '',
      storeName: purchase.store?.name || 'N/A',
      supplierName: purchase.partyName || 'Unknown',
      supplierContact: purchase.partyContact || '',
      amount: purchase.totalAmount,
      paymentMethod: purchase.paymentMethod,
      status: purchase.status,
      items: purchase.items.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product?.name || 'Unknown',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
      })),
    }));

    return NextResponse.json({
      purchases: formattedPurchases,
      summary: {
        totalPurchaseAmount,
        totalPurchases: purchases.length,
        uniqueSuppliers: purchasesBySupplier.size,
        uniqueProducts: purchasesByProduct.size,
      },
      purchasesBySupplier: purchasesBySupplierArray,
      purchasesByStore: purchasesByStoreArray,
      purchasesByDate: purchasesByDateArray,
      purchasesByProduct: purchasesByProductArray,
    });
  } catch (error) {
    console.error('Database error, using mock data:', error);

    // Return mock data as fallback
    return NextResponse.json({
      purchases: [
        {
          id: 'pur-1',
          date: '2024-01-01',
          storeId: 'store-1',
          storeName: 'Main Store',
          supplier: 'ABC Suppliers',
          totalAmount: 15000,
          items: 5
        },
        {
          id: 'pur-2',
          date: '2024-01-02',
          storeId: 'store-1',
          storeName: 'Main Store',
          supplier: 'XYZ Distributors',
          totalAmount: 12000,
          items: 3
        }
      ],
      summary: {
        totalPurchaseAmount: 27000,
        totalPurchases: 2,
        uniqueSuppliers: 2,
        uniqueProducts: 8,
      },
      purchasesBySupplier: [
        { supplier: 'ABC Suppliers', amount: 15000, purchases: 1 },
        { supplier: 'XYZ Distributors', amount: 12000, purchases: 1 }
      ],
      purchasesByStore: [
        { storeName: 'Main Store', amount: 27000, purchases: 2 }
      ],
      purchasesByDate: [
        { date: '2024-01-01', amount: 15000, purchases: 1 },
        { date: '2024-01-02', amount: 12000, purchases: 1 }
      ],
      purchasesByProduct: [
        { productName: 'Flour', amount: 8000, quantity: 100 },
        { productName: 'Sugar', amount: 6000, quantity: 80 },
        { productName: 'Eggs', amount: 5000, quantity: 200 }
      ],
    });
  }
}
