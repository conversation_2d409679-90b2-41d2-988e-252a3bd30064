import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/reports/wastage - Get wastage report data
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || 'month';
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Calculate date range based on timeRange or explicit dates
    let dateFilter: { gte: Date; lte: Date };
    
    if (startDate && endDate) {
      dateFilter = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else {
      const now = new Date();
      let start = new Date();
      
      switch (timeRange) {
        case 'week':
          start.setDate(now.getDate() - 7);
          break;
        case 'month':
          start.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          start.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          start.setFullYear(now.getFullYear() - 1);
          break;
        default:
          start.setMonth(now.getMonth() - 1);
      }
      
      dateFilter = {
        gte: start,
        lte: now,
      };
    }

    // Get all wastage records within the date range
    const wastageRecords = await prisma.wastage.findMany({
      where: {
        date: dateFilter,
      },
      include: {
        product: true,
      },
      orderBy: {
        date: 'desc',
      },
    });

    // Calculate total wastage value
    const totalWastageValue = wastageRecords.reduce(
      (sum, record) => sum + (record.product.price || 0) * record.quantity,
      0
    );

    // Calculate total wastage quantity
    const totalWastageQuantity = wastageRecords.reduce(
      (sum, record) => sum + record.quantity,
      0
    );

    // Group wastage by reason
    const wastageByReason = new Map<string, { reason: string; quantity: number; value: number }>();
    
    wastageRecords.forEach(record => {
      const reason = record.reason || 'Unknown';
      const value = (record.product.price || 0) * record.quantity;
      
      if (!wastageByReason.has(reason)) {
        wastageByReason.set(reason, { reason, quantity: 0, value: 0 });
      }
      
      const reasonData = wastageByReason.get(reason);
      reasonData.quantity += record.quantity;
      reasonData.value += value;
    });

    // Group wastage by product
    const wastageByProduct = new Map<string, { 
      productId: string;
      productName: string;
      quantity: number;
      value: number;
    }>();
    
    wastageRecords.forEach(record => {
      const productId = record.productId;
      const productName = record.product.name;
      const value = (record.product.price || 0) * record.quantity;
      
      if (!wastageByProduct.has(productId)) {
        wastageByProduct.set(productId, { 
          productId, 
          productName, 
          quantity: 0, 
          value: 0 
        });
      }
      
      const productData = wastageByProduct.get(productId);
      productData.quantity += record.quantity;
      productData.value += value;
    });

    // Group wastage by date
    const wastageByDate = new Map<string, { 
      date: string;
      quantity: number;
      value: number;
    }>();
    
    wastageRecords.forEach(record => {
      const date = record.date.toISOString().split('T')[0];
      const value = (record.product.price || 0) * record.quantity;
      
      if (!wastageByDate.has(date)) {
        wastageByDate.set(date, { date, quantity: 0, value: 0 });
      }
      
      const dateData = wastageByDate.get(date);
      dateData.quantity += record.quantity;
      dateData.value += value;
    });

    // Convert to arrays and sort
    const wastageByReasonArray = Array.from(wastageByReason.values())
      .sort((a, b) => b.value - a.value);
    
    const wastageByProductArray = Array.from(wastageByProduct.values())
      .sort((a, b) => b.value - a.value);
    
    const wastageByDateArray = Array.from(wastageByDate.values())
      .sort((a, b) => a.date.localeCompare(b.date));

    // Format wastage records for the table
    const formattedWastageRecords = wastageRecords.map(record => ({
      id: record.id,
      date: record.date.toISOString().split('T')[0],
      productId: record.productId,
      productName: record.product.name,
      quantity: record.quantity,
      reason: record.reason || 'Unknown',
      value: (record.product.price || 0) * record.quantity,
      productPrice: record.product.price || 0,
      productUnit: record.product.unit || 'unit',
    }));

    return NextResponse.json({
      wastageRecords: formattedWastageRecords,
      summary: {
        totalWastageValue,
        totalWastageQuantity,
        totalRecords: wastageRecords.length,
      },
      wastageByReason: wastageByReasonArray,
      wastageByProduct: wastageByProductArray,
      wastageByDate: wastageByDateArray,
    });
  } catch (error) {
    console.error('Error generating wastage report:', error);
    return NextResponse.json(
      { error: 'Failed to generate wastage report' },
      { status: 500 }
    );
  }
}
