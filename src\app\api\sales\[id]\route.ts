import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/sales/[id] - Get a specific sale
export async function GET(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: true,
      },
    });

    if (!transaction) {
      return NextResponse.json(
        { error: 'Sale not found' },
        { status: 404 }
      );
    }

    // Transform transaction to sale format
    const sale = {
      id: transaction.id,
      invoiceNumber: `INV-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt.toISOString().split('T')[0],
      storeId: transaction.storeId,
      storeName: transaction.store?.name || 'Unknown Store',
      customerName: transaction.partyName || '',
      customerContact: transaction.partyContact || '',
      totalAmount: transaction.totalAmount,
      discount: transaction.discount || 0,
      paymentMethod: transaction.paymentMethod,
      status: transaction.status,
      items: transaction.items.map((item: { id: string; productId: string; product?: { name: string }; quantity: number; unitPrice: number }) => ({
        id: item.id,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.quantity * item.unitPrice,
      })),
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };

    return NextResponse.json(sale);
  } catch (error) {
    console.error('Error fetching sale:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sale' },
      { status: 500 }
    );
  }
}

// DELETE /api/sales/[id] - Delete a sale
export async function DELETE(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    // Find the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: true,
      },
    });

    if (!transaction) {
      return NextResponse.json(
        { error: 'Sale not found' },
        { status: 404 }
      );
    }

    // Restore inventory if possible
    for (const item of transaction.items) {
      if (item.productId) {
        // Check if there's inventory for this product in the store
        const storeInventory = await prisma.storeInventory.findFirst({
          where: {
            storeId: transaction.storeId || undefined, // Use undefined if null to avoid type error
            productId: item.productId,
          },
        });

        if (storeInventory) {
          // Update existing inventory
          await prisma.storeInventory.update({
            where: { id: storeInventory.id },
            data: {
              quantity: storeInventory.quantity + item.quantity,
            },
          });
        }
      }
    }

    // Delete transaction items
    await prisma.transactionItem.deleteMany({
      where: { transactionId: params.id },
    });

    // Delete the transaction
    await prisma.transaction.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting sale:', error);
    return NextResponse.json(
      { error: 'Failed to delete sale' },
      { status: 500 }
    );
  }
}
