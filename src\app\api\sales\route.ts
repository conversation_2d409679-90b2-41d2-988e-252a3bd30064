import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// Helper function to generate invoice number
function generateInvoiceNumber() {
  const prefix = 'INV';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
}

// GET /api/sales - Get all sales (NeonDB preferred, mock fallback)
export async function GET(request: NextRequest) {
  try {
    console.log('💰 Sales API called - trying NeonDB first');

    // Try NeonDB first
    try {
      const transactions = await prisma.transaction.findMany({
      where: {
        type: 'SALE',
        // Show all sales - both POS and manual sales
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform transactions to sales format
    const sales = transactions.map(transaction => ({
      id: transaction.id,
      invoiceNumber: `INV-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt.toISOString().split('T')[0],
      time: transaction.createdAt.toLocaleTimeString('en-IN', {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Kolkata'
      }),
      storeId: transaction.storeId,
      storeName: transaction.store?.name || 'Unknown Store',
      customerName: transaction.partyName || 'Walk-in Customer',
      customerEmail: transaction.partyContact?.includes('@') ? transaction.partyContact : '',
      customerPhone: transaction.partyContact && !transaction.partyContact.includes('@') ? transaction.partyContact : '',
      totalAmount: transaction.totalAmount,
      subtotal: (transaction.totalAmount + (transaction.discount || 0)),
      discount: transaction.discount || 0,
      tax: 0, // Calculate tax if needed
      paymentMethod: transaction.paymentMethod,
      status: transaction.status,
      source: transaction.notes === 'POS_SALE' ? 'POS' : 'Manual',
      items: transaction.items.map(item => ({
        id: item.id,
        name: item.product?.name || 'Unknown Product',
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        price: item.unitPrice,
        unitPrice: item.unitPrice,
        totalPrice: item.quantity * item.unitPrice,
      })),
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    }));

      return NextResponse.json(sales);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error in sales API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sales' },
      { status: 500 }
    );
  }
}

// POST /api/sales - Create a new sale
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.storeId) {
      return NextResponse.json(
        { error: 'Store is required' },
        { status: 400 }
      );
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: 'At least one item is required' },
        { status: 400 }
      );
    }

    // Calculate total amount
    let subtotal = 0;
    for (const item of data.items) {
      subtotal += parseFloat(item.totalPrice);
    }

    const discount = parseFloat(data.discount) || 0;
    const totalAmount = subtotal - discount;

    // Get the first user from the database to use as the default user
    const defaultUser = await prisma.user.findFirst();

    if (!defaultUser) {
      return NextResponse.json(
        { error: 'No users found in the system' },
        { status: 500 }
      );
    }

    // Create the transaction
    const transaction = await prisma.transaction.create({
      data: {
        type: 'SALE',
        storeId: data.storeId,
        userId: defaultUser.id, // Use the default user
        partyName: data.customerName || null,
        partyContact: data.customerContact || null,
        totalAmount,
        discount,
        paymentMethod: data.paymentMethod,
        status: 'COMPLETED',
        items: {
          create: data.items.map((item: { productId: string; productName: string; quantity: string; unitPrice: string }) => ({
            productId: item.productId,
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            totalPrice: parseFloat(item.quantity) * parseFloat(item.unitPrice),
          })),
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        store: true,
      },
    });

    // Update inventory if product IDs are provided
    for (const item of data.items) {
      if (item.productId) {
        // Check if there's inventory for this product in the store
        const storeInventory = await prisma.storeInventory.findFirst({
          where: {
            storeId: data.storeId,
            productId: item.productId,
          },
        });

        if (storeInventory) {
          // Update existing inventory
          await prisma.storeInventory.update({
            where: { id: storeInventory.id },
            data: {
              quantity: Math.max(0, storeInventory.quantity - parseFloat(item.quantity)),
            },
          });
        }
      }
    }

    // Format the response
    const sale = {
      id: transaction.id,
      invoiceNumber: `INV-${transaction.id.substring(0, 8)}`,
      date: transaction.createdAt.toISOString().split('T')[0],
      storeId: transaction.storeId,
      storeName: transaction.store?.name || 'Unknown Store',
      customerName: transaction.partyName || '',
      customerContact: transaction.partyContact || '',
      totalAmount: transaction.totalAmount,
      discount: transaction.discount || 0,
      paymentMethod: transaction.paymentMethod,
      status: transaction.status,
      items: transaction.items.map((item: { id: string; productId: string; product?: { name: string }; quantity: number; unitPrice: number }) => ({
        id: item.id,
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.quantity * item.unitPrice,
      })),
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };

    return NextResponse.json(sale, { status: 201 });
  } catch (error) {
    console.error('Error creating sale:', error);
    return NextResponse.json(
      { error: 'Failed to create sale' },
      { status: 500 }
    );
  }
}
