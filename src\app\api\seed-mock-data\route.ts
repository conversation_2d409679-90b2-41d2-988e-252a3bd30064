import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { MockDataSeeder } from '@/lib/mock-data-seeder';

// POST /api/seed-mock-data - Seed mock data for testing
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only allow admin users to seed data
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    console.log('🌱 Admin user requesting mock data seeding...');

    // Seed all mock data
    await MockDataSeeder.seedAll();

    return NextResponse.json({
      success: true,
      message: 'Mock data seeded successfully! You can now test cart, checkout, and wishlist features.',
      data: {
        productsAdded: 12,
        categoriesAdded: 6,
        features: [
          'Product catalog with images',
          'Multiple categories',
          'Featured products',
          'Price variations',
          'Stock management',
          'SEO metadata'
        ]
      }
    });
  } catch (error) {
    console.error('Error seeding mock data:', error);
    return NextResponse.json(
      { 
        error: 'Failed to seed mock data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/seed-mock-data - Get seeding status and information
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      available: true,
      description: 'Mock data seeding endpoint for testing e-commerce features',
      mockData: {
        products: 12,
        categories: 6,
        features: [
          'Cakes with high-quality images',
          'Flowers and bouquets',
          'Birthday and anniversary items',
          'Gift boxes and plants',
          'Featured products for homepage',
          'Complete product information'
        ]
      },
      usage: {
        method: 'POST',
        endpoint: '/api/seed-mock-data',
        description: 'Seeds the database with mock products and categories for testing'
      }
    });
  } catch (error) {
    console.error('Error getting seed info:', error);
    return NextResponse.json(
      { error: 'Failed to get seed information' },
      { status: 500 }
    );
  }
}
