import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting database seeding via API...');

    // Simple security check - only allow in development or with a secret key
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('secret');
    
    // Allow seeding in development or with the correct secret
    if (process.env.NODE_ENV === 'production' && secretKey !== 'seed-database-2024') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('✅ Admin user already exists, skipping seed');
      return NextResponse.json({
        message: 'Admin user already exists',
        user: {
          id: existingAdmin.id,
          name: existingAdmin.name,
          email: existingAdmin.email,
          role: existingAdmin.role
        }
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    console.log(`✅ Created admin user: ${adminUser.name} (${adminUser.email})`);

    // Create some basic stores for testing
    const store1 = await prisma.store.create({
      data: {
        name: 'Maa Tarini',
        location: 'Chandrasekharpur',
        managerId: adminUser.id, // Temporarily assign admin as manager
        isActive: true,
      }
    });

    const store2 = await prisma.store.create({
      data: {
        name: 'Downtown Bakery',
        location: 'City Center',
        managerId: adminUser.id, // Temporarily assign admin as manager
        isActive: true,
      }
    });

    // Create a store manager
    const managerPassword = await bcrypt.hash('manager123', 10);
    const managerUser = await prisma.user.create({
      data: {
        name: 'Store Manager',
        email: '<EMAIL>',
        password: managerPassword,
        role: 'STORE_MANAGER',
        storeId: store1.id,
      }
    });

    // Create a staff member
    const staffPassword = await bcrypt.hash('staff123', 10);
    const staffUser = await prisma.user.create({
      data: {
        name: 'Staff Member',
        email: '<EMAIL>',
        password: staffPassword,
        role: 'STAFF',
        storeId: store1.id,
      }
    });

    console.log('✅ Database seeding completed successfully!');

    return NextResponse.json({
      message: 'Database seeded successfully',
      users: [
        {
          id: adminUser.id,
          name: adminUser.name,
          email: adminUser.email,
          role: adminUser.role
        },
        {
          id: managerUser.id,
          name: managerUser.name,
          email: managerUser.email,
          role: managerUser.role
        },
        {
          id: staffUser.id,
          name: staffUser.name,
          email: staffUser.email,
          role: staffUser.role
        }
      ],
      stores: [
        {
          id: store1.id,
          name: store1.name,
          location: store1.location
        },
        {
          id: store2.id,
          name: store2.name,
          location: store2.location
        }
      ],
      credentials: {
        admin: '<EMAIL> / admin123',
        manager: '<EMAIL> / manager123',
        staff: '<EMAIL> / staff123'
      }
    });

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    return NextResponse.json(
      { 
        error: 'Failed to seed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
