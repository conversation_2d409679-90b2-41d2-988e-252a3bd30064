import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/settings/api-integrations/[id] - Get a specific API integration
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔧 Getting API integration ${params.id} from database`);

    const apiIntegration = await prisma.apiIntegration.findUnique({
      where: { id: params.id },
    });

    if (!apiIntegration) {
      return NextResponse.json(
        { error: 'API integration not found' },
        { status: 404 }
      );
    }

    // Mask sensitive credentials before returning
    const maskedIntegration = {
      ...apiIntegration,
      credentials: maskCredentials(apiIntegration.type, apiIntegration.credentials as any),
    };

    console.log(`✅ Returning API integration from database: ${maskedIntegration.name}`);
    return NextResponse.json(maskedIntegration);
  } catch (error) {
    console.error('Error fetching API integration:', error);
    return NextResponse.json(
      { error: 'Failed to fetch API integration' },
      { status: 500 }
    );
  }
}

// PUT /api/settings/api-integrations/[id] - Update an API integration
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔧 Updating API integration ${params.id}`);

    const data = await request.json();

    // Find the existing integration
    const existingIntegration = await prisma.apiIntegration.findUnique({
      where: { id: params.id },
    });

    if (!existingIntegration) {
      return NextResponse.json(
        { error: 'API integration not found' },
        { status: 404 }
      );
    }

    // Merge existing credentials with new ones if provided
    let updatedCredentials = existingIntegration.credentials as any;
    if (data.credentials) {
      updatedCredentials = {
        ...updatedCredentials,
        ...data.credentials,
      };
    }

    // Merge existing settings with new ones if provided
    let updatedSettings = existingIntegration.settings as any;
    if (data.settings) {
      updatedSettings = {
        ...updatedSettings,
        ...data.settings,
      };
    }

    // Update the API integration
    const apiIntegration = await prisma.apiIntegration.update({
      where: { id: params.id },
      data: {
        name: data.name !== undefined ? data.name : existingIntegration.name,
        isActive: data.isActive !== undefined ? data.isActive : existingIntegration.isActive,
        credentials: updatedCredentials,
        settings: updatedSettings,
      },
    });

    // Mask sensitive credentials before returning
    const maskedIntegration = {
      ...apiIntegration,
      credentials: maskCredentials(apiIntegration.type, apiIntegration.credentials as any),
    };

    console.log(`✅ Updated API integration: ${maskedIntegration.name}`);
    return NextResponse.json(maskedIntegration);
  } catch (error) {
    console.error('Error updating API integration:', error);
    return NextResponse.json(
      { error: 'Failed to update API integration' },
      { status: 500 }
    );
  }
}

// DELETE /api/settings/api-integrations/[id] - Delete an API integration
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔧 Deleting API integration ${params.id}`);

    // Check if the integration exists
    const existingIntegration = await prisma.apiIntegration.findUnique({
      where: { id: params.id },
    });

    if (!existingIntegration) {
      return NextResponse.json(
        { error: 'API integration not found' },
        { status: 404 }
      );
    }

    // Delete the API integration
    await prisma.apiIntegration.delete({
      where: { id: params.id },
    });

    console.log(`✅ Deleted API integration: ${params.id}`);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting API integration:', error);
    return NextResponse.json(
      { error: 'Failed to delete API integration' },
      { status: 500 }
    );
  }
}

// Helper function to mask sensitive strings
function maskString(str: string): string {
  if (!str || str.length < 4) return '****';
  return str.substring(0, 2) + '*'.repeat(str.length - 4) + str.substring(str.length - 2);
}

// Helper function to mask credentials based on integration type
function maskCredentials(type: string, credentials: any): any {
  if (!credentials) return {};

  const maskedCredentials = { ...credentials };

  switch (type) {
    case 'PAYMENT_GATEWAY':
      if (maskedCredentials.apiKey) maskedCredentials.apiKey = maskString(maskedCredentials.apiKey);
      if (maskedCredentials.secretKey) maskedCredentials.secretKey = maskString(maskedCredentials.secretKey);
      break;
    case 'WHATSAPP':
      if (maskedCredentials.apiKey) maskedCredentials.apiKey = maskString(maskedCredentials.apiKey);
      if (maskedCredentials.phoneNumberId) maskedCredentials.phoneNumberId = maskString(maskedCredentials.phoneNumberId);
      break;
    case 'FACEBOOK_PIXEL':
    case 'GOOGLE_TAG_MANAGER':
      if (maskedCredentials.trackingId) maskedCredentials.trackingId = maskString(maskedCredentials.trackingId);
      break;
  }

  return maskedCredentials;
}
