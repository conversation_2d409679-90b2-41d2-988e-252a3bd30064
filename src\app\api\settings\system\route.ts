import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/settings/system - Get all system settings
export async function GET(request: NextRequest) {
  try {
    const systemSettings = await prisma.systemSetting.findMany({
      orderBy: [
        { category: 'asc' },
        { key: 'asc' },
      ],
    });
    
    // Group settings by category
    const groupedSettings = systemSettings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = {};
      }
      
      acc[setting.category][setting.key] = setting.value;
      
      return acc;
    }, {} as Record<string, Record<string, string>>);
    
    return NextResponse.json(groupedSettings);
  } catch (error) {
    console.error('Error fetching system settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch system settings' },
      { status: 500 }
    );
  }
}

// POST /api/settings/system - Create or update system settings
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate the data structure
    if (!data || typeof data !== 'object') {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      );
    }
    
    const results = [];
    
    // Process each category
    for (const [category, settings] of Object.entries(data)) {
      if (typeof settings !== 'object') continue;
      
      // Process each setting in the category
      for (const [key, value] of Object.entries(settings as Record<string, string>)) {
        // Check if the setting already exists
        const existingSetting = await prisma.systemSetting.findFirst({
          where: {
            category,
            key,
          },
        });
        
        if (existingSetting) {
          // Update existing setting
          const updatedSetting = await prisma.systemSetting.update({
            where: { id: existingSetting.id },
            data: { value: String(value) },
          });
          
          results.push(updatedSetting);
        } else {
          // Create new setting
          const newSetting = await prisma.systemSetting.create({
            data: {
              category,
              key,
              value: String(value),
            },
          });
          
          results.push(newSetting);
        }
      }
    }
    
    return NextResponse.json(results);
  } catch (error) {
    console.error('Error updating system settings:', error);
    return NextResponse.json(
      { error: 'Failed to update system settings' },
      { status: 500 }
    );
  }
}
