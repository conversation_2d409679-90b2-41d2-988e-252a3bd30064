import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/setup-demo - Create demo customer and sample data
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Setting up demo customer and sample data...');

    // Step 1: Create or find demo customer
    let user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    if (!user) {
      console.log('Creating demo customer user...');
      user = await prisma.user.create({
        data: {
          id: 'customer-user-id',
          name: 'Demo Customer',
          email: '<EMAIL>',
          password: 'customer123',
          role: 'CUSTOMER',
          customer: {
            create: {
              firstName: 'Demo',
              lastName: 'Customer',
              phone: '+91 9876543210',
              isSubscribed: false,
              loyaltyPoints: 100,
            }
          }
        },
        include: { customer: true }
      });
      console.log('✅ Demo customer created');
    } else {
      console.log('✅ Demo customer already exists');
    }

    // Step 2: Ensure we have at least one product
    let product = await prisma.product.findFirst({
      where: { isActive: true }
    });

    if (!product) {
      console.log('Creating demo product...');
      product = await prisma.product.create({
        data: {
          name: 'Chocolate Birthday Cake',
          description: 'Delicious chocolate cake perfect for birthdays',
          price: 599.99,
          costPrice: 300.00,
          sku: 'CHOC-CAKE-001',
          unit: 'piece',
          isActive: true,
          imageUrl: '/images/chocolate-cake.jpg',
        }
      });
      console.log('✅ Demo product created');
    } else {
      console.log('✅ Products already exist');
    }

    // Step 3: Test order creation
    console.log('🔄 Testing order creation...');
    
    const address = await prisma.address.create({
      data: {
        customerId: user.customer!.id,
        street: '123 Test Street',
        city: 'Bhubaneswar',
        state: 'Odisha',
        postalCode: '751001',
        country: 'India',
        isDefault: false,
      }
    });

    const orderNumber = `SETUP-${Date.now()}`;
    const order = await prisma.order.create({
      data: {
        customerId: user.customer!.id,
        addressId: address.id,
        orderNumber,
        totalAmount: product.price,
        subtotal: product.price,
        shipping: 0,
        status: 'PENDING_ASSIGNMENT',
        paymentMethod: 'COD',
        paymentStatus: 'PENDING',
        orderType: 'ONLINE',
      }
    });

    const orderItem = await prisma.orderItem.create({
      data: {
        orderId: order.id,
        productId: product.id,
        quantity: 1,
        unitPrice: product.price,
      }
    });

    console.log('✅ Test order created successfully');

    // Clean up test order
    await prisma.orderItem.delete({ where: { id: orderItem.id } });
    await prisma.order.delete({ where: { id: order.id } });
    await prisma.address.delete({ where: { id: address.id } });
    console.log('✅ Test order cleaned up');

    return NextResponse.json({
      success: true,
      message: 'Demo setup completed successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          customerId: user.customer!.id,
        },
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
        },
        testOrderCreated: true,
      }
    });

  } catch (error) {
    console.error('❌ Demo setup failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined,
    }, { status: 500 });
  }
}

// GET /api/setup-demo - Check demo setup status
export async function GET(request: NextRequest) {
  try {
    // Check if demo customer exists
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    // Check products
    const productCount = await prisma.product.count({
      where: { isActive: true }
    });

    // Check recent orders
    const orderCount = await prisma.order.count();

    return NextResponse.json({
      demoCustomer: user ? {
        exists: true,
        id: user.id,
        email: user.email,
        hasCustomerProfile: !!user.customer,
        customerId: user.customer?.id,
      } : {
        exists: false,
      },
      products: {
        count: productCount,
        hasProducts: productCount > 0,
      },
      orders: {
        count: orderCount,
      },
      setupRequired: !user || productCount === 0,
    });

  } catch (error) {
    console.error('❌ Demo status check failed:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
