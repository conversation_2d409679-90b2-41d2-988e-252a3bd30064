import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/stock-adjustments - Create a stock adjustment
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    if (!data.warehouseId) {
      return NextResponse.json(
        { error: 'Warehouse ID is required' },
        { status: 400 }
      );
    }

    if (!data.quantity || parseFloat(data.quantity) === 0) {
      return NextResponse.json(
        { error: 'Quantity must be a non-zero number' },
        { status: 400 }
      );
    }

    if (!data.type || !['add', 'remove'].includes(data.type)) {
      return NextResponse.json(
        { error: 'Type must be either "add" or "remove"' },
        { status: 400 }
      );
    }

    if (!data.reason) {
      return NextResponse.json(
        { error: 'Reason is required for stock adjustments' },
        { status: 400 }
      );
    }

    const quantity = parseFloat(data.quantity);
    const adjustmentQuantity = data.type === 'add' ? quantity : -quantity;

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: data.productId },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Check if warehouse exists
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: data.warehouseId },
    });

    if (!warehouse) {
      return NextResponse.json(
        { error: 'Warehouse not found' },
        { status: 404 }
      );
    }

    // Find existing inventory
    const existingInventory = await prisma.warehouseInventory.findUnique({
      where: {
        warehouseId_productId: {
          warehouseId: data.warehouseId,
          productId: data.productId,
        },
      },
    });

    let newQuantity: number;

    if (existingInventory) {
      newQuantity = existingInventory.quantity + adjustmentQuantity;
      
      // Prevent negative inventory
      if (newQuantity < 0) {
        return NextResponse.json(
          { error: `Cannot remove ${quantity} units. Only ${existingInventory.quantity} units available in stock.` },
          { status: 400 }
        );
      }

      // Update existing inventory
      await prisma.warehouseInventory.update({
        where: { id: existingInventory.id },
        data: { quantity: newQuantity },
      });
    } else {
      // Create new inventory entry
      if (data.type === 'remove') {
        return NextResponse.json(
          { error: 'Cannot remove stock from a product that has no inventory in this warehouse' },
          { status: 400 }
        );
      }

      newQuantity = quantity;
      await prisma.warehouseInventory.create({
        data: {
          warehouseId: data.warehouseId,
          productId: data.productId,
          quantity: newQuantity,
        },
      });
    }

    // Create a transaction record for tracking
    const transaction = await prisma.transaction.create({
      data: {
        type: data.type === 'add' ? 'PURCHASE' : 'ADJUSTMENT',
        totalAmount: data.type === 'add' ? quantity * product.costPrice : 0,
        discount: 0,
        status: 'COMPLETED',
        paymentMethod: 'CASH',
        partyName: `Stock ${data.type === 'add' ? 'Addition' : 'Removal'} - ${data.reason}`,
        userId: 'system', // You might want to get this from authentication
        items: {
          create: [
            {
              productId: data.productId,
              quantity: adjustmentQuantity,
              unitPrice: product.costPrice,
              totalPrice: adjustmentQuantity * product.costPrice,
            },
          ],
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully ${data.type === 'add' ? 'added' : 'removed'} ${quantity} ${product.unit} ${data.type === 'add' ? 'to' : 'from'} ${product.name} in ${warehouse.name}`,
      adjustment: {
        productId: data.productId,
        productName: product.name,
        warehouseId: data.warehouseId,
        warehouseName: warehouse.name,
        type: data.type,
        quantity: quantity,
        newQuantity: newQuantity,
        reason: data.reason,
        transactionId: transaction.id,
      },
    });
  } catch (error) {
    console.error('Error creating stock adjustment:', error);
    return NextResponse.json(
      { error: 'Failed to create stock adjustment' },
      { status: 500 }
    );
  }
}

// GET /api/stock-adjustments - Get stock adjustment history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const warehouseId = searchParams.get('warehouseId');
    const limit = parseInt(searchParams.get('limit') || '50');

    let whereClause: any = {
      type: {
        in: ['PURCHASE', 'ADJUSTMENT'],
      },
    };

    // Build where clause based on filters
    if (productId || warehouseId) {
      whereClause.items = {
        some: {},
      };

      if (productId) {
        whereClause.items.some.productId = productId;
      }
    }

    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    // Transform the data to match stock adjustment format
    const adjustments = transactions.flatMap(transaction =>
      transaction.items.map(item => ({
        id: `${transaction.id}-${item.id}`,
        transactionId: transaction.id,
        productId: item.productId,
        productName: item.product.name,
        type: item.quantity > 0 ? 'add' : 'remove',
        quantity: Math.abs(item.quantity),
        reason: transaction.partyName,
        createdAt: transaction.createdAt,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
      }))
    );

    return NextResponse.json(adjustments);
  } catch (error) {
    console.error('Error fetching stock adjustments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stock adjustments' },
      { status: 500 }
    );
  }
}
