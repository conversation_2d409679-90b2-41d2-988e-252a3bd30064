import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser, canAccessStore } from '@/lib/auth/api-auth';
import { storeStorage, userStorage } from '@/lib/storage/user-storage';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/stores/[id] - Get a specific store
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('stores.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check store access
    if (!canAccessStore(authUser, params.id)) {
      return NextResponse.json(
        { error: 'Access denied to this store' },
        { status: 403 }
      );
    }

    // Try to get store from database first
    let store;
    let useMockData = false;

    try {
      // Check if DATABASE_URL is available
      if (!process.env.DATABASE_URL) {
        console.log('⚠️ DATABASE_URL not found, using mock storage for get');
        useMockData = true;
      } else {
        await prisma.$queryRaw`SELECT 1`;
        console.log('✅ Database connection successful for store get');

        // Get store from database
        store = await prisma.store.findUnique({
          where: { id: params.id },
          include: {
            users: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              }
            }
          }
        });

        if (store) {
          console.log('✅ Store found in database:', store.name);
        }
      }
    } catch (dbError) {
      console.error('❌ Database get failed, using fallback:', dbError.message);
      useMockData = true;
    }

    if (useMockData || !store) {
      console.log('🔄 Using fallback storage for store get');
      store = storeStorage.getStoreById(params.id);

      if (!store) {
        return NextResponse.json(
          { error: 'Store not found' },
          { status: 404 }
        );
      }

      // Get users assigned to this store
      const allUsers = userStorage.getAllUsers();
      const storeUsers = allUsers.filter(user => user.storeId === params.id);

      const storeWithUsers = {
        ...store,
        users: storeUsers.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
        })),
        inventory: [], // Mock inventory for now
      };

      return NextResponse.json(storeWithUsers);
    }

    // Return database store with users
    const storeWithUsers = {
      ...store,
      inventory: [], // Mock inventory for now
    };

    return NextResponse.json(storeWithUsers);
  } catch (error) {
    console.error('Error fetching store:', error);
    return NextResponse.json(
      { error: 'Failed to fetch store' },
      { status: 500 }
    );
  }
}

// PUT /api/stores/[id] - Update a store
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('stores.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Store name is required' },
        { status: 400 }
      );
    }

    // Try to update store in database first
    let store;
    let useMockData = false;

    try {
      // Check if DATABASE_URL is available
      if (!process.env.DATABASE_URL) {
        console.log('⚠️ DATABASE_URL not found, using mock storage for update');
        useMockData = true;
      } else {
        await prisma.$queryRaw`SELECT 1`;
        console.log('✅ Database connection successful for store update');

        // Update store in database
        store = await prisma.store.update({
          where: { id: params.id },
          data: {
            name: data.name,
            location: data.location || '',
          }
        });
        console.log('✅ Store updated in database:', store.name);
      }
    } catch (dbError) {
      console.error('❌ Database update failed, using fallback:', dbError.message);
      useMockData = true;
    }

    if (useMockData) {
      console.log('🔄 Using fallback storage for store update');
      store = storeStorage.updateStore(params.id, {
        name: data.name,
        location: data.location || '',
      });

      if (!store) {
        return NextResponse.json(
          { error: 'Store not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(store);
  } catch (error) {
    console.error('Error updating store:', error);
    return NextResponse.json(
      { error: 'Failed to update store' },
      { status: 500 }
    );
  }
}

// DELETE /api/stores/[id] - Delete a store
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('stores.delete')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Try to delete store from database first
    let deleted = false;
    let useMockData = false;

    try {
      // Check if DATABASE_URL is available
      if (!process.env.DATABASE_URL) {
        console.log('⚠️ DATABASE_URL not found, using mock storage for delete');
        useMockData = true;
      } else {
        await prisma.$queryRaw`SELECT 1`;
        console.log('✅ Database connection successful for store delete');

        // Check if store has users assigned to it in database
        const storeWithUsers = await prisma.store.findUnique({
          where: { id: params.id },
          include: {
            users: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            }
          }
        });

        if (!storeWithUsers) {
          return NextResponse.json(
            { error: 'Store not found' },
            { status: 404 }
          );
        }

        if (storeWithUsers.users.length > 0) {
          return NextResponse.json(
            {
              error: `Cannot delete store. ${storeWithUsers.users.length} user(s) are assigned to this store. Please reassign or remove users first.`,
              usersCount: storeWithUsers.users.length,
              users: storeWithUsers.users.map(u => ({ name: u.name, email: u.email }))
            },
            { status: 400 }
          );
        }

        // Delete store from database
        await prisma.store.delete({
          where: { id: params.id }
        });
        console.log('✅ Store deleted from database');
        deleted = true;
      }
    } catch (dbError) {
      console.error('❌ Database delete failed, using fallback:', dbError.message);
      useMockData = true;
    }

    if (useMockData) {
      console.log('🔄 Using fallback storage for store delete');

      // Check if store has users assigned to it
      const allUsers = userStorage.getAllUsers();
      const storeUsers = allUsers.filter(user => user.storeId === params.id);

      if (storeUsers.length > 0) {
        return NextResponse.json(
          {
            error: `Cannot delete store. ${storeUsers.length} user(s) are assigned to this store. Please reassign or remove users first.`,
            usersCount: storeUsers.length,
            users: storeUsers.map(u => ({ name: u.name, email: u.email }))
          },
          { status: 400 }
        );
      }

      deleted = storeStorage.deleteStore(params.id);

      if (!deleted) {
        return NextResponse.json(
          { error: 'Store not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting store:', error);
    return NextResponse.json(
      { error: 'Failed to delete store' },
      { status: 500 }
    );
  }
}
