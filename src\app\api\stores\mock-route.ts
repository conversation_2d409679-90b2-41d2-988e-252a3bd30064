
// Mock Stores API for testing
import { NextRequest, NextResponse } from 'next/server';

const mockStores = [
  {
    "id": "store-1",
    "name": "Main Store",
    "location": "Bhubaneswar"
  },
  {
    "id": "store-2",
    "name": "Branch Store",
    "location": "Cuttack"
  },
  {
    "id": "store-3",
    "name": "Express Store",
    "location": "Puri"
  }
];

export async function GET(request: NextRequest) {
  try {
    console.log('🏪 Using mock stores API');
    console.log('✅ Returning', mockStores.length, 'stores');
    return NextResponse.json(mockStores);
  } catch (error) {
    console.error('❌ Mock stores API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch mock stores' },
      { status: 500 }
    );
  }
}
