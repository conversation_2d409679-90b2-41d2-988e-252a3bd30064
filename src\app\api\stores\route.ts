import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { storeStorage } from '@/lib/storage/user-storage';
import { prisma } from '@/lib/db';

// GET /api/stores - Get all stores
export async function GET(request: NextRequest) {
  try {
    console.log('🏪 Stores API called');

    // Try database first, fallback to mock data
    try {
      const stores = await prisma.store.findMany({
        orderBy: {
          createdAt: 'desc',
        },
      });

      console.log(`✅ Returning ${stores.length} stores from database`);
      return NextResponse.json(stores);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error fetching stores:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stores' },
      { status: 500 }
    );
  }
}

// POST /api/stores - Create a new store (Admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('stores.create')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'Store name is required' },
        { status: 400 }
      );
    }

    // Create store in database
    const store = await prisma.store.create({
      data: {
        name: data.name,
        location: data.location || '',
      }
    });
    console.log('✅ Store created in database:', store.name);

    return NextResponse.json(store, { status: 201 });
  } catch (error) {
    console.error('Error creating store:', error);
    return NextResponse.json(
      { error: 'Failed to create store' },
      { status: 500 }
    );
  }
}
