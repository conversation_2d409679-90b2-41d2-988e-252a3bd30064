import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/suppliers/[id] - Get a specific supplier
export async function GET(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    // Find the transaction that represents the supplier
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    // Find all purchase transactions from this supplier
    const purchases = await prisma.transaction.findMany({
      where: {
        type: 'PURCHASE',
        partyName: transaction.partyName,
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Calculate total purchases
    const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0);

    // Get last purchase date
    const lastPurchaseDate = purchases.length > 0 ? purchases[0].createdAt : null;

    // Format purchase orders
    const purchaseOrders = purchases.map(purchase => ({
      id: purchase.id,
      orderNumber: `PO-${purchase.id.substring(0, 8)}`,
      date: purchase.createdAt,
      status: purchase.status.toLowerCase(),
      total: purchase.totalAmount,
      paymentStatus: purchase.status === 'COMPLETED' ? 'paid' : 'unpaid',
      expectedDeliveryDate: purchase.updatedAt, // Using updatedAt as a placeholder
      items: purchase.items.map((item: { id: string; productId: string; product?: { name: string }; quantity: number; unitPrice: number }) => ({
        id: item.id,
        productId: item.productId || '',
        productName: item.product?.name || 'Unknown Product',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
      })),
    }));

    // Format supplier products (unique products from all purchases)
    const productMap = new Map();
    purchases.forEach(purchase => {
      purchase.items.forEach(item => {
        const productName = item.product?.name || 'Unknown Product';
        const productId = item.productId || '';

        if (!productMap.has(productId) && productId) {
          productMap.set(productId, {
            id: item.id,
            name: productName,
            category: item.product?.category || 'General', // Use product category if available
            sku: productId,
            unitPrice: item.unitPrice,
            minOrderQuantity: 1, // Default
            leadTime: 3, // Default
            lastOrderDate: purchase.createdAt,
          });
        }
      });
    });

    const products = Array.from(productMap.values());

    // Format the supplier data
    const supplier = {
      id: transaction.id,
      name: transaction.partyName || '',
      contactName: '',
      email: transaction.partyContact || '',
      phone: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      website: '',
      category: 'General',
      status: 'active',
      paymentTerms: 'Net 30',
      taxId: '',
      notes: '',
      rating: 3,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      totalPurchases,
      lastPurchaseDate,
      purchaseOrders,
      products,
    };

    return NextResponse.json(supplier);
  } catch (error) {
    console.error('Error fetching supplier:', error);
    return NextResponse.json(
      { error: 'Failed to fetch supplier' },
      { status: 500 }
    );
  }
}

// PUT /api/suppliers/[id] - Update a supplier
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Find the transaction that represents the supplier
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    // Update the transaction with new supplier info
    const updatedTransaction = await prisma.transaction.update({
      where: { id: params.id },
      data: {
        partyName: data.name,
        partyContact: data.email,
        // notes field doesn't exist in the schema
      },
    });

    // Update all transactions with the same supplier name
    if (transaction.partyName !== data.name) {
      await prisma.transaction.updateMany({
        where: {
          type: 'PURCHASE',
          partyName: transaction.partyName,
        },
        data: {
          partyName: data.name,
          partyContact: data.email,
        },
      });
    }

    // Find all purchase transactions from this supplier to calculate metrics
    const purchases = await prisma.transaction.findMany({
      where: {
        type: 'PURCHASE',
        partyName: updatedTransaction.partyName,
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Calculate total purchases
    const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0);

    // Get last purchase date
    const lastPurchaseDate = purchases.length > 0 ? purchases[0].createdAt : null;

    // Count unique products purchased from this supplier
    const productSet = new Set();
    purchases.forEach(purchase => {
      purchase.items.forEach(item => {
        if (item.product) {
          productSet.add(item.product.id);
        }
      });
    });

    return NextResponse.json({
      id: updatedTransaction.id,
      name: updatedTransaction.partyName || '',
      contactName: data.contactName || '',
      email: updatedTransaction.partyContact || '',
      phone: data.phone || '',
      address: data.address || '',
      city: data.city || '',
      state: data.state || '',
      postalCode: data.postalCode || '',
      country: data.country || '',
      website: data.website || '',
      category: data.category || 'General',
      status: data.status || 'active',
      paymentTerms: data.paymentTerms || 'Net 30',
      taxId: data.taxId || '',
      notes: data.notes || '',
      rating: data.rating || 3,
      totalPurchases,
      totalProducts: productSet.size,
      lastPurchaseDate,
      createdAt: updatedTransaction.createdAt,
      updatedAt: updatedTransaction.updatedAt,
    });
  } catch (error) {
    console.error('Error updating supplier:', error);
    return NextResponse.json(
      { error: 'Failed to update supplier' },
      { status: 500 }
    );
  }
}

// DELETE /api/suppliers/[id] - Delete a supplier
export async function DELETE(
  _request: NextRequest, // Prefix with underscore to indicate it's not used
  { params }: { params: { id: string } }
) {
  try {
    // Find the transaction that represents the supplier
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
    });

    if (!transaction || transaction.type !== 'PURCHASE') {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    // We don't actually delete the supplier or transactions,
    // as that would affect financial records.
    // Instead, we mark the supplier as inactive by updating the transaction
    await prisma.transaction.update({
      where: { id: params.id },
      data: {
        status: 'CANCELLED',
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting supplier:', error);
    return NextResponse.json(
      { error: 'Failed to delete supplier' },
      { status: 500 }
    );
  }
}
