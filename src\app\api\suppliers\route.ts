import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/suppliers - Get all suppliers
export async function GET(request: NextRequest) {
  try {
    console.log('🏢 Suppliers API called');

    // Try database first, fallback to mock data
    try {
      // Since we don't have a dedicated Supplier model, we'll extract supplier info from transactions
      const transactions = await prisma.transaction.findMany({
        where: {
          type: 'PURCHASE',
        },
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          store: true,
          user: true,
        },
      });

      // Extract unique suppliers from transactions
      const supplierMap = new Map();

      transactions.forEach(transaction => {
        if (transaction.partyName && !supplierMap.has(transaction.partyName)) {
          // Calculate total purchases for this supplier
          const supplierTransactions = transactions.filter(t => t.partyName === transaction.partyName);
          const totalPurchases = supplierTransactions.reduce((sum, t) => sum + t.totalAmount, 0);

          // Get last purchase date
          const lastPurchaseDate = supplierTransactions
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0].createdAt;

          // Count unique products purchased from this supplier
          const productSet = new Set();
          supplierTransactions.forEach(t => {
            t.items.forEach(item => {
              if (item.product) {
                productSet.add(item.product.id);
              }
            });
          });

          // Get the most recent transaction for this supplier
          const mostRecentTransaction = supplierTransactions
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];

          supplierMap.set(transaction.partyName, {
            id: mostRecentTransaction.id,
            name: transaction.partyName,
            contactName: '',
            email: transaction.partyContact || '',
            phone: '',
            address: '',
            city: '',
            state: '',
            postalCode: '',
            country: '',
            website: '',
            category: 'General',
            status: 'active',
            paymentTerms: 'Net 30',
            taxId: '',
            notes: '',
            rating: 3,
            totalPurchases,
            totalProducts: productSet.size,
            lastPurchaseDate,
            createdAt: transaction.createdAt,
            updatedAt: transaction.updatedAt,
          });
        }
      });

      const suppliers = Array.from(supplierMap.values());

      console.log(`✅ Returning ${suppliers.length} suppliers from database`);
      return NextResponse.json(suppliers);
    } catch (dbError) {
      console.error('❌ Database query failed, using mock data:', dbError.message);

      // Return comprehensive mock data as fallback
      const mockSuppliers = [
        {
          id: 'supplier-1',
          name: 'Fresh Ingredients Co.',
          contactName: 'Rajesh Kumar',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          address: 'Industrial Area, Bhubaneswar',
          city: 'Bhubaneswar',
          state: 'Odisha',
          postalCode: '751001',
          country: 'India',
          website: 'www.freshingredients.com',
          category: 'Food Ingredients',
          status: 'active',
          paymentTerms: 'Net 30',
          taxId: 'GST123456789',
          notes: 'Reliable supplier for fresh ingredients',
          rating: 4,
          totalPurchases: 125000,
          totalProducts: 15,
          lastPurchaseDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
          updatedAt: new Date()
        },
        {
          id: 'supplier-2',
          name: 'Dairy Products Ltd.',
          contactName: 'Priya Sharma',
          email: '<EMAIL>',
          phone: '+91 9876543211',
          address: 'Milk Colony, Cuttack',
          city: 'Cuttack',
          state: 'Odisha',
          postalCode: '753001',
          country: 'India',
          website: 'www.dairyproducts.com',
          category: 'Dairy Products',
          status: 'active',
          paymentTerms: 'Net 15',
          taxId: 'GST987654321',
          notes: 'Quality dairy products supplier',
          rating: 5,
          totalPurchases: 85000,
          totalProducts: 8,
          lastPurchaseDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000),
          updatedAt: new Date()
        },
        {
          id: 'supplier-3',
          name: 'Flower Market Suppliers',
          contactName: 'Amit Patel',
          email: '<EMAIL>',
          phone: '+91 9876543212',
          address: 'Flower Market, Puri',
          city: 'Puri',
          state: 'Odisha',
          postalCode: '752001',
          country: 'India',
          website: '',
          category: 'Flowers',
          status: 'active',
          paymentTerms: 'Cash on Delivery',
          taxId: 'GST456789123',
          notes: 'Fresh flower supplier',
          rating: 4,
          totalPurchases: 45000,
          totalProducts: 12,
          lastPurchaseDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
          updatedAt: new Date()
        },
        {
          id: 'supplier-4',
          name: 'Packaging Solutions Pvt Ltd',
          contactName: 'Sunita Das',
          email: '<EMAIL>',
          phone: '+91 9876543213',
          address: 'Industrial Estate, Bhubaneswar',
          city: 'Bhubaneswar',
          state: 'Odisha',
          postalCode: '751010',
          country: 'India',
          website: 'www.packagingsolutions.com',
          category: 'Packaging',
          status: 'active',
          paymentTerms: 'Net 45',
          taxId: 'GST789123456',
          notes: 'Eco-friendly packaging supplier',
          rating: 4,
          totalPurchases: 32000,
          totalProducts: 6,
          lastPurchaseDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
          updatedAt: new Date()
        },
        {
          id: 'supplier-5',
          name: 'Decorative Items Hub',
          contactName: 'Manoj Singh',
          email: '<EMAIL>',
          phone: '+91 9876543214',
          address: 'Market Complex, Cuttack',
          city: 'Cuttack',
          state: 'Odisha',
          postalCode: '753003',
          country: 'India',
          website: 'www.decorativehub.com',
          category: 'Decorations',
          status: 'active',
          paymentTerms: 'Net 30',
          taxId: 'GST321654987',
          notes: 'Party and event decoration supplier',
          rating: 3,
          totalPurchases: 18500,
          totalProducts: 25,
          lastPurchaseDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          updatedAt: new Date()
        }
      ];

      console.log(`✅ Returning ${mockSuppliers.length} mock suppliers`);
      return NextResponse.json(mockSuppliers);
    }
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch suppliers' },
      { status: 500 }
    );
  }
}

// POST /api/suppliers - Create a new supplier (as a purchase transaction)
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Get the first store and user for demo purposes
    const store = await prisma.store.findFirst();
    const user = await prisma.user.findFirst();

    if (!store || !user) {
      return NextResponse.json(
        { error: 'No store or user found' },
        { status: 400 }
      );
    }

    // Check if supplier with this name already exists
    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        type: 'PURCHASE',
        partyName: data.name,
      },
    });

    if (existingTransaction) {
      return NextResponse.json(
        { error: 'Supplier with this name already exists' },
        { status: 400 }
      );
    }

    // Create a transaction to represent the supplier
    const transaction = await prisma.transaction.create({
      data: {
        type: 'PURCHASE',
        storeId: store.id,
        userId: user.id,
        partyName: data.name,
        partyContact: data.email,
        totalAmount: 0, // Initial transaction with no amount
        status: 'COMPLETED',
      },
    });

    // Format the response to match the GET endpoint format
    return NextResponse.json({
      id: transaction.id,
      name: transaction.partyName || '',
      contactName: data.contactName || '',
      email: transaction.partyContact || '',
      phone: data.phone || '',
      address: data.address || '',
      city: data.city || '',
      state: data.state || '',
      postalCode: data.postalCode || '',
      country: data.country || '',
      website: data.website || '',
      category: data.category || 'General',
      status: 'active',
      paymentTerms: data.paymentTerms || 'Net 30',
      taxId: data.taxId || '',
      notes: data.notes || '',
      rating: data.rating || 3,
      totalPurchases: 0,
      totalProducts: 0,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating supplier:', error);
    return NextResponse.json(
      { error: 'Failed to create supplier' },
      { status: 500 }
    );
  }
}
