import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/test-db - Test database connection
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing database connection...');
    console.log('🔍 DATABASE_URL exists:', !!process.env.DATABASE_URL);
    console.log('🔍 DATABASE_URL preview:', process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 50) + '...' : 'undefined');

    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database query successful:', result);

    // Test schema access
    const tableCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = 'bakery'
    `;
    console.log('✅ Schema access successful:', tableCount);

    // Test specific tables
    const tables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'bakery'
      ORDER BY table_name
    `;
    console.log('✅ Tables found:', tables);

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      details: {
        connected: true,
        schema: 'bakery',
        tables: tables,
        tableCount: tableCount
      }
    });
  } catch (error) {
    console.error('❌ Database connection failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        hasUrl: !!process.env.DATABASE_URL,
        urlPreview: process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 50) + '...' : 'undefined'
      }
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
