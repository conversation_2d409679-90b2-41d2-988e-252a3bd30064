import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/test-order - Test order creation
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Testing order creation...');

    // Step 1: Check if demo customer exists
    let user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    if (!user) {
      console.log('Creating demo customer...');
      user = await prisma.user.create({
        data: {
          name: 'Demo Customer',
          email: '<EMAIL>',
          password: 'customer123',
          role: 'CUSTOMER',
          customer: {
            create: {
              firstName: 'Demo',
              lastName: 'Customer',
              phone: '+91 9876543210',
              isSubscribed: false,
              loyaltyPoints: 100,
            }
          }
        },
        include: { customer: true }
      });
    }

    console.log('✅ User found/created:', user.email);

    // Step 2: Check for products
    const products = await prisma.product.findMany({
      take: 1,
      where: { isActive: true }
    });

    if (products.length === 0) {
      return NextResponse.json({
        error: 'No products available for testing'
      }, { status: 400 });
    }

    const testProduct = products[0];
    console.log('✅ Using product:', testProduct.name);

    // Step 3: Create address
    const address = await prisma.address.create({
      data: {
        customerId: user.customer!.id,
        street: '123 Test Street',
        city: 'Bhubaneswar',
        state: 'Odisha',
        postalCode: '751001',
        country: 'India',
        isDefault: false,
      }
    });

    console.log('✅ Address created:', address.id);

    // Step 4: Create order
    const orderNumber = `TEST-${Date.now()}`;
    const order = await prisma.order.create({
      data: {
        customerId: user.customer!.id,
        addressId: address.id,
        orderNumber,
        totalAmount: testProduct.price,
        subtotal: testProduct.price,
        shipping: 0,
        status: 'PENDING_ASSIGNMENT',
        paymentMethod: 'COD',
        paymentStatus: 'PENDING',
        orderType: 'ONLINE',
      }
    });

    console.log('✅ Order created:', order.orderNumber);

    // Step 5: Create order item
    const orderItem = await prisma.orderItem.create({
      data: {
        orderId: order.id,
        productId: testProduct.id,
        quantity: 1,
        unitPrice: testProduct.price,
      }
    });

    console.log('✅ Order item created:', orderItem.id);

    // Step 6: Fetch complete order
    const completeOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        orderItems: {
          include: {
            product: true,
          }
        },
        address: true,
        customer: {
          include: {
            user: true,
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Order created successfully',
      order: completeOrder,
      testData: {
        userId: user.id,
        customerId: user.customer!.id,
        productId: testProduct.id,
        orderNumber: order.orderNumber,
      }
    });

  } catch (error) {
    console.error('❌ Test order creation failed:', error);
    return NextResponse.json({
      error: 'Test order creation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET /api/test-order - Get test information
export async function GET(request: NextRequest) {
  try {
    // Check database connection
    await prisma.$connect();
    
    // Count users, customers, products, orders
    const [userCount, customerCount, productCount, orderCount] = await Promise.all([
      prisma.user.count(),
      prisma.customer.count(),
      prisma.product.count(),
      prisma.order.count(),
    ]);

    // Check if demo customer exists
    const demoCustomer = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    return NextResponse.json({
      database: 'connected',
      counts: {
        users: userCount,
        customers: customerCount,
        products: productCount,
        orders: orderCount,
      },
      demoCustomer: demoCustomer ? {
        id: demoCustomer.id,
        email: demoCustomer.email,
        hasCustomerProfile: !!demoCustomer.customer,
      } : null,
    });

  } catch (error) {
    console.error('❌ Database test failed:', error);
    return NextResponse.json({
      error: 'Database test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
