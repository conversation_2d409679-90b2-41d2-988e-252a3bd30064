import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Simple test API called');
    
    return NextResponse.json({
      status: 'success',
      message: 'API is working correctly',
      timestamp: new Date().toISOString(),
      data: {
        test: 'This is test data',
        number: 42,
        array: [1, 2, 3, 4, 5]
      }
    });
    
  } catch (error) {
    console.error('❌ Simple test API failed:', error);
    return NextResponse.json({
      status: 'error',
      message: 'API test failed',
      error: error.message
    }, { status: 500 });
  }
}
