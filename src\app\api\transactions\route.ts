import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/transactions - Get transactions with optional filtering
export async function GET(request: NextRequest) {
  try {
    console.log('💰 Transactions API called');

    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const type = url.searchParams.get('type');
    const storeId = url.searchParams.get('storeId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Try database first, fallback to mock data
    try {
      // Build filter conditions
      const where: any = {};

      if (type) {
        where.type = type;
      }

      if (storeId) {
        where.storeId = storeId;
      }

      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }

        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // Fetch transactions from database
      const transactions = await prisma.transaction.findMany({
        where,
        include: {
          store: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: offset,
        take: limit,
      });

      // Transform the data to match expected format
      const transformedTransactions = transactions.map(transaction => ({
        id: transaction.id,
        type: transaction.type,
        storeName: transaction.store?.name || 'Unknown Store',
        amount: transaction.totalAmount,
        date: transaction.createdAt.toISOString(),
        status: 'Completed', // Default status
      }));

      console.log(`✅ Returning ${transformedTransactions.length} transactions from database`);
      return NextResponse.json(transformedTransactions);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch transactions' },
      { status: 500 }
    );
  }
}
