import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/transfers/[id] - Get a specific transfer
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const transfer = await prisma.transfer.findUnique({
      where: { id: params.id },
      include: {
        warehouse: true,
        store: true,
        items: true,
      },
    });

    if (!transfer) {
      return NextResponse.json(
        { error: 'Transfer not found' },
        { status: 404 }
      );
    }

    // Format the response
    const formattedTransfer = {
      id: transfer.id,
      transferNumber: `TRF-${transfer.id.substring(0, 8)}`,
      date: transfer.createdAt.toISOString().split('T')[0],
      warehouseId: transfer.warehouseId,
      warehouseName: transfer.warehouse.name,
      storeId: transfer.storeId,
      storeName: transfer.store.name,
      status: transfer.status,
      items: await Promise.all(transfer.items.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { name: true },
        });
        
        return {
          id: item.id,
          productId: item.productId,
          productName: product?.name || 'Unknown Product',
          quantity: item.quantity,
        };
      })),
      createdAt: transfer.createdAt,
      updatedAt: transfer.updatedAt,
    };

    return NextResponse.json(formattedTransfer);
  } catch (error) {
    console.error('Error fetching transfer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch transfer' },
      { status: 500 }
    );
  }
}

// PATCH /api/transfers/[id] - Update a transfer's status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Validate status
    if (!data.status || !['PENDING', 'COMPLETED', 'CANCELLED'].includes(data.status)) {
      return NextResponse.json(
        { error: 'Valid status is required (PENDING, COMPLETED, CANCELLED)' },
        { status: 400 }
      );
    }

    // Find the transfer
    const transfer = await prisma.transfer.findUnique({
      where: { id: params.id },
      include: {
        items: true,
      },
    });

    if (!transfer) {
      return NextResponse.json(
        { error: 'Transfer not found' },
        { status: 404 }
      );
    }

    // Update the transfer status
    const updatedTransfer = await prisma.transfer.update({
      where: { id: params.id },
      data: {
        status: data.status,
      },
      include: {
        warehouse: true,
        store: true,
        items: true,
      },
    });

    // If the transfer is completed, update the inventory
    if (data.status === 'COMPLETED') {
      // Process each item in the transfer
      for (const item of transfer.items) {
        // Decrease warehouse inventory
        const warehouseInventory = await prisma.warehouseInventory.findFirst({
          where: {
            warehouseId: transfer.warehouseId,
            productId: item.productId,
          },
        });

        if (warehouseInventory) {
          await prisma.warehouseInventory.update({
            where: { id: warehouseInventory.id },
            data: {
              quantity: Math.max(0, warehouseInventory.quantity - item.quantity),
            },
          });
        }

        // Increase store inventory
        const storeInventory = await prisma.storeInventory.findFirst({
          where: {
            storeId: transfer.storeId,
            productId: item.productId,
          },
        });

        if (storeInventory) {
          await prisma.storeInventory.update({
            where: { id: storeInventory.id },
            data: {
              quantity: storeInventory.quantity + item.quantity,
            },
          });
        } else {
          await prisma.storeInventory.create({
            data: {
              storeId: transfer.storeId,
              productId: item.productId,
              quantity: item.quantity,
            },
          });
        }
      }
    }

    // Format the response
    const formattedTransfer = {
      id: updatedTransfer.id,
      transferNumber: `TRF-${updatedTransfer.id.substring(0, 8)}`,
      date: updatedTransfer.createdAt.toISOString().split('T')[0],
      warehouseId: updatedTransfer.warehouseId,
      warehouseName: updatedTransfer.warehouse.name,
      storeId: updatedTransfer.storeId,
      storeName: updatedTransfer.store.name,
      status: updatedTransfer.status,
      items: await Promise.all(updatedTransfer.items.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { name: true },
        });
        
        return {
          id: item.id,
          productId: item.productId,
          productName: product?.name || 'Unknown Product',
          quantity: item.quantity,
        };
      })),
      createdAt: updatedTransfer.createdAt,
      updatedAt: updatedTransfer.updatedAt,
    };

    return NextResponse.json(formattedTransfer);
  } catch (error) {
    console.error('Error updating transfer:', error);
    return NextResponse.json(
      { error: 'Failed to update transfer' },
      { status: 500 }
    );
  }
}

// DELETE /api/transfers/[id] - Delete a transfer
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Find the transfer
    const transfer = await prisma.transfer.findUnique({
      where: { id: params.id },
    });

    if (!transfer) {
      return NextResponse.json(
        { error: 'Transfer not found' },
        { status: 404 }
      );
    }

    // Delete the transfer items first (due to foreign key constraints)
    await prisma.transferItem.deleteMany({
      where: { transferId: params.id },
    });

    // Delete the transfer
    await prisma.transfer.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting transfer:', error);
    return NextResponse.json(
      { error: 'Failed to delete transfer' },
      { status: 500 }
    );
  }
}
