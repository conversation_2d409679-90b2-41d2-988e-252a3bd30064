import { NextRequest, NextResponse } from 'next/server';
import { uploadImageToPublic } from '@/lib/imageUpload';

export async function POST(request: NextRequest) {
  try {
    console.log('🖼️ Upload API called');

    const data = await request.json();
    console.log('📊 Upload data received:', {
      folder: data.folder,
      hasImage: !!data.image,
      imageSize: data.image ? data.image.length : 0
    });

    if (!data.image) {
      return NextResponse.json(
        { error: 'No image data provided' },
        { status: 400 }
      );
    }

    // Validate image data format
    if (!data.image.startsWith('data:image/')) {
      return NextResponse.json(
        { error: 'Invalid image format. Must be a valid base64 image.' },
        { status: 400 }
      );
    }

    const folder = data.folder || 'uploads';
    console.log(`🔄 Attempting to upload image to folder: ${folder}`);

    // Try to upload to file system first (works in development)
    let imageUrl;
    let uploadMethod = 'unknown';

    try {
      // Check if we're in a production environment (Vercel)
      const isProduction = process.env.VERCEL || process.env.NODE_ENV === 'production';

      if (isProduction) {
        console.log('🌐 Production environment detected - using base64 storage');
        // In production, return the base64 data directly
        imageUrl = data.image;
        uploadMethod = 'base64';
        console.log('✅ Using base64 data URL for production compatibility');
      } else {
        console.log('🏠 Development environment detected - using file system');
        // In development, try to save to file system
        imageUrl = await uploadImageToPublic(data.image, folder);
        uploadMethod = 'filesystem';
        console.log(`✅ File saved to: ${imageUrl}`);
      }
    } catch (fileSystemError) {
      console.log('⚠️ File system upload failed, falling back to base64:', fileSystemError.message);
      // Fallback to base64 if file system fails
      imageUrl = data.image;
      uploadMethod = 'base64-fallback';
    }

    console.log(`✅ Image upload successful using ${uploadMethod}`);

    return NextResponse.json({
      imageUrl,
      success: true,
      message: 'Image uploaded successfully',
      uploadMethod,
      isBase64: uploadMethod.includes('base64')
    });
  } catch (error) {
    console.error('❌ Error uploading image:', error);

    return NextResponse.json({
      error: 'Failed to upload image',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }, { status: 500 });
  }
}
