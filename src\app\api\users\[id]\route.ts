import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { getUserWithStore, userStorage, storeStorage } from '@/lib/storage/user-storage';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/users/[id] - Get a specific user (Admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('users.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = await params;

    // Try to get user from database first, fallback to mock storage
    let user = null;

    try {
      if (!process.env.DATABASE_URL) {
        console.log('⚠️ DATABASE_URL not found, using mock storage for user retrieval');
        user = getUserWithStore(id);
      } else {
        await prisma.$queryRaw`SELECT 1`;
        console.log('✅ Database connection successful for user retrieval');

        // Get user from database
        const dbUser = await prisma.user.findUnique({
          where: { id },
          include: {
            store: {
              select: {
                id: true,
                name: true,
                location: true,
              }
            }
          }
        });

        if (dbUser) {
          const { password, ...userWithoutPassword } = dbUser;
          user = userWithoutPassword;
        } else {
          // Try mock storage as fallback
          user = getUserWithStore(id);
        }
      }
    } catch (dbError) {
      console.error('Database error, falling back to mock storage:', dbError);
      user = getUserWithStore(id);
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update a user (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('users.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const data = await request.json();

    // Check if email already exists (if changing email)
    if (data.email) {
      try {
        if (!process.env.DATABASE_URL) {
          if (userStorage.emailExists(data.email, params.id)) {
            return NextResponse.json(
              { error: 'Email already in use' },
              { status: 400 }
            );
          }
        } else {
          const existingUser = await prisma.user.findFirst({
            where: {
              email: data.email,
              NOT: { id }
            }
          });

          if (existingUser) {
            return NextResponse.json(
              { error: 'Email already in use' },
              { status: 400 }
            );
          }
        }
      } catch (dbError) {
        console.error('Database error checking email, falling back to mock storage:', dbError);
        if (userStorage.emailExists(data.email, id)) {
          return NextResponse.json(
            { error: 'Email already in use' },
            { status: 400 }
          );
        }
      }
    }

    // Validate email format if provided
    if (data.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        );
      }
    }

    // Validate password strength if provided
    if (data.password && data.password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Validate store ID if provided
    if (data.storeId) {
      try {
        if (!process.env.DATABASE_URL) {
          if (!storeStorage.getStoreById(data.storeId)) {
            return NextResponse.json(
              { error: 'Invalid store ID' },
              { status: 400 }
            );
          }
        } else {
          const existingStore = await prisma.store.findUnique({
            where: { id: data.storeId }
          });

          if (!existingStore) {
            return NextResponse.json(
              { error: 'Invalid store ID' },
              { status: 400 }
            );
          }
        }
      } catch (dbError) {
        console.error('Database error checking store, falling back to mock storage:', dbError);
        if (!storeStorage.getStoreById(data.storeId)) {
          return NextResponse.json(
            { error: 'Invalid store ID' },
            { status: 400 }
          );
        }
      }
    }

    // Update user data
    const updateData: any = {};
    if (data.name) updateData.name = data.name;
    if (data.email) updateData.email = data.email;
    if (data.password) {
      // Hash the password
      updateData.password = await bcrypt.hash(data.password, 10);
    }
    if (data.role) updateData.role = data.role;
    if (data.hasOwnProperty('storeId')) updateData.storeId = data.storeId || null;

    // Try to update in database first, fallback to mock storage
    let user = null;

    try {
      if (!process.env.DATABASE_URL) {
        console.log('⚠️ DATABASE_URL not found, using mock storage for user update');
        user = userStorage.updateUser(id, updateData);
      } else {
        await prisma.$queryRaw`SELECT 1`;
        console.log('✅ Database connection successful for user update');

        // Check if user exists in database
        const existingUser = await prisma.user.findUnique({
          where: { id }
        });

        if (!existingUser) {
          // Try mock storage as fallback
          user = userStorage.updateUser(id, updateData);
        } else {
          // Update in database
          const updatedUser = await prisma.user.update({
            where: { id },
            data: updateData,
            include: {
              store: {
                select: {
                  id: true,
                  name: true,
                  location: true,
                }
              }
            }
          });

          const { password, ...userWithoutPassword } = updatedUser;
          user = userWithoutPassword;
          console.log(`✅ User ${id} updated in database`);
        }
      }
    } catch (dbError) {
      console.error('Database error, falling back to mock storage:', dbError);
      user = userStorage.updateUser(id, updateData);
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get store information if user has a store (for mock storage)
    let userWithStore = user;
    if (!user.store && user.storeId) {
      const store = user.storeId ? storeStorage.getStoreById(user.storeId) : null;
      userWithStore = {
        ...user,
        store: store ? { name: store.name } : null
      };
    }

    return NextResponse.json(userWithStore);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete a user (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    const authUser = await getAuthenticatedUser(request);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!authUser.permissions.includes('users.delete')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = await params;

    // Try to delete from database first, fallback to mock storage
    let deleted = false;

    try {
      if (!process.env.DATABASE_URL) {
        console.log('⚠️ DATABASE_URL not found, using mock storage for user deletion');
        deleted = userStorage.deleteUser(id);
      } else {
        await prisma.$queryRaw`SELECT 1`;
        console.log('✅ Database connection successful for user deletion');

        // Check if user exists in database
        const existingUser = await prisma.user.findUnique({
          where: { id }
        });

        if (!existingUser) {
          // Try mock storage as fallback
          deleted = userStorage.deleteUser(id);
        } else {
          // Delete from database
          await prisma.user.delete({
            where: { id }
          });
          deleted = true;
          console.log(`✅ User ${id} deleted from database`);
        }
      }
    } catch (dbError) {
      console.error('Database error, falling back to mock storage:', dbError);
      deleted = userStorage.deleteUser(id);
    }

    if (!deleted) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
