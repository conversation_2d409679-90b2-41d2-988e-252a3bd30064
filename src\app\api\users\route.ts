import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { getAllUsersWithStores } from '@/lib/storage/user-storage';
import { prisma } from '@/lib/db';

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    console.log('👥 Users API called');

    // Try database first, fallback to mock data
    try {
      const dbUsers = await prisma.user.findMany({
        include: {
          store: {
            select: {
              id: true,
              name: true,
              location: true,
            }
          }
        }
      });

      // Transform to match expected format (without passwords)
      const users = dbUsers.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      console.log(`✅ Retrieved ${users.length} users from database`);
      return NextResponse.json(users);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user (Admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const user = await getAuthenticatedUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!user.permissions.includes('users.create')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const data = await request.json();
    console.log('👤 Creating user with data:', {
      name: data.name,
      email: data.email,
      role: data.role,
      storeId: data.storeId,
      hasPassword: !!data.password
    });

    // Validate required fields
    if (!data.name || !data.email || !data.password) {
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (data.password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Check if email already exists in database
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      );
    }

    // Validate store ID if provided (check database)
    if (data.storeId) {
      const store = await prisma.store.findUnique({
        where: { id: data.storeId },
      });

      if (!store) {
        return NextResponse.json(
          { error: 'Invalid store ID' },
          { status: 400 }
        );
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 10);

    // Create user in database
    const newUser = await prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
        role: data.role || 'STAFF',
        storeId: data.storeId || null,
      },
      include: {
        store: {
          select: {
            id: true,
            name: true,
            location: true,
          }
        }
      }
    });

    console.log('✅ User created in database:', newUser.name);

    // Return user without password
    const { password, ...userWithoutPassword } = newUser;
    return NextResponse.json(userWithoutPassword, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
