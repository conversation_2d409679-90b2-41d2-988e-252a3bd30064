import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/warehouse-inventory - Get all warehouse inventory from database
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Warehouse Inventory API called - fetching from database');

    const warehouseInventory = await prisma.warehouseInventory.findMany({
      include: {
        product: true,
        warehouse: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    // Transform the data to match expected format
    const formattedInventory = warehouseInventory.map(item => ({
      id: item.id,
      productId: item.productId,
      productName: item.product.name,
      category: item.product.category || 'Uncategorized',
      warehouseId: item.warehouseId,
      warehouseName: item.warehouse.name,
      quantity: item.quantity,
      unit: item.product.unit || 'piece',
      costPrice: item.product.costPrice,
      lowStockThreshold: item.product.lowStockThreshold,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    }));

    console.log(`✅ Returning ${formattedInventory.length} warehouse inventory items from database`);
    return NextResponse.json(formattedInventory);
  } catch (error) {
    console.error('❌ Error fetching warehouse inventory from database:', error);
    return NextResponse.json(
      { error: 'Failed to fetch warehouse inventory from database' },
      { status: 500 }
    );
  }
}

// POST /api/warehouse-inventory - Create or update warehouse inventory
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Check if inventory entry already exists
    const existingInventory = await prisma.warehouseInventory.findUnique({
      where: {
        warehouseId_productId: {
          warehouseId: data.warehouseId,
          productId: data.productId,
        },
      },
    });

    let inventory;

    if (existingInventory) {
      // Update existing inventory
      inventory = await prisma.warehouseInventory.update({
        where: {
          warehouseId_productId: {
            warehouseId: data.warehouseId,
            productId: data.productId,
          },
        },
        data: {
          quantity: data.quantity,
        },
        include: {
          warehouse: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } else {
      // Create new inventory entry
      inventory = await prisma.warehouseInventory.create({
        data: {
          warehouseId: data.warehouseId,
          productId: data.productId,
          quantity: data.quantity,
        },
        include: {
          warehouse: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    }

    return NextResponse.json({
      productId: inventory.productId,
      warehouseId: inventory.warehouseId,
      warehouseName: inventory.warehouse.name,
      quantity: inventory.quantity,
    });
  } catch (error) {
    console.error('Error creating/updating warehouse inventory:', error);
    return NextResponse.json(
      { error: 'Failed to create/update warehouse inventory' },
      { status: 500 }
    );
  }
}
