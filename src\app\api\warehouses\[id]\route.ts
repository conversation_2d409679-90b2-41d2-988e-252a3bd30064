import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/warehouses/[id] - Get a specific warehouse (database preferred, mock fallback)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔍 Fetching warehouse: ${params.id}`);

    // Try database first, fallback to mock data
    try {
      const warehouse = await prisma.warehouse.findUnique({
        where: { id: params.id },
        include: {
          inventory: {
            include: {
              product: true,
            },
          },
        },
      });

      if (!warehouse) {
        return NextResponse.json(
          { error: 'Warehouse not found' },
          { status: 404 }
        );
      }

      console.log(`✅ Found warehouse in database: ${warehouse.name}`);
      return NextResponse.json(warehouse);
    } catch (dbError) {
      console.error('❌ Database fetch failed:', dbError);
      throw new Error(`Database fetch failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error fetching warehouse:', error);
    return NextResponse.json(
      { error: 'Failed to fetch warehouse' },
      { status: 500 }
    );
  }
}

// PUT /api/warehouses/[id] - Update a warehouse (database preferred, mock fallback)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`📝 Updating warehouse: ${params.id}`);
    const data = await request.json();

    // Try database first, fallback to mock success
    try {
      const warehouse = await prisma.warehouse.update({
        where: { id: params.id },
        data: {
          name: data.name,
          location: data.location,
        },
      });

      console.log(`✅ Updated warehouse in database: ${warehouse.name}`);
      return NextResponse.json(warehouse);
    } catch (dbError) {
      console.error('❌ Database update failed:', dbError);
      throw new Error(`Database update failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error updating warehouse:', error);
    return NextResponse.json(
      { error: 'Failed to update warehouse' },
      { status: 500 }
    );
  }
}

// DELETE /api/warehouses/[id] - Delete a warehouse (database preferred, mock fallback)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🗑️ Attempting to delete warehouse: ${params.id}`);

    // Try database first, fallback to mock success
    try {
      // Check if warehouse exists
      const warehouse = await prisma.warehouse.findUnique({
        where: { id: params.id },
        include: {
          inventory: true,
        },
      });

      if (!warehouse) {
        return NextResponse.json(
          { error: 'Warehouse not found' },
          { status: 404 }
        );
      }

      // Check if warehouse has inventory
      if (warehouse.inventory && warehouse.inventory.length > 0) {
        return NextResponse.json(
          { error: 'Cannot delete warehouse with existing inventory. Please move or remove all inventory items first.' },
          { status: 400 }
        );
      }

      // Delete the warehouse
      const deletedWarehouse = await prisma.warehouse.delete({
        where: { id: params.id },
      });

      console.log(`✅ Successfully deleted warehouse from database: ${deletedWarehouse.name} (${params.id})`);
      return NextResponse.json({
        success: true,
        message: `Warehouse "${deletedWarehouse.name}" has been permanently deleted from the database.`,
        deletedWarehouse: {
          id: deletedWarehouse.id,
          name: deletedWarehouse.name
        }
      });
    } catch (dbError) {
      console.error('❌ Database deletion failed, using mock success:', dbError);

      // Handle specific database errors
      if (dbError.code === 'P2003') {
        return NextResponse.json(
          { error: 'Cannot delete warehouse because it has related records. Please remove all inventory first.' },
          { status: 400 }
        );
      }

      if (dbError.code === 'P2025') {
        return NextResponse.json(
          { error: 'Warehouse not found' },
          { status: 404 }
        );
      }

      // Throw error instead of mock response
      throw dbError;
    }
  } catch (error) {
    console.error('❌ General error deleting warehouse:', error);
    return NextResponse.json(
      { error: 'Failed to delete warehouse' },
      { status: 500 }
    );
  }
}
