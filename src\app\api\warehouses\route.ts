import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/warehouses - Get all warehouses
export async function GET(request: NextRequest) {
  try {
    console.log('🏭 Warehouses API called');

    // Try database first, fallback to mock data
    try {
      const warehouses = await prisma.warehouse.findMany({
        include: {
          inventory: true,
        },
      });

      console.log(`✅ Found ${warehouses.length} warehouses in database`);
      return NextResponse.json(warehouses);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error fetching warehouses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch warehouses' },
      { status: 500 }
    );
  }
}

// POST /api/warehouses - Create a new warehouse (database preferred, mock fallback)
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('🏗️ Creating warehouse:', data);

    // Try database first, fallback to mock success
    try {
      const warehouse = await prisma.warehouse.create({
        data: {
          name: data.name,
          location: data.location || data.address || '',
        },
      });

      console.log(`✅ Created warehouse in database: ${warehouse.name}`);
      return NextResponse.json(warehouse, { status: 201 });
    } catch (dbError) {
      console.error('❌ Database creation failed:', dbError);
      throw new Error(`Database creation failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error creating warehouse:', error);
    return NextResponse.json(
      { error: 'Failed to create warehouse' },
      { status: 500 }
    );
  }
}
