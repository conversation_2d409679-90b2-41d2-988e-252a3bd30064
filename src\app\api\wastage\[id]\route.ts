import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/wastage/[id] - Get a specific wastage record
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const wastage = await prisma.wastage.findUnique({
      where: { id: params.id },
      include: {
        product: true,
      },
    });

    if (!wastage) {
      return NextResponse.json(
        { error: 'Wastage record not found' },
        { status: 404 }
      );
    }

    // Format the response
    const formattedWastage = {
      id: wastage.id,
      date: wastage.date.toISOString().split('T')[0],
      productId: wastage.productId,
      productName: wastage.product.name,
      quantity: wastage.quantity,
      reason: wastage.reason || '',
      storeId: '',
      storeName: '',
      productPrice: wastage.product.price,
      productUnit: wastage.product.unit,
    };

    return NextResponse.json(formattedWastage);
  } catch (error) {
    console.error('Error fetching wastage record:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wastage record' },
      { status: 500 }
    );
  }
}

// PUT /api/wastage/[id] - Update a wastage record
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();

    // Validate required fields
    if (data.date === undefined) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    if (data.productId === undefined) {
      return NextResponse.json(
        { error: 'Product is required' },
        { status: 400 }
      );
    }

    if (data.quantity === undefined || isNaN(parseFloat(data.quantity)) || parseFloat(data.quantity) <= 0) {
      return NextResponse.json(
        { error: 'Valid quantity is required' },
        { status: 400 }
      );
    }

    // Check if the wastage record exists
    const existingWastage = await prisma.wastage.findUnique({
      where: { id: params.id },
    });

    if (!existingWastage) {
      return NextResponse.json(
        { error: 'Wastage record not found' },
        { status: 404 }
      );
    }

    // Update the wastage record
    const wastage = await prisma.wastage.update({
      where: { id: params.id },
      data: {
        date: new Date(data.date),
        productId: data.productId,
        quantity: parseFloat(data.quantity),
        reason: data.reason || null,
      },
      include: {
        product: true,
      },
    });

    // Format the response
    const formattedWastage = {
      id: wastage.id,
      date: wastage.date.toISOString().split('T')[0],
      productId: wastage.productId,
      productName: wastage.product.name,
      quantity: wastage.quantity,
      reason: wastage.reason || '',
      storeId: data.storeId || '',
      storeName: data.storeName || '',
      productPrice: wastage.product.price,
      productUnit: wastage.product.unit,
    };

    return NextResponse.json(formattedWastage);
  } catch (error) {
    console.error('Error updating wastage record:', error);
    return NextResponse.json(
      { error: 'Failed to update wastage record' },
      { status: 500 }
    );
  }
}

// DELETE /api/wastage/[id] - Delete a wastage record
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the wastage record exists
    const existingWastage = await prisma.wastage.findUnique({
      where: { id: params.id },
    });

    if (!existingWastage) {
      return NextResponse.json(
        { error: 'Wastage record not found' },
        { status: 404 }
      );
    }

    // Delete the wastage record
    await prisma.wastage.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting wastage record:', error);
    return NextResponse.json(
      { error: 'Failed to delete wastage record' },
      { status: 500 }
    );
  }
}
