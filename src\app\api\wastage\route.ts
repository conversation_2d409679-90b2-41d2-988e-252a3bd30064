import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/wastage - Get all wastage records (NeonDB preferred, mock fallback)
export async function GET(request: NextRequest) {
  try {
    console.log('🗑️ Wastage API called - trying NeonDB first');

    // Try NeonDB first
    try {
    // Get query parameters for filtering
    const url = new URL(request.url);
    const storeId = url.searchParams.get('storeId');
    const productId = url.searchParams.get('productId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Build the where clause for filtering
    const where: any = {};

    if (productId) {
      where.productId = productId;
    }

    if (startDate || endDate) {
      where.date = {};

      if (startDate) {
        where.date.gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        where.date.lt = endDateObj;
      }
    }

    const wastageRecords = await prisma.wastage.findMany({
      where,
      include: {
        product: true,
      },
      orderBy: {
        date: 'desc',
      },
    });

    // Format the response
    const formattedWastage = wastageRecords.map(record => ({
      id: record.id,
      date: record.date.toISOString().split('T')[0],
      productId: record.productId,
      productName: record.product.name,
      quantity: record.quantity,
      reason: record.reason || '',
      // Note: storeId is not in the Wastage model, but we'll include it in the response
      // for compatibility with the frontend
      storeId: '',
      storeName: '',
      productPrice: record.product.price,
      productUnit: record.product.unit,
    }));

      return NextResponse.json(formattedWastage);
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('❌ General error in wastage API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wastage records' },
      { status: 500 }
    );
  }
}

// POST /api/wastage - Create a new wastage record
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.date) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    if (!data.productId) {
      return NextResponse.json(
        { error: 'Product is required' },
        { status: 400 }
      );
    }

    if (!data.quantity || isNaN(parseFloat(data.quantity)) || parseFloat(data.quantity) <= 0) {
      return NextResponse.json(
        { error: 'Valid quantity is required' },
        { status: 400 }
      );
    }

    // Create the wastage record
    const wastage = await prisma.wastage.create({
      data: {
        date: new Date(data.date),
        productId: data.productId,
        quantity: parseFloat(data.quantity),
        reason: data.reason || null,
      },
      include: {
        product: true,
      },
    });

    // Update inventory if storeId is provided
    if (data.storeId) {
      try {
        // Find store inventory for this product
        const storeInventory = await prisma.storeInventory.findFirst({
          where: {
            storeId: data.storeId,
            productId: data.productId,
          },
        });

        if (storeInventory) {
          // Update existing inventory
          await prisma.storeInventory.update({
            where: { id: storeInventory.id },
            data: {
              quantity: Math.max(0, storeInventory.quantity - parseFloat(data.quantity)),
            },
          });
        }
      } catch (inventoryError) {
        console.error('Error updating inventory:', inventoryError);
        // We don't want to fail the wastage creation if inventory update fails
      }
    }

    // Format the response
    const formattedWastage = {
      id: wastage.id,
      date: wastage.date.toISOString().split('T')[0],
      productId: wastage.productId,
      productName: wastage.product.name,
      quantity: wastage.quantity,
      reason: wastage.reason || '',
      storeId: data.storeId || '',
      storeName: data.storeName || '',
      productPrice: wastage.product.price,
      productUnit: wastage.product.unit,
    };

    return NextResponse.json(formattedWastage, { status: 201 });
  } catch (error) {
    console.error('Error creating wastage record:', error);
    return NextResponse.json(
      { error: 'Failed to create wastage record' },
      { status: 500 }
    );
  }
}
