'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/auth-context';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login, isLoading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await login(email, password);
      // Redirect will be handled by the auth context
    } catch (err) {
      setError('Invalid email or password');
    }
  };

  return (
    <>
      <style jsx>{`
        .login-container {
          min-height: 100vh;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 3rem 1rem;
        }
        .login-card {
          max-width: 400px;
          width: 100%;
          background: white;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          padding: 2rem;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .login-container {
            background-color: #1a1a1a;
          }
          .login-card {
            background: #2d2d2d;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
          }
          .title {
            color: #ffffff !important;
          }
          .subtitle {
            color: #d1d5db !important;
          }
          .form-label {
            color: #e5e7eb !important;
          }
          .form-input {
            background: #374151 !important;
            border-color: #4b5563 !important;
            color: #ffffff !important;
          }
          .form-input::placeholder {
            color: #9ca3af !important;
          }
          .form-input:focus {
            background: #4b5563 !important;
            border-color: #2563eb !important;
          }
          .footer {
            color: #9ca3af !important;
          }
        }
        .logo-container {
          width: 120px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
        }
        .logo-image {
          width: 120px;
          height: 120px;
          object-fit: contain;
        }
        .title {
          font-size: 1.875rem;
          font-weight: 800;
          color: #111827;
          text-align: center;
          margin-bottom: 0.5rem;
        }
        .subtitle {
          color: #6b7280;
          text-align: center;
          margin-bottom: 2rem;
        }
        .form-group {
          margin-bottom: 1.5rem;
        }
        .form-label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }
        .form-input {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 0.875rem;
          transition: all 0.2s;
          background: white;
        }
        .form-input:focus {
          outline: none;
          border-color: #2563eb;
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .form-input::placeholder {
          color: #9ca3af;
        }
        .forgot-link {
          color: #2563eb;
          text-decoration: none;
          font-size: 0.875rem;
          font-weight: 500;
        }
        .forgot-link:hover {
          color: #1d4ed8;
        }
        .submit-btn {
          width: 100%;
          background: #2563eb;
          color: white;
          border: none;
          padding: 0.75rem 1rem;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
        }
        .submit-btn:hover:not(:disabled) {
          background: #1d4ed8;
        }
        .submit-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        .error-alert {
          background: #fef2f2;
          border: 1px solid #fecaca;
          color: #dc2626;
          padding: 0.75rem;
          border-radius: 6px;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        /* Dark mode error alert */
        @media (prefers-color-scheme: dark) {
          .error-alert {
            background: #7f1d1d !important;
            border-color: #dc2626 !important;
            color: #fecaca !important;
          }
        }
        .demo-section {
          margin-top: 1.5rem;
          padding-top: 1.5rem;
          border-top: 1px solid #e5e7eb;
          text-align: center;
        }
        .demo-title {
          color: #6b7280;
          font-size: 0.875rem;
          margin-bottom: 0.5rem;
        }
        .demo-credentials {
          font-family: monospace;
          color: #2563eb;
          font-size: 0.875rem;
        }
        .footer {
          text-align: center;
          margin-top: 2rem;
          color: #9ca3af;
          font-size: 0.75rem;
        }
        .spinner {
          width: 20px;
          height: 20px;
          border: 2px solid transparent;
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        .flex-between {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;
        }
      `}</style>

      <div className="login-container">
        <div className="login-card">
          <div className="logo-container">
            <img src="/LOGO.png" alt="Mispri Logo" className="logo-image" />
          </div>

          <h1 className="title">Admin Portal</h1>
          <p className="subtitle">Sign in to your admin account</p>

          <form onSubmit={handleSubmit}>
            {error && (
              <div className="error-alert">
                <span>⚠️</span>
                <span>{error}</span>
              </div>
            )}

            <div className="form-group">
              <label htmlFor="email" className="form-label">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="form-input"
                placeholder="Enter your email"
              />
            </div>

            <div className="form-group">
              <div className="flex-between">
                <label htmlFor="password" className="form-label">
                  Password
                </label>
                <Link href="/auth/forgot-password" className="forgot-link">
                  Forgot password?
                </Link>
              </div>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="form-input"
                placeholder="Enter your password"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="submit-btn"
            >
              {isLoading ? (
                <>
                  <div className="spinner"></div>
                  <span>Signing in...</span>
                </>
              ) : (
                'Sign in'
              )}
            </button>

          </form>

          <div className="footer">
            Developed by Wipster Technologies Private Limited All right reserved by Mispri © 2025
          </div>
        </div>
      </div>
    </>
  );
}
