'use client';

import { useState, useEffect } from 'react';
import { CategoryManagement } from '@/components/products/category-management';
import { Loader2 } from 'lucide-react';
import { getAuthHeaders } from '@/lib/auth/api-client';

interface Category {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  slug: string;
  displayOrder: number;
  isActive: boolean;
  productCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories data
  useEffect(() => {
    const fetchCategories = async () => {
      console.log('🔄 Fetching categories...');
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await response.json();

        // Transform the data to match the Category interface
        const formattedCategories = data.map((category: any, index: number) => ({
          id: category.id,
          name: category.name,
          description: category.description || '',
          imageUrl: category.imageUrl || '',
          slug: category.slug || category.name.toLowerCase().replace(/\s+/g, '-'),
          displayOrder: category.displayOrder || (index + 1),
          isActive: category.isActive !== undefined ? category.isActive : true,
          productCount: category.productCount || 0,
          createdAt: category.createdAt || new Date().toISOString().split('T')[0],
          updatedAt: category.updatedAt || new Date().toISOString().split('T')[0],
        }));

        // Product counts are already included in the API response, no need to fetch separately

        setCategories(formattedCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleAddCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>) => {
    try {
      console.log('🏗️ Creating category:', categoryData.name);
      console.log('🔑 Auth headers:', getAuthHeaders());

      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: categoryData.name,
          description: categoryData.description,
          imageUrl: categoryData.imageUrl,
          slug: categoryData.slug,
          displayOrder: categoryData.displayOrder,
          isActive: categoryData.isActive,
        }),
      });

      let responseData;
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error('Failed to parse server response');
      }

      if (!response.ok) {
        throw new Error(responseData?.error || `Failed to create category (${response.status})`);
      }

      // Transform the response to match the Category interface
      const formattedCategory: Category = {
        id: responseData.id,
        name: responseData.name,
        description: categoryData.description || '',
        imageUrl: categoryData.imageUrl || '',
        slug: categoryData.slug,
        displayOrder: categoryData.displayOrder,
        isActive: categoryData.isActive,
        productCount: 0,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0],
      };

      setCategories([...categories, formattedCategory]);
      return formattedCategory;
    } catch (err) {
      console.error('Error creating category:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while creating the category');
      throw err; // Re-throw to allow the calling code to handle the error
    }
  };

  const handleUpdateCategory = async (id: string, categoryData: Partial<Category>) => {
    try {
      console.log('📝 Updating category:', id);
      console.log('🔑 Auth headers:', getAuthHeaders());

      const response = await fetch(`/api/categories/${id}`, {
        method: 'PUT',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: categoryData.name,
          description: categoryData.description,
          imageUrl: categoryData.imageUrl,
          isActive: categoryData.isActive,
        }),
      });

      let responseData;
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error('Failed to parse server response');
      }

      if (!response.ok) {
        throw new Error(responseData?.error || `Failed to update category (${response.status})`);
      }

      // Update the category in the local state
      setCategories(categories.map(category =>
        category.id === id
          ? {
              ...category,
              ...categoryData,
              updatedAt: new Date().toISOString().split('T')[0]
            }
          : category
      ));

      return responseData;
    } catch (err) {
      console.error('Error updating category:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while updating the category');
      throw err; // Re-throw to allow the calling code to handle the error
    }
  };

  const handleDeleteCategory = async (id: string) => {
    try {
      console.log('🗑️ Starting category deletion for ID:', id);

      // Find the category to get its name for better logging
      const categoryToDelete = categories.find(c => c.id === id);
      console.log('📦 Category to delete:', categoryToDelete?.name || 'Unknown category');

      const response = await fetch(`/api/categories/${id}`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json',
        },
      });

      console.log('📊 Delete response status:', response.status);

      let responseData;
      try {
        responseData = await response.json();
        console.log('📄 Delete response data:', responseData);
      } catch (parseError) {
        console.error('❌ Error parsing response:', parseError);
        throw new Error('Failed to parse server response');
      }

      if (!response.ok) {
        console.error('❌ Delete failed with response:', responseData);
        throw new Error(responseData?.error || `Failed to delete category (${response.status})`);
      }

      console.log('✅ Category deleted successfully from database');
      console.log('🔄 Updating local state...');

      // Update the categories list
      const updatedCategories = categories.filter(category => category.id !== id);
      setCategories(updatedCategories);

      console.log('✅ State updated successfully');
      console.log('📊 Categories remaining:', updatedCategories.length);

      // Show success message
      if (responseData?.message) {
        console.log('📝 Server message:', responseData.message);
        alert(`Success: ${responseData.message}`);
      } else {
        alert(`Category "${categoryToDelete?.name || 'Unknown'}" has been deleted successfully.`);
      }

      return responseData;
    } catch (err) {
      console.error('Error deleting category:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the category');
      throw err; // Re-throw to allow the calling code to handle the error
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>

      {loading ? (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: 'var(--primary)' }} />
          <span style={{ marginLeft: '0.5rem' }}>Loading categories...</span>
        </div>
      ) : error ? (
        <div style={{
          borderRadius: '6px',
          backgroundColor: '#fef2f2',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      ) : (
        <CategoryManagement
          categories={categories}
          onAddCategory={handleAddCategory}
          onUpdateCategory={handleUpdateCategory}
          onDeleteCategory={handleDeleteCategory}
        />
      )}
    </div>
  );
}
