'use client';

import React, { useState, useEffect } from 'react';

// Define types locally to avoid import issues
interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  addresses: CustomerAddress[];
  createdAt: string;
  updatedAt: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  favoriteProducts?: string[];
  notes?: string;
  tags?: string[];
  birthdate?: string;
  isSubscribedToNewsletter: boolean;
}

interface CustomerAddress {
  id?: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  type: 'billing' | 'shipping';
}

interface CustomerOrder {
  id: string;
  orderNumber: string;
  createdAt: string;
  status: 'pending' | 'processing' | 'ready' | 'out-for-delivery' | 'delivered' | 'completed' | 'cancelled' | 'refunded';
  total: number;
  items: any[];
}

type CustomerStatus = 'new' | 'active' | 'inactive' | 'dormant';

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerOrders, setCustomerOrders] = useState<Record<string, CustomerOrder[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  // Fetch customers data
  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/customers');
      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }
      const data = await response.json();

      // Transform the data to match the Customer interface
      const formattedCustomers = data.map((customer: any) => ({
        id: customer.id,
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email || '',
        phone: customer.phone || '',
        addresses: customer.addresses || [],
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
        totalOrders: customer.totalOrders || 0,
        totalSpent: customer.totalSpent || 0,
        lastOrderDate: customer.lastOrderDate,
        favoriteProducts: customer.favoriteProducts || [],
        notes: customer.notes || '',
        tags: customer.tags || [],
        birthdate: customer.birthdate,
        isSubscribedToNewsletter: customer.isSubscribed || false,
      }));

      setCustomers(formattedCustomers);

      // Fetch orders for each customer
      const ordersData: Record<string, CustomerOrder[]> = {};
      for (const customer of formattedCustomers) {
        try {
          const ordersResponse = await fetch(`/api/customers/${customer.id}/orders`);
          if (ordersResponse.ok) {
            const customerOrdersData = await ordersResponse.json();
            ordersData[customer.id] = customerOrdersData;
          }
        } catch (err) {
          console.error(`Error fetching orders for customer ${customer.id}:`, err);
        }
      }

      setCustomerOrders(ordersData);
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Get customer status
  const getCustomerStatus = (customer: Customer): CustomerStatus => {
    if (!customer.lastOrderDate) return 'new';

    const daysSinceLastOrder = Math.floor(
      (new Date().getTime() - new Date(customer.lastOrderDate).getTime()) / (1000 * 3600 * 24)
    );

    if (daysSinceLastOrder <= 30) return 'active';
    if (daysSinceLastOrder <= 90) return 'inactive';
    return 'dormant';
  };

  const handleViewCustomer = (id: string) => {
    setSelectedCustomerId(id);
  };

  const selectedCustomer = selectedCustomerId ? customers.find(c => c.id === selectedCustomerId) : null;

  if (loading) {
    return (
      <>
        <div style={{
          minHeight: '100vh',
          backgroundColor: '#f8fafc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>
            <p style={{ color: '#64748b' }}>Loading customers...</p>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <div style={{
          minHeight: '100vh',
          backgroundColor: '#f8fafc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>❌</div>
            <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
            <button
              onClick={fetchCustomers}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '0.5rem 1rem',
                cursor: 'pointer'
              }}
            >
              Try Again
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc',
      padding: '1.5rem'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          padding: '2rem',
          marginBottom: '1.5rem'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1e293b', marginBottom: '0.5rem' }}>
                Customer Management
              </h1>
              <p style={{ color: '#64748b', fontSize: '1rem' }}>
                Manage customer relationships and track customer data
              </p>
            </div>
            <div style={{ display: 'flex', gap: '0.75rem' }}>
              <button
                onClick={() => window.location.reload()}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#475569',
                  border: '1px solid #cbd5e1',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                }}
              >
                🔄 Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Customer Detail View */}
        {selectedCustomerId && selectedCustomer ? (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            {/* Customer Detail Header */}
            <div style={{
              padding: '1.5rem',
              borderBottom: '1px solid #e2e8f0',
              backgroundColor: '#f8fafc'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                  <button
                    onClick={() => setSelectedCustomerId(null)}
                    style={{
                      backgroundColor: '#f1f5f9',
                      color: '#475569',
                      border: '1px solid #cbd5e1',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    ← Back
                  </button>
                  <div>
                    <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1e293b' }}>
                      {selectedCustomer.firstName} {selectedCustomer.lastName}
                    </h2>
                    <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                      Customer ID: #{selectedCustomer.id.slice(0, 8)}
                    </p>
                  </div>
                </div>
                <div style={{
                  backgroundColor: getCustomerStatus(selectedCustomer) === 'active' ? '#dcfce7' :
                                 getCustomerStatus(selectedCustomer) === 'new' ? '#fef3c7' : '#fef2f2',
                  color: getCustomerStatus(selectedCustomer) === 'active' ? '#166534' :
                         getCustomerStatus(selectedCustomer) === 'new' ? '#92400e' : '#dc2626',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '6px',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  textTransform: 'capitalize'
                }}>
                  {getCustomerStatus(selectedCustomer)}
                </div>
              </div>
            </div>

            {/* Customer Details */}
            <div style={{ padding: '1.5rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
                <div>
                  <h3 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '0.75rem' }}>
                    Contact Information
                  </h3>
                  <div>
                    <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                      <strong>Email:</strong> {selectedCustomer.email}
                    </p>
                    <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                      <strong>Phone:</strong> {selectedCustomer.phone}
                    </p>
                    <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                      <strong>Joined:</strong> {new Date(selectedCustomer.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '0.75rem' }}>
                    Order Statistics
                  </h3>
                  <div>
                    <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                      <strong>Total Orders:</strong> {selectedCustomer.totalOrders}
                    </p>
                    <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                      <strong>Total Spent:</strong> ₹{selectedCustomer.totalSpent.toLocaleString()}
                    </p>
                    <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                      <strong>Last Order:</strong> {selectedCustomer.lastOrderDate ? new Date(selectedCustomer.lastOrderDate).toLocaleDateString() : 'Never'}
                    </p>
                  </div>
                </div>

                {selectedCustomer.addresses && selectedCustomer.addresses.length > 0 && (
                  <div>
                    <h3 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '0.75rem' }}>
                      Address
                    </h3>
                    <div style={{ fontSize: '0.875rem', color: '#64748b' }}>
                      <p>{selectedCustomer.addresses[0].street}</p>
                      <p>{selectedCustomer.addresses[0].city}, {selectedCustomer.addresses[0].state} {selectedCustomer.addresses[0].postalCode}</p>
                      <p>{selectedCustomer.addresses[0].country}</p>
                    </div>
                  </div>
                )}
              </div>

              {selectedCustomer.notes && (
                <div style={{ marginTop: '1.5rem', padding: '1rem', backgroundColor: '#f8fafc', borderRadius: '6px' }}>
                  <h3 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Notes
                  </h3>
                  <p style={{ fontSize: '0.875rem', color: '#64748b', whiteSpace: 'pre-wrap' }}>
                    {selectedCustomer.notes}
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : !loading && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            {customers.length === 0 ? (
              <div style={{
                padding: '3rem',
                textAlign: 'center',
                color: '#64748b'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>👥</div>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '500', marginBottom: '0.5rem', color: '#374151' }}>
                  No customers found
                </h3>
                <p style={{ fontSize: '0.875rem' }}>
                  Customers will appear here when they sign up.
                </p>
              </div>
            ) : (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', fontSize: '0.875rem' }}>
                  <thead>
                    <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Customer</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Email</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Phone</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Orders</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Total Spent</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Status</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Joined</th>
                      <th style={{ padding: '1rem', textAlign: 'right', fontWeight: '500', color: '#374151' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.map((customer) => (
                      <tr
                        key={customer.id}
                        style={{
                          borderBottom: '1px solid #f1f5f9',
                          transition: 'background-color 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#f8fafc';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <td style={{ padding: '1rem' }}>
                          <div>
                            <p style={{ fontWeight: '500', color: '#1e293b' }}>
                              {customer.firstName} {customer.lastName}
                            </p>
                            <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
                              ID: #{customer.id.slice(0, 8)}
                            </p>
                          </div>
                        </td>
                        <td style={{ padding: '1rem', color: '#64748b' }}>{customer.email}</td>
                        <td style={{ padding: '1rem', color: '#64748b' }}>{customer.phone}</td>
                        <td style={{ padding: '1rem', fontWeight: '500' }}>{customer.totalOrders}</td>
                        <td style={{ padding: '1rem', fontWeight: '500', color: '#059669' }}>₹{customer.totalSpent.toLocaleString()}</td>
                        <td style={{ padding: '1rem' }}>
                          <span style={{
                            backgroundColor: getCustomerStatus(customer) === 'active' ? '#dcfce7' :
                                           getCustomerStatus(customer) === 'new' ? '#fef3c7' : '#fef2f2',
                            color: getCustomerStatus(customer) === 'active' ? '#166534' :
                                   getCustomerStatus(customer) === 'new' ? '#92400e' : '#dc2626',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '6px',
                            fontSize: '0.75rem',
                            fontWeight: '500',
                            textTransform: 'capitalize'
                          }}>
                            {getCustomerStatus(customer)}
                          </span>
                        </td>
                        <td style={{ padding: '1rem', color: '#64748b' }}>
                          {new Date(customer.createdAt).toLocaleDateString()}
                        </td>
                        <td style={{ padding: '1rem', textAlign: 'right' }}>
                          <button
                            onClick={() => handleViewCustomer(customer.id)}
                            style={{
                              backgroundColor: '#3b82f6',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              padding: '0.375rem 0.75rem',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#2563eb';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '#3b82f6';
                            }}
                          >
                            View Details
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
    </>
  );
}
