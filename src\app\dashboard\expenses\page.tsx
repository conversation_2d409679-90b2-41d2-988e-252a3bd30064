'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, Loader2 } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';



// Expense categories
const expenseCategories = [
  'Utilities',
  'Rent',
  'Salaries',
  'Maintenance',
  'Supplies',
  'Marketing',
  'Insurance',
  'Taxes',
  'Transportation',
  'Miscellaneous',
];

// Define interface for expense object
interface Expense {
  id: string;
  date: string;
  storeId: string;
  storeName: string;
  category: string;
  amount: number;
  description: string;
  userId?: string;
  userName?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Define interface for store object
interface Store {
  id: string;
  name: string;
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    storeId: '',
    category: '',
    amount: '',
    description: '',
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [filterStore, setFilterStore] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch expenses and stores when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch all data in parallel for better performance
        const [expensesResponse, storesResponse] = await Promise.all([
          fetch('/api/expenses'),
          fetch('/api/stores')
        ]);

        // Handle expenses response
        if (!expensesResponse.ok) {
          const errorData = await expensesResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch expenses');
        }

        // Handle stores response
        if (!storesResponse.ok) {
          const errorData = await storesResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch stores');
        }

        // Parse response data
        const [expensesData, storesData] = await Promise.all([
          expensesResponse.json(),
          storesResponse.json()
        ]);

        // Update state with fetched data
        setExpenses(expensesData);
        setStores(storesData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch filtered expenses when filters change
  useEffect(() => {
    const fetchFilteredExpenses = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters for filtering
        const params = new URLSearchParams();

        if (filterStore !== 'all') {
          params.append('storeId', filterStore);
        }

        if (filterCategory !== 'all') {
          params.append('category', filterCategory);
        }

        if (dateRange.startDate) {
          params.append('startDate', dateRange.startDate);
        }

        if (dateRange.endDate) {
          params.append('endDate', dateRange.endDate);
        }

        // Fetch filtered expenses
        const response = await fetch(`/api/expenses?${params.toString()}`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch expenses');
        }

        const data = await response.json();
        setExpenses(data);
      } catch (err) {
        console.error('Error fetching filtered expenses:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if we're not in the initial loading state
    if (!loading) {
      fetchFilteredExpenses();
    }
  }, [filterStore, filterCategory, dateRange.startDate, dateRange.endDate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange({ ...dateRange, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.storeId || !formData.category || !formData.amount) {
      alert('Please fill in all required fields');
      return;
    }

    if (isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (editingId) {
        // Update existing expense
        const response = await fetch(`/api/expenses/${editingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            date: formData.date,
            storeId: formData.storeId,
            category: formData.category,
            amount: formData.amount,
            description: formData.description,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update expense');
        }

        const updatedExpense = await response.json();

        // Update the expenses list
        setExpenses(expenses.map(expense =>
          expense.id === editingId ? updatedExpense : expense
        ));

        setEditingId(null);
        alert('Expense updated successfully!');
      } else {
        // Add new expense
        const response = await fetch('/api/expenses', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            date: formData.date,
            storeId: formData.storeId,
            category: formData.category,
            amount: formData.amount,
            description: formData.description,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create expense');
        }

        const newExpense = await response.json();

        // Add the new expense to the list
        setExpenses([newExpense, ...expenses]);
        alert('Expense added successfully!');
      }

      // Reset form
      setFormData({
        date: new Date().toISOString().split('T')[0],
        storeId: '',
        category: '',
        amount: '',
        description: '',
      });
      setShowForm(false);
    } catch (err) {
      console.error('Error saving expense:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while saving the expense');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (expense: Expense) => {
    setFormData({
      date: expense.date,
      storeId: expense.storeId,
      category: expense.category,
      amount: expense.amount.toString(),
      description: expense.description || '',
    });
    setEditingId(expense.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this expense?')) {
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/expenses/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete expense');
      }

      // Remove the expense from the list
      setExpenses(expenses.filter(expense => expense.id !== id));
      alert('Expense deleted successfully!');
    } catch (err) {
      console.error('Error deleting expense:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the expense');
    } finally {
      setIsDeleting(false);
    }
  };

  // Filter expenses based on selected filters
  const filteredExpenses = expenses.filter(expense => {
    // Filter by store
    if (filterStore !== 'all' && expense.storeId !== filterStore) {
      return false;
    }

    // Filter by category
    if (filterCategory !== 'all' && expense.category !== filterCategory) {
      return false;
    }

    // Filter by date range
    if (dateRange.startDate && expense.date < dateRange.startDate) {
      return false;
    }

    if (dateRange.endDate && expense.date > dateRange.endDate) {
      return false;
    }

    return true;
  });

  // Calculate total expenses
  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Expenses Management
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Manage and track your business expenses • ₹{formatCurrency(totalExpenses)} total
            </p>
          </div>
          <button
            onClick={() => {
              setFormData({
                date: new Date().toISOString().split('T')[0],
                storeId: '',
                category: '',
                amount: '',
                description: '',
              });
              setEditingId(null);
              setShowForm(!showForm);
            }}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            Add Expense
          </button>
        </div>
      </div>

      {/* Content */}
      <div style={{ padding: '2rem' }}>

      {showForm && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          marginBottom: '2rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ fontSize: '1.125rem', fontWeight: '500', marginBottom: '1.5rem', color: '#0f172a' }}>
            {editingId ? 'Edit Expense' : 'Add New Expense'}
          </h2>
          <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="date" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Date
                </label>
                <input
                  id="date"
                  name="date"
                  type="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="storeId" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Store
                </label>
                <select
                  id="storeId"
                  name="storeId"
                  value={formData.storeId}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="">Select a store</option>
                  {stores.map(store => (
                    <option key={store.id} value={store.id}>
                      {store.name}
                    </option>
                  ))}
                </select>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="category" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Category
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="">Select a category</option>
                  {expenseCategories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="amount" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Amount
                </label>
                <input
                  id="amount"
                  name="amount"
                  type="number"
                  step="0.01"
                  min="0.01"
                  value={formData.amount}
                  onChange={handleInputChange}
                  placeholder="Enter amount"
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', gridColumn: 'span 2' }}>
                <label htmlFor="description" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Description
                </label>
                <input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter description"
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                type="submit"
                disabled={isSubmitting}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: isSubmitting ? '#94a3b8' : '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
                onMouseEnter={(e) => {
                  if (!isSubmitting) {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSubmitting) {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }
                }}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 style={{ height: '16px', width: '16px', animation: 'spin 1s linear infinite' }} />
                    {editingId ? 'Updating...' : 'Saving...'}
                  </>
                ) : (
                  editingId ? 'Update' : 'Save'
                )}
              </button>
              <button
                type="button"
                onClick={() => setShowForm(false)}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#64748b',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                }}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '1.5rem',
        marginBottom: '2rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '1rem', color: '#0f172a' }}>
          Filter Expenses
        </h3>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <label htmlFor="filterStore" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
              Filter by Store
            </label>
            <select
              id="filterStore"
              value={filterStore}
              onChange={(e) => setFilterStore(e.target.value)}
              style={{
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <option value="all">All Stores</option>
              {stores.map(store => (
                <option key={store.id} value={store.id}>
                  {store.name}
                </option>
              ))}
            </select>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <label htmlFor="filterCategory" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
              Filter by Category
            </label>
            <select
              id="filterCategory"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              style={{
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <option value="all">All Categories</option>
              {expenseCategories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.5rem' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <label htmlFor="startDate" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                From
              </label>
              <input
                id="startDate"
                name="startDate"
                type="date"
                value={dateRange.startDate}
                onChange={handleDateRangeChange}
                style={{
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid #d1d5db',
                  fontSize: '0.875rem',
                  backgroundColor: 'white',
                  transition: 'all 0.2s'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = '#3b82f6';
                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <label htmlFor="endDate" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                To
              </label>
              <input
                id="endDate"
                name="endDate"
                type="date"
                value={dateRange.endDate}
                onChange={handleDateRangeChange}
                style={{
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid #d1d5db',
                  fontSize: '0.875rem',
                  backgroundColor: 'white',
                  transition: 'all 0.2s'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = '#3b82f6';
                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          {loading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '3rem',
              color: '#64748b'
            }}>
              <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem', color: '#3b82f6' }} />
              <span>Loading expenses...</span>
            </div>
          ) : error ? (
            <div style={{ padding: '3rem', textAlign: 'center' }}>
              <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
              <button
                onClick={() => {
                  setLoading(true);
                  setError(null);
                  fetch('/api/expenses')
                    .then(res => res.json())
                    .then(data => setExpenses(data))
                    .catch(err => setError(err instanceof Error ? err.message : 'An error occurred'))
                    .finally(() => setLoading(false));
                }}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#3b82f6',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer'
                }}
              >
                Retry
              </button>
            </div>
          ) : (
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
            <thead>
              <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Date</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Store</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Category</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Amount</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Description</th>
                <th style={{ padding: '1rem', textAlign: 'right', fontWeight: '500', color: '#374151' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredExpenses.map((expense) => (
                <tr
                  key={expense.id}
                  style={{
                    borderBottom: '1px solid #f1f5f9',
                    transition: 'background-color 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <td style={{ padding: '1rem', color: '#64748b' }}>{formatDate(expense.date)}</td>
                  <td style={{ padding: '1rem' }}>{expense.storeName}</td>
                  <td style={{ padding: '1rem' }}>
                    <span style={{
                      backgroundColor: '#dbeafe',
                      color: '#1e40af',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '12px',
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}>
                      {expense.category}
                    </span>
                  </td>
                  <td style={{ padding: '1rem', fontWeight: '500', color: '#dc2626' }}>₹{expense.amount}</td>
                  <td style={{ padding: '1rem' }}>{expense.description}</td>
                  <td style={{ padding: '1rem', textAlign: 'right' }}>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem' }}>
                      <button
                        onClick={() => handleEdit(expense)}
                        style={{
                          backgroundColor: 'transparent',
                          color: '#3b82f6',
                          border: 'none',
                          borderRadius: '6px',
                          padding: '0.5rem',
                          cursor: 'pointer',
                          transition: 'all 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#eff6ff';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <Edit style={{ height: '16px', width: '16px' }} />
                      </button>
                      <button
                        onClick={() => handleDelete(expense.id)}
                        disabled={isDeleting}
                        style={{
                          backgroundColor: 'transparent',
                          color: '#dc2626',
                          border: 'none',
                          borderRadius: '6px',
                          padding: '0.5rem',
                          cursor: 'pointer',
                          transition: 'all 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#fef2f2';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        {isDeleting ? (
                          <Loader2 style={{ height: '16px', width: '16px', animation: 'spin 1s linear infinite' }} />
                        ) : (
                          <Trash style={{ height: '16px', width: '16px' }} />
                        )}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              {filteredExpenses.length === 0 && (
                <tr>
                  <td colSpan={6} style={{ padding: '3rem', textAlign: 'center', color: '#64748b' }}>
                    <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💰</div>
                    <p>No expenses found. Add a new expense to get started or adjust your filters.</p>
                  </td>
                </tr>
              )}
            </tbody>
            <tfoot>
              <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                <td colSpan={3} style={{ padding: '1rem', textAlign: 'right', fontWeight: '600', color: '#374151' }}>
                  Total Expenses:
                </td>
                <td style={{ padding: '1rem', fontWeight: '700', color: '#dc2626', fontSize: '1rem' }}>
                  ₹{totalExpenses.toFixed(2)}
                </td>
                <td colSpan={2}></td>
              </tr>
            </tfoot>
          </table>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
