'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertTriangle, ArrowUpDown, Plus, Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { InventoryDashboard } from '@/components/inventory/inventory-dashboard';
import { InventoryItemDetail } from '@/components/inventory/inventory-item-detail';
import { InventoryItemForm } from '@/components/inventory/inventory-item-form';
import { TransferHistory } from '@/components/inventory/transfer-history';

// Mock data for categories
const inventoryCategories = [
  'Flour & Grains',
  'Sweeteners',
  'Dairy',
  'Eggs',
  'Fats & Oils',
  'Leavening Agents',
  'Flavorings',
  'Nuts & Seeds',
  'Fruits',
  'Chocolate',
  'Packaging',
  'Other'
];

// Mock data for units of measure
const unitOptions = [
  'kg',
  'g',
  'l',
  'ml',
  'pcs',
  'dozen',
  'box',
  'case'
];

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  category: string;
  warehouseId?: string;
  warehouseName?: string;
  storeId?: string;
  storeName?: string;
  quantity: number;
  lowStockThreshold: number;
  unit: string;
  price: number;
}

interface Warehouse {
  id: string;
  name: string;
}

interface Store {
  id: string;
  name: string;
}

interface EnhancedInventoryItem {
  id: string;
  name: string;
  sku: string;
  category: string;
  description?: string;
  unitOfMeasure: string;
  costPerUnit: number;
  currentStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  location?: string;
  supplier?: string;
  lastRestocked?: string;
  imageUrl?: string;
  transactions?: InventoryTransaction[];
  warehouseInventory?: {
    warehouseId: string;
    warehouseName: string;
    quantity: number;
  }[];
  storeInventory?: {
    storeId: string;
    storeName: string;
    quantity: number;
  }[];
}

interface InventoryTransaction {
  id: string;
  itemId: string;
  date: string;
  type: 'purchase' | 'sale' | 'production' | 'adjustment';
  quantity: number;
  unitCost: number;
  totalCost: number;
  reference?: string;
  notes?: string;
}

interface Supplier {
  id: string;
  name: string;
}

interface Location {
  id: string;
  name: string;
}

export default function InventoryPage() {
  // State for the original inventory system
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState('all');
  const [showTransferForm, setShowTransferForm] = useState(false);
  const [transferData, setTransferData] = useState({
    productId: '',
    sourceWarehouseId: '',
    destinationId: '',
    destinationType: 'warehouse', // 'warehouse' or 'store'
    quantity: '',
    notes: '',
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for the enhanced inventory system
  const [items, setItems] = useState<EnhancedInventoryItem[]>([]);
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<EnhancedInventoryItem | null>(null);
  const [showEnhancedView, setShowEnhancedView] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (!warehousesResponse.ok) {
          throw new Error('Failed to fetch warehouses');
        }
        const warehousesData = await warehousesResponse.json();
        setWarehouses(warehousesData);

        // Fetch stores
        const storesResponse = await fetch('/api/stores');
        if (!storesResponse.ok) {
          throw new Error('Failed to fetch stores');
        }
        const storesData = await storesResponse.json();
        setStores(storesData);

        // Fetch inventory
        const inventoryResponse = await fetch('/api/inventory');
        if (!inventoryResponse.ok) {
          throw new Error('Failed to fetch inventory');
        }
        const inventoryData = await inventoryResponse.json();
        setInventory(inventoryData);

        // Fetch enhanced inventory items
        const itemsResponse = await fetch('/api/inventory-items');
        if (!itemsResponse.ok) {
          throw new Error('Failed to fetch inventory items');
        }
        const itemsData = await itemsResponse.json();
        setItems(itemsData);

        // Fetch suppliers
        const suppliersResponse = await fetch('/api/suppliers');
        if (!suppliersResponse.ok) {
          throw new Error('Failed to fetch suppliers');
        }
        const suppliersData = await suppliersResponse.json();
        setSuppliers(suppliersData);

        // Use warehouses as locations
        setLocations(warehousesData.map((warehouse: Warehouse) => ({
          id: warehouse.id,
          name: warehouse.name,
        })));
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const selectedItem = selectedItemId ? items.find(item => item.id === selectedItemId) : null;
  const itemTransactions = selectedItem?.transactions || [];

  const filteredInventory = selectedWarehouse === 'all'
    ? inventory
    : inventory.filter(item => item.warehouseId === selectedWarehouse);

  // Handlers for the original inventory system
  const handleTransferInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTransferData({ ...transferData, [name]: value });
  };

  const handleTransferSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/inventory/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transferData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to transfer inventory');
      }

      // Refresh inventory data
      const inventoryResponse = await fetch('/api/inventory');
      if (!inventoryResponse.ok) {
        throw new Error('Failed to fetch updated inventory');
      }
      const inventoryData = await inventoryResponse.json();
      setInventory(inventoryData);

      setShowTransferForm(false);
      setTransferData({
        productId: '',
        sourceWarehouseId: '',
        destinationId: '',
        destinationType: 'warehouse',
        quantity: '',
        notes: '',
      });

      alert('Inventory transferred successfully');
    } catch (err) {
      console.error('Error transferring inventory:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while transferring inventory');
    }
  };

  // Handlers for the enhanced inventory system
  const handleViewItem = async (id: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/inventory-items/${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch inventory item details');
      }

      const itemData = await response.json();

      // Update the item in the items array
      setItems(items.map(item => item.id === id ? itemData : item));

      setSelectedItemId(id);
      setShowForm(false);
    } catch (err) {
      console.error('Error fetching inventory item details:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while fetching inventory item details');
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = () => {
    setEditingItem(null);
    setSelectedItemId(null);
    setShowForm(true);
  };

  const handleEditItem = (item: EnhancedInventoryItem) => {
    setEditingItem(item);
    setSelectedItemId(null);
    setShowForm(true);
  };

  const handleDeleteItem = async (id: string) => {
    if (!confirm('Are you sure you want to delete this inventory item?')) {
      return;
    }

    try {
      const response = await fetch(`/api/inventory-items/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete inventory item');
      }

      setItems(items.filter(item => item.id !== id));
      setSelectedItemId(null);
    } catch (err) {
      console.error('Error deleting inventory item:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the inventory item');
    }
  };

  const handleAddStock = async (itemId: string, quantity: number, unitCost: number, notes?: string) => {
    try {
      const response = await fetch('/api/inventory-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemId,
          type: 'purchase',
          quantity,
          unitCost,
          reference: `PO-${new Date().getFullYear()}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
          notes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add stock');
      }

      const newTransaction = await response.json();

      // Refresh the item details
      await handleViewItem(itemId);

      alert('Stock added successfully');
    } catch (err) {
      console.error('Error adding stock:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while adding stock');
    }
  };

  const handleFormSubmit = async (formData: any) => {
    try {
      if (editingItem) {
        // Update existing item
        const response = await fetch(`/api/inventory-items/${editingItem.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update inventory item');
        }

        const updatedItem = await response.json();
        setItems(items.map(item => item.id === editingItem.id ? updatedItem : item));
      } else {
        // Add new item
        const response = await fetch('/api/inventory-items', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create inventory item');
        }

        const newItem = await response.json();
        setItems([...items, newItem]);
      }

      setShowForm(false);
      setEditingItem(null);
    } catch (err) {
      console.error('Error saving inventory item:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while saving the inventory item');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '700', letterSpacing: '-0.025em' }}>Inventory Management</h1>
          <p style={{ color: '#6b7280' }}>
            Manage your bakery inventory and supplies
          </p>
        </div>
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          {/* <Button
            variant={showEnhancedView ? 'outline' : 'default'}
            onClick={() => setShowEnhancedView(false)}
            disabled={loading}
          >
            Basic View
          </Button> */}
          {/* <Button
            variant={showEnhancedView ? 'default' : 'outline'}
            onClick={() => setShowEnhancedView(true)}
            disabled={loading}
          >
            Enhanced View
          </Button> */}
          {!showEnhancedView && (
            <Button
              onClick={() => setShowTransferForm(!showTransferForm)}
              disabled={loading}
            >
              <ArrowUpDown style={{ marginRight: '0.5rem', height: '1rem', width: '1rem' }} />
              Transfer Items
            </Button>
          )}
          {showEnhancedView && !selectedItemId && !showForm && (
            <Button
              onClick={handleAddItem}
              disabled={loading}
            >
              <Plus style={{ marginRight: '0.5rem', height: '1rem', width: '1rem' }} />
              Add Item
            </Button>
          )}
        </div>
      </div>

      {error && (
        <div style={{
          borderRadius: '6px',
          backgroundColor: '#fef2f2',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {loading && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: 'var(--primary)' }} />
          <span style={{ marginLeft: '0.5rem' }}>Loading inventory data...</span>
        </div>
      )}

      {/* Enhanced Inventory View */}
      {showEnhancedView && !loading && (
        <>
          {showForm && (
            <div style={{
              borderRadius: '8px',
              border: '1px solid var(--border)',
              backgroundColor: 'var(--background)',
              padding: '1.5rem',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}>
              <h2 style={{ marginBottom: '1.5rem', fontSize: '1.125rem', fontWeight: '500' }}>
                {editingItem ? 'Edit Inventory Item' : 'Add New Inventory Item'}
              </h2>
              <InventoryItemForm
                initialData={editingItem || undefined}
                onSubmit={handleFormSubmit}
                onCancel={() => {
                  setShowForm(false);
                  setEditingItem(null);
                }}
                categories={inventoryCategories}
                unitOptions={unitOptions}
                suppliers={suppliers}
                locations={locations}
              />
            </div>
          )}

          {selectedItem && (
            <InventoryItemDetail
              item={selectedItem}
              transactions={itemTransactions}
              onBack={() => setSelectedItemId(null)}
              onEdit={handleEditItem}
              onDelete={handleDeleteItem}
              onAddStock={handleAddStock}
            />
          )}

          {!selectedItemId && !showForm && (
            <InventoryDashboard
              items={items}
              categories={inventoryCategories}
              onViewItem={handleViewItem}
            />
          )}
        </>
      )}

      {/* Basic Inventory View */}
      {!showEnhancedView && !loading && (
        <>
          {showTransferForm && (
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e5e7eb',
              padding: '2rem',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}>
              <h2 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '1.5rem'
              }}>
                Transfer Inventory
              </h2>
              <form onSubmit={handleTransferSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '1rem'
                }}>
                  <div>
                    <label htmlFor="productId" style={{
                      display: 'block',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem'
                    }}>
                      Product
                    </label>
                    <select
                      id="productId"
                      name="productId"
                      value={transferData.productId}
                      onChange={handleTransferInputChange}
                      style={{
                        width: '100%',
                        height: '2.5rem',
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        padding: '0 0.75rem',
                        fontSize: '0.875rem',
                        backgroundColor: 'white'
                      }}
                      required
                    >
                      <option value="">Select a product</option>
                      {inventory
                        .filter(item => item.warehouseId)
                        .filter((item, index, self) =>
                          index === self.findIndex(t => t.productId === item.productId)
                        )
                        .map(item => (
                          <option key={item.productId} value={item.productId}>
                            {item.productName}
                          </option>
                        ))
                      }
                    </select>
                  </div>

                  <div>
                    <label htmlFor="sourceWarehouseId" style={{
                      display: 'block',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem'
                    }}>
                      Source Warehouse
                    </label>
                    <select
                      id="sourceWarehouseId"
                      name="sourceWarehouseId"
                      value={transferData.sourceWarehouseId}
                      onChange={handleTransferInputChange}
                      style={{
                        width: '100%',
                        height: '2.5rem',
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        padding: '0 0.75rem',
                        fontSize: '0.875rem',
                        backgroundColor: 'white'
                      }}
                      required
                    >
                      <option value="">Select source warehouse</option>
                      {warehouses.map(warehouse => (
                        <option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="destinationType" style={{
                      display: 'block',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem'
                    }}>
                      Destination Type
                    </label>
                    <select
                      id="destinationType"
                      name="destinationType"
                      value={transferData.destinationType}
                      onChange={handleTransferInputChange}
                      style={{
                        width: '100%',
                        height: '2.5rem',
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        padding: '0 0.75rem',
                        fontSize: '0.875rem',
                        backgroundColor: 'white'
                      }}
                      required
                    >
                      <option value="warehouse">Warehouse</option>
                      <option value="store">Store</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="destinationId" style={{
                      display: 'block',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem'
                    }}>
                      Destination
                    </label>
                    <select
                      id="destinationId"
                      name="destinationId"
                      value={transferData.destinationId}
                      onChange={handleTransferInputChange}
                      style={{
                        width: '100%',
                        height: '2.5rem',
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        padding: '0 0.75rem',
                        fontSize: '0.875rem',
                        backgroundColor: 'white'
                      }}
                      required
                    >
                      <option value="">Select destination</option>
                      {transferData.destinationType === 'warehouse'
                        ? warehouses
                            .filter(w => w.id !== transferData.sourceWarehouseId)
                            .map(warehouse => (
                              <option key={warehouse.id} value={warehouse.id}>
                                {warehouse.name}
                              </option>
                            ))
                        : stores.map(store => (
                            <option key={store.id} value={store.id}>
                              {store.name}
                            </option>
                          ))
                      }
                    </select>
                  </div>

                  <div>
                    <label htmlFor="quantity" style={{
                      display: 'block',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem'
                    }}>
                      Quantity
                    </label>
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={transferData.quantity}
                      onChange={handleTransferInputChange}
                      placeholder="Enter quantity to transfer"
                      style={{
                        width: '100%',
                        height: '2.5rem',
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        padding: '0 0.75rem',
                        fontSize: '0.875rem'
                      }}
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="notes" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '0.5rem'
                  }}>
                    Notes
                  </label>
                  <textarea
                    id="notes"
                    name="notes"
                    value={transferData.notes}
                    onChange={handleTransferInputChange}
                    placeholder="Enter notes about this transfer"
                    style={{
                      width: '100%',
                      minHeight: '80px',
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      padding: '0.75rem',
                      fontSize: '0.875rem',
                      resize: 'vertical'
                    }}
                  />
                </div>

                <div style={{ display: 'flex', gap: '0.5rem', marginTop: '1rem' }}>
                  <Button
                    type="submit"
                    style={{
                      backgroundColor: '#059669',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.75rem 1.5rem',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer'
                    }}
                  >
                    Transfer Inventory
                  </Button>
                  <Button
                    type="button"
                    onClick={() => setShowTransferForm(false)}
                    style={{
                      backgroundColor: 'white',
                      color: '#374151',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px',
                      padding: '0.75rem 1.5rem',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer'
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          )}

          {/* Warehouse Filter */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e5e7eb',
            padding: '1.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
              <label htmlFor="warehouseFilter" style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}>
                📍 Filter by Warehouse:
              </label>
              <select
                id="warehouseFilter"
                value={selectedWarehouse}
                onChange={(e) => setSelectedWarehouse(e.target.value)}
                style={{
                  height: '2.25rem',
                  width: '200px',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb',
                  padding: '0 0.75rem',
                  fontSize: '0.875rem',
                  backgroundColor: 'white'
                }}
              >
                <option value="all">All Warehouses</option>
                {warehouses.map(warehouse => (
                  <option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Inventory Cards Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
              gap: '1.5rem'
            }}>
              {filteredInventory.length > 0 ? filteredInventory.map((item) => {
                const isLowStock = item.quantity <= item.lowStockThreshold;
                const totalValue = item.price * item.quantity;

                return (
                  <div
                    key={item.id}
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      border: '1px solid #e5e7eb',
                      padding: '1.5rem',
                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    }}
                  >
                    {/* Header */}
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                      <div>
                        <h3 style={{
                          fontSize: '1.125rem',
                          fontWeight: '600',
                          color: '#111827',
                          marginBottom: '0.25rem'
                        }}>
                          {item.productName}
                        </h3>
                        <p style={{
                          fontSize: '0.875rem',
                          color: '#6b7280'
                        }}>
                          {item.category}
                        </p>
                      </div>
                      <div style={{
                        padding: '0.25rem 0.75rem',
                        borderRadius: '9999px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        backgroundColor: isLowStock ? '#fef3c7' : '#dcfce7',
                        color: isLowStock ? '#92400e' : '#166534'
                      }}>
                        {isLowStock ? '⚠️ Low Stock' : '✅ In Stock'}
                      </div>
                    </div>

                    {/* Location */}
                    <div style={{ marginBottom: '1rem' }}>
                      <p style={{
                        fontSize: '0.875rem',
                        color: '#6b7280',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem'
                      }}>
                        📍 {item.warehouseName || item.storeName}
                      </p>
                    </div>

                    {/* Quantity and Price */}
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: '1rem',
                      marginBottom: '1rem'
                    }}>
                      <div>
                        <p style={{
                          fontSize: '0.75rem',
                          color: '#6b7280',
                          marginBottom: '0.25rem'
                        }}>
                          Quantity
                        </p>
                        <p style={{
                          fontSize: '1.25rem',
                          fontWeight: '700',
                          color: '#111827'
                        }}>
                          {item.quantity} {item.unit}
                        </p>
                      </div>
                      <div>
                        <p style={{
                          fontSize: '0.75rem',
                          color: '#6b7280',
                          marginBottom: '0.25rem'
                        }}>
                          Unit Price
                        </p>
                        <p style={{
                          fontSize: '1.25rem',
                          fontWeight: '700',
                          color: '#111827'
                        }}>
                          {formatCurrency(item.price)}
                        </p>
                      </div>
                    </div>

                    {/* Total Value */}
                    <div style={{
                      borderTop: '1px solid #e5e7eb',
                      paddingTop: '1rem'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{
                          fontSize: '0.875rem',
                          fontWeight: '500',
                          color: '#6b7280'
                        }}>
                          Total Value
                        </span>
                        <span style={{
                          fontSize: '1.125rem',
                          fontWeight: '700',
                          color: '#059669'
                        }}>
                          {formatCurrency(totalValue)}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              }) : (
                <div style={{
                  gridColumn: '1 / -1',
                  textAlign: 'center',
                  padding: '3rem',
                  color: '#6b7280'
                }}>
                  <div style={{
                    fontSize: '3rem',
                    marginBottom: '1rem'
                  }}>
                    📦
                  </div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    No inventory items found
                  </h3>
                  <p style={{ fontSize: '0.875rem' }}>
                    Try adjusting your warehouse filter or add some inventory items.
                  </p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
