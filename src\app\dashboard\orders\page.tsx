'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/auth-context';

// Define types locally to avoid import issues
interface Order {
  id: string;
  orderNumber?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  totalAmount: number;
  status: string;
  paymentStatus?: string;
  paymentMethod?: string;
  orderType?: string;
  storeName?: string;
  storeId?: string;
  createdAt: string;
  items?: Array<{
    id: string;
    productName: string;
    quantity: number;
    total: number;
  }>;
}

type OrderStatus = 'PENDING' | 'PROCESSING' | 'DELIVERED' | 'CANCELLED';
type PaymentStatus = 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';

export default function OrdersPage() {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [stores, setStores] = useState<Array<{id: string, name: string, location: string}>>([]);
  const [updating, setUpdating] = useState(false);

  // Fetch orders and stores data
  useEffect(() => {
    const fetchData = async () => {
      if (!user) return; // Wait for user to be loaded

      setLoading(true);
      setError(null);
      try {
        // Build API URL with user's role and store ID
        const params = new URLSearchParams({
          userRole: user.role,
        });

        // Add store ID for store managers and staff
        if (user.storeId && (user.role === 'STORE_MANAGER' || user.role === 'STAFF')) {
          params.append('storeId', user.storeId);
        }

        // Add order type for admins
        if (user.role === 'ADMIN') {
          params.append('orderType', 'ONLINE');
        }

        console.log('🔍 Fetching orders with params:', {
          userRole: user.role,
          storeId: user.storeId,
          params: params.toString()
        });

        const ordersResponse = await fetch(`/api/orders?${params.toString()}`);
        if (!ordersResponse.ok) {
          throw new Error('Failed to fetch orders');
        }
        const ordersData = await ordersResponse.json();
        console.log('📋 Admin Panel: Fetched orders:', ordersData);
        console.log('📋 Admin Panel: Orders data type:', typeof ordersData);
        console.log('📋 Admin Panel: Orders is array:', Array.isArray(ordersData));
        console.log('📋 Admin Panel: Orders length:', ordersData?.length);

        // Ensure we have an array
        const ordersArray = Array.isArray(ordersData) ? ordersData : [];
        console.log('📋 Admin Panel: Setting orders array:', ordersArray);
        setOrders(ordersArray);

        // Fetch available stores
        const storesResponse = await fetch('/api/stores');
        if (storesResponse.ok) {
          const storesData = await storesResponse.json();
          console.log('🏪 Admin Panel: Fetched stores:', storesData);
          setStores(storesData);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  const selectedOrder = selectedOrderId ? orders.find(order => order.id === selectedOrderId) : null;

  // Debug log for render
  console.log('🔍 Render Debug - Orders state:', orders);
  console.log('🔍 Render Debug - Orders length:', orders.length);
  console.log('🔍 Render Debug - Loading:', loading);
  console.log('🔍 Render Debug - Error:', error);

  const handleViewOrder = (id: string) => {
    setSelectedOrderId(id);
  };

  const handleUpdateOrderStatus = async (id: string, status: OrderStatus) => {
    try {
      setUpdating(true);
      console.log('📊 Updating order status:', { orderId: id, status });

      const response = await fetch(`/api/orders/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      console.log('📊 Order status update response:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Order status update failed:', errorText);

        let errorMessage = 'Failed to update order status';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const updatedOrder = await response.json();
      console.log('✅ Order status updated successfully:', updatedOrder);

      setOrders(orders.map(order =>
        order.id === id ? updatedOrder : order
      ));

      alert('Order status updated successfully!');
    } catch (err) {
      console.error('❌ Error updating order status:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while updating the order status');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdatePaymentStatus = async (id: string, paymentStatus: PaymentStatus) => {
    try {
      setUpdating(true);
      console.log('💳 Updating payment status:', { orderId: id, paymentStatus });

      const response = await fetch(`/api/orders/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentStatus: paymentStatus
        }),
      });

      console.log('📊 Payment status update response:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Payment status update failed:', errorText);

        let errorMessage = 'Failed to update payment status';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const updatedOrder = await response.json();
      console.log('✅ Payment status updated successfully:', updatedOrder);

      setOrders(orders.map(order =>
        order.id === id ? updatedOrder : order
      ));

      alert('Payment status updated successfully!');
    } catch (err) {
      console.error('❌ Error updating payment status:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while updating the payment status');
    } finally {
      setUpdating(false);
    }
  };

  const handleAssignStore = async (id: string, storeId: string) => {
    try {
      setUpdating(true);
      console.log('🏪 Assigning store:', { orderId: id, storeId });

      const response = await fetch(`/api/orders/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storeId: storeId
        }),
      });

      console.log('📊 Store assignment response:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Store assignment failed:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });

        let errorMessage = 'Failed to assign store';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }

        // Add more specific error messages
        if (response.status === 500) {
          errorMessage = 'Server error - please try again or contact support';
        } else if (response.status === 404) {
          errorMessage = 'Order not found - please refresh the page';
        } else if (response.status === 403) {
          errorMessage = 'Permission denied - you may not have access to assign stores';
        }

        throw new Error(errorMessage);
      }

      const updatedOrder = await response.json();
      console.log('✅ Store assignment successful:', updatedOrder);

      setOrders(orders.map(order =>
        order.id === id ? updatedOrder : order
      ));

      alert('Store assigned successfully!');
    } catch (err) {
      console.error('❌ Error assigning store:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while assigning the store');
    } finally {
      setUpdating(false);
    }
  };

  return (
    <>
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
      <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Orders Management
          </h1>
          <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
            Manage and process customer orders • {orders.length} orders
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div style={{
          margin: '2rem',
          borderRadius: '8px',
          backgroundColor: '#fef2f2',
          border: '1px solid #fca5a5',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {/* Loading State */}
      {loading && !selectedOrderId && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}>
          <div style={{
            height: '2rem',
            width: '2rem',
            border: '2px solid #e2e8f0',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <span style={{ marginLeft: '0.5rem', color: '#64748b' }}>Loading orders...</span>
        </div>
      )}

      {/* Content */}
      <div style={{ padding: '2rem' }}>
        {selectedOrder ? (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a' }}>
                Order Details - #{selectedOrder.orderNumber || selectedOrder.id}
              </h2>
              <button
                onClick={() => setSelectedOrderId(null)}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#64748b',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                }}
              >
                ← Back to Orders
              </button>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '2rem',
              marginBottom: '2rem'
            }}>
              <div>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                  Order Information
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Order ID:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.id}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Customer:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.customerName || 'N/A'}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Email:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.customerEmail || 'N/A'}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Phone:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.customerPhone || 'N/A'}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Total Amount:</span>
                    <span style={{ fontWeight: '500', color: '#059669' }}>₹{selectedOrder.totalAmount}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                  Status & Payment
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Order Status:</span>
                    <span style={{
                      backgroundColor: selectedOrder.status === 'DELIVERED' ? '#dcfce7' :
                                     selectedOrder.status === 'PROCESSING' ? '#fef3c7' : '#fef2f2',
                      color: selectedOrder.status === 'DELIVERED' ? '#166534' :
                             selectedOrder.status === 'PROCESSING' ? '#92400e' : '#dc2626',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '6px',
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}>
                      {selectedOrder.status || 'PENDING'}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Payment Status:</span>
                    <span style={{
                      backgroundColor: selectedOrder.paymentStatus === 'PAID' ? '#dcfce7' : '#fef2f2',
                      color: selectedOrder.paymentStatus === 'PAID' ? '#166534' : '#dc2626',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '6px',
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}>
                      {selectedOrder.paymentStatus || 'PENDING'}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Payment Method:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.paymentMethod || 'N/A'}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Order Type:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.orderType || 'N/A'}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ color: '#64748b' }}>Store:</span>
                    <span style={{ fontWeight: '500' }}>{selectedOrder.storeName || 'Not Assigned'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Management Actions */}
            <div style={{ marginTop: '2rem', padding: '1.5rem', backgroundColor: '#f8fafc', borderRadius: '8px' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#374151', marginBottom: '1.5rem' }}>
                Order Management
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
                {/* Order Status Management */}
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Order Status
                  </label>
                  <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                    <button
                      onClick={() => handleUpdateOrderStatus(selectedOrder.id, 'PROCESSING')}
                      disabled={updating || selectedOrder.status === 'PROCESSING'}
                      style={{
                        backgroundColor: selectedOrder.status === 'PROCESSING' ? '#d97706' : '#f59e0b',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem 0.75rem',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: updating || selectedOrder.status === 'PROCESSING' ? 'not-allowed' : 'pointer',
                        opacity: updating || selectedOrder.status === 'PROCESSING' ? 0.6 : 1,
                        transition: 'all 0.2s'
                      }}
                    >
                      {updating ? 'Updating...' : 'Processing'}
                    </button>
                    <button
                      onClick={() => handleUpdateOrderStatus(selectedOrder.id, 'DELIVERED')}
                      disabled={updating || selectedOrder.status === 'DELIVERED'}
                      style={{
                        backgroundColor: selectedOrder.status === 'DELIVERED' ? '#047857' : '#059669',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem 0.75rem',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: updating || selectedOrder.status === 'DELIVERED' ? 'not-allowed' : 'pointer',
                        opacity: updating || selectedOrder.status === 'DELIVERED' ? 0.6 : 1,
                        transition: 'all 0.2s'
                      }}
                    >
                      {updating ? 'Updating...' : 'Delivered'}
                    </button>
                  </div>
                </div>

                {/* Payment Status Management */}
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Payment Status
                  </label>
                  <select
                    value={selectedOrder.paymentStatus || 'PENDING'}
                    onChange={(e) => handleUpdatePaymentStatus(selectedOrder.id, e.target.value as PaymentStatus)}
                    disabled={updating}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: updating ? 'not-allowed' : 'pointer',
                      opacity: updating ? 0.6 : 1
                    }}
                  >
                    <option value="PENDING">Pending</option>
                    <option value="PAID">Paid</option>
                    <option value="FAILED">Failed</option>
                    <option value="REFUNDED">Refunded</option>
                  </select>
                </div>

                {/* Store Assignment */}
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Assign to Store
                  </label>
                  <select
                    value={selectedOrder.storeId || ''}
                    onChange={(e) => e.target.value && handleAssignStore(selectedOrder.id, e.target.value)}
                    disabled={updating}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: updating ? 'not-allowed' : 'pointer',
                      opacity: updating ? 0.6 : 1
                    }}
                  >
                    <option value="">Select Store...</option>
                    {stores.map((store) => (
                      <option key={store.id} value={store.id}>
                        {store.name} - {store.location}
                      </option>
                    ))}
                  </select>
                  {selectedOrder.storeName && (
                    <div style={{ marginTop: '0.5rem', fontSize: '0.75rem', color: '#059669', fontWeight: '500' }}>
                      Currently assigned to: {selectedOrder.storeName}
                    </div>
                  )}
                </div>
              </div>

              {updating && (
                <div style={{ marginTop: '1rem', padding: '0.75rem', backgroundColor: '#fef3c7', borderRadius: '6px', fontSize: '0.875rem', color: '#92400e' }}>
                  ⏳ Updating order... Please wait.
                </div>
              )}
            </div>
          </div>
        ) : !loading && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            {orders.length === 0 ? (
              <div style={{
                padding: '3rem',
                textAlign: 'center',
                color: '#64748b'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📦</div>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '500', marginBottom: '0.5rem', color: '#374151' }}>
                  No orders found
                </h3>
                <p style={{ fontSize: '0.875rem' }}>
                  Orders will appear here when customers place them.
                </p>
              </div>
            ) : (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', fontSize: '0.875rem' }}>
                  <thead>
                    <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Order ID</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Customer</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Email</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Amount</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Status</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Store</th>
                      <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Date</th>
                      <th style={{ padding: '1rem', textAlign: 'right', fontWeight: '500', color: '#374151' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr
                        key={order.id}
                        style={{
                          borderBottom: '1px solid #f1f5f9',
                          transition: 'background-color 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#f8fafc';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <td style={{ padding: '1rem', fontWeight: '500' }}>#{order.orderNumber || order.id.slice(0, 8)}</td>
                        <td style={{ padding: '1rem' }}>{order.customerName || 'N/A'}</td>
                        <td style={{ padding: '1rem', color: '#64748b', fontSize: '0.75rem' }}>{order.customerEmail || 'N/A'}</td>
                        <td style={{ padding: '1rem', fontWeight: '500', color: '#059669' }}>₹{order.totalAmount}</td>
                        <td style={{ padding: '1rem' }}>
                          <span style={{
                            backgroundColor: order.status === 'DELIVERED' ? '#dcfce7' :
                                           order.status === 'PROCESSING' ? '#fef3c7' : '#fef2f2',
                            color: order.status === 'DELIVERED' ? '#166534' :
                                   order.status === 'PROCESSING' ? '#92400e' : '#dc2626',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '6px',
                            fontSize: '0.75rem',
                            fontWeight: '500'
                          }}>
                            {order.status || 'PENDING'}
                          </span>
                        </td>
                        <td style={{ padding: '1rem', color: '#64748b', fontSize: '0.75rem' }}>
                          {order.storeName ? (
                            <span style={{ color: '#059669', fontWeight: '500' }}>{order.storeName}</span>
                          ) : (
                            <span style={{ color: '#dc2626', fontStyle: 'italic' }}>Not Assigned</span>
                          )}
                        </td>
                        <td style={{ padding: '1rem', color: '#64748b' }}>
                          {new Date(order.createdAt).toLocaleDateString()}
                        </td>
                        <td style={{ padding: '1rem', textAlign: 'right' }}>
                          <button
                            onClick={() => handleViewOrder(order.id)}
                            style={{
                              backgroundColor: '#3b82f6',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              padding: '0.375rem 0.75rem',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#2563eb';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '#3b82f6';
                            }}
                          >
                            View Details
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
    </>
  );
}
