'use client';

import { useAuth } from '@/lib/auth/auth-context';
import { 
  ShoppingCart, 
  Package, 
  Users, 
  DollarSign,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: {
    value: string;
    isPositive: boolean;
  };
}

const StatCard = ({ title, value, icon, trend }: StatCardProps) => {
  return (
    <div style={{
      backgroundColor: 'var(--background)',
      border: '1px solid var(--border)',
      borderRadius: '8px',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1rem',
      }}>
        <h3 style={{
          fontSize: '0.875rem',
          fontWeight: '500',
          color: '#6b7280',
        }}>
          {title}
        </h3>
        <div style={{ color: '#6b7280' }}>
          {icon}
        </div>
      </div>
      
      <div style={{
        fontSize: '2rem',
        fontWeight: '700',
        color: 'var(--foreground)',
        marginBottom: '0.5rem',
      }}>
        {value}
      </div>
      
      {trend && (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.25rem',
          fontSize: '0.875rem',
          color: trend.isPositive ? '#059669' : '#dc2626',
        }}>
          {trend.isPositive ? (
            <TrendingUp style={{ height: '1rem', width: '1rem' }} />
          ) : (
            <TrendingDown style={{ height: '1rem', width: '1rem' }} />
          )}
          <span>{trend.value}</span>
        </div>
      )}
    </div>
  );
};

export default function SimpleDashboardPage() {
  const { user } = useAuth();

  return (
    <div>
      {/* Welcome Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: '700',
          color: 'var(--foreground)',
          marginBottom: '0.5rem',
        }}>
          Welcome back, {user?.name}! 👋
        </h1>
        <p style={{
          fontSize: '1rem',
          color: '#6b7280',
        }}>
          Here's what's happening with your bakery today.
        </p>
      </div>

      {/* Stats Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem',
      }}>
        <StatCard
          title="Total Sales"
          value="₹12,345"
          icon={<DollarSign style={{ height: '1.25rem', width: '1.25rem' }} />}
          trend={{ value: "+12.5%", isPositive: true }}
        />
        <StatCard
          title="Orders Today"
          value="23"
          icon={<ShoppingCart style={{ height: '1.25rem', width: '1.25rem' }} />}
          trend={{ value: "+8.2%", isPositive: true }}
        />
        <StatCard
          title="Products"
          value="156"
          icon={<Package style={{ height: '1.25rem', width: '1.25rem' }} />}
        />
        <StatCard
          title="Customers"
          value="1,234"
          icon={<Users style={{ height: '1.25rem', width: '1.25rem' }} />}
          trend={{ value: "+5.1%", isPositive: true }}
        />
      </div>

      {/* Quick Actions */}
      <div style={{
        backgroundColor: 'var(--background)',
        border: '1px solid var(--border)',
        borderRadius: '8px',
        padding: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        marginBottom: '2rem',
      }}>
        <h2 style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: 'var(--foreground)',
          marginBottom: '1rem',
        }}>
          Quick Actions
        </h2>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
        }}>
          <button
            className="btn"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              justifyContent: 'center',
            }}
            onClick={() => window.location.href = '/dashboard/products'}
          >
            <Package style={{ height: '1rem', width: '1rem' }} />
            Add Product
          </button>
          
          <button
            className="btn"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              justifyContent: 'center',
              backgroundColor: '#059669',
            }}
            onClick={() => window.location.href = '/dashboard/orders'}
          >
            <ShoppingCart style={{ height: '1rem', width: '1rem' }} />
            View Orders
          </button>
          
          <button
            className="btn"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              justifyContent: 'center',
              backgroundColor: '#7c3aed',
            }}
            onClick={() => window.location.href = '/dashboard/reports'}
          >
            <TrendingUp style={{ height: '1rem', width: '1rem' }} />
            View Reports
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div style={{
        backgroundColor: 'var(--background)',
        border: '1px solid var(--border)',
        borderRadius: '8px',
        padding: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      }}>
        <h2 style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: 'var(--foreground)',
          marginBottom: '1rem',
        }}>
          Recent Activity
        </h2>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem',
            padding: '0.75rem',
            backgroundColor: '#f9fafb',
            borderRadius: '6px',
          }}>
            <div style={{
              backgroundColor: '#059669',
              borderRadius: '50%',
              padding: '0.5rem',
              color: 'white',
            }}>
              <ShoppingCart style={{ height: '1rem', width: '1rem' }} />
            </div>
            <div style={{ flex: 1 }}>
              <p style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                New order #1234 received
              </p>
              <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                2 minutes ago
              </p>
            </div>
          </div>
          
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem',
            padding: '0.75rem',
            backgroundColor: '#f9fafb',
            borderRadius: '6px',
          }}>
            <div style={{
              backgroundColor: '#3b82f6',
              borderRadius: '50%',
              padding: '0.5rem',
              color: 'white',
            }}>
              <Package style={{ height: '1rem', width: '1rem' }} />
            </div>
            <div style={{ flex: 1 }}>
              <p style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                Product "Chocolate Cake" updated
              </p>
              <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                15 minutes ago
              </p>
            </div>
          </div>
          
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem',
            padding: '0.75rem',
            backgroundColor: '#f9fafb',
            borderRadius: '6px',
          }}>
            <div style={{
              backgroundColor: '#f59e0b',
              borderRadius: '50%',
              padding: '0.5rem',
              color: 'white',
            }}>
              <Users style={{ height: '1rem', width: '1rem' }} />
            </div>
            <div style={{ flex: 1 }}>
              <p style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                New customer registered
              </p>
              <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                1 hour ago
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
