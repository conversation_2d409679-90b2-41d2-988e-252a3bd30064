'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import {
  ShoppingCart,
  CreditCard,
  Package,
  AlertTriangle,
  IndianRupee
} from 'lucide-react';
import { authenticatedFetch } from '@/lib/auth/api-client';

// Colors for the pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const StatCard = ({
  title,
  value,
  icon,
  description,
  trend,
  trendValue = '0'
}: {
  title: string;
  value: string;
  icon: React.ReactNode;
  description: string;
  trend: 'up' | 'down' | 'neutral';
  trendValue?: string;
}) => {
  return (
    <div style={{
      borderRadius: '8px',
      border: '1px solid var(--border)',
      backgroundColor: 'var(--background)',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        <h3 style={{
          fontSize: '0.875rem',
          fontWeight: '500',
          color: '#6b7280',
        }}>
          {title}
        </h3>
        <div style={{
          borderRadius: '50%',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          padding: '0.5rem',
          color: 'var(--primary)',
        }}>
          {icon}
        </div>
      </div>
      <div style={{ marginTop: '0.5rem' }}>
        <p style={{
          fontSize: '1.5rem',
          fontWeight: '700',
        }}>
          {value}
        </p>
        <p style={{
          fontSize: '0.75rem',
          color: '#6b7280',
        }}>
          {description}
        </p>
      </div>
      <div style={{
        marginTop: '0.5rem',
        fontSize: '0.75rem',
        color: trend === 'up' ? '#059669' : trend === 'down' ? '#dc2626' : '#6b7280',
      }}>
        {trend === 'up' ? '↑' : trend === 'down' ? '↓' : '→'} {' '}
        {trend === 'up'
          ? `+${trendValue}% from last month`
          : trend === 'down'
            ? `-${trendValue}% from last month`
            : 'No change from last month'}
      </div>
    </div>
  );
};

// Define types for dashboard data
interface DashboardStat {
  value: number;
  trend: 'up' | 'down' | 'neutral';
  trendValue: string;
}

interface DashboardStats {
  totalSales: DashboardStat;
  totalPurchases: DashboardStat;
  lowStockItems: DashboardStat;
  totalProducts: DashboardStat;
}

interface SalesDataPoint {
  name: string;
  sales: number;
}

interface CategoryDataPoint {
  name: string;
  value: number;
}

interface DashboardData {
  stats: DashboardStats;
  salesData: SalesDataPoint[];
  productCategoryData: CategoryDataPoint[];
}

interface RecentTransaction {
  id: string;
  type: string;
  storeName: string;
  amount: number;
  date: string;
  status: string;
}

// Utility function to format currency
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
  }).format(amount);
}

export default function DashboardPage() {
  const [isClient, setIsClient] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dbUnavailable, setDbUnavailable] = useState(false);
  const [dbErrorMessage, setDbErrorMessage] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([]);

  useEffect(() => {
    setIsClient(true);

    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        setDbUnavailable(false);
        setDbErrorMessage(null);

        // Fetch dashboard data
        const response = await authenticatedFetch('/api/dashboard');
        const data = await response.json();

        if (!response.ok) {
          // Check if it's a database connection error
          if (response.status === 503 && data.status === 'DB_UNAVAILABLE') {
            setDbUnavailable(true);
            setDbErrorMessage(data.message);
            return;
          }
          throw new Error('Failed to fetch dashboard data');
        }

        setDashboardData(data);

        // Fetch recent transactions
        const transactionsResponse = await authenticatedFetch('/api/transactions?limit=5');

        if (!transactionsResponse.ok) {
          // Don't throw here, just log the error
          console.error('Failed to fetch recent transactions');
        } else {
          const transactionsData = await transactionsResponse.json();
          setRecentTransactions(transactionsData);
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);



  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div>
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: '700',
          letterSpacing: '-0.025em',
        }}>
          Dashboard
        </h1>
        <p style={{
          color: '#6b7280',
        }}>
          Overview of your bakery business
        </p>
      </div>



      {loading ? (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem',
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{
              animation: 'spin 1s linear infinite',
              borderRadius: '50%',
              height: '3rem',
              width: '3rem',
              borderBottom: '2px solid var(--primary)',
              margin: '0 auto 1rem auto',
            }}></div>
            <p>Loading dashboard data...</p>
          </div>
        </div>
      ) : dbUnavailable ? (
        <div style={{
          padding: '2rem',
          textAlign: 'center',
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          color: '#dc2626',
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem' }}>
            Database Connection Error
          </h3>
          <p style={{ marginBottom: '1rem' }}>
            {dbErrorMessage || 'Unable to connect to the database. Please check your connection and try again.'}
          </p>
          <button
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: 'var(--primary)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
            }}
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      ) : error ? (
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
          <button
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: 'var(--primary)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
            }}
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gap: '1.5rem',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        }}>
          <StatCard
            title="Total Sales"
            value={formatCurrency(dashboardData?.stats.totalSales.value || 0)}
            icon={<ShoppingCart style={{ height: '1rem', width: '1rem' }} />}
            description="Total sales this month"
            trend={dashboardData?.stats.totalSales.trend || 'neutral'}
            trendValue={dashboardData?.stats.totalSales.trendValue || '0'}
          />
          <StatCard
            title="Total Purchases"
            value={formatCurrency(dashboardData?.stats.totalPurchases.value || 0)}
            icon={<CreditCard style={{ height: '1rem', width: '1rem' }} />}
            description="Total purchases this month"
            trend={dashboardData?.stats.totalPurchases.trend || 'neutral'}
            trendValue={dashboardData?.stats.totalPurchases.trendValue || '0'}
          />
          <StatCard
            title="Low Stock Items"
            value={String(dashboardData?.stats.lowStockItems.value || 0)}
            icon={<AlertTriangle style={{ height: '1rem', width: '1rem' }} />}
            description="Products below threshold"
            trend={dashboardData?.stats.lowStockItems.trend || 'neutral'}
            trendValue={dashboardData?.stats.lowStockItems.trendValue || '0'}
          />
          <StatCard
            title="Total Products"
            value={String(dashboardData?.stats.totalProducts.value || 0)}
            icon={<Package style={{ height: '1rem', width: '1rem' }} />}
            description="Active products"
            trend={dashboardData?.stats.totalProducts.trend || 'neutral'}
            trendValue={dashboardData?.stats.totalProducts.trendValue || '0'}
          />
        </div>
      )}

      {!loading && !error && dashboardData && (
        <div style={{
          display: 'grid',
          gap: '1.5rem',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        }}>
          <div style={{
            borderRadius: '8px',
            border: '1px solid var(--border)',
            backgroundColor: 'var(--background)',
            padding: '1.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}>
            <h3 style={{ marginBottom: '1rem', fontSize: '1.125rem', fontWeight: '500' }}>
              Monthly Sales
            </h3>
            {isClient && (
              <div style={{ height: '20rem' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={dashboardData.salesData || []}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Bar dataKey="sales" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>

          <div style={{
            borderRadius: '8px',
            border: '1px solid var(--border)',
            backgroundColor: 'var(--background)',
            padding: '1.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}>
            <h3 style={{ marginBottom: '1rem', fontSize: '1.125rem', fontWeight: '500' }}>
              Product Categories
            </h3>
            {isClient && (
              <div style={{ height: '20rem' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={dashboardData.productCategoryData || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dashboardData.productCategoryData && dashboardData.productCategoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>
        </div>
      )}

      {!loading && !error && (
        <div style={{
          borderRadius: '8px',
          border: '1px solid var(--border)',
          backgroundColor: 'var(--background)',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.125rem', fontWeight: '500' }}>
            Recent Transactions
          </h3>
          <div style={{ overflowX: 'auto' }}>
            {recentTransactions.length > 0 ? (
              <table style={{ width: '100%', fontSize: '0.875rem' }}>
                <thead>
                  <tr style={{ borderBottom: '1px solid var(--border)' }}>
                    <th style={{ padding: '0.5rem 1rem', textAlign: 'left', fontWeight: '500' }}>ID</th>
                    <th style={{ padding: '0.5rem 1rem', textAlign: 'left', fontWeight: '500' }}>Type</th>
                    <th style={{ padding: '0.5rem 1rem', textAlign: 'left', fontWeight: '500' }}>Store</th>
                    <th style={{ padding: '0.5rem 1rem', textAlign: 'left', fontWeight: '500' }}>Amount</th>
                    <th style={{ padding: '0.5rem 1rem', textAlign: 'left', fontWeight: '500' }}>Date</th>
                    <th style={{ padding: '0.5rem 1rem', textAlign: 'left', fontWeight: '500' }}>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {recentTransactions.map((transaction) => (
                    <tr key={transaction.id} style={{ borderBottom: '1px solid var(--border)' }}>
                      <td style={{ padding: '0.5rem 1rem' }}>#{transaction.id}</td>
                      <td style={{ padding: '0.5rem 1rem' }}>{transaction.type}</td>
                      <td style={{ padding: '0.5rem 1rem' }}>{transaction.storeName}</td>
                      <td style={{ padding: '0.5rem 1rem' }}>{formatCurrency(transaction.amount)}</td>
                      <td style={{ padding: '0.5rem 1rem' }}>{new Date(transaction.date).toLocaleDateString()}</td>
                      <td style={{ padding: '0.5rem 1rem' }}>
                        <span style={{
                          borderRadius: '9999px',
                          padding: '0.25rem 0.5rem',
                          fontSize: '0.75rem',
                          backgroundColor: transaction.status === 'Completed'
                            ? '#dcfce7'
                            : transaction.status === 'Pending'
                              ? '#fef3c7'
                              : '#fee2e2',
                          color: transaction.status === 'Completed'
                            ? '#166534'
                            : transaction.status === 'Pending'
                              ? '#92400e'
                              : '#991b1b',
                        }}>
                          {transaction.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div style={{
                padding: '2rem',
                textAlign: 'center',
                color: '#6b7280',
              }}>
                No recent transactions found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
