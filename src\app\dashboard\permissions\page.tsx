'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Shield, Users, Settings, Save, X, Check } from 'lucide-react';
import { PermissionGuard } from '@/components/permissions/PermissionGuard';
import { ROLE_PERMISSIONS, Permission, UserRole } from '@/lib/permissions/permissions';
import { apiClient } from '@/lib/auth/api-client';

type User = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  storeId: string | null;
  store?: {
    name: string;
  };
};

export default function PermissionsPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [customPermissions, setCustomPermissions] = useState<Permission[]>([]);
  const [savingPermissions, setSavingPermissions] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await apiClient.get('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleManagePermissions = async (user: User) => {
    setSelectedUser(user);
    setShowPermissionModal(true);

    try {
      // Fetch custom permissions for this user
      const response = await apiClient.get(`/api/permissions?userEmail=${user.email}`);
      if (response.ok) {
        const data = await response.json();
        setCustomPermissions(data.permissions || []);
      } else {
        // Fallback to role-based permissions
        setCustomPermissions(ROLE_PERMISSIONS[user.role] || []);
      }
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      // Fallback to role-based permissions
      setCustomPermissions(ROLE_PERMISSIONS[user.role] || []);
    }

    // Set role-based permissions for comparison
    setUserPermissions(ROLE_PERMISSIONS[user.role] || []);
  };

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    try {
      const response = await apiClient.put(`/api/users/${userId}`, { role: newRole });
      if (response.ok) {
        await fetchUsers();
      }
    } catch (error) {
      console.error('Error updating user role:', error);
    }
  };

  const handlePermissionToggle = async (permission: Permission, grant: boolean) => {
    if (!selectedUser) return;

    try {
      setSavingPermissions(true);

      const response = await apiClient.put('/api/permissions', {
        userEmail: selectedUser.email,
        permission,
        grant
      });

      if (response.ok) {
        // Update local state
        if (grant) {
          setCustomPermissions(prev => [...prev.filter(p => p !== permission), permission]);
        } else {
          setCustomPermissions(prev => prev.filter(p => p !== permission));
        }
      } else {
        alert('Failed to update permission');
      }
    } catch (error) {
      console.error('Error updating permission:', error);
      alert('Error updating permission');
    } finally {
      setSavingPermissions(false);
    }
  };

  const handleSaveAllPermissions = async () => {
    if (!selectedUser) return;

    try {
      setSavingPermissions(true);

      const response = await apiClient.post('/api/permissions', {
        userEmail: selectedUser.email,
        permissions: customPermissions
      });

      if (response.ok) {
        alert('Permissions saved successfully!');
        setShowPermissionModal(false);
        await fetchUsers(); // Refresh user list
      } else {
        alert('Failed to save permissions');
      }
    } catch (error) {
      console.error('Error saving permissions:', error);
      alert('Error saving permissions');
    } finally {
      setSavingPermissions(false);
    }
  };

  const permissionCategories = {
    'Dashboard': ['dashboard.view'],
    'Products': ['products.view', 'products.create', 'products.edit', 'products.delete'],
    'Categories': ['categories.view', 'categories.create', 'categories.edit', 'categories.delete'],
    'Orders': ['orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.view_all_stores'],
    'Customers': ['customers.view', 'customers.create', 'customers.edit', 'customers.delete', 'customers.view_all_stores'],
    'Users': ['users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles'],
    'Stores': ['stores.view', 'stores.create', 'stores.edit', 'stores.delete', 'stores.manage_access'],
    'Inventory': ['inventory.view', 'inventory.edit', 'inventory.transfer', 'inventory.view_all_stores'],
    'Raw Materials': ['raw_materials.view', 'raw_materials.create', 'raw_materials.edit', 'raw_materials.delete'],
    'Recipes': ['recipes.view', 'recipes.create', 'recipes.edit', 'recipes.delete'],
    'Production': ['production.view', 'production.create', 'production.edit', 'production.delete'],
    'Sales': ['sales.view', 'sales.create', 'sales.process'],
    'Purchases': ['purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete'],
    'Expenses': ['expenses.view', 'expenses.create', 'expenses.edit', 'expenses.delete'],
    'Wastage': ['wastage.view', 'wastage.create', 'wastage.edit', 'wastage.delete'],
    'Reports': ['reports.view', 'reports.sales', 'reports.inventory', 'reports.financial', 'reports.production', 'reports.all_stores'],
    'Settings': ['settings.view', 'settings.edit', 'settings.system']
  };

  return (
    <PermissionGuard
      permission="users.manage_roles"
      fallback={
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '400px',
          textAlign: 'center',
          padding: '2rem'
        }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔒</div>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#374151',
            marginBottom: '0.5rem'
          }}>
            Access Denied
          </h2>
          <p style={{ color: '#6b7280', fontSize: '1rem' }}>
            You don't have permission to manage user permissions.
          </p>
        </div>
      }
      showFallback={true}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', padding: '1.5rem' }}>
        {/* Header */}
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: '700',
              color: '#111827',
              marginBottom: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            }}>
              <Shield style={{ height: '2rem', width: '2rem', color: '#6366f1' }} />
              Permission Management
            </h1>
            <p style={{ color: '#6b7280', fontSize: '1rem' }}>
              Manage user roles and permissions across the system
            </p>
          </div>
        </div>

        {/* Role Overview Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          {Object.entries(ROLE_PERMISSIONS).map(([role, permissions]) => (
            <div
              key={role}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                border: '1px solid #e5e7eb',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}
            >
              <h3 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '0.5rem'
              }}>
                {role.replace('_', ' ')}
              </h3>
              <p style={{
                color: '#6b7280',
                fontSize: '0.875rem',
                marginBottom: '1rem'
              }}>
                {permissions.length} permissions
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.75rem',
                color: '#059669',
                backgroundColor: '#ecfdf5',
                padding: '0.25rem 0.5rem',
                borderRadius: '6px',
                width: 'fit-content'
              }}>
                <Check style={{ height: '0.75rem', width: '0.75rem' }} />
                Active Role
              </div>
            </div>
          ))}
        </div>

        {/* Users Table */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          overflow: 'hidden'
        }}>
          <div style={{
            padding: '1.5rem',
            borderBottom: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb'
          }}>
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: '#111827',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <Users style={{ height: '1.25rem', width: '1.25rem' }} />
              User Permissions
            </h2>
          </div>

          {loading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '3rem'
            }}>
              <div>Loading users...</div>
            </div>
          ) : (
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ backgroundColor: '#f9fafb' }}>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: '#374151',
                      borderBottom: '1px solid #e5e7eb'
                    }}>
                      User
                    </th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: '#374151',
                      borderBottom: '1px solid #e5e7eb'
                    }}>
                      Role
                    </th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: '#374151',
                      borderBottom: '1px solid #e5e7eb'
                    }}>
                      Store
                    </th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: '#374151',
                      borderBottom: '1px solid #e5e7eb'
                    }}>
                      Permissions
                    </th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'center',
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: '#374151',
                      borderBottom: '1px solid #e5e7eb'
                    }}>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} style={{ borderBottom: '1px solid #f3f4f6' }}>
                      <td style={{ padding: '1rem' }}>
                        <div>
                          <div style={{
                            fontWeight: '500',
                            color: '#111827',
                            marginBottom: '0.25rem'
                          }}>
                            {user.name}
                          </div>
                          <div style={{
                            fontSize: '0.875rem',
                            color: '#6b7280'
                          }}>
                            {user.email}
                          </div>
                        </div>
                      </td>
                      <td style={{ padding: '1rem' }}>
                        <select
                          value={user.role}
                          onChange={(e) => handleRoleChange(user.id, e.target.value as UserRole)}
                          style={{
                            padding: '0.5rem',
                            borderRadius: '6px',
                            border: '1px solid #d1d5db',
                            fontSize: '0.875rem'
                          }}
                        >
                          <option value="ADMIN">Admin</option>
                          <option value="WAREHOUSE_MANAGER">Warehouse Manager</option>
                          <option value="STORE_MANAGER">Store Manager</option>
                          <option value="STAFF">Staff</option>
                        </select>
                      </td>
                      <td style={{ padding: '1rem' }}>
                        <span style={{
                          fontSize: '0.875rem',
                          color: '#6b7280'
                        }}>
                          {user.store?.name || 'All Stores'}
                        </span>
                      </td>
                      <td style={{ padding: '1rem' }}>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                          <span style={{
                            fontSize: '0.875rem',
                            color: '#059669',
                            backgroundColor: '#ecfdf5',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '6px',
                            width: 'fit-content'
                          }}>
                            {ROLE_PERMISSIONS[user.role]?.length || 0} role permissions
                          </span>
                          <span style={{
                            fontSize: '0.75rem',
                            color: '#6b7280'
                          }}>
                            + custom grants
                          </span>
                        </div>
                      </td>
                      <td style={{ padding: '1rem', textAlign: 'center' }}>
                        <Button
                          variant="outline"
                          onClick={() => handleManagePermissions(user)}
                          style={{
                            fontSize: '0.875rem',
                            padding: '0.5rem 1rem'
                          }}
                        >
                          <Settings style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                          Manage
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Permission Management Modal */}
        {showPermissionModal && selectedUser && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
            padding: '1rem'
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '2rem',
              maxWidth: '800px',
              width: '100%',
              maxHeight: '80vh',
              overflowY: 'auto',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            }}>
              {/* Modal Header */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '2rem',
                paddingBottom: '1rem',
                borderBottom: '1px solid #e5e7eb'
              }}>
                <div>
                  <h2 style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: '#111827',
                    marginBottom: '0.5rem'
                  }}>
                    Permission Details
                  </h2>
                  <p style={{
                    color: '#6b7280',
                    fontSize: '0.875rem'
                  }}>
                    {selectedUser.name} ({selectedUser.role})
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setShowPermissionModal(false)}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '8px',
                    border: '1px solid #e5e7eb'
                  }}
                >
                  <X style={{ height: '1rem', width: '1rem' }} />
                </Button>
              </div>

              {/* Permission Categories */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '1.5rem'
              }}>
                {Object.entries(permissionCategories).map(([category, permissions]) => (
                  <div
                    key={category}
                    style={{
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px',
                      padding: '1rem'
                    }}
                  >
                    <h3 style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: '1rem'
                    }}>
                      {category}
                    </h3>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                      {permissions.map((permission) => {
                        const hasPermission = customPermissions.includes(permission as Permission);
                        const isRoleDefault = userPermissions.includes(permission as Permission);

                        return (
                          <div
                            key={permission}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.75rem',
                              padding: '0.75rem',
                              borderRadius: '8px',
                              backgroundColor: hasPermission ? '#ecfdf5' : '#f9fafb',
                              border: hasPermission ? '1px solid #d1fae5' : '1px solid #e5e7eb',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onClick={() => handlePermissionToggle(permission as Permission, !hasPermission)}
                          >
                            {/* Interactive Checkbox */}
                            <div style={{
                              width: '1.25rem',
                              height: '1.25rem',
                              borderRadius: '4px',
                              backgroundColor: hasPermission ? '#10b981' : 'white',
                              border: hasPermission ? '2px solid #10b981' : '2px solid #d1d5db',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}>
                              {hasPermission && (
                                <Check style={{
                                  height: '0.875rem',
                                  width: '0.875rem',
                                  color: 'white',
                                  strokeWidth: 3
                                }} />
                              )}
                            </div>

                            {/* Permission Details */}
                            <div style={{ flex: 1 }}>
                              <div style={{
                                fontSize: '0.875rem',
                                fontWeight: '500',
                                color: hasPermission ? '#065f46' : '#374151',
                                marginBottom: '0.125rem'
                              }}>
                                {permission.replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </div>
                              <div style={{
                                fontSize: '0.75rem',
                                color: '#6b7280',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem'
                              }}>
                                {isRoleDefault && (
                                  <span style={{
                                    backgroundColor: '#dbeafe',
                                    color: '#1e40af',
                                    padding: '0.125rem 0.375rem',
                                    borderRadius: '4px',
                                    fontSize: '0.625rem',
                                    fontWeight: '500'
                                  }}>
                                    Role Default
                                  </span>
                                )}
                                {hasPermission && !isRoleDefault && (
                                  <span style={{
                                    backgroundColor: '#fef3c7',
                                    color: '#92400e',
                                    padding: '0.125rem 0.375rem',
                                    borderRadius: '4px',
                                    fontSize: '0.625rem',
                                    fontWeight: '500'
                                  }}>
                                    Custom Grant
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Status Indicator */}
                            <div style={{
                              width: '0.5rem',
                              height: '0.5rem',
                              borderRadius: '50%',
                              backgroundColor: hasPermission ? '#10b981' : '#d1d5db'
                            }} />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>

              {/* Modal Footer */}
              <div style={{
                marginTop: '2rem',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div style={{
                  fontSize: '0.875rem',
                  color: '#6b7280'
                }}>
                  {customPermissions.length} permissions granted
                  {savingPermissions && (
                    <span style={{ marginLeft: '0.5rem', color: '#059669' }}>
                      • Saving...
                    </span>
                  )}
                </div>

                <div style={{ display: 'flex', gap: '1rem' }}>
                  <Button
                    variant="outline"
                    onClick={() => setShowPermissionModal(false)}
                    disabled={savingPermissions}
                    style={{
                      backgroundColor: '#f3f4f6',
                      color: '#374151',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem 1rem'
                    }}
                  >
                    Close
                  </Button>

                  <Button
                    onClick={handleSaveAllPermissions}
                    disabled={savingPermissions}
                    style={{
                      backgroundColor: '#10b981',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem 1rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <Save style={{ height: '1rem', width: '1rem' }} />
                    {savingPermissions ? 'Saving...' : 'Save All Changes'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </PermissionGuard>
  );
}
