'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Minus, Trash, ShoppingCart, CreditCard, Printer, Receipt, Search, Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { CustomerSelector } from '@/components/pos/customer-selector';
import { CategorySelector } from '@/components/pos/category-selector';
import { ProductCard } from '@/components/pos/product-card';
import { CartItemComponent } from '@/components/pos/cart-item';
import { PaymentModal } from '@/components/pos/payment-modal';
import { Receipt as ReceiptComponent } from '@/components/pos/receipt';

// Product interface
interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  description: string;
  image: string;
  inStock: boolean;
}

// Product categories
const productCategories = [
  'All',
  'Bread',
  'Cakes',
  'Pastries',
  'Cookies',
];

// Payment methods
const paymentMethods = [
  { id: 'cash', name: 'Cash' },
  { id: 'card', name: 'Credit/Debit Card' },
  { id: 'mobile', name: 'Mobile Payment' },
];

interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  discount?: number;
  notes?: string;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  loyaltyPoints?: number;
}

export default function POSPage() {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [loyaltyDiscount, setLoyaltyDiscount] = useState(0);
  const [paymentDetails, setPaymentDetails] = useState<{
    method: string;
    amountPaid: number;
    change: number;
    reference?: string;
  } | null>(null);
  const [receiptNumber, setReceiptNumber] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch products and set isClient to true when component mounts
  useEffect(() => {
    setIsClient(true);

    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/pos/products');

        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }

        const data = await response.json();
        setProducts(data);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const handleAddToCart = (product: {
    id: string;
    name: string;
    price: number;
    category: string;
    description?: string;
    image?: string;
    inStock?: boolean;
  }) => {
    if (product.inStock === false) return;

    const existingItem = cart.find(item => item.productId === product.id);

    if (existingItem) {
      setCart(cart.map(item =>
        item.productId === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      const newItem: CartItem = {
        id: Date.now().toString(),
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        discount: 0,
        notes: ''
      };
      setCart([...cart, newItem]);
    }
  };

  const handleUpdateQuantity = (id: string, change: number) => {
    const item = cart.find(item => item.id === id);
    if (!item) return;

    const newQuantity = item.quantity + change;

    if (newQuantity <= 0) {
      setCart(cart.filter(item => item.id !== id));
    } else {
      setCart(cart.map(item =>
        item.id === id
          ? { ...item, quantity: newQuantity }
          : item
      ));
    }
  };

  const handleRemoveItem = (id: string) => {
    setCart(cart.filter(item => item.id !== id));
  };

  const handleUpdateDiscount = (id: string, discount: number) => {
    setCart(cart.map(item =>
      item.id === id
        ? { ...item, discount }
        : item
    ));
  };

  const handleUpdateNotes = (id: string, notes: string) => {
    setCart(cart.map(item =>
      item.id === id
        ? { ...item, notes }
        : item
    ));
  };

  const handleClearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
    setLoyaltyDiscount(0);
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      alert('Cart is empty');
      return;
    }

    setShowPaymentModal(true);
  };

  const handleCompletePayment = async (details: {
    method: string;
    amountPaid: number;
    change: number;
    reference?: string;
  }) => {
    setIsSubmitting(true);

    try {
      // Create the sale data
      const saleData = {
        items: cart.map(item => ({
          productId: item.productId,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          discount: item.discount || 0,
          notes: item.notes || ''
        })),
        customer: selectedCustomer,
        discount: loyaltyDiscount + calculateItemDiscounts(),
        tax: calculateTax(),
        paymentDetails: details
      };

      // Send the sale data to the API
      const response = await fetch('/api/pos/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(saleData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create sale');
      }

      const saleResponse = await response.json();

      // Set payment details and receipt number
      setPaymentDetails(details);
      setReceiptNumber(saleResponse.receiptNumber);

      // Close payment modal and show receipt
      setShowPaymentModal(false);
      setShowReceiptModal(true);
    } catch (err) {
      console.error('Error creating sale:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while processing payment');
      alert('Payment failed: ' + (err instanceof Error ? err.message : 'An error occurred'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFinishSale = () => {
    // Reset everything for a new sale
    setCart([]);
    setSelectedCustomer(null);
    setLoyaltyDiscount(0);
    setPaymentDetails(null);
    setShowReceiptModal(false);
  };

  const calculateItemDiscounts = () => {
    return cart.reduce((sum, item) => sum + (item.discount || 0), 0);
  };

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const calculateTax = () => {
    return (calculateSubtotal() - calculateItemDiscounts() - loyaltyDiscount) * 0.1; // 10% tax
  };

  const calculateTotal = () => {
    return calculateSubtotal() - calculateItemDiscounts() - loyaltyDiscount + calculateTax();
  };

  const handlePrintReceipt = () => {
    window.print();
  };

  // Filter products based on category and search term
  const filteredProducts = products.filter((product: Product) => {
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Point of Sale
          </h1>
          <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
            Process sales and manage transactions • {cart.length} items in cart
          </p>
        </div>
      </div>

      {/* Content */}
      <div style={{ padding: '2rem' }}>
        <div style={{
          display: 'grid',
          gap: '2rem',
          gridTemplateColumns: '1fr 400px'
        }}>
          {/* Products Section */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* Search and Filter */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              padding: '1.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                <div style={{ position: 'relative', flex: 1 }}>
                  <Search style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    height: '16px',
                    width: '16px',
                    color: '#64748b'
                  }} />
                  <input
                    placeholder="Search products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    minWidth: '150px'
                  }}
                >
                  <option value="All">All Categories</option>
                  {productCategories.filter(cat => cat !== 'All').map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>


            {/* Products Grid */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              padding: '1.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              {loading ? (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '3rem',
                  textAlign: 'center',
                  color: '#64748b'
                }}>
                  <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', marginBottom: '1rem', color: '#3b82f6' }} />
                  <p>Loading products...</p>
                </div>
              ) : error ? (
                <div style={{
                  padding: '3rem',
                  textAlign: 'center',
                  color: '#dc2626'
                }}>
                  <p style={{ marginBottom: '1rem' }}>{error}</p>
                  <button
                    onClick={() => {
                      setLoading(true);
                      setError(null);
                      fetch('/api/pos/products')
                        .then(res => res.json())
                        .then(data => setProducts(data))
                        .catch(err => setError(err.message))
                        .finally(() => setLoading(false));
                    }}
                    style={{
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem 1rem',
                      fontSize: '0.875rem',
                      cursor: 'pointer'
                    }}
                  >
                    Retry
                  </button>
                </div>
              ) : filteredProducts.length > 0 ? (
                <div>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                    gap: '1rem'
                  }}>
                    {filteredProducts.map(product => (
                      <div
                        key={product.id}
                        onClick={() => {
                          console.log('Adding product to cart:', product.name);
                          handleAddToCart(product);
                        }}
                        style={{
                          border: '2px solid #3b82f6',
                          borderRadius: '8px',
                          padding: '1rem',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          backgroundColor: 'white'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#1d4ed8';
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 8px 16px -4px rgba(59, 130, 246, 0.3)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#3b82f6';
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        <div style={{
                          width: '100%',
                          height: '120px',
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px',
                          marginBottom: '0.75rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '3rem',
                          border: '1px solid #e2e8f0'
                        }}>
                          {product.category === 'Cakes' ? '🎂' :
                           product.category === 'Flowers' ? '🌹' :
                           product.category === 'Gifts' ? '🎁' :
                           product.category === 'Combos' ? '🎉' : '🧁'}
                        </div>
                        <h3 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem', color: '#0f172a' }}>
                          {product.name}
                        </h3>
                        <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.5rem' }}>
                          {product.category}
                        </p>
                        <p style={{ fontSize: '1.25rem', fontWeight: '700', color: '#059669' }}>
                          ₹{product.price}
                        </p>
                        <div style={{
                          marginTop: '0.5rem',
                          padding: '0.25rem 0.5rem',
                          backgroundColor: '#dcfce7',
                          borderRadius: '4px',
                          fontSize: '0.75rem',
                          color: '#166534',
                          textAlign: 'center'
                        }}>
                          {product.inStock ? 'In Stock' : 'Out of Stock'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div style={{
                  padding: '3rem',
                  textAlign: 'center',
                  color: '#64748b'
                }}>
                  <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔍</div>
                  <p>No products found. Try a different search or category.</p>
                </div>
              )}
            </div>
          </div>

          {/* Cart Section */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            display: 'flex',
            flexDirection: 'column',
            height: 'fit-content',
            minHeight: '600px'
          }}>
            {/* Cart Header */}
            <div style={{
              borderBottom: '1px solid #e2e8f0',
              padding: '1.5rem',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#0f172a' }}>
                Current Sale
              </h2>
              <button
                onClick={handleClearCart}
                disabled={cart.length === 0}
                style={{
                  backgroundColor: cart.length === 0 ? '#f1f5f9' : '#fef2f2',
                  color: cart.length === 0 ? '#94a3b8' : '#dc2626',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 0.75rem',
                  fontSize: '0.75rem',
                  cursor: cart.length === 0 ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}
              >
                <Trash style={{ height: '14px', width: '14px' }} />
                Clear
              </button>
            </div>

            {/* Cart Items */}
            <div style={{ flex: 1, padding: '1.5rem', overflowY: 'auto' }}>
              {cart.length === 0 ? (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '200px',
                  textAlign: 'center',
                  color: '#64748b'
                }}>
                  <ShoppingCart style={{ height: '2rem', width: '2rem', marginBottom: '0.5rem' }} />
                  <p style={{ marginBottom: '0.25rem' }}>Cart is empty</p>
                  <p style={{ fontSize: '0.75rem' }}>Add products by clicking on them</p>
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {cart.map(item => (
                    <div
                      key={item.id}
                      style={{
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        padding: '1rem',
                        backgroundColor: '#f8fafc'
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>
                        <h4 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#0f172a' }}>
                          {item.name}
                        </h4>
                        <button
                          onClick={() => handleRemoveItem(item.id)}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#dc2626',
                            border: 'none',
                            cursor: 'pointer',
                            padding: '0.25rem'
                          }}
                        >
                          <Trash style={{ height: '14px', width: '14px' }} />
                        </button>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                          <button
                            onClick={() => handleUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                            style={{
                              backgroundColor: '#f1f5f9',
                              border: 'none',
                              borderRadius: '4px',
                              width: '24px',
                              height: '24px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer'
                            }}
                          >
                            <Minus style={{ height: '12px', width: '12px' }} />
                          </button>
                          <span style={{ fontSize: '0.875rem', fontWeight: '500', minWidth: '20px', textAlign: 'center' }}>
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                            style={{
                              backgroundColor: '#f1f5f9',
                              border: 'none',
                              borderRadius: '4px',
                              width: '24px',
                              height: '24px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer'
                            }}
                          >
                            <Plus style={{ height: '12px', width: '12px' }} />
                          </button>
                        </div>
                        <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#059669' }}>
                          ₹{(item.price * item.quantity).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Cart Summary */}
            <div style={{
              borderTop: '1px solid #e2e8f0',
              padding: '1.5rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', marginBottom: '1rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.875rem' }}>
                  <span style={{ color: '#64748b' }}>Subtotal</span>
                  <span>₹{calculateSubtotal().toFixed(2)}</span>
                </div>
                {(calculateItemDiscounts() > 0 || loyaltyDiscount > 0) && (
                  <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.875rem', color: '#059669' }}>
                    <span>Discounts</span>
                    <span>-₹{(calculateItemDiscounts() + loyaltyDiscount).toFixed(2)}</span>
                  </div>
                )}
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.875rem' }}>
                  <span style={{ color: '#64748b' }}>Tax (10%)</span>
                  <span>₹{calculateTax().toFixed(2)}</span>
                </div>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '1rem',
                  fontWeight: '600',
                  paddingTop: '0.5rem',
                  borderTop: '1px solid #e2e8f0'
                }}>
                  <span>Total</span>
                  <span style={{ color: '#059669' }}>₹{calculateTotal().toFixed(2)}</span>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                disabled={cart.length === 0}
                style={{
                  width: '100%',
                  backgroundColor: cart.length === 0 ? '#f1f5f9' : '#3b82f6',
                  color: cart.length === 0 ? '#94a3b8' : 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.875rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: cart.length === 0 ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '0.5rem',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (cart.length > 0) {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (cart.length > 0) {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                  }
                }}
              >
                <CreditCard style={{ height: '16px', width: '16px' }} />
                Checkout
              </button>
            </div>
          </div>
        </div>
      </div>

      {isClient && showPaymentModal && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          totalAmount={calculateTotal()}
          onCompletePayment={handleCompletePayment}
          customer={selectedCustomer}
          isSubmitting={isSubmitting}
        />
      )}

      {isClient && showReceiptModal && paymentDetails && (
        <ReceiptComponent
          items={cart}
          customer={selectedCustomer}
          paymentDetails={paymentDetails}
          subtotal={calculateSubtotal()}
          tax={calculateTax()}
          discount={calculateItemDiscounts() + loyaltyDiscount}
          total={calculateTotal()}
          receiptNumber={receiptNumber}
          date={new Date().toISOString()}
          onClose={handleFinishSale}
          onPrint={handlePrintReceipt}
        />
      )}
    </div>
  );
}
