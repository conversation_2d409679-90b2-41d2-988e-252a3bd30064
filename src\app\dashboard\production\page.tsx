'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Eye, Trash, AlertTriangle, Check, Loader2 } from 'lucide-react';
import { formatDate } from '@/lib/utils';

// Define types for our data
type RawMaterial = {
  id: string;
  name: string;
  unit: string;
  quantity?: number;
  quantityRequired?: number;
  currentStock?: number;
};

type Product = {
  id: string;
  name: string;
  rawMaterials?: RawMaterial[];
};

type Production = {
  id: string;
  productionNumber: string;
  date: string;
  productId: string;
  productName: string;
  quantity: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  rawMaterials: RawMaterial[];
  createdAt: string;
  updatedAt: string;
};

export default function ProductionPage() {
  // State for data
  const [production, setProduction] = useState<Production[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [rawMaterialsStock, setRawMaterialsStock] = useState<RawMaterial[]>([]);

  // State for UI
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [selectedProduction, setSelectedProduction] = useState<Production | null>(null);
  const [formData, setFormData] = useState({
    productId: '',
    quantity: '',
    date: new Date().toISOString().split('T')[0],
  });
  const [filterStatus, setFilterStatus] = useState('all');
  const [materialWarnings, setMaterialWarnings] = useState<string[]>([]);

  // State for product raw materials
  const [selectedProductRawMaterials, setSelectedProductRawMaterials] = useState<RawMaterial[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [loadingProductMaterials, setLoadingProductMaterials] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch production records
        const productionResponse = await fetch('/api/production');
        const productionData = await productionResponse.json();
        setProduction(productionData);

        // Fetch products
        const productsResponse = await fetch('/api/products');
        const productsData = await productsResponse.json();
        setProducts(productsData);

        // Fetch raw materials
        const rawMaterialsResponse = await fetch('/api/raw-materials');
        const rawMaterialsData = await rawMaterialsResponse.json();
        setRawMaterialsStock(rawMaterialsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Check raw material availability when product or quantity changes
    if (name === 'productId' || name === 'quantity') {
      checkRawMaterialAvailability(
        name === 'productId' ? value : formData.productId,
        name === 'quantity' ? parseFloat(value) : parseFloat(formData.quantity)
      );

      // Fetch raw materials for the selected product
      if (name === 'productId' && value) {
        fetchProductRawMaterials(value);
      }
    }
  };

  const fetchProductRawMaterials = async (productId: string) => {
    setLoadingProductMaterials(true);
    try {
      const response = await fetch(`/api/products/${productId}/raw-materials`);
      if (!response.ok) {
        throw new Error('Failed to fetch product raw materials');
      }
      const data = await response.json();
      setSelectedProductRawMaterials(data);
    } catch (error) {
      console.error('Error fetching product raw materials:', error);
    } finally {
      setLoadingProductMaterials(false);
    }
  };

  const checkRawMaterialAvailability = (productId: string, quantity: number) => {
    if (!productId || isNaN(quantity)) {
      setMaterialWarnings([]);
      return;
    }

    // Find the product
    const product = products.find((p: Product) => p.id === productId);
    if (!product) {
      setMaterialWarnings([]);
      return;
    }

    const warnings: string[] = [];

    // Check if we have the raw materials for this product
    if (selectedProductRawMaterials.length > 0) {
      selectedProductRawMaterials.forEach((material: RawMaterial) => {
        if (material.quantityRequired && material.currentStock !== undefined) {
          const requiredAmount = material.quantityRequired * quantity;

          if (material.currentStock < requiredAmount) {
            warnings.push(
              `Not enough ${material.name}: Need ${requiredAmount} ${material.unit}, but only have ${material.currentStock} ${material.unit} in stock.`
            );
          }
        }
      });
    }

    setMaterialWarnings(warnings);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (materialWarnings.length > 0) {
      if (!confirm('There are warnings about raw material availability. Do you want to proceed anyway?')) {
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Prepare raw materials data
      const rawMaterials = selectedProductRawMaterials.map((material: RawMaterial) => ({
        id: material.id,
        quantity: material.quantityRequired ? material.quantityRequired * parseFloat(formData.quantity) : 0,
      }));

      // Create production record
      const response = await fetch('/api/production', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: formData.productId,
          quantity: formData.quantity,
          date: formData.date,
          rawMaterials,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create production record');
      }

      const newProduction = await response.json();

      // Add the new production record to the list
      setProduction([newProduction, ...production]);

      // Reset form
      setFormData({
        productId: '',
        quantity: '',
        date: new Date().toISOString().split('T')[0],
      });
      setShowForm(false);
      setMaterialWarnings([]);
      setSelectedProductRawMaterials([]);
    } catch (error) {
      console.error('Error creating production record:', error);
      alert('Failed to create production record. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewProduction = (prod: Production) => {
    setSelectedProduction(prod);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this production record?')) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/production/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete production record');
      }

      // Remove the production record from the list
      setProduction(production.filter(prod => prod.id !== id));

      // Close the production details if it's the one being deleted
      if (selectedProduction && selectedProduction.id === id) {
        setSelectedProduction(null);
      }
    } catch (error) {
      console.error('Error deleting production record:', error);
      alert('Failed to delete production record. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleUpdateStatus = async (id: string, status: 'COMPLETED' | 'CANCELLED') => {
    setIsUpdatingStatus(true);

    try {
      const response = await fetch(`/api/production/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update production status');
      }

      const updatedProduction = await response.json();

      // Update the production record in the list
      setProduction(production.map(prod =>
        prod.id === id ? updatedProduction : prod
      ));

      // Update the selected production if it's the one being updated
      if (selectedProduction && selectedProduction.id === id) {
        setSelectedProduction(updatedProduction);
      }
    } catch (error) {
      console.error('Error updating production status:', error);
      alert('Failed to update production status. Please try again.');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Filter production based on selected status
  const filteredProduction = filterStatus === 'all'
    ? production
    : production.filter(prod => prod.status === filterStatus);

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Production Planning
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Plan and track your bakery production • {filteredProduction.length} plans
            </p>
          </div>
          <button
            onClick={() => {
              setShowForm(!showForm);
              setSelectedProduction(null);
              setMaterialWarnings([]);
            }}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            New Production
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#3b82f6' }} />
          <span style={{ marginLeft: '0.5rem', color: '#64748b' }}>Loading production data...</span>
        </div>
      )}

      {/* New Production Form */}
      {showForm && (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                New Production Plan
              </h2>
              <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                Create a new production plan with raw material requirements
              </p>
            </div>
            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1.5rem'
              }}>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Product
                  </label>
                  <select
                    name="productId"
                    value={formData.productId}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <option value="">Select a product</option>
                    {products.map((product: Product) => (
                      <option key={product.id} value={product.id}>
                        {product.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Quantity
                  </label>
                  <input
                    name="quantity"
                    type="number"
                    min="1"
                    value={formData.quantity}
                    onChange={handleInputChange}
                    placeholder="Enter quantity"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Production Date
                  </label>
                  <input
                    name="date"
                    type="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
              </div>

              {formData.productId && formData.quantity && (
                <div>
                  <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                    Raw Materials Required
                  </h3>
                  <div style={{
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    overflow: 'hidden'
                  }}>
                    <table style={{ width: '100%', fontSize: '0.875rem' }}>
                      <thead>
                        <tr style={{
                          borderBottom: '1px solid #e2e8f0',
                          backgroundColor: '#f8fafc'
                        }}>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Material</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Required</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Available</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedProductRawMaterials.length > 0 && (
                          selectedProductRawMaterials.map((material: RawMaterial) => {
                            const requiredAmount = material.quantityRequired ? material.quantityRequired * parseFloat(formData.quantity) : 0;
                            const isAvailable = material.currentStock !== undefined && material.currentStock >= requiredAmount;

                            return (
                              <tr key={material.id} style={{ borderBottom: '1px solid #f1f5f9' }}>
                                <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>{material.name}</td>
                                <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                                  {requiredAmount} {material.unit}
                                </td>
                                <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                                  {material.currentStock !== undefined ? `${material.currentStock} ${material.unit}` : 'N/A'}
                                </td>
                                <td style={{ padding: '0.75rem 1rem' }}>
                                  {isAvailable ? (
                                    <span style={{ color: '#059669', fontWeight: '500' }}>Available</span>
                                  ) : (
                                    <span style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: '0.5rem',
                                      color: '#d97706',
                                      fontWeight: '500'
                                    }}>
                                      <AlertTriangle style={{ height: '1rem', width: '1rem' }} />
                                      <span>Insufficient</span>
                                    </span>
                                  )}
                                </td>
                              </tr>
                            );
                          }))
                        }
                      </tbody>
                    </table>
                  </div>

                  {materialWarnings.length > 0 && (
                    <div style={{
                      marginTop: '1rem',
                      borderRadius: '8px',
                      backgroundColor: '#fef3c7',
                      border: '1px solid #f59e0b',
                      padding: '1rem',
                      fontSize: '0.875rem'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}>
                        <AlertTriangle style={{ height: '1.25rem', width: '1.25rem', color: '#d97706', flexShrink: 0, marginTop: '0.125rem' }} />
                        <div>
                          <p style={{ fontWeight: '500', color: '#92400e', marginBottom: '0.5rem' }}>Warning: Insufficient Raw Materials</p>
                          <ul style={{ listStyle: 'disc', listStylePosition: 'inside', color: '#92400e' }}>
                            {materialWarnings.map((warning, index) => (
                              <li key={index}>{warning}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', paddingTop: '1.5rem', borderTop: '1px solid #e2e8f0' }}>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    backgroundColor: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!formData.productId || !formData.quantity || isSubmitting}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: 'none',
                    backgroundColor: isSubmitting || !formData.productId || !formData.quantity ? '#9ca3af' : '#3b82f6',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: isSubmitting || !formData.productId || !formData.quantity ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    if (!isSubmitting && formData.productId && formData.quantity) {
                      e.currentTarget.style.backgroundColor = '#2563eb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSubmitting && formData.productId && formData.quantity) {
                      e.currentTarget.style.backgroundColor = '#3b82f6';
                    }
                  }}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                      Creating...
                    </>
                  ) : (
                    'Create Production Plan'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Production Details Modal */}
      {selectedProduction && (
        <div style={{
          padding: '2rem',
          margin: '2rem',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          backgroundColor: 'white',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '1.5rem'
          }}>
            <h2 style={{
              fontSize: '1.125rem',
              fontWeight: '500',
              color: '#0f172a'
            }}>Production Details</h2>
            <button
              onClick={() => setSelectedProduction(null)}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                backgroundColor: 'white',
                color: '#374151',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
              }}
            >
              Close
            </button>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div>
              <h3 style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#0f172a',
                marginBottom: '0.75rem'
              }}>Production Information</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Production Number:</span>
                  <span style={{ color: '#0f172a' }}>{selectedProduction.productionNumber}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Date:</span>
                  <span style={{ color: '#0f172a' }}>{formatDate(selectedProduction.date)}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Status:</span>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '9999px',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    backgroundColor: selectedProduction.status === 'COMPLETED' ? '#dcfce7' : selectedProduction.status === 'PENDING' ? '#fef3c7' : '#fee2e2',
                    color: selectedProduction.status === 'COMPLETED' ? '#166534' : selectedProduction.status === 'PENDING' ? '#92400e' : '#991b1b'
                  }}>
                    {selectedProduction.status}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#0f172a',
                marginBottom: '0.75rem'
              }}>Product Information</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Product:</span>
                  <span style={{ color: '#0f172a' }}>{selectedProduction.productName}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Quantity:</span>
                  <span style={{ color: '#0f172a' }}>{selectedProduction.quantity}</span>
                </div>
              </div>
            </div>
          </div>

          <h3 style={{
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#0f172a',
            marginBottom: '0.75rem'
          }}>Raw Materials Used</h3>
          <div style={{
            borderRadius: '6px',
            border: '1px solid #e2e8f0'
          }}>
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
              <thead>
                <tr style={{
                  borderBottom: '1px solid #e2e8f0',
                  backgroundColor: '#f8fafc'
                }}>
                  <th style={{
                    padding: '0.5rem 1rem',
                    textAlign: 'left',
                    fontWeight: '500'
                  }}>Material</th>
                  <th style={{
                    padding: '0.5rem 1rem',
                    textAlign: 'right',
                    fontWeight: '500'
                  }}>Quantity</th>
                </tr>
              </thead>
              <tbody>
                {selectedProduction.rawMaterials.map((material) => (
                  <tr key={material.id} style={{ borderBottom: '1px solid #e2e8f0' }}>
                    <td style={{ padding: '0.5rem 1rem' }}>{material.name}</td>
                    <td style={{ padding: '0.5rem 1rem', textAlign: 'right' }}>
                      {material.quantity} {material.unit}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {selectedProduction.status === 'PENDING' && (
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '0.75rem',
              marginTop: '1.5rem'
            }}>
              <button
                onClick={() => handleUpdateStatus(selectedProduction.id, 'COMPLETED')}
                disabled={isUpdatingStatus}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: isUpdatingStatus ? '#9ca3af' : '#10b981',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: isUpdatingStatus ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = '#059669';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = '#10b981';
                  }
                }}
              >
                {isUpdatingStatus ? (
                  <>
                    <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem' }} />
                    Updating...
                  </>
                ) : (
                  <>
                    <Check style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    Mark as Completed
                  </>
                )}
              </button>
              <button
                onClick={() => handleUpdateStatus(selectedProduction.id, 'CANCELLED')}
                disabled={isUpdatingStatus}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  border: '1px solid #d1d5db',
                  backgroundColor: isUpdatingStatus ? '#f3f4f6' : 'white',
                  color: isUpdatingStatus ? '#9ca3af' : '#dc2626',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: isUpdatingStatus ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = '#fef2f2';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = 'white';
                  }
                }}
              >
                {isUpdatingStatus ? (
                  <>
                    <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem' }} />
                    Updating...
                  </>
                ) : (
                  <>
                    <Trash style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    Cancel Production
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Filter Section */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '1.5rem 2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
            Filter by Status:
          </label>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            style={{
              padding: '0.625rem 1rem',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              minWidth: '200px'
            }}
          >
            <option value="all">All Production</option>
            <option value="PENDING">Pending</option>
            <option value="COMPLETED">Completed</option>
            <option value="CANCELLED">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Production Table */}
      <div style={{ padding: '2rem' }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
              <thead>
                <tr style={{
                  borderBottom: '1px solid #e2e8f0',
                  backgroundColor: '#f8fafc'
                }}>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Production #</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Date</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Product</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Quantity</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Status</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'right',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredProduction.map((prod) => (
                  <tr key={prod.id} style={{
                    borderBottom: '1px solid #f1f5f9',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}>
                    <td style={{ padding: '1rem', fontWeight: '500', color: '#0f172a' }}>{prod.productionNumber}</td>
                    <td style={{ padding: '1rem', color: '#64748b' }}>{formatDate(prod.date)}</td>
                    <td style={{ padding: '1rem', fontWeight: '500', color: '#0f172a' }}>{prod.productName}</td>
                    <td style={{ padding: '1rem', color: '#0f172a', fontWeight: '500' }}>{prod.quantity}</td>
                    <td style={{ padding: '1rem' }}>
                      <span style={{
                        padding: '0.25rem 0.75rem',
                        borderRadius: '9999px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        backgroundColor: prod.status === 'COMPLETED' ? '#dcfce7' : prod.status === 'PENDING' ? '#fef3c7' : '#fee2e2',
                        color: prod.status === 'COMPLETED' ? '#166534' : prod.status === 'PENDING' ? '#92400e' : '#991b1b'
                      }}>
                        {prod.status}
                      </span>
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'right' }}>
                      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem' }}>
                        <button
                          onClick={() => handleViewProduction(prod)}
                          style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '2.5rem',
                            height: '2.5rem',
                            borderRadius: '6px',
                            border: 'none',
                            backgroundColor: 'transparent',
                            color: '#64748b',
                            cursor: 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f1f5f9';
                            e.currentTarget.style.color = '#3b82f6';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                            e.currentTarget.style.color = '#64748b';
                          }}
                        >
                          <Eye style={{ height: '1rem', width: '1rem' }} />
                        </button>
                        <button
                          onClick={() => handleDelete(prod.id)}
                          disabled={isDeleting}
                          style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '2.5rem',
                            height: '2.5rem',
                            borderRadius: '6px',
                            border: 'none',
                            backgroundColor: 'transparent',
                            color: '#64748b',
                            cursor: isDeleting ? 'not-allowed' : 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            if (!isDeleting) {
                              e.currentTarget.style.backgroundColor = '#fef2f2';
                              e.currentTarget.style.color = '#dc2626';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isDeleting) {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.color = '#64748b';
                            }
                          }}
                        >
                          {isDeleting && prod.id === selectedProduction?.id ? (
                            <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                          ) : (
                            <Trash style={{ height: '1rem', width: '1rem' }} />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
                {filteredProduction.length === 0 && !loading && (
                  <tr>
                    <td colSpan={6} style={{
                      padding: '3rem',
                      textAlign: 'center',
                      color: '#64748b',
                      fontSize: '0.875rem'
                    }}>
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
                        <div style={{
                          width: '3rem',
                          height: '3rem',
                          borderRadius: '50%',
                          backgroundColor: '#f1f5f9',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Plus style={{ height: '1.5rem', width: '1.5rem', color: '#64748b' }} />
                        </div>
                        <div>
                          <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>No production plans found</p>
                          <p style={{ fontSize: '0.75rem' }}>Create your first production plan to get started</p>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
