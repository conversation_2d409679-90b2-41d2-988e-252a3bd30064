'use client';

import { useState, useEffect } from 'react';

import { ProductList } from '@/components/products/product-list-new';
import { ProductDetail } from '@/components/products/product-detail-new';
import { ProductFormWithVariants } from '@/components/products/product-form-with-variants';
import { ProductFormData } from '@/components/products/product-form-with-variants';
import { formatCurrency } from '@/lib/utils';
import { getAuthHeaders } from '@/lib/auth/api-client';
import { Loader2 } from 'lucide-react';

interface ProductVariant {
  id?: string;
  weight: string;
  price: number;
  costPrice: number;
  sku?: string;
  isDefault: boolean;
  isActive: boolean;
  sortOrder: number;
}

interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  categoryName: string;
  price: number;
  costPrice: number;
  unit: string;
  lowStockThreshold: number;
  sku: string;
  barcode?: string;
  weight?: string;
  dimensions?: string;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl?: string;
  gallery?: string[];
  tags?: string;
  metaTitle?: string;
  metaDescription?: string;
  variants?: ProductVariant[];
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: string;
  name: string;
}

interface InventoryItem {
  productId: string;
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<(Partial<ProductFormData> & { id?: string }) | null>(null);

  // Fetch products, categories, and inventory data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch products with variants
        const productsResponse = await fetch('/api/products-with-variants?limit=1000', {
          headers: getAuthHeaders()
        });
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products');
        }
        const productsResponseData = await productsResponse.json();
        const productsData = productsResponseData.products || [];

        // Fetch categories
        const categoriesResponse = await fetch('/api/categories', {
          headers: getAuthHeaders()
        });
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }
        const categoriesData = await categoriesResponse.json();

        // Fetch inventory (warehouse inventory)
        const warehouseInventoryResponse = await fetch('/api/warehouse-inventory', {
          headers: getAuthHeaders()
        });
        if (!warehouseInventoryResponse.ok) {
          throw new Error('Failed to fetch warehouse inventory');
        }
        const warehouseInventoryData = await warehouseInventoryResponse.json();

        // Process the data to add categoryName to products
        const productsWithCategoryNames = productsData.map((product: Product) => {
          const category = categoriesData.find((cat: Category) => cat.id === product.category);
          return {
            ...product,
            categoryName: category ? category.name : 'Uncategorized',
          };
        });

        setProducts(productsWithCategoryNames);
        setCategories(categoriesData);
        setInventory(warehouseInventoryData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const selectedProduct = selectedProductId ? products.find(p => p.id === selectedProductId) : null;

  // Get inventory for a specific product
  const getProductInventory = (productId: string) => {
    return inventory.filter(item => item.productId === productId);
  };

  const handleViewProduct = (id: string) => {
    setSelectedProductId(id);
    setShowForm(false);
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    setSelectedProductId(null);
    setShowForm(true);
  };

  const handleEditProduct = (product: Product) => {
    // Convert the product to ProductFormData format
    const productFormData = {
      id: product.id, // Store the ID for the update operation
      name: product.name,
      description: product.description || '',
      category: product.category,
      price: product.price.toString(),
      costPrice: product.costPrice.toString(),
      unit: product.unit,
      lowStockThreshold: product.lowStockThreshold.toString(),
      initialStock: '0', // Add this required field
      warehouseId: '', // Add this required field
      sku: product.sku,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      imageUrl: product.imageUrl || '',
      metaTitle: product.metaTitle || '',
      metaDescription: product.metaDescription || '',
      variants: product.variants || [
        { weight: '0.5 Kg', price: 595, costPrice: 300, isDefault: true, isActive: true, sortOrder: 1 },
        { weight: '1 Kg', price: 1045, costPrice: 500, isDefault: false, isActive: true, sortOrder: 2 },
        { weight: '1.5 Kg', price: 1545, costPrice: 750, isDefault: false, isActive: true, sortOrder: 3 },
        { weight: '2 Kg', price: 2045, costPrice: 1000, isDefault: false, isActive: true, sortOrder: 4 }
      ]
    };

    setEditingProduct(productFormData);
    setSelectedProductId(null);
    setShowForm(true);
  };

  const handleDeleteProduct = async (id: string) => {
    console.log('🗑️ Delete product called with ID:', id);

    // Find the product to get its name for better logging
    const productToDelete = products.find(p => p.id === id);
    console.log('📦 Product to delete:', productToDelete?.name || 'Unknown product');

    if (!confirm(`Are you sure you want to delete "${productToDelete?.name || 'this product'}"?\n\nThis action cannot be undone and will remove all related data including inventory, orders, and reviews.`)) {
      console.log('❌ Delete cancelled by user');
      return;
    }

    try {
      console.log('🚀 Sending DELETE request to:', `/api/products/${id}`);
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📊 Delete response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Delete error response:', errorData);
        throw new Error(errorData.error || 'Failed to delete product');
      }

      console.log('✅ Product deleted successfully from database');
      console.log('🔄 Updating local state...');

      // Update the products list
      const updatedProducts = products.filter(product => product.id !== id);
      setProducts(updatedProducts);
      setSelectedProductId(null);

      console.log('✅ State updated successfully');
      console.log('📊 Products remaining:', updatedProducts.length);

      // Show success message
      alert(`Product "${productToDelete?.name || 'Unknown'}" has been deleted successfully.`);
    } catch (err) {
      console.error('❌ Error deleting product:', err);
      alert(`Failed to delete product: ${err instanceof Error ? err.message : 'Unknown error'}. Please try again.`);
    }
  };

  const handleProductFormSubmit = async (formData: ProductFormData) => {
    try {
      // Check if user is logged in
      const storedUser = localStorage.getItem('user');
      console.log('👤 Stored user:', storedUser ? JSON.parse(storedUser) : 'No user found');

      console.log('🔄 Submitting product form:', {
        isEdit: !!(editingProduct && editingProduct.id),
        productId: editingProduct?.id,
        formData: {
          name: formData.name,
          category: formData.category,
          variantsCount: formData.variants?.length || 0
        }
      });

      if (editingProduct && editingProduct.id) {
        // Update existing product with variants
        console.log('📝 Updating existing product:', editingProduct.id);
        console.log('📦 Form data being sent:', JSON.stringify(formData, null, 2));
        console.log('🔑 Auth headers:', getAuthHeaders());

        const response = await fetch(`/api/products-with-variants/${editingProduct.id}`, {
          method: 'PUT',
          headers: {
            ...getAuthHeaders(),
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        console.log('📊 Update response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ Update API error:', {
            status: response.status,
            statusText: response.statusText,
            errorText,
            url: response.url,
            headers: Object.fromEntries(response.headers.entries())
          });

          let errorMessage = 'Failed to update product';
          try {
            const errorData = JSON.parse(errorText);
            console.error('❌ Parsed error data:', errorData);
            errorMessage = errorData.error || errorData.message || errorMessage;
          } catch (parseError) {
            console.error('❌ Error parsing response:', parseError);
            errorMessage = `Server error (${response.status}): ${errorText.substring(0, 200)}`;
          }

          throw new Error(errorMessage);
        }

        const updatedProduct = await response.json();
        const category = categories.find(c => c.id === updatedProduct.category);

        setProducts(products.map(product =>
          product.id === editingProduct.id
            ? { ...updatedProduct, categoryName: category ? category.name : 'Uncategorized' }
            : product
        ));
      } else {
        // Add new product with variants
        console.log('➕ Creating new product');

        const response = await fetch('/api/products-with-variants', {
          method: 'POST',
          headers: {
            ...getAuthHeaders(),
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        console.log('📊 Create response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ Create API error:', {
            status: response.status,
            statusText: response.statusText,
            errorText
          });

          let errorMessage = 'Failed to create product';
          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.error || errorMessage;
          } catch {
            errorMessage = `Server error (${response.status}): ${errorText}`;
          }

          throw new Error(errorMessage);
        }

        const newProduct = await response.json();
        const category = categories.find(c => c.id === newProduct.category);

        setProducts([...products, { ...newProduct, categoryName: category ? category.name : 'Uncategorized' }]);
      }

      setShowForm(false);
      setEditingProduct(null);
    } catch (err) {
      console.error('Error saving product:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while saving the product');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>

      {error && (
        <div style={{
          borderRadius: '6px',
          backgroundColor: '#fef2f2',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {loading && !showForm && !selectedProductId && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: 'var(--primary)' }} />
          <span style={{ marginLeft: '0.5rem' }}>Loading products...</span>
        </div>
      )}

      {showForm && (
        <div style={{
          borderRadius: '8px',
          border: '1px solid var(--border)',
          backgroundColor: 'var(--background)',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ marginBottom: '1.5rem', fontSize: '1.125rem', fontWeight: '500' }}>
            {editingProduct ? 'Edit Product' : 'Add New Product'}
          </h2>
          <ProductFormWithVariants
            initialData={editingProduct as Partial<ProductFormData> | undefined}
            categories={categories}
            onSubmit={handleProductFormSubmit}
            onCancel={() => {
              setShowForm(false);
              setEditingProduct(null);
            }}
          />
        </div>
      )}

      {selectedProduct && (
        <ProductDetail
          product={selectedProduct}
          inventory={getProductInventory(selectedProduct.id)}
          onBack={() => setSelectedProductId(null)}
          onEdit={handleEditProduct}
          onDelete={handleDeleteProduct}
        />
      )}

      {!selectedProductId && !showForm && !loading && (
        <ProductList
          products={products}
          categories={categories}
          inventory={inventory.reduce((acc, item) => {
            const existingItem = acc.find(i => i.productId === item.productId);
            if (existingItem) {
              existingItem.quantity += item.quantity;
            } else {
              acc.push({ productId: item.productId, quantity: item.quantity });
            }
            return acc;
          }, [] as { productId: string; quantity: number }[])}
          onViewProduct={handleViewProduct}
          onAddProduct={handleAddProduct}
        />
      )}
    </div>
  );
}
