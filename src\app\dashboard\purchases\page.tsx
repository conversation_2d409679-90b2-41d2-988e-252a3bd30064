'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Eye, Trash, FileText, Loader2 } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

// Purchase interface
interface Purchase {
  id: string;
  invoiceNumber: string;
  date: string;
  warehouseId: string;
  warehouseName: string;
  supplierName: string;
  supplierContact: string;
  totalAmount: number;
  paymentMethod: string;
  status: string;
  items: PurchaseItem[];
}

// Purchase item interface
interface PurchaseItem {
  id: string;
  productId?: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

// Warehouse interface
interface Warehouse {
  id: string;
  name: string;
  location?: string;
}

// Product interface
interface Product {
  id: string;
  name: string;
  price: number;
  costPrice: number;
  unit: string;
  category: string;
}

export default function PurchasesPage() {
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [formData, setFormData] = useState({
    warehouseId: '',
    supplierName: '',
    supplierContact: '',
    paymentMethod: 'BANK_TRANSFER',
  });
  const [purchaseItems, setPurchaseItems] = useState<Array<{
    productId: string;
    productName: string;
    quantity: string;
    unitPrice: string;
    totalPrice: string;
  }>>([]);

  // Fetch purchases, warehouses, and products when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch all data in parallel for better performance
        const [purchasesResponse, warehousesResponse, productsResponse] = await Promise.all([
          fetch('/api/purchases'),
          fetch('/api/warehouses'),
          fetch('/api/products')
        ]);

        // Handle purchases response
        if (!purchasesResponse.ok) {
          const errorData = await purchasesResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch purchases');
        }

        // Handle warehouses response
        if (!warehousesResponse.ok) {
          const errorData = await warehousesResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch warehouses');
        }

        // Handle products response
        if (!productsResponse.ok) {
          const errorData = await productsResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch products');
        }

        // Parse response data
        const [purchasesData, warehousesData, productsData] = await Promise.all([
          purchasesResponse.json(),
          warehousesResponse.json(),
          productsResponse.json()
        ]);

        // Update state with fetched data
        setPurchases(purchasesData);
        setWarehouses(warehousesData);
        setProducts(productsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleAddItem = () => {
    setPurchaseItems([
      ...purchaseItems,
      {
        productId: '',
        productName: '',
        quantity: '',
        unitPrice: '',
        totalPrice: '',
      },
    ]);
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const updatedItems = [...purchaseItems];

    if (name === 'productId') {
      const product = products.find(p => p.id === value);
      if (product) {
        updatedItems[index] = {
          ...updatedItems[index],
          productId: value,
          productName: product.name,
          unitPrice: product.price.toString(),
          totalPrice: (product.price * parseFloat(updatedItems[index].quantity || '1')).toString(),
        };
      }
    } else if (name === 'quantity') {
      const quantity = parseFloat(value) || 0;
      const unitPrice = parseFloat(updatedItems[index].unitPrice) || 0;
      updatedItems[index] = {
        ...updatedItems[index],
        quantity: value,
        totalPrice: (quantity * unitPrice).toString(),
      };
    } else if (name === 'unitPrice') {
      const quantity = parseFloat(updatedItems[index].quantity) || 0;
      const unitPrice = parseFloat(value) || 0;
      updatedItems[index] = {
        ...updatedItems[index],
        unitPrice: value,
        totalPrice: (quantity * unitPrice).toString(),
      };
    } else {
      updatedItems[index] = {
        ...updatedItems[index],
        [name]: value,
      };
    }

    setPurchaseItems(updatedItems);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...purchaseItems];
    updatedItems.splice(index, 1);
    setPurchaseItems(updatedItems);
  };

  const calculateTotal = () => {
    return purchaseItems.reduce((sum, item) => {
      return sum + (parseFloat(item.totalPrice) || 0);
    }, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    if (!formData.warehouseId) {
      alert('Please select a warehouse');
      return;
    }

    if (!formData.supplierName) {
      alert('Please enter a supplier name');
      return;
    }

    if (purchaseItems.length === 0) {
      alert('Please add at least one item to the purchase');
      return;
    }

    // Validate purchase items
    for (const item of purchaseItems) {
      if (!item.productId) {
        alert('Please select a product for all items');
        return;
      }

      if (!item.quantity || parseFloat(item.quantity) <= 0) {
        alert('Please enter a valid quantity for all items');
        return;
      }

      if (!item.unitPrice || parseFloat(item.unitPrice) <= 0) {
        alert('Please enter a valid unit price for all items');
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/purchases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          warehouseId: formData.warehouseId,
          supplierName: formData.supplierName,
          supplierContact: formData.supplierContact,
          paymentMethod: formData.paymentMethod,
          items: purchaseItems,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create purchase');
      }

      const newPurchase = await response.json();
      setPurchases([newPurchase, ...purchases]);
      setShowForm(false);
      setFormData({
        warehouseId: '',
        supplierName: '',
        supplierContact: '',
        paymentMethod: 'BANK_TRANSFER',
      });
      setPurchaseItems([]);

      // Show success message
      alert('Purchase created successfully!');
    } catch (err) {
      console.error('Error creating purchase:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while creating the purchase');
      alert(err instanceof Error ? err.message : 'An error occurred while creating the purchase');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewPurchase = (purchase: Purchase) => {
    setSelectedPurchase(purchase);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this purchase? This will also update the inventory.')) {
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/purchases/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete purchase');
      }

      setPurchases(purchases.filter(purchase => purchase.id !== id));

      // If the deleted purchase is currently selected, clear the selection
      if (selectedPurchase && selectedPurchase.id === id) {
        setSelectedPurchase(null);
      }

      // Show success message
      alert('Purchase deleted successfully!');
    } catch (err) {
      console.error('Error deleting purchase:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while deleting the purchase');
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the purchase');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Purchases Management
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Manage purchase transactions and supplier orders • {purchases.length} purchases
            </p>
          </div>
          <button
            onClick={() => {
              setShowForm(!showForm);
              setSelectedPurchase(null);
            }}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            New Purchase
          </button>
        </div>
      </div>

      {/* Content */}
      <div style={{ padding: '2rem' }}>
        {showForm && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          marginBottom: '2rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ fontSize: '1.125rem', fontWeight: '500', marginBottom: '1.5rem', color: '#0f172a' }}>
            New Purchase
          </h2>
          <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="warehouseId" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Warehouse
                </label>
                <select
                  id="warehouseId"
                  name="warehouseId"
                  value={formData.warehouseId}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="">Select a warehouse</option>
                  {warehouses.map((warehouse) => (
                    <option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </option>
                  ))}
                </select>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="paymentMethod" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="CASH">Cash</option>
                  <option value="CARD">Card</option>
                  <option value="BANK_TRANSFER">Bank Transfer</option>
                  <option value="ONLINE">Online</option>
                </select>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="supplierName" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Supplier Name
                </label>
                <input
                  id="supplierName"
                  name="supplierName"
                  value={formData.supplierName}
                  onChange={handleInputChange}
                  placeholder="Enter supplier name"
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="supplierContact" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Supplier Contact
                </label>
                <input
                  id="supplierContact"
                  name="supplierContact"
                  value={formData.supplierContact}
                  onChange={handleInputChange}
                  placeholder="Enter supplier contact"
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Purchase Items</h3>
                <button
                  type="button"
                  onClick={handleAddItem}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem',
                    backgroundColor: '#f1f5f9',
                    color: '#3b82f6',
                    border: '1px solid #e2e8f0',
                    borderRadius: '6px',
                    padding: '0.5rem 0.75rem',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#e2e8f0';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                  }}
                >
                  <Plus style={{ height: '12px', width: '12px' }} />
                  Add Item
                </button>
              </div>

              {purchaseItems.length > 0 ? (
                <div style={{
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  overflow: 'hidden'
                }}>
                  <table style={{ width: '100%', fontSize: '0.875rem' }}>
                    <thead>
                      <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                        <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Product</th>
                        <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Quantity</th>
                        <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Unit Price</th>
                        <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Total</th>
                        <th style={{ padding: '1rem', textAlign: 'right', fontWeight: '500', color: '#374151' }}>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {purchaseItems.map((item, index) => (
                        <tr
                          key={index}
                          style={{
                            borderBottom: '1px solid #f1f5f9',
                            transition: 'background-color 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f8fafc';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }}
                        >
                          <td style={{ padding: '1rem' }}>
                            <select
                              name="productId"
                              value={item.productId}
                              onChange={(e) => handleItemChange(index, e)}
                              style={{
                                width: '100%',
                                padding: '0.5rem',
                                borderRadius: '6px',
                                border: '1px solid #d1d5db',
                                fontSize: '0.875rem',
                                backgroundColor: 'white',
                                cursor: 'pointer'
                              }}
                              required
                            >
                              <option value="">Select a product</option>
                              {products.map((product) => (
                                <option key={product.id} value={product.id}>
                                  {product.name} (₹{product.price}/{product.unit})
                                </option>
                              ))}
                            </select>
                          </td>
                          <td style={{ padding: '1rem' }}>
                            <input
                              name="quantity"
                              type="number"
                              min="0.01"
                              step="0.01"
                              value={item.quantity}
                              onChange={(e) => handleItemChange(index, e)}
                              style={{
                                width: '100%',
                                padding: '0.5rem',
                                borderRadius: '6px',
                                border: '1px solid #d1d5db',
                                fontSize: '0.875rem',
                                backgroundColor: 'white'
                              }}
                              required
                            />
                          </td>
                          <td style={{ padding: '1rem' }}>
                            <input
                              name="unitPrice"
                              type="number"
                              min="0.01"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => handleItemChange(index, e)}
                              style={{
                                width: '100%',
                                padding: '0.5rem',
                                borderRadius: '6px',
                                border: '1px solid #d1d5db',
                                fontSize: '0.875rem',
                                backgroundColor: 'white'
                              }}
                              required
                            />
                          </td>
                          <td style={{ padding: '1rem', fontWeight: '500', color: '#059669' }}>
                            ₹{(parseFloat(item.totalPrice) || 0).toFixed(2)}
                          </td>
                          <td style={{ padding: '1rem', textAlign: 'right' }}>
                            <button
                              type="button"
                              onClick={() => handleRemoveItem(index)}
                              style={{
                                backgroundColor: 'transparent',
                                color: '#dc2626',
                                border: 'none',
                                borderRadius: '6px',
                                padding: '0.5rem',
                                cursor: 'pointer',
                                transition: 'all 0.2s'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#fef2f2';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                              }}
                            >
                              <Trash style={{ height: '16px', width: '16px' }} />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div style={{
                  border: '2px dashed #d1d5db',
                  borderRadius: '8px',
                  padding: '3rem',
                  textAlign: 'center',
                  color: '#64748b',
                  backgroundColor: '#f8fafc'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>📦</div>
                  <p>No items added. Click "Add Item" to add products to this purchase.</p>
                </div>
              )}
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              padding: '1rem',
              borderTop: '1px solid #e2e8f0',
              backgroundColor: '#f8fafc'
            }}>
              <div style={{ display: 'flex', gap: '2rem', fontSize: '1rem', fontWeight: '600' }}>
                <span style={{ color: '#64748b' }}>Total:</span>
                <span style={{ color: '#059669' }}>₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                type="submit"
                disabled={isSubmitting}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: isSubmitting ? '#94a3b8' : '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
                onMouseEnter={(e) => {
                  if (!isSubmitting) {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSubmitting) {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }
                }}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 style={{ height: '16px', width: '16px', animation: 'spin 1s linear infinite' }} />
                    Processing...
                  </>
                ) : (
                  'Complete Purchase'
                )}
              </button>
              <button
                type="button"
                onClick={() => setShowForm(false)}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#64748b',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                }}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {selectedPurchase && (
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-medium">Purchase Details</h2>
            <Button variant="outline" size="sm" onClick={() => setSelectedPurchase(null)}>
              Close
            </Button>
          </div>

          <div className="mb-6 grid gap-4 md:grid-cols-2">
            <div>
              <h3 className="mb-2 text-sm font-medium">Invoice Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Invoice Number:</span>
                  <span>{selectedPurchase.invoiceNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Date:</span>
                  <span>{formatDate(selectedPurchase.date)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Warehouse:</span>
                  <span>{selectedPurchase.warehouseName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                    {selectedPurchase.status}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="mb-2 text-sm font-medium">Supplier Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Name:</span>
                  <span>{selectedPurchase.supplierName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Contact:</span>
                  <span>{selectedPurchase.supplierContact || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method:</span>
                  <span>{selectedPurchase.paymentMethod.replace('_', ' ')}</span>
                </div>
              </div>
            </div>
          </div>

          <h3 className="mb-2 text-sm font-medium">Purchase Items</h3>
          <div className="rounded-md border">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="px-4 py-2 text-left font-medium">Product</th>
                  <th className="px-4 py-2 text-left font-medium">Quantity</th>
                  <th className="px-4 py-2 text-left font-medium">Unit Price</th>
                  <th className="px-4 py-2 text-right font-medium">Total</th>
                </tr>
              </thead>
              <tbody>
                {selectedPurchase.items.map((item) => (
                  <tr key={item.id} className="border-b">
                    <td className="px-4 py-2">{item.productName}</td>
                    <td className="px-4 py-2">{item.quantity}</td>
                    <td className="px-4 py-2">{formatCurrency(item.unitPrice)}</td>
                    <td className="px-4 py-2 text-right">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="font-medium">
                  <td colSpan={3} className="px-4 py-2 text-right">
                    Total:
                  </td>
                  <td className="px-4 py-2 text-right">
                    {formatCurrency(selectedPurchase.totalAmount)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          <div className="mt-4 flex justify-end gap-2">
            <Button variant="outline" size="sm">
              <FileText className="mr-2 h-4 w-4" />
              Print Invoice
            </Button>
          </div>
        </div>
      )}

      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          {loading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '3rem',
              color: '#64748b'
            }}>
              <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem', color: '#3b82f6' }} />
              <span>Loading purchases...</span>
            </div>
          ) : error ? (
            <div style={{ padding: '3rem', textAlign: 'center' }}>
              <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
              <button
                onClick={() => {
                  setLoading(true);
                  setError(null);
                  fetch('/api/purchases')
                    .then(res => res.json())
                    .then(data => setPurchases(data))
                    .catch(err => setError(err instanceof Error ? err.message : 'An error occurred'))
                    .finally(() => setLoading(false));
                }}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#3b82f6',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer'
                }}
              >
                Retry
              </button>
            </div>
          ) : (
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Invoice #</th>
                  <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Date</th>
                  <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Warehouse</th>
                  <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Supplier</th>
                  <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Amount</th>
                  <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Status</th>
                  <th style={{ padding: '1rem', textAlign: 'right', fontWeight: '500', color: '#374151' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {purchases.map((purchase) => (
                  <tr
                    key={purchase.id}
                    style={{
                      borderBottom: '1px solid #f1f5f9',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <td style={{ padding: '1rem', fontWeight: '500' }}>{purchase.invoiceNumber}</td>
                    <td style={{ padding: '1rem', color: '#64748b' }}>{formatDate(purchase.date)}</td>
                    <td style={{ padding: '1rem' }}>{purchase.warehouseName}</td>
                    <td style={{ padding: '1rem' }}>{purchase.supplierName}</td>
                    <td style={{ padding: '1rem', fontWeight: '500', color: '#059669' }}>₹{purchase.totalAmount}</td>
                    <td style={{ padding: '1rem' }}>
                      <span style={{
                        backgroundColor: '#dcfce7',
                        color: '#166534',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '12px',
                        fontSize: '0.75rem',
                        fontWeight: '500'
                      }}>
                        {purchase.status}
                      </span>
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'right' }}>
                      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem' }}>
                        <button
                          onClick={() => handleViewPurchase(purchase)}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#3b82f6',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#eff6ff';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }}
                        >
                          <Eye style={{ height: '16px', width: '16px' }} />
                        </button>
                        <button
                          onClick={() => handleDelete(purchase.id)}
                          disabled={isDeleting && selectedPurchase?.id === purchase.id}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#dc2626',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#fef2f2';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }}
                        >
                          {isDeleting && selectedPurchase?.id === purchase.id ? (
                            <Loader2 style={{ height: '16px', width: '16px', animation: 'spin 1s linear infinite' }} />
                          ) : (
                            <Trash style={{ height: '16px', width: '16px' }} />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
                {purchases.length === 0 && (
                  <tr>
                    <td colSpan={7} style={{ padding: '3rem', textAlign: 'center', color: '#64748b' }}>
                      <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📦</div>
                      <p>No purchases found. Create a new purchase to get started.</p>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
