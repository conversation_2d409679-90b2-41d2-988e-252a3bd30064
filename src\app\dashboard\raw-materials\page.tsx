'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, AlertTriangle, Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// Define types for our data
type RawMaterial = {
  id: string;
  name: string;
  unit: string;
  costPerUnit: number;
  currentStock: number;
  lowStockThreshold: number;
  createdAt: string;
  updatedAt: string;
};

export default function RawMaterialsPage() {
  // State for data
  const [rawMaterials, setRawMaterials] = useState<RawMaterial[]>([]);

  // State for UI
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    unit: '',
    costPerUnit: '',
    currentStock: '',
    lowStockThreshold: '',
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showStockForm, setShowStockForm] = useState(false);
  const [stockFormData, setStockFormData] = useState({
    materialId: '',
    materialName: '',
    currentStock: '',
    addStock: '',
  });

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isAddingStock, setIsAddingStock] = useState(false);

  // Fetch raw materials on component mount
  useEffect(() => {
    const fetchRawMaterials = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/raw-materials');
        if (!response.ok) {
          throw new Error('Failed to fetch raw materials');
        }
        const data = await response.json();
        setRawMaterials(data);
      } catch (error) {
        console.error('Error fetching raw materials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRawMaterials();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleStockInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setStockFormData({ ...stockFormData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const materialData = {
      name: formData.name,
      unit: formData.unit,
      costPerUnit: formData.costPerUnit,
      currentStock: formData.currentStock,
      lowStockThreshold: formData.lowStockThreshold,
    };

    try {
      if (editingId) {
        // Update existing raw material
        const response = await fetch(`/api/raw-materials/${editingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(materialData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update raw material');
        }

        const updatedMaterial = await response.json();

        // Update the raw materials list
        setRawMaterials(rawMaterials.map(material =>
          material.id === editingId ? updatedMaterial : material
        ));

        setEditingId(null);
      } else {
        // Add new raw material
        const response = await fetch('/api/raw-materials', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(materialData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create raw material');
        }

        const newMaterial = await response.json();

        // Add the new raw material to the list
        setRawMaterials([...rawMaterials, newMaterial]);
      }

      // Reset form
      setFormData({
        name: '',
        unit: '',
        costPerUnit: '',
        currentStock: '',
        lowStockThreshold: '',
      });
      setShowForm(false);
    } catch (error) {
      console.error('Error saving raw material:', error);
      alert('Failed to save raw material. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStockSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAddingStock(true);

    const materialId = stockFormData.materialId;
    const addStock = parseFloat(stockFormData.addStock);

    if (materialId && !isNaN(addStock)) {
      try {
        const response = await fetch(`/api/raw-materials/${materialId}/add-stock`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ quantity: addStock }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to add stock');
        }

        const updatedMaterial = await response.json();

        // Update the raw materials list
        setRawMaterials(rawMaterials.map(material =>
          material.id === materialId ? updatedMaterial : material
        ));

        // Reset form
        setStockFormData({
          materialId: '',
          materialName: '',
          currentStock: '',
          addStock: '',
        });
        setShowStockForm(false);
      } catch (error) {
        console.error('Error adding stock:', error);
        alert('Failed to add stock. Please try again.');
      } finally {
        setIsAddingStock(false);
      }
    }
  };

  const handleEdit = (material: RawMaterial) => {
    setFormData({
      name: material.name,
      unit: material.unit,
      costPerUnit: material.costPerUnit.toString(),
      currentStock: material.currentStock.toString(),
      lowStockThreshold: material.lowStockThreshold.toString(),
    });
    setEditingId(material.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this raw material?')) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/raw-materials/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete raw material');
      }

      // Remove the raw material from the list
      setRawMaterials(rawMaterials.filter(material => material.id !== id));
    } catch (error) {
      console.error('Error deleting raw material:', error);
      alert('Failed to delete raw material. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleAddStock = (material: RawMaterial) => {
    setStockFormData({
      materialId: material.id,
      materialName: material.name,
      currentStock: material.currentStock.toString(),
      addStock: '',
    });
    setShowStockForm(true);
  };

  // Calculate total inventory value
  const totalInventoryValue = rawMaterials.reduce(
    (sum, material) => sum + (material.currentStock * material.costPerUnit),
    0
  );

  // Count low stock items
  const lowStockCount = rawMaterials.filter(
    material => material.currentStock <= material.lowStockThreshold
  ).length;

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Raw Materials
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Manage your bakery raw materials and ingredients • {rawMaterials.length} materials
            </p>
          </div>
          <button
            onClick={() => {
              setFormData({
                name: '',
                unit: '',
                costPerUnit: '',
                currentStock: '',
                lowStockThreshold: '',
              });
              setEditingId(null);
              setShowForm(!showForm);
              setShowStockForm(false);
            }}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            Add Raw Material
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#3b82f6' }} />
          <span style={{ marginLeft: '0.5rem', color: '#64748b' }}>Loading raw materials...</span>
        </div>
      )}

      {/* Statistics Cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '1.5rem',
        padding: '2rem'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.2s'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Materials</h3>
          </div>
          <div style={{ marginTop: '0.75rem' }}>
            <p style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a' }}>{rawMaterials.length}</p>
            <p style={{ fontSize: '0.75rem', color: '#64748b', marginTop: '0.25rem' }}>Different raw materials in inventory</p>
          </div>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.2s'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Inventory Value</h3>
          </div>
          <div style={{ marginTop: '0.75rem' }}>
            <p style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a' }}>{formatCurrency(totalInventoryValue)}</p>
            <p style={{ fontSize: '0.75rem', color: '#64748b', marginTop: '0.25rem' }}>Total value of raw materials</p>
          </div>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.2s'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Low Stock Items</h3>
            {lowStockCount > 0 && (
              <div style={{
                borderRadius: '50%',
                backgroundColor: '#fef3c7',
                padding: '0.5rem',
                color: '#d97706'
              }}>
                <AlertTriangle style={{ height: '1rem', width: '1rem' }} />
              </div>
            )}
          </div>
          <div style={{ marginTop: '0.75rem' }}>
            <p style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a' }}>{lowStockCount}</p>
            <p style={{ fontSize: '0.75rem', color: '#64748b', marginTop: '0.25rem' }}>Materials below threshold</p>
          </div>
        </div>
      </div>

      {showForm && (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                {editingId ? 'Edit Raw Material' : 'Add New Raw Material'}
              </h2>
              <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                {editingId ? 'Update the raw material information' : 'Add a new raw material to your inventory'}
              </p>
            </div>
            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '1.5rem'
              }}>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Material Name
                  </label>
                  <input
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter material name"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Unit
                  </label>
                  <input
                    name="unit"
                    value={formData.unit}
                    onChange={handleInputChange}
                    placeholder="e.g., kg, liter, piece"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Cost Per Unit (₹)
                  </label>
                  <input
                    name="costPerUnit"
                    type="number"
                    step="0.01"
                    min="0.01"
                    value={formData.costPerUnit}
                    onChange={handleInputChange}
                    placeholder="Enter cost per unit"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Current Stock
                  </label>
                  <input
                    name="currentStock"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.currentStock}
                    onChange={handleInputChange}
                    placeholder="Enter current stock"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Low Stock Threshold
                  </label>
                  <input
                    name="lowStockThreshold"
                    type="number"
                    min="1"
                    value={formData.lowStockThreshold}
                    onChange={handleInputChange}
                    placeholder="Enter threshold"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', paddingTop: '1.5rem', borderTop: '1px solid #e2e8f0' }}>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    backgroundColor: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: 'none',
                    backgroundColor: isSubmitting ? '#9ca3af' : '#3b82f6',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: isSubmitting ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    if (!isSubmitting) {
                      e.currentTarget.style.backgroundColor = '#2563eb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSubmitting) {
                      e.currentTarget.style.backgroundColor = '#3b82f6';
                    }
                  }}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                      {editingId ? 'Updating...' : 'Saving...'}
                    </>
                  ) : (
                    editingId ? 'Update Material' : 'Save Material'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showStockForm && (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                Add Stock
              </h2>
              <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                Add inventory to existing raw material
              </p>
            </div>
            <form onSubmit={handleStockSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1.5rem'
              }}>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Material
                  </label>
                  <div style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    backgroundColor: '#f8fafc',
                    fontSize: '0.875rem',
                    color: '#374151'
                  }}>
                    {stockFormData.materialName}
                  </div>
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Current Stock
                  </label>
                  <div style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    backgroundColor: '#f8fafc',
                    fontSize: '0.875rem',
                    color: '#374151'
                  }}>
                    {stockFormData.currentStock}
                  </div>
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Add Stock
                  </label>
                  <input
                    name="addStock"
                    type="number"
                    step="0.01"
                    min="0.01"
                    value={stockFormData.addStock}
                    onChange={handleStockInputChange}
                    placeholder="Enter amount to add"
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', paddingTop: '1.5rem', borderTop: '1px solid #e2e8f0' }}>
                <button
                  type="button"
                  onClick={() => setShowStockForm(false)}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    backgroundColor: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isAddingStock}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: 'none',
                    backgroundColor: isAddingStock ? '#9ca3af' : '#10b981',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: isAddingStock ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    if (!isAddingStock) {
                      e.currentTarget.style.backgroundColor = '#059669';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isAddingStock) {
                      e.currentTarget.style.backgroundColor = '#10b981';
                    }
                  }}
                >
                  {isAddingStock ? (
                    <>
                      <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus style={{ height: '1rem', width: '1rem' }} />
                      Add Stock
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Materials Table */}
      <div style={{ padding: '2rem' }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
              <thead>
                <tr style={{
                  borderBottom: '1px solid #e2e8f0',
                  backgroundColor: '#f8fafc'
                }}>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Name</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Unit</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Cost Per Unit</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Current Stock</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Value</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Status</th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'right',
                    fontWeight: '600',
                    color: '#374151'
                  }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {rawMaterials.map((material) => {
                  const isLowStock = material.currentStock <= material.lowStockThreshold;
                  return (
                    <tr key={material.id} style={{
                      borderBottom: '1px solid #f1f5f9',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}>
                      <td style={{ padding: '1rem', fontWeight: '500', color: '#0f172a' }}>{material.name}</td>
                      <td style={{ padding: '1rem', color: '#64748b' }}>{material.unit}</td>
                      <td style={{ padding: '1rem', color: '#0f172a', fontWeight: '500' }}>{formatCurrency(material.costPerUnit)}</td>
                      <td style={{ padding: '1rem', color: '#0f172a', fontWeight: '500' }}>{material.currentStock}</td>
                      <td style={{ padding: '1rem', color: '#0f172a', fontWeight: '600' }}>{formatCurrency(material.currentStock * material.costPerUnit)}</td>
                      <td style={{ padding: '1rem' }}>
                        {isLowStock ? (
                          <span style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            color: '#d97706',
                            fontWeight: '500'
                          }}>
                            <AlertTriangle style={{ height: '1rem', width: '1rem' }} />
                            <span>Low Stock</span>
                          </span>
                        ) : (
                          <span style={{ color: '#059669', fontWeight: '500' }}>In Stock</span>
                        )}
                      </td>
                      <td style={{ padding: '1rem', textAlign: 'right' }}>
                        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem' }}>
                          <button
                            onClick={() => handleEdit(material)}
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '2.5rem',
                              height: '2.5rem',
                              borderRadius: '6px',
                              border: 'none',
                              backgroundColor: 'transparent',
                              color: '#64748b',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#f1f5f9';
                              e.currentTarget.style.color = '#3b82f6';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.color = '#64748b';
                            }}
                          >
                            <Edit style={{ height: '1rem', width: '1rem' }} />
                          </button>
                          <button
                            onClick={() => handleDelete(material.id)}
                            disabled={isDeleting}
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '2.5rem',
                              height: '2.5rem',
                              borderRadius: '6px',
                              border: 'none',
                              backgroundColor: 'transparent',
                              color: '#64748b',
                              cursor: isDeleting ? 'not-allowed' : 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              if (!isDeleting) {
                                e.currentTarget.style.backgroundColor = '#fef2f2';
                                e.currentTarget.style.color = '#dc2626';
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (!isDeleting) {
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.color = '#64748b';
                              }
                            }}
                          >
                            {isDeleting && material.id === stockFormData.materialId ? (
                              <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                            ) : (
                              <Trash style={{ height: '1rem', width: '1rem' }} />
                            )}
                          </button>
                          <button
                            onClick={() => handleAddStock(material)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 0.75rem',
                              borderRadius: '6px',
                              border: '1px solid #d1d5db',
                              backgroundColor: 'white',
                              color: '#374151',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#f0fdf4';
                              e.currentTarget.style.borderColor = '#10b981';
                              e.currentTarget.style.color = '#059669';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'white';
                              e.currentTarget.style.borderColor = '#d1d5db';
                              e.currentTarget.style.color = '#374151';
                            }}
                          >
                            <Plus style={{ height: '0.875rem', width: '0.875rem' }} />
                            Add Stock
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
                {rawMaterials.length === 0 && !loading && (
                  <tr>
                    <td colSpan={7} style={{
                      padding: '3rem',
                      textAlign: 'center',
                      color: '#64748b',
                      fontSize: '0.875rem'
                    }}>
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
                        <div style={{
                          width: '3rem',
                          height: '3rem',
                          borderRadius: '50%',
                          backgroundColor: '#f1f5f9',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Plus style={{ height: '1.5rem', width: '1.5rem', color: '#64748b' }} />
                        </div>
                        <div>
                          <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>No raw materials found</p>
                          <p style={{ fontSize: '0.75rem' }}>Add your first raw material to get started</p>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
