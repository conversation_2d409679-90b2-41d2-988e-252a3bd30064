'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, Eye, UtensilsCrossed, Loader2 } from 'lucide-react';
import { EnhancedRecipeForm } from '@/components/recipes/enhanced-recipe-form';
import { RecipeDetail } from '@/components/recipes/recipe-detail';

// Define types for our data
type Ingredient = {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost?: number;
  category?: string;
  isOptional?: boolean;
  substitutes?: string;
};

type Recipe = {
  id: string;
  name: string;
  description: string;
  preparationTime: number;
  bakingTime: number;
  restingTime?: number;
  totalTime?: number;
  yield: number;
  yieldUnit: string;
  difficulty: string;
  category: string;
  tags: string[];
  ingredients: Ingredient[];
  instructions: string[];
  notes?: string;
  tips?: string[];
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sugar?: number;
  };
  costPerUnit?: number;
  sellingPrice?: number;
  profitMargin?: number;
  imageUrl?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
};

export default function RecipesPage() {
  // State for data
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingRecipe, setEditingRecipe] = useState<Recipe | null>(null);
  const [rawMaterials, setRawMaterials] = useState([]);

  // State for loading indicators
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch recipes and raw materials on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch recipes
        const recipesResponse = await fetch('/api/recipes');
        if (!recipesResponse.ok) {
          throw new Error('Failed to fetch recipes');
        }
        const recipesData = await recipesResponse.json();
        setRecipes(recipesData);

        // Fetch raw materials for ingredient selection
        const rawMaterialsResponse = await fetch('/api/raw-materials');
        if (!rawMaterialsResponse.ok) {
          throw new Error('Failed to fetch raw materials');
        }
        const rawMaterialsData = await rawMaterialsResponse.json();
        setRawMaterials(rawMaterialsData.map((material: any) => ({
          id: material.id,
          name: material.name,
          unit: material.unit,
          cost: material.costPerUnit,
        })));
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewRecipe = (recipe: Recipe) => {
    setSelectedRecipe(recipe);
    setShowForm(false);
  };

  const handleAddRecipe = () => {
    setEditingRecipe(null);
    setSelectedRecipe(null);
    setShowForm(true);
  };

  const handleEditRecipe = (recipe: Recipe) => {
    setEditingRecipe(recipe);
    setSelectedRecipe(null);
    setShowForm(true);
  };

  const handleDeleteRecipe = async (id: string) => {
    if (!confirm('Are you sure you want to delete this recipe?')) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/recipes/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete recipe');
      }

      // Remove the recipe from the state
      setRecipes(recipes.filter(recipe => recipe.id !== id));
    } catch (error) {
      console.error('Error deleting recipe:', error);
      alert('Failed to delete recipe. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleFormSubmit = async (formData: any) => {
    setIsSubmitting(true);
    try {
      if (editingRecipe) {
        // Update existing recipe
        const response = await fetch(`/api/recipes/${editingRecipe.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          throw new Error('Failed to update recipe');
        }

        const updatedRecipe = await response.json();

        // Update the recipe in the state
        setRecipes(recipes.map(recipe =>
          recipe.id === editingRecipe.id ? updatedRecipe : recipe
        ));
      } else {
        // Add new recipe
        const response = await fetch('/api/recipes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          throw new Error('Failed to create recipe');
        }

        const newRecipe = await response.json();

        // Add the new recipe to the state
        setRecipes([...recipes, newRecipe]);
      }

      setShowForm(false);
      setEditingRecipe(null);
    } catch (error) {
      console.error('Error saving recipe:', error);
      alert('Failed to save recipe. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingRecipe(null);
  };

  // Filter recipes based on search term
  const filteredRecipes = recipes.filter(recipe =>
    recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    recipe.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Recipe Management
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Manage your bakery recipes and ingredients • {filteredRecipes.length} recipes
            </p>
          </div>
          <button
            onClick={handleAddRecipe}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            Add Recipe
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#3b82f6' }} />
          <span style={{ marginLeft: '0.5rem', color: '#64748b' }}>Loading recipes...</span>
        </div>
      )}

      {/* Recipe Form */}
      {showForm && (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                {editingRecipe ? 'Edit Recipe' : 'Add New Recipe'}
              </h2>
              <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                {editingRecipe ? 'Update the recipe information and ingredients' : 'Create a new recipe with detailed ingredients and instructions'}
              </p>
            </div>
            <EnhancedRecipeForm
              initialData={editingRecipe || undefined}
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
              rawMaterials={rawMaterials}
            />
          </div>
        </div>
      )}

      {selectedRecipe && !showForm && (
        <RecipeDetail
          recipe={selectedRecipe}
          onBack={() => setSelectedRecipe(null)}
          onEdit={(recipe) => {
            setEditingRecipe(recipe as Recipe);
            setSelectedRecipe(null);
            setShowForm(true);
          }}
        />
      )}

      {/* Search and Recipe Grid */}
      {!selectedRecipe && !showForm && !loading && (
        <>
          {/* Search Section */}
          <div style={{
            backgroundColor: 'white',
            borderBottom: '1px solid #e2e8f0',
            padding: '1.5rem 2rem'
          }}>
            <div style={{ position: 'relative', maxWidth: '400px' }}>
              <svg
                style={{
                  position: 'absolute',
                  left: '14px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  height: '1.25rem',
                  width: '1.25rem',
                  color: '#64748b'
                }}
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.3-4.3" />
              </svg>
              <input
                type="text"
                placeholder="Search recipes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  paddingLeft: '3rem',
                  paddingRight: '1rem',
                  paddingTop: '0.875rem',
                  paddingBottom: '0.875rem',
                  borderRadius: '10px',
                  border: '1px solid #d1d5db',
                  fontSize: '0.875rem',
                  transition: 'all 0.2s',
                  backgroundColor: '#f8fafc'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = '#3b82f6';
                  e.currentTarget.style.backgroundColor = 'white';
                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.backgroundColor = '#f8fafc';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>
          </div>

          {/* Recipe Grid */}
          <div style={{ padding: '2rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
              gap: '1.5rem'
            }}>
              {filteredRecipes.map((recipe) => (
                <div
                  key={recipe.id}
                  style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    border: '1px solid #e2e8f0',
                    padding: '1.5rem',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.2s',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                  onClick={() => handleViewRecipe(recipe)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a' }}>{recipe.name}</h3>
                    <UtensilsCrossed style={{ height: '1.25rem', width: '1.25rem', color: '#64748b' }} />
                  </div>
                  <p style={{
                    marginBottom: '1rem',
                    fontSize: '0.875rem',
                    color: '#64748b',
                    lineHeight: '1.5',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}>
                    {recipe.description}
                  </p>
                  <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
                    <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <svg
                        style={{ height: '1rem', width: '1rem' }}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="12 6 12 12 16 14" />
                      </svg>
                      {recipe.preparationTime + recipe.bakingTime} min
                    </span>
                    <span style={{ marginLeft: '1rem' }}>
                      Yield: {recipe.yield} {recipe.yieldUnit}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem', paddingTop: '1rem', borderTop: '1px solid #f1f5f9' }}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewRecipe(recipe);
                      }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 0.75rem',
                        borderRadius: '6px',
                        border: '1px solid #d1d5db',
                        backgroundColor: 'white',
                        color: '#374151',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f0f9ff';
                        e.currentTarget.style.borderColor = '#3b82f6';
                        e.currentTarget.style.color = '#1d4ed8';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#d1d5db';
                        e.currentTarget.style.color = '#374151';
                      }}
                    >
                      <Eye style={{ height: '0.875rem', width: '0.875rem' }} />
                      View
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditRecipe(recipe);
                      }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 0.75rem',
                        borderRadius: '6px',
                        border: '1px solid #d1d5db',
                        backgroundColor: 'white',
                        color: '#374151',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f0fdf4';
                        e.currentTarget.style.borderColor = '#10b981';
                        e.currentTarget.style.color = '#059669';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#d1d5db';
                        e.currentTarget.style.color = '#374151';
                      }}
                    >
                      <Edit style={{ height: '0.875rem', width: '0.875rem' }} />
                      Edit
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteRecipe(recipe.id);
                      }}
                      disabled={isDeleting}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 0.75rem',
                        borderRadius: '6px',
                        border: '1px solid #fca5a5',
                        backgroundColor: '#fef2f2',
                        color: '#dc2626',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: isDeleting ? 'not-allowed' : 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        if (!isDeleting) {
                          e.currentTarget.style.backgroundColor = '#fee2e2';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isDeleting) {
                          e.currentTarget.style.backgroundColor = '#fef2f2';
                        }
                      }}
                    >
                      {isDeleting ? (
                        <Loader2 style={{ height: '0.875rem', width: '0.875rem', animation: 'spin 1s linear infinite' }} />
                      ) : (
                        <Trash style={{ height: '0.875rem', width: '0.875rem' }} />
                      )}
                      Delete
                    </button>
                  </div>
                </div>
              ))}
              {filteredRecipes.length === 0 && (
                <div style={{
                  gridColumn: '1 / -1',
                  borderRadius: '12px',
                  border: '2px dashed #d1d5db',
                  padding: '3rem 2rem',
                  textAlign: 'center'
                }}>
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
                    <div style={{
                      width: '3rem',
                      height: '3rem',
                      borderRadius: '50%',
                      backgroundColor: '#f1f5f9',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <UtensilsCrossed style={{ height: '1.5rem', width: '1.5rem', color: '#64748b' }} />
                    </div>
                    <div>
                      <p style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>
                        No recipes found
                      </p>
                      <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
                        {searchTerm ? 'Try adjusting your search criteria' : 'Add your first recipe to get started'}
                      </p>
                    </div>
                    {!searchTerm && (
                      <button
                        onClick={handleAddRecipe}
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          backgroundColor: '#3b82f6',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          padding: '0.75rem 1.5rem',
                          fontSize: '0.875rem',
                          fontWeight: '600',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          marginTop: '0.5rem'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#2563eb';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '#3b82f6';
                        }}
                      >
                        <Plus style={{ height: '1rem', width: '1rem' }} />
                        Create First Recipe
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
