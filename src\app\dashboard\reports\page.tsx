'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Import only what we need

// Import report components
import { SalesOverview } from '@/components/reports/sales-overview';
import { ProductPerformance } from '@/components/reports/product-performance';
import { CustomerInsights } from '@/components/reports/customer-insights';
import { FinancialSummary } from '@/components/reports/financial-summary';
import { WastageReport } from '@/components/reports/wastage-report';
import { ExpensesReport } from '@/components/reports/expenses-report';
import { PurchaseReport } from '@/components/reports/purchase-report';
import { LowStockReport } from '@/components/reports/low-stock-report';
import { ReportsDashboard } from '@/components/reports/reports-dashboard';
import { ProductionReport } from '@/components/reports/production-report';
import { InventoryReport } from '@/components/reports/inventory-report';

// No static data - all data comes from API endpoints

// Report types for the sidebar
const reportTypes = [
  { id: 'dashboard', name: 'Dashboard' },
  { id: 'sales', name: 'Sales Report' },
  { id: 'products', name: 'Product Performance' },
  { id: 'customers', name: 'Customer Insights' },
  { id: 'financial', name: 'Financial Summary' },
  { id: 'inventory', name: 'Inventory Report' },
  { id: 'purchase', name: 'Purchase Reports' },
  { id: 'lowStock', name: 'Low Stock Report' },
  { id: 'wastage', name: 'Wastage Report' },
  { id: 'expenses', name: 'Expenses Report' },
  { id: 'production', name: 'Production Report' },
];

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState('dashboard');
  const [timeRange, setTimeRange] = useState('week');
  const [dateRange, setDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true when component mounts
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange({ ...dateRange, [name]: value });
  };

  // Reusable report header component
  const renderReportHeader = (title: string, icon: string, showTimeRange = true) => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a' }}>{title}</h2>
        <div style={{
          backgroundColor: '#f1f5f9',
          padding: '0.5rem',
          borderRadius: '8px',
          fontSize: '1.25rem'
        }}>
          {icon}
        </div>
      </div>

      {showTimeRange && (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '1rem' }}>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            {['week', 'month', 'quarter', 'year'].map((period) => (
              <button
                key={period}
                onClick={() => setTimeRange(period)}
                style={{
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  border: timeRange === period ? 'none' : '1px solid #e2e8f0',
                  backgroundColor: timeRange === period ? '#3b82f6' : 'white',
                  color: timeRange === period ? 'white' : '#64748b',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textTransform: 'capitalize'
                }}
                onMouseEnter={(e) => {
                  if (timeRange !== period) {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                  }
                }}
                onMouseLeave={(e) => {
                  if (timeRange !== period) {
                    e.currentTarget.style.backgroundColor = 'white';
                  }
                }}
              >
                {period}
              </button>
            ))}
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <input
              type="date"
              name="startDate"
              value={dateRange.startDate}
              onChange={handleDateChange}
              style={{
                padding: '0.5rem',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white'
              }}
            />
            <input
              type="date"
              name="endDate"
              value={dateRange.endDate}
              onChange={handleDateChange}
              style={{
                padding: '0.5rem',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white'
              }}
            />
          </div>
        </div>
      )}
    </div>
  );

  const renderReportContent = () => {
    if (!isClient) return null;

    switch (selectedReport) {
      case 'dashboard':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a' }}>Dashboard Overview</h2>
              <div style={{
                backgroundColor: '#f1f5f9',
                padding: '0.5rem',
                borderRadius: '8px',
                fontSize: '1.25rem'
              }}>
                📊
              </div>
            </div>

            <div style={{
              display: 'flex',
              gap: '0.5rem',
              borderBottom: '1px solid #e2e8f0',
              paddingBottom: '1rem'
            }}>
              {['week', 'month', 'quarter', 'year'].map((period) => (
                <button
                  key={period}
                  onClick={() => setTimeRange(period)}
                  style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: timeRange === period ? '#3b82f6' : 'transparent',
                    color: timeRange === period ? 'white' : '#64748b',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    textTransform: 'capitalize'
                  }}
                  onMouseEnter={(e) => {
                    if (timeRange !== period) {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (timeRange !== period) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  {period}
                </button>
              ))}
            </div>

            <ReportsDashboard
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
            />
          </div>
        );

      case 'sales':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a' }}>Sales Report</h2>
              <div style={{
                backgroundColor: '#f1f5f9',
                padding: '0.5rem',
                borderRadius: '8px',
                fontSize: '1.25rem'
              }}>
                💰
              </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '1rem' }}>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                {['week', 'month', 'quarter', 'year'].map((period) => (
                  <button
                    key={period}
                    onClick={() => setTimeRange(period)}
                    style={{
                      padding: '0.5rem 1rem',
                      borderRadius: '6px',
                      border: timeRange === period ? 'none' : '1px solid #e2e8f0',
                      backgroundColor: timeRange === period ? '#3b82f6' : 'white',
                      color: timeRange === period ? 'white' : '#64748b',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      textTransform: 'capitalize'
                    }}
                    onMouseEnter={(e) => {
                      if (timeRange !== period) {
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (timeRange !== period) {
                        e.currentTarget.style.backgroundColor = 'white';
                      }
                    }}
                  >
                    {period}
                  </button>
                ))}
              </div>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <input
                  type="date"
                  name="startDate"
                  value={dateRange.startDate}
                  onChange={handleDateChange}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '6px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white'
                  }}
                />
                <input
                  type="date"
                  name="endDate"
                  value={dateRange.endDate}
                  onChange={handleDateChange}
                  style={{
                    padding: '0.5rem',
                    borderRadius: '6px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white'
                  }}
                />
              </div>
            </div>

            <SalesOverview
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'products':
        return (
          <div>
            {renderReportHeader('Product Performance', '📦')}
            <ProductPerformance
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'purchase':
        return (
          <div>
            {renderReportHeader('Purchase Report', '🛒')}
            <PurchaseReport
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'itemSales':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {renderReportHeader('Item Wise Sales Report', '📋', false)}
            <div style={{
              backgroundColor: '#f8fafc',
              border: '2px dashed #d1d5db',
              borderRadius: '12px',
              padding: '3rem',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
              <p style={{ color: '#64748b', marginBottom: '1rem', fontSize: '1rem' }}>
                This report has been updated to use real data.
              </p>
              <p style={{ color: '#64748b', marginBottom: '2rem', fontSize: '0.875rem' }}>
                Please use the Product Performance report for detailed product sales analysis.
              </p>
              <button
                onClick={() => setSelectedReport('products')}
                style={{
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2563eb';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#3b82f6';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                Go to Product Performance
              </button>
            </div>
          </div>
        );

      case 'lowStock':
        return (
          <div>
            {renderReportHeader('Low Stock Report', '⚠️', false)}
            <LowStockReport />
          </div>
        );

      case 'wastage':
        return (
          <div>
            {renderReportHeader('Wastage Report', '🗑️')}
            <WastageReport
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'expenses':
        return (
          <div>
            {renderReportHeader('Expenses Report', '💸')}
            <ExpensesReport
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'customers':
        return (
          <div>
            {renderReportHeader('Customer Insights', '👥')}
            <CustomerInsights
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'financial':
        return (
          <div>
            {renderReportHeader('Financial Summary', '💼')}
            <FinancialSummary
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      case 'inventory':
        return (
          <div>
            {renderReportHeader('Inventory Report', '📦', false)}
            <InventoryReport />
          </div>
        );

      case 'production':
        return (
          <div>
            {renderReportHeader('Production Report', '🏭')}
            <ProductionReport
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
            />
          </div>
        );

      default:
        return (
          <div style={{
            backgroundColor: '#f8fafc',
            border: '2px dashed #d1d5db',
            borderRadius: '12px',
            padding: '4rem',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📊</div>
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Select a Report
            </h3>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Choose a report type from the sidebar to view detailed analytics and insights.
            </p>
          </div>
        );
    }
  };

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem',
        marginBottom: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Reports & Analytics
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Generate comprehensive reports and analyze your bakery business performance
            </p>
          </div>
          <div style={{
            backgroundColor: '#f1f5f9',
            padding: '1rem',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem'
          }}>
            <div style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '8px',
              fontSize: '1.25rem'
            }}>
              📊
            </div>
            <div>
              <div style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '500' }}>ACTIVE REPORTS</div>
              <div style={{ fontSize: '1.25rem', fontWeight: '700', color: '#0f172a' }}>{reportTypes.length}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div style={{ padding: '0 2rem 2rem' }}>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '300px 1fr',
          gap: '2rem'
        }}>
          {/* Sidebar */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* Report Types */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              padding: '1.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h3 style={{
                fontSize: '1rem',
                fontWeight: '500',
                color: '#0f172a',
                marginBottom: '1rem'
              }}>
                Report Types
              </h3>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '0.25rem'
              }}>
                {reportTypes.map((report) => (
                  <button
                    key={report.id}
                    onClick={() => setSelectedReport(report.id)}
                    style={{
                      width: '100%',
                      padding: '0.75rem 1rem',
                      borderRadius: '8px',
                      border: 'none',
                      backgroundColor: selectedReport === report.id ? '#3b82f6' : 'transparent',
                      color: selectedReport === report.id ? 'white' : '#374151',
                      fontSize: '0.875rem',
                      fontWeight: selectedReport === report.id ? '500' : '400',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      textAlign: 'left'
                    }}
                    onMouseEnter={(e) => {
                      if (selectedReport !== report.id) {
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedReport !== report.id) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    {report.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              padding: '1.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h3 style={{
                fontSize: '1rem',
                fontWeight: '500',
                color: '#0f172a',
                marginBottom: '1rem'
              }}>
                Date Range
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div>
                  <label
                    htmlFor="startDate"
                    style={{
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem',
                      display: 'block'
                    }}
                  >
                    Start Date
                  </label>
                  <input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={dateRange.startDate}
                    onChange={handleDateChange}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label
                    htmlFor="endDate"
                    style={{
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem',
                      display: 'block'
                    }}
                  >
                    End Date
                  </label>
                  <input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={dateRange.endDate}
                    onChange={handleDateChange}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <button
                  style={{
                    width: '100%',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '0.875rem',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  Generate Report
                </button>
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            minHeight: '600px'
          }}>
            {renderReportContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
