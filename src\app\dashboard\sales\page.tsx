'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Eye, Trash, FileText, Loader2 } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

interface Sale {
  id: string;
  invoiceNumber: string;
  date: string;
  storeId: string;
  storeName: string;
  customerName: string;
  customerContact: string;
  totalAmount: number;
  discount: number;
  paymentMethod: string;
  status: string;
  items: {
    id: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

interface Store {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  unit: string;
}

export default function SalesPage() {
  const [sales, setSales] = useState<Sale[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [formData, setFormData] = useState({
    storeId: '',
    customerName: '',
    customerContact: '',
    paymentMethod: 'CASH',
    discount: '0',
  });
  const [saleItems, setSaleItems] = useState<Array<{
    productId: string;
    productName: string;
    quantity: string;
    unitPrice: string;
    totalPrice: string;
  }>>([]);

  // Fetch sales, stores, and products data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch sales
        const salesResponse = await fetch('/api/sales');
        if (!salesResponse.ok) {
          throw new Error('Failed to fetch sales');
        }
        const salesData = await salesResponse.json();
        setSales(salesData);

        // Fetch stores
        const storesResponse = await fetch('/api/stores');
        if (!storesResponse.ok) {
          throw new Error('Failed to fetch stores');
        }
        const storesData = await storesResponse.json();
        setStores(storesData);

        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products');
        }
        const productsData = await productsResponse.json();

        // Transform products to the expected format
        const formattedProducts = productsData.map((product: any) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          unit: product.unit,
        }));

        setProducts(formattedProducts);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleAddItem = () => {
    setSaleItems([
      ...saleItems,
      {
        productId: '',
        productName: '',
        quantity: '',
        unitPrice: '',
        totalPrice: '',
      },
    ]);
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const updatedItems = [...saleItems];

    if (name === 'productId') {
      const product = products.find(p => p.id === value);
      if (product) {
        updatedItems[index] = {
          ...updatedItems[index],
          productId: value,
          productName: product.name,
          unitPrice: product.price.toString(),
          totalPrice: (product.price * parseFloat(updatedItems[index].quantity || '1')).toString(),
        };
      }
    } else if (name === 'quantity') {
      const quantity = parseFloat(value) || 0;
      const unitPrice = parseFloat(updatedItems[index].unitPrice) || 0;
      updatedItems[index] = {
        ...updatedItems[index],
        quantity: value,
        totalPrice: (quantity * unitPrice).toString(),
      };
    } else {
      updatedItems[index] = {
        ...updatedItems[index],
        [name]: value,
      };
    }

    setSaleItems(updatedItems);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...saleItems];
    updatedItems.splice(index, 1);
    setSaleItems(updatedItems);
  };

  const calculateTotal = () => {
    const subtotal = saleItems.reduce((sum, item) => {
      return sum + (parseFloat(item.totalPrice) || 0);
    }, 0);

    const discount = parseFloat(formData.discount) || 0;
    return subtotal - discount;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (saleItems.length === 0) {
      alert('Please add at least one item to the sale');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storeId: formData.storeId,
          customerName: formData.customerName,
          customerContact: formData.customerContact,
          paymentMethod: formData.paymentMethod,
          discount: formData.discount,
          items: saleItems,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create sale');
      }

      const newSale = await response.json();
      setSales([newSale, ...sales]);
      setShowForm(false);
      setFormData({
        storeId: '',
        customerName: '',
        customerContact: '',
        paymentMethod: 'CASH',
        discount: '0',
      });
      setSaleItems([]);
    } catch (err) {
      console.error('Error creating sale:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while creating the sale');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewSale = (sale: Sale) => {
    setSelectedSale(sale);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this sale?')) {
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/sales/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete sale');
      }

      setSales(sales.filter(sale => sale.id !== id));

      // If the deleted sale is currently selected, clear the selection
      if (selectedSale && selectedSale.id === id) {
        setSelectedSale(null);
      }
    } catch (err) {
      console.error('Error deleting sale:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while deleting the sale');
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePrintInvoice = (sale: any) => {
    // Create a professional MISPRI receipt
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const receiptHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt - ${sale.invoiceNumber}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 20px;
            color: #333;
          }
          .receipt-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
          }
          .receipt-header {
            background: linear-gradient(135deg, #5f9ea0 0%, #4682b4 100%);
            color: white;
            padding: 25px 20px;
            text-align: center;
            position: relative;
          }
          .receipt-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 20px;
            background: white;
            border-radius: 50% 50% 0 0 / 100% 100% 0 0;
          }
          .company-logo {
            font-size: 32px;
            font-weight: 800;
            letter-spacing: 2px;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
          }
          .company-tagline {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 300;
          }
          .receipt-body {
            padding: 30px 20px 20px;
          }
          .receipt-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #5f9ea0;
          }
          .receipt-info h3 {
            color: #5f9ea0;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 600;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
          }
          .info-label {
            color: #666;
            font-weight: 500;
          }
          .info-value {
            color: #333;
            font-weight: 600;
          }
          .customer-info {
            background: #e8f4f8;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #4682b4;
          }
          .items-section {
            margin-bottom: 20px;
          }
          .items-header {
            background: #5f9ea0;
            color: white;
            padding: 12px 15px;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            font-size: 14px;
          }
          .item-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            background: white;
          }
          .item-row:last-child {
            border-bottom: none;
            border-radius: 0 0 8px 8px;
          }
          .item-details {
            flex: 1;
          }
          .item-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
            margin-bottom: 2px;
          }
          .item-qty-price {
            font-size: 12px;
            color: #666;
          }
          .item-total {
            font-weight: 700;
            color: #5f9ea0;
            font-size: 14px;
          }
          .totals-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
          }
          .total-row.final {
            border-top: 2px solid #5f9ea0;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 16px;
            font-weight: 700;
            color: #5f9ea0;
          }
          .payment-info {
            background: #e8f4f8;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
          }
          .payment-method {
            font-size: 14px;
            color: #4682b4;
            font-weight: 600;
            margin-bottom: 5px;
          }
          .payment-status {
            font-size: 12px;
            color: #28a745;
            font-weight: 500;
          }
          .receipt-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
          }
          .thank-you {
            font-size: 16px;
            font-weight: 600;
            color: #5f9ea0;
            margin-bottom: 10px;
          }
          .footer-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
          }
          .contact-info {
            margin-top: 15px;
            font-size: 11px;
            color: #888;
          }
          @media print {
            body {
              background: white;
              padding: 0;
            }
            .receipt-container {
              box-shadow: none;
              max-width: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="receipt-container">
          <!-- Header -->
          <div class="receipt-header">
            <div class="company-logo">MISPRI</div>
            <div class="company-tagline">Bakery & Flower Shop • Bhubaneswar</div>
          </div>

          <!-- Body -->
          <div class="receipt-body">
            <!-- Receipt Info -->
            <div class="receipt-info">
              <h3>Receipt Details</h3>
              <div class="info-row">
                <span class="info-label">Receipt #:</span>
                <span class="info-value">${sale.invoiceNumber}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Date:</span>
                <span class="info-value">${formatDate(sale.date)}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Time:</span>
                <span class="info-value">${sale.time || new Date().toLocaleTimeString('en-IN', { hour: '2-digit', minute: '2-digit' })}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Store:</span>
                <span class="info-value">${sale.storeName}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Source:</span>
                <span class="info-value">${sale.source || 'POS'}</span>
              </div>
            </div>

            <!-- Customer Info -->
            <div class="customer-info">
              <h3 style="color: #4682b4; font-size: 16px; margin-bottom: 8px; font-weight: 600;">Customer Information</h3>
              <div class="info-row">
                <span class="info-label">Name:</span>
                <span class="info-value">${sale.customerName || 'Walk-in Customer'}</span>
              </div>
              ${sale.customerPhone ? `
                <div class="info-row">
                  <span class="info-label">Phone:</span>
                  <span class="info-value">${sale.customerPhone}</span>
                </div>
              ` : ''}
              ${sale.customerEmail ? `
                <div class="info-row">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${sale.customerEmail}</span>
                </div>
              ` : ''}
            </div>

            <!-- Items -->
            <div class="items-section">
              <div class="items-header">Items Purchased</div>
              ${sale.items.map((item: any) => `
                <div class="item-row">
                  <div class="item-details">
                    <div class="item-name">${item.name || item.productName}</div>
                    <div class="item-qty-price">${item.quantity} × ₹${(item.price || item.unitPrice).toFixed(2)}</div>
                  </div>
                  <div class="item-total">₹${((item.price || item.unitPrice) * item.quantity).toFixed(2)}</div>
                </div>
              `).join('')}
            </div>

            <!-- Totals -->
            <div class="totals-section">
              <div class="total-row">
                <span>Subtotal:</span>
                <span>₹${(sale.subtotal || sale.totalAmount + (sale.discount || 0)).toFixed(2)}</span>
              </div>
              ${sale.discount > 0 ? `
                <div class="total-row">
                  <span>Discount:</span>
                  <span style="color: #dc3545;">-₹${sale.discount.toFixed(2)}</span>
                </div>
              ` : ''}
              <div class="total-row">
                <span>Tax (GST):</span>
                <span>₹${(sale.tax || 0).toFixed(2)}</span>
              </div>
              <div class="total-row final">
                <span>Total Amount:</span>
                <span>₹${sale.totalAmount.toFixed(2)}</span>
              </div>
            </div>

            <!-- Payment Info -->
            <div class="payment-info">
              <div class="payment-method">Payment Method: ${sale.paymentMethod || 'Cash'}</div>
              <div class="payment-status">✓ Payment Completed</div>
            </div>
          </div>

          <!-- Footer -->
          <div class="receipt-footer">
            <div class="thank-you">Thank You for Your Purchase! 🎉</div>
            <div class="footer-text">
              We appreciate your business and hope you enjoy your delicious treats from MISPRI!
            </div>
            <div class="contact-info">
              📍 Bhubaneswar, Odisha | 📞 +91-XXXXXXXXXX | 📧 <EMAIL><br>
              Visit us: www.mispri.com | Follow us on social media
            </div>
          </div>
        </div>

        <script>
          window.onload = function() {
            window.print();
            window.onafterprint = function() {
              window.close();
            };
          };
        </script>
      </body>
      </html>
    `;

    printWindow.document.write(receiptHTML);
    printWindow.document.close();
  };

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Sales Management
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Manage sales transactions and invoices • {sales.length} sales
            </p>
          </div>
          <button
            onClick={() => {
              setShowForm(!showForm);
              setSelectedSale(null);
            }}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            New Sale
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div style={{
          margin: '2rem',
          borderRadius: '8px',
          backgroundColor: '#fef2f2',
          border: '1px solid #fca5a5',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {/* New Sale Form */}
      {showForm && (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                New Sale
              </h2>
              <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                Create a new sales transaction with customer details and items
              </p>
            </div>
            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1.5rem'
              }}>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Store
                  </label>
                  <select
                    name="storeId"
                    value={formData.storeId}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <option value="">Select a store</option>
                    {stores.map(store => (
                      <option key={store.id} value={store.id}>
                        {store.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Payment Method
                  </label>
                  <select
                    name="paymentMethod"
                    value={formData.paymentMethod}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <option value="CASH">Cash</option>
                    <option value="CARD">Card</option>
                    <option value="BANK_TRANSFER">Bank Transfer</option>
                    <option value="ONLINE">Online</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Customer Name
                  </label>
                  <input
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    placeholder="Enter customer name"
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Customer Contact
                  </label>
                  <input
                    name="customerContact"
                    value={formData.customerContact}
                    onChange={handleInputChange}
                    placeholder="Enter customer contact"
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                </div>
              </div>

              {/* Sale Items Section */}
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                  <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Sale Items</h3>
                  <button
                    type="button"
                    onClick={handleAddItem}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      borderRadius: '6px',
                      border: '1px solid #d1d5db',
                      backgroundColor: 'white',
                      color: '#374151',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f0f9ff';
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.color = '#1d4ed8';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.color = '#374151';
                    }}
                  >
                    <Plus style={{ height: '0.875rem', width: '0.875rem' }} />
                    Add Item
                  </button>
                </div>

                {saleItems.length > 0 ? (
                  <div style={{
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0',
                    overflow: 'hidden'
                  }}>
                    <table style={{ width: '100%', fontSize: '0.875rem' }}>
                      <thead>
                        <tr style={{
                          borderBottom: '1px solid #e2e8f0',
                          backgroundColor: '#f8fafc'
                        }}>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Product</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Quantity</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Unit Price</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'left',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Total</th>
                          <th style={{
                            padding: '0.75rem 1rem',
                            textAlign: 'right',
                            fontWeight: '500',
                            color: '#374151'
                          }}>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {saleItems.map((item, index) => (
                          <tr key={index} style={{ borderBottom: '1px solid #f1f5f9' }}>
                            <td style={{ padding: '0.75rem 1rem' }}>
                              <select
                                name="productId"
                                value={item.productId}
                                onChange={(e) => handleItemChange(index, e)}
                                required
                                style={{
                                  width: '100%',
                                  padding: '0.5rem',
                                  borderRadius: '6px',
                                  border: '1px solid #d1d5db',
                                  fontSize: '0.75rem',
                                  backgroundColor: 'white'
                                }}
                              >
                                <option value="">Select a product</option>
                                {products.map(product => (
                                  <option key={product.id} value={product.id}>
                                    {product.name} ({formatCurrency(product.price)}/{product.unit})
                                  </option>
                                ))}
                              </select>
                            </td>
                            <td style={{ padding: '0.75rem 1rem' }}>
                              <input
                                name="quantity"
                                type="number"
                                min="0.01"
                                step="0.01"
                                value={item.quantity}
                                onChange={(e) => handleItemChange(index, e)}
                                required
                                style={{
                                  width: '100%',
                                  padding: '0.5rem',
                                  borderRadius: '6px',
                                  border: '1px solid #d1d5db',
                                  fontSize: '0.75rem'
                                }}
                              />
                            </td>
                            <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                              {formatCurrency(parseFloat(item.unitPrice) || 0)}
                            </td>
                            <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                              {formatCurrency(parseFloat(item.totalPrice) || 0)}
                            </td>
                            <td style={{ padding: '0.75rem 1rem', textAlign: 'right' }}>
                              <button
                                type="button"
                                onClick={() => handleRemoveItem(index)}
                                style={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  width: '2rem',
                                  height: '2rem',
                                  borderRadius: '6px',
                                  border: 'none',
                                  backgroundColor: 'transparent',
                                  color: '#64748b',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.backgroundColor = '#fef2f2';
                                  e.currentTarget.style.color = '#dc2626';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.backgroundColor = 'transparent';
                                  e.currentTarget.style.color = '#64748b';
                                }}
                              >
                                <Trash style={{ height: '1rem', width: '1rem' }} />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div style={{
                    borderRadius: '8px',
                    border: '2px dashed #d1d5db',
                    padding: '2rem',
                    textAlign: 'center',
                    color: '#64748b'
                  }}>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '0.75rem' }}>
                      <div style={{
                        width: '2.5rem',
                        height: '2.5rem',
                        borderRadius: '50%',
                        backgroundColor: '#f1f5f9',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <Plus style={{ height: '1.25rem', width: '1.25rem', color: '#64748b' }} />
                      </div>
                      <p style={{ fontSize: '0.875rem' }}>No items added. Click "Add Item" to add products to this sale.</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Totals Section */}
              <div style={{ paddingTop: '1.5rem', borderTop: '1px solid #e2e8f0' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', alignItems: 'flex-end' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', width: '300px', fontSize: '0.875rem' }}>
                    <span style={{ color: '#64748b' }}>Subtotal:</span>
                    <span style={{ fontWeight: '500' }}>
                      {formatCurrency(
                        saleItems.reduce((sum, item) => sum + (parseFloat(item.totalPrice) || 0), 0)
                      )}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '300px', fontSize: '0.875rem' }}>
                    <span style={{ color: '#64748b' }}>Discount:</span>
                    <input
                      name="discount"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.discount}
                      onChange={handleInputChange}
                      style={{
                        width: '6rem',
                        padding: '0.5rem',
                        borderRadius: '6px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.75rem',
                        textAlign: 'right'
                      }}
                    />
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', width: '300px', fontSize: '1rem', fontWeight: '600', paddingTop: '0.5rem', borderTop: '1px solid #e2e8f0' }}>
                    <span>Total:</span>
                    <span style={{ color: '#059669' }}>{formatCurrency(calculateTotal())}</span>
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', paddingTop: '1.5rem', borderTop: '1px solid #e2e8f0' }}>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    backgroundColor: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: 'none',
                    backgroundColor: isSubmitting ? '#9ca3af' : '#059669',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: isSubmitting ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    if (!isSubmitting) {
                      e.currentTarget.style.backgroundColor = '#047857';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSubmitting) {
                      e.currentTarget.style.backgroundColor = '#059669';
                    }
                  }}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                      Processing...
                    </>
                  ) : (
                    'Complete Sale'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Sale Details Modal */}
      {selectedSale && (
        <div style={{
          padding: '2rem',
          margin: '2rem',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          backgroundColor: 'white',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '1.5rem'
          }}>
            <h2 style={{
              fontSize: '1.125rem',
              fontWeight: '500',
              color: '#0f172a'
            }}>Sale Details</h2>
            <button
              onClick={() => setSelectedSale(null)}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                backgroundColor: 'white',
                color: '#374151',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
              }}
            >
              Close
            </button>
          </div>

          <div className="mb-6 grid gap-4 md:grid-cols-2">
            <div>
              <h3 className="mb-2 text-sm font-medium">Invoice Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Invoice Number:</span>
                  <span>{selectedSale.invoiceNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Date:</span>
                  <span>{formatDate(selectedSale.date)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Store:</span>
                  <span>{selectedSale.storeName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                    {selectedSale.status}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="mb-2 text-sm font-medium">Customer Information</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Name:</span>
                  <span>{selectedSale.customerName || 'Walk-in Customer'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Contact:</span>
                  <span>{selectedSale.customerContact || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method:</span>
                  <span>{selectedSale.paymentMethod.replace('_', ' ')}</span>
                </div>
              </div>
            </div>
          </div>

          <h3 className="mb-2 text-sm font-medium">Sale Items</h3>
          <div className="rounded-md border">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="px-4 py-2 text-left font-medium">Product</th>
                  <th className="px-4 py-2 text-left font-medium">Quantity</th>
                  <th className="px-4 py-2 text-left font-medium">Unit Price</th>
                  <th className="px-4 py-2 text-right font-medium">Total</th>
                </tr>
              </thead>
              <tbody>
                {selectedSale.items.map((item) => (
                  <tr key={item.id} className="border-b">
                    <td className="px-4 py-2">{item.productName}</td>
                    <td className="px-4 py-2">{item.quantity}</td>
                    <td className="px-4 py-2">{formatCurrency(item.unitPrice)}</td>
                    <td className="px-4 py-2 text-right">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="border-t">
                  <td colSpan={3} className="px-4 py-2 text-right font-medium">
                    Subtotal:
                  </td>
                  <td className="px-4 py-2 text-right">
                    {formatCurrency(
                      selectedSale.items.reduce((sum, item) => sum + item.totalPrice, 0)
                    )}
                  </td>
                </tr>
                <tr>
                  <td colSpan={3} className="px-4 py-2 text-right font-medium">
                    Discount:
                  </td>
                  <td className="px-4 py-2 text-right">
                    {formatCurrency(selectedSale.discount)}
                  </td>
                </tr>
                <tr className="font-medium">
                  <td colSpan={3} className="px-4 py-2 text-right">
                    Total:
                  </td>
                  <td className="px-4 py-2 text-right">
                    {formatCurrency(selectedSale.totalAmount)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          <div className="mt-4 flex justify-end gap-2">
            <Button variant="outline" size="sm" onClick={() => handlePrintInvoice(selectedSale)}>
              <FileText className="mr-2 h-4 w-4" />
              Print Invoice
            </Button>
          </div>
        </div>
      )}

      {/* Sales Table */}
      {loading ? (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#3b82f6' }} />
          <span style={{ marginLeft: '0.5rem', color: '#64748b' }}>Loading sales...</span>
        </div>
      ) : (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', fontSize: '0.875rem' }}>
                <thead>
                  <tr style={{
                    borderBottom: '1px solid #e2e8f0',
                    backgroundColor: '#f8fafc'
                  }}>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Invoice #</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Date</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Store</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Customer</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Source</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Amount</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Status</th>
                    <th style={{
                      padding: '1rem',
                      textAlign: 'right',
                      fontWeight: '600',
                      color: '#374151'
                    }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sales.map((sale) => (
                    <tr key={sale.id} style={{
                      borderBottom: '1px solid #f1f5f9',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}>
                      <td style={{ padding: '1rem', fontWeight: '500', color: '#0f172a' }}>{sale.invoiceNumber}</td>
                      <td style={{ padding: '1rem', color: '#64748b' }}>{formatDate(sale.date)}</td>
                      <td style={{ padding: '1rem', fontWeight: '500', color: '#0f172a' }}>{sale.storeName}</td>
                      <td style={{ padding: '1rem', color: '#64748b' }}>{sale.customerName || 'Walk-in Customer'}</td>
                      <td style={{ padding: '1rem', fontWeight: '600', color: '#059669' }}>{formatCurrency(sale.totalAmount)}</td>
                      <td style={{ padding: '1rem' }}>
                        <span style={{
                          padding: '0.25rem 0.75rem',
                          borderRadius: '9999px',
                          fontSize: '0.75rem',
                          fontWeight: '500',
                          backgroundColor: '#dcfce7',
                          color: '#166534'
                        }}>
                          {sale.status}
                        </span>
                      </td>
                      <td style={{ padding: '1rem', textAlign: 'right' }}>
                        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem' }}>
                          <button
                            onClick={() => handleViewSale(sale)}
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '2.5rem',
                              height: '2.5rem',
                              borderRadius: '6px',
                              border: 'none',
                              backgroundColor: 'transparent',
                              color: '#64748b',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#f1f5f9';
                              e.currentTarget.style.color = '#3b82f6';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.color = '#64748b';
                            }}
                          >
                            <Eye style={{ height: '1rem', width: '1rem' }} />
                          </button>
                          <button
                            onClick={() => handleDelete(sale.id)}
                            disabled={isDeleting}
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '2.5rem',
                              height: '2.5rem',
                              borderRadius: '6px',
                              border: 'none',
                              backgroundColor: 'transparent',
                              color: '#64748b',
                              cursor: isDeleting ? 'not-allowed' : 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              if (!isDeleting) {
                                e.currentTarget.style.backgroundColor = '#fef2f2';
                                e.currentTarget.style.color = '#dc2626';
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (!isDeleting) {
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.color = '#64748b';
                              }
                            }}
                          >
                            {isDeleting && selectedSale?.id === sale.id ? (
                              <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                            ) : (
                              <Trash style={{ height: '1rem', width: '1rem' }} />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                  {sales.length === 0 && !loading && (
                    <tr>
                      <td colSpan={7} style={{
                        padding: '3rem',
                        textAlign: 'center',
                        color: '#64748b',
                        fontSize: '0.875rem'
                      }}>
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
                          <div style={{
                            width: '3rem',
                            height: '3rem',
                            borderRadius: '50%',
                            backgroundColor: '#f1f5f9',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <Plus style={{ height: '1.5rem', width: '1.5rem', color: '#64748b' }} />
                          </div>
                          <div>
                            <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>No sales found</p>
                            <p style={{ fontSize: '0.75rem' }}>Create your first sale to get started</p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
