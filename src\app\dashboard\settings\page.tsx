'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Save, RefreshCw, Plus, Trash, Eye, EyeOff, CreditCard, MessageSquare, Tag, AlertCircle, Loader2, Share2 } from 'lucide-react';

// API Integration types
interface ApiIntegration {
  id: string;
  type: string;
  name: string;
  isActive: boolean;
  credentials: any;
  settings: any;
  createdAt: string;
  updatedAt: string;
}

// Mock settings data
const mockSettings = {
  company: {
    name: 'Bakery Market',
    address: '123 Bakery Street, City',
    phone: '+1234567890',
    email: '<EMAIL>',
    website: 'www.bakerymarket.com',
    taxId: 'TAX-12345678',
  },
  invoice: {
    prefix: 'INV-',
    footer: 'Thank you for your business!',
    terms: 'Payment due within 30 days.',
    showLogo: true,
    showTax: true,
  },
  notifications: {
    lowStockAlert: true,
    lowStockThreshold: 10,
    orderNotifications: true,
    emailNotifications: true,
    smsNotifications: false,
  },
  backup: {
    autoBackup: true,
    backupFrequency: 'daily',
    lastBackup: '2023-04-15 08:30:00',
  },
  integrations: {
    paymentGateways: [],
    whatsapp: [],
    facebookPixel: [],
    googleTagManager: [],
  }
};

export default function SettingsPage() {
  const [settings, setSettings] = useState(mockSettings);
  const [activeTab, setActiveTab] = useState('company');
  const [activeIntegrationTab, setActiveIntegrationTab] = useState('payment');
  const [apiIntegrations, setApiIntegrations] = useState<ApiIntegration[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for API integration form
  const [showIntegrationForm, setShowIntegrationForm] = useState(false);
  const [integrationFormType, setIntegrationFormType] = useState<string>('');
  const [integrationFormData, setIntegrationFormData] = useState<any>({});
  const [editingIntegrationId, setEditingIntegrationId] = useState<string | null>(null);

  // Fetch API integrations when component mounts
  useEffect(() => {
    const fetchApiIntegrations = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/settings/api-integrations');
        if (!response.ok) {
          throw new Error('Failed to fetch API integrations');
        }

        const data = await response.json();
        setApiIntegrations(data);
      } catch (err) {
        console.error('Error fetching API integrations:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchApiIntegrations();
  }, []);

  // API Integration handlers
  const handleAddIntegration = (type: string) => {
    setIntegrationFormType(type);
    setEditingIntegrationId(null);

    // Set default form data based on integration type
    let defaultData: any = {
      name: '',
      isActive: false,
      credentials: {},
      settings: {},
    };

    switch (type) {
      case 'PAYMENT_GATEWAY':
        defaultData.name = 'New Payment Gateway';
        defaultData.credentials = { apiKey: '', secretKey: '' };
        defaultData.settings = { mode: 'test', currency: 'INR' };
        break;
      case 'WHATSAPP':
        defaultData.name = 'WhatsApp Business API';
        defaultData.credentials = { apiKey: '', phoneNumberId: '' };
        defaultData.settings = { phoneNumber: '', businessName: '' };
        break;
      case 'FACEBOOK_PIXEL':
        defaultData.name = 'Facebook Pixel';
        defaultData.credentials = { pixelId: '' };
        defaultData.settings = { events: ['PageView', 'Purchase'] };
        break;
      case 'GOOGLE_TAG_MANAGER':
        defaultData.name = 'Google Tag Manager';
        defaultData.credentials = { containerId: '' };
        defaultData.settings = { environment: 'production' };
        break;
    }

    setIntegrationFormData(defaultData);
    setShowIntegrationForm(true);
  };

  const handleEditIntegration = (integration: ApiIntegration) => {
    setIntegrationFormType(integration.type);
    setEditingIntegrationId(integration.id);
    setIntegrationFormData({
      name: integration.name,
      isActive: integration.isActive,
      credentials: integration.credentials || {},
      settings: integration.settings || {},
    });
    setShowIntegrationForm(true);
  };

  const handleSaveIntegration = async () => {
    setLoading(true);
    setError(null);

    try {
      const url = editingIntegrationId
        ? `/api/settings/api-integrations/${editingIntegrationId}`
        : '/api/settings/api-integrations';

      const method = editingIntegrationId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: integrationFormType,
          ...integrationFormData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save integration');
      }

      const savedIntegration = await response.json();

      // Update the integrations list
      if (editingIntegrationId) {
        setApiIntegrations(apiIntegrations.map(integration =>
          integration.id === editingIntegrationId ? savedIntegration : integration
        ));
      } else {
        setApiIntegrations([...apiIntegrations, savedIntegration]);
      }

      // Close the form
      setShowIntegrationForm(false);
      setIntegrationFormData({});
      setEditingIntegrationId(null);

      alert('Integration saved successfully!');
    } catch (err) {
      console.error('Error saving integration:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while saving the integration');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteIntegration = async (id: string) => {
    if (!confirm('Are you sure you want to delete this integration?')) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/settings/api-integrations/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete integration');
      }

      // Remove the integration from the list
      setApiIntegrations(apiIntegrations.filter(integration => integration.id !== id));

      alert('Integration deleted successfully!');
    } catch (err) {
      console.error('Error deleting integration:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the integration');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleIntegration = async (id: string, isActive: boolean) => {
    setLoading(true);
    setError(null);

    try {
      const integration = apiIntegrations.find(api => api.id === id);

      if (!integration) {
        throw new Error('Integration not found');
      }

      const response = await fetch(`/api/settings/api-integrations/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update integration');
      }

      const updatedIntegration = await response.json();

      // Update the integration in the list
      setApiIntegrations(apiIntegrations.map(api =>
        api.id === id ? updatedIntegration : api
      ));
    } catch (err) {
      console.error('Error updating integration:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while updating the integration');
    } finally {
      setLoading(false);
    }
  };

  const handleTestIntegration = async (id: string) => {
    alert('This would test the integration in a real implementation.');
  };

  const handleCompanyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSettings({
      ...settings,
      company: {
        ...settings.company,
        [name]: value,
      },
    });
  };

  const handleInvoiceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      invoice: {
        ...settings.invoice,
        [name]: type === 'checkbox' ? checked : value,
      },
    });
  };

  const handleNotificationsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      notifications: {
        ...settings.notifications,
        [name]: type === 'checkbox' ? checked : value,
      },
    });
  };

  const handleBackupChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const inputElement = e.target as HTMLInputElement;
    const checked = type === 'checkbox' ? inputElement.checked : undefined;

    setSettings({
      ...settings,
      backup: {
        ...settings.backup,
        [name]: type === 'checkbox' ? checked : value,
      },
    });
  };

  const handleSaveSettings = () => {
    // In a real app, this would save to the backend
    alert('Settings saved successfully!');
  };

  const handleBackupNow = () => {
    // In a real app, this would trigger a backup
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19);
    setSettings({
      ...settings,
      backup: {
        ...settings.backup,
        lastBackup: now,
      },
    });
    alert('Backup initiated successfully!');
  };

  // API Integration render functions
  const renderPaymentGatewaySettings = () => {
    const paymentGateways = apiIntegrations.filter(api => api.type === 'PAYMENT_GATEWAY');

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827' }}>
            Payment Gateway Integration
          </h3>
          <button
            onClick={() => handleAddIntegration('PAYMENT_GATEWAY')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#6366f1',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#4f46e5';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#6366f1';
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
            Add Gateway
          </button>
        </div>

        {paymentGateways.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            backgroundColor: '#f9fafb'
          }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" strokeWidth="2">
                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                <line x1="1" y1="10" x2="23" y2="10"/>
              </svg>
            </div>
            <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
              No payment gateways configured
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1.5rem' }}>
              Add a payment gateway to process online payments.
            </p>
            <button
              onClick={() => handleAddIntegration('PAYMENT_GATEWAY')}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              Add Payment Gateway
            </button>
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {paymentGateways.map(gateway => (
              <div key={gateway.id} style={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                padding: '1.5rem',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <div>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
                      {gateway.name}
                    </h4>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <span style={{
                        width: '0.5rem',
                        height: '0.5rem',
                        borderRadius: '50%',
                        backgroundColor: gateway.isActive ? '#10b981' : '#9ca3af'
                      }}></span>
                      <span style={{
                        fontSize: '0.875rem',
                        color: gateway.isActive ? '#059669' : '#6b7280'
                      }}>
                        {gateway.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <button
                      onClick={() => handleEditIntegration(gateway)}
                      style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        color: '#6b7280',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                        e.currentTarget.style.color = '#374151';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6b7280';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                    </button>
                    <button
                      onClick={() => handleDeleteIntegration(gateway.id)}
                      style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        color: '#6b7280',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fef2f2';
                        e.currentTarget.style.color = '#dc2626';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6b7280';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="3,6 5,6 21,6"/>
                        <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '1rem',
                  marginBottom: '1rem',
                  fontSize: '0.875rem'
                }}>
                  <div>
                    <span style={{ color: '#6b7280' }}>API Key:</span>{' '}
                    <span style={{ color: '#111827' }}>{gateway.credentials?.apiKey || '••••••••'}</span>
                  </div>
                  <div>
                    <span style={{ color: '#6b7280' }}>Mode:</span>{' '}
                    <span style={{ color: '#111827' }}>{gateway.settings?.mode || 'Test'}</span>
                  </div>
                </div>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <button
                    onClick={() => handleTestIntegration(gateway.id)}
                    style={{
                      backgroundColor: 'white',
                      color: '#374151',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      padding: '0.5rem 1rem',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#9ca3af';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                  >
                    Test Connection
                  </button>
                  <Switch
                    checked={gateway.isActive}
                    onCheckedChange={(checked) => handleToggleIntegration(gateway.id, checked)}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderWhatsAppSettings = () => {
    const whatsappIntegrations = apiIntegrations.filter(api => api.type === 'WHATSAPP');

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827' }}>
            WhatsApp Business API
          </h3>
          <button
            onClick={() => handleAddIntegration('WHATSAPP')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#6366f1',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#4f46e5';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#6366f1';
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
            Add Integration
          </button>
        </div>

        {whatsappIntegrations.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            backgroundColor: '#f9fafb'
          }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" strokeWidth="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              </svg>
            </div>
            <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
              No WhatsApp integration configured
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1.5rem' }}>
              Connect WhatsApp to send order updates and notifications.
            </p>
            <button
              onClick={() => handleAddIntegration('WHATSAPP')}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              Connect WhatsApp
            </button>
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {whatsappIntegrations.map(integration => (
              <div key={integration.id} style={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                padding: '1.5rem',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <div>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
                      {integration.name}
                    </h4>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <span style={{
                        width: '0.5rem',
                        height: '0.5rem',
                        borderRadius: '50%',
                        backgroundColor: integration.isActive ? '#10b981' : '#9ca3af'
                      }}></span>
                      <span style={{
                        fontSize: '0.875rem',
                        color: integration.isActive ? '#059669' : '#6b7280'
                      }}>
                        {integration.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <button
                      onClick={() => handleEditIntegration(integration)}
                      style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        color: '#6b7280',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                        e.currentTarget.style.color = '#374151';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6b7280';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                    </button>
                    <button
                      onClick={() => handleDeleteIntegration(integration.id)}
                      style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        color: '#6b7280',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fef2f2';
                        e.currentTarget.style.color = '#dc2626';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6b7280';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="3,6 5,6 21,6"/>
                        <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '1rem',
                  marginBottom: '1rem',
                  fontSize: '0.875rem'
                }}>
                  <div>
                    <span style={{ color: '#6b7280' }}>Phone Number:</span>{' '}
                    <span style={{ color: '#111827' }}>{integration.settings?.phoneNumber || 'Not set'}</span>
                  </div>
                  <div>
                    <span style={{ color: '#6b7280' }}>API Key:</span>{' '}
                    <span style={{ color: '#111827' }}>{integration.credentials?.apiKey || '••••••••'}</span>
                  </div>
                </div>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <button
                    onClick={() => handleTestIntegration(integration.id)}
                    style={{
                      backgroundColor: 'white',
                      color: '#374151',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      padding: '0.5rem 1rem',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#9ca3af';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                  >
                    Test Connection
                  </button>
                  <Switch
                    checked={integration.isActive}
                    onCheckedChange={(checked) => handleToggleIntegration(integration.id, checked)}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderFacebookPixelSettings = () => {
    const facebookIntegrations = apiIntegrations.filter(api => api.type === 'FACEBOOK_PIXEL');

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827' }}>
            Facebook Pixel
          </h3>
          <button
            onClick={() => handleAddIntegration('FACEBOOK_PIXEL')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#6366f1',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#4f46e5';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#6366f1';
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
            Add Pixel
          </button>
        </div>

        {facebookIntegrations.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            backgroundColor: '#f9fafb'
          }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" strokeWidth="2">
                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/>
              </svg>
            </div>
            <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
              No Facebook Pixel configured
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1.5rem' }}>
              Add Facebook Pixel to track conversions and optimize ads.
            </p>
            <button
              onClick={() => handleAddIntegration('FACEBOOK_PIXEL')}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              Add Facebook Pixel
            </button>
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {facebookIntegrations.map(integration => (
              <div key={integration.id} style={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                padding: '1.5rem',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <div>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
                      {integration.name}
                    </h4>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <span style={{
                        width: '0.5rem',
                        height: '0.5rem',
                        borderRadius: '50%',
                        backgroundColor: integration.isActive ? '#10b981' : '#9ca3af'
                      }}></span>
                      <span style={{
                        fontSize: '0.875rem',
                        color: integration.isActive ? '#059669' : '#6b7280'
                      }}>
                        {integration.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <button
                      onClick={() => handleEditIntegration(integration)}
                      style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        color: '#6b7280',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                        e.currentTarget.style.color = '#374151';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6b7280';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                    </button>
                    <button
                      onClick={() => handleDeleteIntegration(integration.id)}
                      style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        color: '#6b7280',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fef2f2';
                        e.currentTarget.style.color = '#dc2626';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6b7280';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="3,6 5,6 21,6"/>
                        <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '1rem',
                  marginBottom: '1rem',
                  fontSize: '0.875rem'
                }}>
                  <div>
                    <span style={{ color: '#6b7280' }}>Pixel ID:</span>{' '}
                    <span style={{ color: '#111827' }}>{integration.credentials?.pixelId || '••••••••'}</span>
                  </div>
                  <div>
                    <span style={{ color: '#6b7280' }}>Events:</span>{' '}
                    <span style={{ color: '#111827' }}>{integration.settings?.events?.join(', ') || 'All events'}</span>
                  </div>
                </div>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <button
                    onClick={() => handleTestIntegration(integration.id)}
                    style={{
                      backgroundColor: 'white',
                      color: '#374151',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      padding: '0.5rem 1rem',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#9ca3af';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                  >
                    Test Pixel
                  </button>
                  <Switch
                    checked={integration.isActive}
                    onCheckedChange={(checked) => handleToggleIntegration(integration.id, checked)}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderGoogleTagManagerSettings = () => {
    const gtmIntegrations = apiIntegrations.filter(api => api.type === 'GOOGLE_TAG_MANAGER');

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827' }}>
            Google Tag Manager
          </h3>
          <button
            onClick={() => handleAddIntegration('GOOGLE_TAG_MANAGER')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#6366f1',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#4f46e5';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#6366f1';
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
            Add GTM
          </button>
        </div>

        {gtmIntegrations.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            backgroundColor: '#f9fafb'
          }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" strokeWidth="2">
                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/>
                <line x1="7" y1="7" x2="7.01" y2="7"/>
              </svg>
            </div>
            <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>
              No Google Tag Manager configured
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1.5rem' }}>
              Add Google Tag Manager to manage all your marketing and tracking tags.
            </p>
            <button
              onClick={() => handleAddIntegration('GOOGLE_TAG_MANAGER')}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              Add GTM Container
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {gtmIntegrations.map(integration => (
              <Card key={integration.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-md">{integration.name}</CardTitle>
                      <CardDescription>
                        {integration.isActive ? (
                          <span className="text-green-600 flex items-center">
                            <span className="h-2 w-2 rounded-full bg-green-600 mr-1"></span>
                            Active
                          </span>
                        ) : (
                          <span className="text-gray-500 flex items-center">
                            <span className="h-2 w-2 rounded-full bg-gray-400 mr-1"></span>
                            Inactive
                          </span>
                        )}
                      </CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditIntegration(integration)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteIntegration(integration.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-sm">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="text-muted-foreground">Container ID:</span>{' '}
                        <span>{integration.credentials?.containerId || '••••••••'}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Environment:</span>{' '}
                        <span>{integration.settings?.environment || 'Production'}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <div className="flex justify-between w-full">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestIntegration(integration.id)}
                    >
                      Test Container
                    </Button>
                    <Switch
                      checked={integration.isActive}
                      onCheckedChange={(checked) => handleToggleIntegration(integration.id, checked)}
                    />
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'integrations':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            {loading ? (
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '1rem',
                padding: '3rem'
              }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  border: '3px solid #f1f5f9',
                  borderTop: '3px solid #3b82f6',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <p style={{ color: '#64748b', fontSize: '1rem', fontWeight: '500' }}>
                  Loading integrations...
                </p>
              </div>
            ) : error ? (
              <div style={{
                padding: '1.5rem',
                backgroundColor: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '8px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <div style={{
                    backgroundColor: '#fee2e2',
                    borderRadius: '50%',
                    padding: '0.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#dc2626" strokeWidth="2">
                      <circle cx="12" cy="12" r="10"/>
                      <line x1="15" y1="9" x2="9" y2="15"/>
                      <line x1="9" y1="9" x2="15" y2="15"/>
                    </svg>
                  </div>
                  <div>
                    <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#dc2626', marginBottom: '0.25rem' }}>
                      Error Loading Integrations
                    </h3>
                    <p style={{ fontSize: '0.875rem', color: '#7f1d1d' }}>
                      {error}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setLoading(true);
                    setError(null);
                    fetch('/api/settings/api-integrations')
                      .then(res => res.json())
                      .then(data => setApiIntegrations(data))
                      .catch(err => setError(err instanceof Error ? err.message : 'An error occurred'))
                      .finally(() => setLoading(false));
                  }}
                  style={{
                    backgroundColor: 'white',
                    color: '#374151',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.borderColor = '#9ca3af';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                >
                  Retry
                </button>
              </div>
            ) : (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                {/* Integration Type Selector */}
                <div style={{
                  display: 'flex',
                  gap: '0.5rem',
                  borderBottom: '1px solid #e5e7eb',
                  paddingBottom: '1rem',
                  marginBottom: '1rem'
                }}>
                  {[
                    { id: 'payment', label: 'Payment Gateway', icon: '💳' },
                    { id: 'whatsapp', label: 'WhatsApp', icon: '💬' },
                    { id: 'facebook', label: 'Facebook Pixel', icon: '📊' },
                    { id: 'gtm', label: 'Google Tag Manager', icon: '🏷️' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveIntegrationTab(tab.id)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.75rem 1rem',
                        borderRadius: '8px',
                        border: 'none',
                        backgroundColor: activeIntegrationTab === tab.id ? '#6366f1' : '#f8fafc',
                        color: activeIntegrationTab === tab.id ? 'white' : '#64748b',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        if (activeIntegrationTab !== tab.id) {
                          e.currentTarget.style.backgroundColor = '#f1f5f9';
                          e.currentTarget.style.color = '#374151';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeIntegrationTab !== tab.id) {
                          e.currentTarget.style.backgroundColor = '#f8fafc';
                          e.currentTarget.style.color = '#64748b';
                        }
                      }}
                    >
                      <span>{tab.icon}</span>
                      {tab.label}
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                <div>
                  {activeIntegrationTab === 'payment' && renderPaymentGatewaySettings()}
                  {activeIntegrationTab === 'whatsapp' && renderWhatsAppSettings()}
                  {activeIntegrationTab === 'facebook' && renderFacebookPixelSettings()}
                  {activeIntegrationTab === 'gtm' && renderGoogleTagManagerSettings()}
                </div>
              </div>
            )}
          </div>
        );
      case 'company':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '1.5rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="name" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Company Name *
                </label>
                <input
                  id="name"
                  name="name"
                  value={settings.company.name}
                  onChange={handleCompanyChange}
                  placeholder="Enter company name"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="email" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Email Address *
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={settings.company.email}
                  onChange={handleCompanyChange}
                  placeholder="Enter email address"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="phone" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Phone Number
                </label>
                <input
                  id="phone"
                  name="phone"
                  value={settings.company.phone}
                  onChange={handleCompanyChange}
                  placeholder="Enter phone number"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="website" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Website
                </label>
                <input
                  id="website"
                  name="website"
                  value={settings.company.website}
                  onChange={handleCompanyChange}
                  placeholder="Enter website URL"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', gridColumn: 'span 2' }}>
                <label htmlFor="address" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Business Address
                </label>
                <input
                  id="address"
                  name="address"
                  value={settings.company.address}
                  onChange={handleCompanyChange}
                  placeholder="Enter complete business address"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="taxId" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Tax ID / VAT Number
                </label>
                <input
                  id="taxId"
                  name="taxId"
                  value={settings.company.taxId}
                  onChange={handleCompanyChange}
                  placeholder="Enter tax identification number"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 'invoice':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '1.5rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="prefix" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Invoice Prefix
                </label>
                <input
                  id="prefix"
                  name="prefix"
                  value={settings.invoice.prefix}
                  onChange={handleInvoiceChange}
                  placeholder="e.g., INV-"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="footer" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Invoice Footer
                </label>
                <input
                  id="footer"
                  name="footer"
                  value={settings.invoice.footer}
                  onChange={handleInvoiceChange}
                  placeholder="Thank you for your business!"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', gridColumn: 'span 2' }}>
                <label htmlFor="terms" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Terms and Conditions
                </label>
                <input
                  id="terms"
                  name="terms"
                  value={settings.invoice.terms}
                  onChange={handleInvoiceChange}
                  placeholder="Payment due within 30 days"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem',
              padding: '1.5rem',
              backgroundColor: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                <input
                  type="checkbox"
                  id="showLogo"
                  name="showLogo"
                  checked={settings.invoice.showLogo}
                  onChange={handleInvoiceChange}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#3b82f6'
                  }}
                />
                <label htmlFor="showLogo" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  Show Logo on Invoice
                </label>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                <input
                  type="checkbox"
                  id="showTax"
                  name="showTax"
                  checked={settings.invoice.showTax}
                  onChange={handleInvoiceChange}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#3b82f6'
                  }}
                />
                <label htmlFor="showTax" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  Show Tax Information
                </label>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                <input
                  type="checkbox"
                  id="lowStockAlert"
                  name="lowStockAlert"
                  checked={settings.notifications.lowStockAlert}
                  onChange={handleNotificationsChange}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#3b82f6'
                  }}
                />
                <label htmlFor="lowStockAlert" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  Enable Low Stock Alerts
                </label>
              </div>

              {settings.notifications.lowStockAlert && (
                <div style={{
                  marginLeft: '2rem',
                  padding: '1rem',
                  backgroundColor: '#f8fafc',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.75rem'
                }}>
                  <label htmlFor="lowStockThreshold" style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    Low Stock Threshold
                  </label>
                  <input
                    id="lowStockThreshold"
                    name="lowStockThreshold"
                    type="number"
                    min="1"
                    value={settings.notifications.lowStockThreshold}
                    onChange={handleNotificationsChange}
                    style={{
                      width: '120px',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  />
                  <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
                    Alert when stock falls below this threshold
                  </p>
                </div>
              )}

              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                <input
                  type="checkbox"
                  id="orderNotifications"
                  name="orderNotifications"
                  checked={settings.notifications.orderNotifications}
                  onChange={handleNotificationsChange}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#3b82f6'
                  }}
                />
                <label htmlFor="orderNotifications" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  New Order Notifications
                </label>
              </div>
            </div>

            <div style={{
              padding: '1.5rem',
              backgroundColor: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0'
            }}>
              <h3 style={{
                fontSize: '1rem',
                fontWeight: '600',
                color: '#0f172a',
                marginBottom: '1rem'
              }}>
                Notification Methods
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <input
                    type="checkbox"
                    id="emailNotifications"
                    name="emailNotifications"
                    checked={settings.notifications.emailNotifications}
                    onChange={handleNotificationsChange}
                    style={{
                      width: '16px',
                      height: '16px',
                      accentColor: '#3b82f6'
                    }}
                  />
                  <label htmlFor="emailNotifications" style={{
                    fontSize: '0.875rem',
                    color: '#374151',
                    cursor: 'pointer'
                  }}>
                    Email Notifications
                  </label>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <input
                    type="checkbox"
                    id="smsNotifications"
                    name="smsNotifications"
                    checked={settings.notifications.smsNotifications}
                    onChange={handleNotificationsChange}
                    style={{
                      width: '16px',
                      height: '16px',
                      accentColor: '#3b82f6'
                    }}
                  />
                  <label htmlFor="smsNotifications" style={{
                    fontSize: '0.875rem',
                    color: '#374151',
                    cursor: 'pointer'
                  }}>
                    SMS Notifications
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      case 'backup':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                <input
                  type="checkbox"
                  id="autoBackup"
                  name="autoBackup"
                  checked={settings.backup.autoBackup}
                  onChange={handleBackupChange}
                  style={{
                    width: '16px',
                    height: '16px',
                    accentColor: '#3b82f6'
                  }}
                />
                <label htmlFor="autoBackup" style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  Enable Automatic Backups
                </label>
              </div>

              {settings.backup.autoBackup && (
                <div style={{
                  marginLeft: '2rem',
                  padding: '1rem',
                  backgroundColor: '#f8fafc',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.75rem'
                }}>
                  <label htmlFor="backupFrequency" style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    Backup Frequency
                  </label>
                  <select
                    id="backupFrequency"
                    name="backupFrequency"
                    value={settings.backup.backupFrequency}
                    onChange={handleBackupChange}
                    style={{
                      width: '200px',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              )}
            </div>

            <div style={{
              padding: '1.5rem',
              backgroundColor: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <h3 style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: '#0f172a',
                    marginBottom: '0.25rem'
                  }}>
                    Last Backup
                  </h3>
                  <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
                    {settings.backup.lastBackup || 'No backup yet'}
                  </p>
                </div>
                <button
                  onClick={handleBackupNow}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    backgroundColor: 'white',
                    color: '#374151',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                    e.currentTarget.style.borderColor = '#9ca3af';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                >
                  <RefreshCw style={{ width: '16px', height: '16px' }} />
                  Backup Now
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Integration form handlers
  const handleIntegrationFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const isCheckbox = type === 'checkbox';
    const inputElement = e.target as HTMLInputElement;
    const checked = isCheckbox ? inputElement.checked : undefined;

    // Handle nested properties (credentials and settings)
    if (name.includes('.')) {
      const [section, key] = name.split('.');
      setIntegrationFormData({
        ...integrationFormData,
        [section]: {
          ...integrationFormData[section],
          [key]: isCheckbox ? checked : value,
        },
      });
    } else {
      setIntegrationFormData({
        ...integrationFormData,
        [name]: isCheckbox ? checked : value,
      });
    }
  };

  const renderIntegrationForm = () => {
    if (!showIntegrationForm) return null;

    let formTitle = '';
    let formFields: React.ReactNode[] = [];

    switch (integrationFormType) {
      case 'PAYMENT_GATEWAY':
        formTitle = editingIntegrationId ? 'Edit Payment Gateway' : 'Add Payment Gateway';
        formFields = [
          <div key="name" className="space-y-2">
            <Label htmlFor="name">Gateway Name</Label>
            <Input
              id="name"
              name="name"
              value={integrationFormData.name || ''}
              onChange={handleIntegrationFormChange}
              placeholder="e.g., Stripe, PayPal"
            />
          </div>,
          <div key="apiKey" className="space-y-2">
            <Label htmlFor="credentials.apiKey">API Key</Label>
            <Input
              id="credentials.apiKey"
              name="credentials.apiKey"
              value={integrationFormData.credentials?.apiKey || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Enter API key"
            />
          </div>,
          <div key="secretKey" className="space-y-2">
            <Label htmlFor="credentials.secretKey">Secret Key</Label>
            <Input
              id="credentials.secretKey"
              name="credentials.secretKey"
              value={integrationFormData.credentials?.secretKey || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Enter secret key"
              type="password"
            />
          </div>,
          <div key="mode" className="space-y-2">
            <Label htmlFor="settings.mode">Mode</Label>
            <Select
              name="settings.mode"
              value={integrationFormData.settings?.mode || 'test'}
              onValueChange={(value) => {
                setIntegrationFormData({
                  ...integrationFormData,
                  settings: {
                    ...integrationFormData.settings,
                    mode: value,
                  },
                });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="test">Test</SelectItem>
                <SelectItem value="live">Live</SelectItem>
              </SelectContent>
            </Select>
          </div>,
          <div key="currency" className="space-y-2">
            <Label htmlFor="settings.currency">Currency</Label>
            <Select
              name="settings.currency"
              value={integrationFormData.settings?.currency || 'INR'}
              onValueChange={(value) => {
                setIntegrationFormData({
                  ...integrationFormData,
                  settings: {
                    ...integrationFormData.settings,
                    currency: value,
                  },
                });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="INR">INR</SelectItem>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="GBP">GBP</SelectItem>
              </SelectContent>
            </Select>
          </div>,
        ];
        break;

      case 'WHATSAPP':
        formTitle = editingIntegrationId ? 'Edit WhatsApp Integration' : 'Add WhatsApp Integration';
        formFields = [
          <div key="name" className="space-y-2">
            <Label htmlFor="name">Integration Name</Label>
            <Input
              id="name"
              name="name"
              value={integrationFormData.name || ''}
              onChange={handleIntegrationFormChange}
              placeholder="e.g., WhatsApp Business API"
            />
          </div>,
          <div key="apiKey" className="space-y-2">
            <Label htmlFor="credentials.apiKey">API Key</Label>
            <Input
              id="credentials.apiKey"
              name="credentials.apiKey"
              value={integrationFormData.credentials?.apiKey || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Enter API key"
            />
          </div>,
          <div key="phoneNumberId" className="space-y-2">
            <Label htmlFor="credentials.phoneNumberId">Phone Number ID</Label>
            <Input
              id="credentials.phoneNumberId"
              name="credentials.phoneNumberId"
              value={integrationFormData.credentials?.phoneNumberId || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Enter phone number ID"
            />
          </div>,
          <div key="phoneNumber" className="space-y-2">
            <Label htmlFor="settings.phoneNumber">Business Phone Number</Label>
            <Input
              id="settings.phoneNumber"
              name="settings.phoneNumber"
              value={integrationFormData.settings?.phoneNumber || ''}
              onChange={handleIntegrationFormChange}
              placeholder="e.g., +1234567890"
            />
          </div>,
          <div key="businessName" className="space-y-2">
            <Label htmlFor="settings.businessName">Business Name</Label>
            <Input
              id="settings.businessName"
              name="settings.businessName"
              value={integrationFormData.settings?.businessName || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Your business name"
            />
          </div>,
        ];
        break;

      case 'FACEBOOK_PIXEL':
        formTitle = editingIntegrationId ? 'Edit Facebook Pixel' : 'Add Facebook Pixel';
        formFields = [
          <div key="name" className="space-y-2">
            <Label htmlFor="name">Pixel Name</Label>
            <Input
              id="name"
              name="name"
              value={integrationFormData.name || ''}
              onChange={handleIntegrationFormChange}
              placeholder="e.g., Main Website Pixel"
            />
          </div>,
          <div key="pixelId" className="space-y-2">
            <Label htmlFor="credentials.pixelId">Pixel ID</Label>
            <Input
              id="credentials.pixelId"
              name="credentials.pixelId"
              value={integrationFormData.credentials?.pixelId || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Enter pixel ID"
            />
          </div>,
          <div key="events" className="space-y-2">
            <Label htmlFor="settings.events">Track Events</Label>
            <div className="grid grid-cols-2 gap-2">
              {['PageView', 'Purchase', 'AddToCart', 'InitiateCheckout'].map(event => (
                <div key={event} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`event-${event}`}
                    checked={integrationFormData.settings?.events?.includes(event) || false}
                    onChange={(e) => {
                      const currentEvents = integrationFormData.settings?.events || [];
                      const updatedEvents = e.target.checked
                        ? [...currentEvents, event]
                        : currentEvents.filter((e: string) => e !== event);

                      setIntegrationFormData({
                        ...integrationFormData,
                        settings: {
                          ...integrationFormData.settings,
                          events: updatedEvents,
                        },
                      });
                    }}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <Label htmlFor={`event-${event}`} className="text-sm">{event}</Label>
                </div>
              ))}
            </div>
          </div>,
        ];
        break;

      case 'GOOGLE_TAG_MANAGER':
        formTitle = editingIntegrationId ? 'Edit Google Tag Manager' : 'Add Google Tag Manager';
        formFields = [
          <div key="name" className="space-y-2">
            <Label htmlFor="name">Container Name</Label>
            <Input
              id="name"
              name="name"
              value={integrationFormData.name || ''}
              onChange={handleIntegrationFormChange}
              placeholder="e.g., Main Website Container"
            />
          </div>,
          <div key="containerId" className="space-y-2">
            <Label htmlFor="credentials.containerId">Container ID</Label>
            <Input
              id="credentials.containerId"
              name="credentials.containerId"
              value={integrationFormData.credentials?.containerId || ''}
              onChange={handleIntegrationFormChange}
              placeholder="Enter container ID (GTM-XXXXXX)"
            />
          </div>,
          <div key="environment" className="space-y-2">
            <Label htmlFor="settings.environment">Environment</Label>
            <Select
              name="settings.environment"
              value={integrationFormData.settings?.environment || 'production'}
              onValueChange={(value) => {
                setIntegrationFormData({
                  ...integrationFormData,
                  settings: {
                    ...integrationFormData.settings,
                    environment: value,
                  },
                });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select environment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="production">Production</SelectItem>
                <SelectItem value="staging">Staging</SelectItem>
                <SelectItem value="development">Development</SelectItem>
              </SelectContent>
            </Select>
          </div>,
        ];
        break;
    }

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
          <h2 className="text-xl font-semibold mb-4">{formTitle}</h2>

          <div className="space-y-4">
            {formFields}

            <div className="flex items-center space-x-2 mt-2">
              <Switch
                id="isActive"
                checked={integrationFormData.isActive || false}
                onCheckedChange={(checked) => {
                  setIntegrationFormData({
                    ...integrationFormData,
                    isActive: checked,
                  });
                }}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <Button
              variant="outline"
              onClick={() => {
                setShowIntegrationForm(false);
                setIntegrationFormData({});
                setEditingIntegrationId(null);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveIntegration}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Integration'
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', padding: '1.5rem' }}>
      {showIntegrationForm && renderIntegrationForm()}

      {/* Header Section */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '2rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: '700',
              color: '#0f172a',
              marginBottom: '0.5rem',
              letterSpacing: '-0.025em'
            }}>
              System Settings
            </h1>
            <p style={{ color: '#64748b', fontSize: '1rem' }}>
              Configure your bakery system settings, integrations, and preferences
            </p>
          </div>

          <div style={{
            backgroundColor: '#f8fafc',
            borderRadius: '12px',
            padding: '1rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#3b82f6" strokeWidth="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </div>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '2rem'
      }}>
        {/* Navigation Sidebar */}
        <aside style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          height: 'fit-content'
        }}>
          <h3 style={{
            fontSize: '1rem',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '1rem'
          }}>
            Settings Menu
          </h3>
          <nav style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          }}>
            {[
              { id: 'company', label: 'Company', icon: '🏢' },
              { id: 'invoice', label: 'Invoice', icon: '📄' },
              { id: 'notifications', label: 'Notifications', icon: '🔔' },
              { id: 'backup', label: 'Backup & Recovery', icon: '💾' },
              { id: 'integrations', label: 'API Integrations', icon: '🔗' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  padding: '0.75rem 1rem',
                  borderRadius: '8px',
                  border: 'none',
                  backgroundColor: activeTab === tab.id ? '#6366f1' : 'transparent',
                  color: activeTab === tab.id ? 'white' : '#374151',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  width: '100%'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.id) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <span style={{ fontSize: '1.125rem' }}>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </aside>

        {/* Content Area */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          padding: '2rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          minHeight: '500px'
        }}>
          <div style={{
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: '1rem',
            marginBottom: '2rem'
          }}>
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: '#111827',
              marginBottom: '0.5rem'
            }}>
              {activeTab === 'company' && 'Company Information'}
              {activeTab === 'invoice' && 'Invoice Settings'}
              {activeTab === 'notifications' && 'Notification Preferences'}
              {activeTab === 'backup' && 'Backup & Recovery'}
              {activeTab === 'integrations' && 'API Integrations'}
            </h2>
            <p style={{
              fontSize: '0.875rem',
              color: '#6b7280'
            }}>
              {activeTab === 'company' && 'Configure your company details and business information'}
              {activeTab === 'invoice' && 'Customize invoice templates and billing preferences'}
              {activeTab === 'notifications' && 'Manage email and system notification settings'}
              {activeTab === 'backup' && 'Configure automated backups and data recovery options'}
              {activeTab === 'integrations' && 'Manage third-party API connections and webhooks'}
            </p>
          </div>

          {/* Content */}
          <div style={{ marginBottom: '2rem' }}>
            {renderTabContent()}
          </div>

          {/* Save Button */}
          <div style={{
            borderTop: '1px solid #e5e7eb',
            paddingTop: '1.5rem',
            display: 'flex',
            justifyContent: 'flex-end'
          }}>
            <Button
              onClick={handleSaveSettings}
              style={{
                backgroundColor: '#6366f1',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#4f46e5';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#6366f1';
              }}
            >
              <Save style={{ height: '1rem', width: '1rem' }} />
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
