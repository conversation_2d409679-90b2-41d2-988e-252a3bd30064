'use client';

import { useState, useEffect } from 'react';
import { Plus, Minus, Package, Search, Filter, AlertTriangle, CheckCircle2, Loader2, Edit3 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  category: string;
  sku: string;
  unit: string;
  price: number;
  costPrice: number;
  lowStockThreshold: number;
  imageUrl?: string;
}

interface InventoryItem {
  productId: string;
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

interface Warehouse {
  id: string;
  name: string;
}

interface StockAdjustment {
  productId: string;
  warehouseId: string;
  quantity: number;
  type: 'add' | 'remove';
  reason: string;
}

export default function StockManagementPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState('all');
  const [showAdjustmentForm, setShowAdjustmentForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [adjustmentData, setAdjustmentData] = useState<StockAdjustment>({
    productId: '',
    warehouseId: '',
    quantity: 0,
    type: 'add',
    reason: ''
  });

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products');
        }
        const productsData = await productsResponse.json();
        setProducts(productsData);

        // Fetch inventory
        const inventoryResponse = await fetch('/api/inventory');
        if (!inventoryResponse.ok) {
          throw new Error('Failed to fetch inventory');
        }
        const inventoryData = await inventoryResponse.json();
        setInventory(inventoryData);

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (!warehousesResponse.ok) {
          throw new Error('Failed to fetch warehouses');
        }
        const warehousesData = await warehousesResponse.json();
        setWarehouses(warehousesData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get stock quantity for a product in a specific warehouse
  const getStockQuantity = (productId: string, warehouseId?: string) => {
    if (warehouseId) {
      const item = inventory.find(inv => inv.productId === productId && inv.warehouseId === warehouseId);
      return item ? item.quantity : 0;
    } else {
      // Total across all warehouses
      return inventory
        .filter(inv => inv.productId === productId)
        .reduce((total, inv) => total + inv.quantity, 0);
    }
  };

  // Get stock status
  const getStockStatus = (product: Product) => {
    const totalStock = getStockQuantity(product.id);
    if (totalStock === 0) {
      return { status: 'out-of-stock', label: 'Out of Stock', color: '#ef4444' };
    } else if (totalStock <= product.lowStockThreshold) {
      return { status: 'low-stock', label: 'Low Stock', color: '#f59e0b' };
    } else {
      return { status: 'in-stock', label: 'In Stock', color: '#10b981' };
    }
  };

  // Filter products based on search and warehouse
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedWarehouse === 'all') {
      return matchesSearch;
    } else {
      const hasStockInWarehouse = inventory.some(inv =>
        inv.productId === product.id && inv.warehouseId === selectedWarehouse
      );
      return matchesSearch && hasStockInWarehouse;
    }
  });

  // Handle stock adjustment
  const handleStockAdjustment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProduct || !adjustmentData.warehouseId || adjustmentData.quantity <= 0) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/stock-adjustments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProduct.id,
          warehouseId: adjustmentData.warehouseId,
          type: adjustmentData.type,
          quantity: adjustmentData.quantity,
          reason: adjustmentData.reason,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to adjust stock');
      }

      const result = await response.json();

      // Refresh inventory data
      const inventoryResponse = await fetch('/api/inventory');
      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json();
        setInventory(inventoryData);
      }

      // Reset form
      setShowAdjustmentForm(false);
      setSelectedProduct(null);
      setAdjustmentData({
        productId: '',
        warehouseId: '',
        quantity: 0,
        type: 'add',
        reason: ''
      });

      alert(result.message || `Stock ${adjustmentData.type === 'add' ? 'added' : 'removed'} successfully`);
    } catch (err) {
      console.error('Error adjusting stock:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while adjusting stock');
    }
  };

  // Handle opening adjustment form
  const openAdjustmentForm = (product: Product) => {
    setSelectedProduct(product);
    setAdjustmentData({
      ...adjustmentData,
      productId: product.id,
      warehouseId: warehouses.length > 0 ? warehouses[0].id : ''
    });
    setShowAdjustmentForm(true);
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        height: '50vh',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#3b82f6' }} />
        <span style={{ color: '#64748b' }}>Loading stock data...</span>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header */}
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '700', color: '#0f172a', marginBottom: '0.5rem' }}>
            Stock Management
          </h1>
          <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
            Manage product stock levels across warehouses
          </p>
        </div>
      </div>

      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          padding: '1rem',
          color: '#dc2626',
          fontSize: '0.875rem'
        }}>
          {error}
        </div>
      )}

      {/* Filters */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          alignItems: 'end'
        }}>
          <div>
            <label style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '0.5rem'
            }}>
              Search Products
            </label>
            <div style={{ position: 'relative' }}>
              <Search style={{
                position: 'absolute',
                left: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                height: '1rem',
                width: '1rem',
                color: '#9ca3af'
              }} />
              <input
                type="text"
                placeholder="Search by name, SKU, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  height: '2.5rem',
                  paddingLeft: '2.5rem',
                  paddingRight: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid #d1d5db',
                  fontSize: '0.875rem'
                }}
              />
            </div>
          </div>

          <div>
            <label style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '0.5rem'
            }}>
              Filter by Warehouse
            </label>
            <select
              value={selectedWarehouse}
              onChange={(e) => setSelectedWarehouse(e.target.value)}
              style={{
                width: '100%',
                height: '2.5rem',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                padding: '0 0.75rem',
                fontSize: '0.875rem',
                backgroundColor: 'white'
              }}
            >
              <option value="all">All Warehouses</option>
              {warehouses.map(warehouse => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Stock Adjustment Form */}
      {showAdjustmentForm && selectedProduct && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#0f172a',
            marginBottom: '1.5rem'
          }}>
            Adjust Stock - {selectedProduct.name}
          </h2>

          <form onSubmit={handleStockAdjustment} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1.5rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Warehouse *
                </label>
                <select
                  value={adjustmentData.warehouseId}
                  onChange={(e) => setAdjustmentData({ ...adjustmentData, warehouseId: e.target.value })}
                  required
                  style={{
                    width: '100%',
                    height: '2.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    padding: '0 0.75rem',
                    fontSize: '0.875rem',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">Select warehouse</option>
                  {warehouses.map(warehouse => (
                    <option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Adjustment Type *
                </label>
                <select
                  value={adjustmentData.type}
                  onChange={(e) => setAdjustmentData({ ...adjustmentData, type: e.target.value as 'add' | 'remove' })}
                  style={{
                    width: '100%',
                    height: '2.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    padding: '0 0.75rem',
                    fontSize: '0.875rem',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="add">Add Stock</option>
                  <option value="remove">Remove Stock</option>
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Quantity *
                </label>
                <input
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={adjustmentData.quantity}
                  onChange={(e) => setAdjustmentData({ ...adjustmentData, quantity: parseFloat(e.target.value) || 0 })}
                  placeholder="Enter quantity"
                  required
                  style={{
                    width: '100%',
                    height: '2.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    padding: '0 0.75rem',
                    fontSize: '0.875rem'
                  }}
                />
              </div>
            </div>

            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Reason for Adjustment *
              </label>
              <textarea
                value={adjustmentData.reason}
                onChange={(e) => setAdjustmentData({ ...adjustmentData, reason: e.target.value })}
                placeholder="Enter reason for stock adjustment..."
                required
                style={{
                  width: '100%',
                  minHeight: '80px',
                  borderRadius: '8px',
                  border: '1px solid #d1d5db',
                  padding: '0.75rem',
                  fontSize: '0.875rem',
                  resize: 'vertical'
                }}
              />
            </div>

            <div style={{ display: 'flex', gap: '0.75rem' }}>
              <button
                type="submit"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2563eb';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#3b82f6';
                }}
              >
                {adjustmentData.type === 'add' ? <Plus style={{ height: '1rem', width: '1rem' }} /> : <Minus style={{ height: '1rem', width: '1rem' }} />}
                {adjustmentData.type === 'add' ? 'Add Stock' : 'Remove Stock'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAdjustmentForm(false);
                  setSelectedProduct(null);
                }}
                style={{
                  backgroundColor: 'white',
                  color: '#374151',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                }}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Products Grid */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{
          fontSize: '1.125rem',
          fontWeight: '600',
          color: '#0f172a',
          marginBottom: '1.5rem'
        }}>
          Product Stock Levels
        </h2>

        {filteredProducts.length > 0 ? (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '1rem'
          }}>
            {filteredProducts.map(product => {
              const stockStatus = getStockStatus(product);
              const totalStock = getStockQuantity(product.id);

              return (
                <div
                  key={product.id}
                  style={{
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '1rem',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  {/* Product Header */}
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.75rem' }}>
                    <div style={{ flex: 1 }}>
                      <h3 style={{
                        fontSize: '0.875rem',
                        fontWeight: '600',
                        color: '#0f172a',
                        marginBottom: '0.25rem'
                      }}>
                        {product.name}
                      </h3>
                      <p style={{
                        fontSize: '0.75rem',
                        color: '#64748b'
                      }}>
                        {product.category} • SKU: {product.sku}
                      </p>
                    </div>
                    <div style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '6px',
                      fontSize: '0.7rem',
                      fontWeight: '500',
                      backgroundColor: stockStatus.color + '20',
                      color: stockStatus.color
                    }}>
                      {stockStatus.label}
                    </div>
                  </div>

                  {/* Stock Information */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '0.75rem',
                    marginBottom: '0.75rem'
                  }}>
                    <div>
                      <p style={{
                        fontSize: '0.7rem',
                        color: '#64748b',
                        marginBottom: '0.25rem'
                      }}>
                        Total Stock
                      </p>
                      <p style={{
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: '#0f172a'
                      }}>
                        {totalStock} {product.unit}
                      </p>
                    </div>
                    <div>
                      <p style={{
                        fontSize: '0.7rem',
                        color: '#64748b',
                        marginBottom: '0.25rem'
                      }}>
                        Low Stock Alert
                      </p>
                      <p style={{
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: '#0f172a'
                      }}>
                        {product.lowStockThreshold} {product.unit}
                      </p>
                    </div>
                  </div>

                  {/* Warehouse Breakdown */}
                  {selectedWarehouse === 'all' && (
                    <div style={{ marginBottom: '0.75rem' }}>
                      <p style={{
                        fontSize: '0.7rem',
                        color: '#64748b',
                        marginBottom: '0.5rem'
                      }}>
                        Warehouse Stock:
                      </p>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                        {warehouses.map(warehouse => {
                          const warehouseStock = getStockQuantity(product.id, warehouse.id);
                          return (
                            <div key={warehouse.id} style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: '0.7rem',
                              color: '#64748b'
                            }}>
                              <span>{warehouse.name}:</span>
                              <span>{warehouseStock} {product.unit}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  <button
                    onClick={() => openAdjustmentForm(product)}
                    style={{
                      width: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem',
                      backgroundColor: '#f8fafc',
                      color: '#475569',
                      border: '1px solid #e2e8f0',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#3b82f6';
                      e.currentTarget.style.color = 'white';
                      e.currentTarget.style.borderColor = '#3b82f6';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                      e.currentTarget.style.color = '#475569';
                      e.currentTarget.style.borderColor = '#e2e8f0';
                    }}
                  >
                    <Edit3 style={{ height: '0.75rem', width: '0.75rem' }} />
                    Adjust Stock
                  </button>
                </div>
              );
            })}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '3rem',
            color: '#64748b'
          }}>
            <Package style={{
              height: '3rem',
              width: '3rem',
              margin: '0 auto 1rem',
              color: '#9ca3af'
            }} />
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '600',
              marginBottom: '0.5rem'
            }}>
              No products found
            </h3>
            <p style={{ fontSize: '0.875rem' }}>
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}