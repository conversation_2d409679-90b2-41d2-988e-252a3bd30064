'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, Users, Loader2, X, UserPlus } from 'lucide-react';
import { PermissionGuard } from '@/components/permissions/PermissionGuard';
import { apiClient } from '@/lib/auth/api-client';

type Store = {
  id: string;
  name: string;
  location: string;
  createdAt: string;
  updatedAt: string;
};

type User = {
  id: string;
  name: string;
  email: string;
  role: string;
  storeId: string | null;
  store?: {
    name: string;
  };
};

export default function StoresPage() {
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({ name: '', location: '' });
  const [editingId, setEditingId] = useState<string | null>(null);

  // Store Access Management
  const [showStoreAccess, setShowStoreAccess] = useState(false);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [storeUsers, setStoreUsers] = useState<User[]>([]);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Fetch stores data
  useEffect(() => {
    const fetchStores = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.get('/api/stores');
        if (!response.ok) {
          throw new Error('Failed to fetch stores');
        }
        const data = await response.json();
        setStores(data);
      } catch (err) {
        console.error('Error fetching stores:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingId) {
        // Update existing store
        const response = await fetch(`/api/stores/${editingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            location: formData.location,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update store');
        }

        const updatedStore = await response.json();
        setStores(stores.map(store => store.id === editingId ? updatedStore : store));
        setEditingId(null);
      } else {
        // Add new store
        const response = await fetch('/api/stores', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            location: formData.location,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create store');
        }

        const newStore = await response.json();
        setStores([...stores, newStore]);
      }

      setFormData({ name: '', location: '' });
      setShowForm(false);
    } catch (err) {
      console.error('Error saving store:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while saving the store');
    }
  };

  const handleEdit = (store: Store) => {
    setFormData({ name: store.name, location: store.location });
    setEditingId(store.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    const store = stores.find(s => s.id === id);
    if (!store) return;

    if (!confirm(`Are you sure you want to delete "${store.name}"?\n\nThis action cannot be undone.`)) {
      return;
    }

    try {
      const response = await apiClient.delete(`/api/stores/${id}`);

      if (!response.ok) {
        const errorData = await response.json();

        // Handle specific error cases
        if (errorData.usersCount && errorData.usersCount > 0) {
          const usersList = errorData.users.map((u: any) => `• ${u.name} (${u.email})`).join('\n');
          alert(`Cannot delete store "${store.name}".\n\n${errorData.usersCount} user(s) are assigned to this store:\n\n${usersList}\n\nPlease reassign or remove these users first.`);
          return;
        }

        throw new Error(errorData.error || 'Failed to delete store');
      }

      // Remove store from local state
      setStores(stores.filter(store => store.id !== id));

      // Show success message
      alert(`Store "${store.name}" has been deleted successfully.`);
    } catch (err) {
      console.error('Error deleting store:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the store');
    }
  };

  // Store Access Management Functions
  const handleStoreAccess = async (store: Store) => {
    setSelectedStore(store);
    setShowStoreAccess(true);
    setLoadingUsers(true);

    try {
      // Fetch all users
      const usersResponse = await fetch('/api/users');
      if (!usersResponse.ok) {
        throw new Error('Failed to fetch users');
      }
      const allUsers = await usersResponse.json();
      setUsers(allUsers);

      // Filter users assigned to this store
      const assignedUsers = allUsers.filter((user: User) => user.storeId === store.id);
      setStoreUsers(assignedUsers);

      // Filter available users (not assigned to any store or admins)
      const available = allUsers.filter((user: User) =>
        !user.storeId && (user.role === 'STORE_MANAGER' || user.role === 'STAFF')
      );
      setAvailableUsers(available);
    } catch (err) {
      console.error('Error fetching users:', err);
      alert('Failed to load users');
    } finally {
      setLoadingUsers(false);
    }
  };

  const assignUserToStore = async (userId: string) => {
    if (!selectedStore) return;

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storeId: selectedStore.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to assign user to store');
      }

      // Refresh the user lists
      await handleStoreAccess(selectedStore);
    } catch (err) {
      console.error('Error assigning user:', err);
      alert('Failed to assign user to store');
    }
  };

  const removeUserFromStore = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storeId: null,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to remove user from store');
      }

      // Refresh the user lists
      if (selectedStore) {
        await handleStoreAccess(selectedStore);
      }
    } catch (err) {
      console.error('Error removing user:', err);
      alert('Failed to remove user from store');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '700', letterSpacing: '-0.025em' }}>Stores</h1>
          <p style={{ color: '#6b7280' }}>
            Manage your stores and their locations
          </p>
        </div>
        <PermissionGuard permission="stores.create">
          <Button
            onClick={() => {
              setFormData({ name: '', location: '' });
              setEditingId(null);
              setShowForm(!showForm);
            }}
            disabled={loading}
          >
            <Plus style={{ marginRight: '0.5rem', height: '1rem', width: '1rem' }} />
            Add Store
          </Button>
        </PermissionGuard>
      </div>

      {error && (
        <div style={{
          borderRadius: '6px',
          backgroundColor: '#fef2f2',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {showForm && (
        <div style={{
          borderRadius: '8px',
          border: '1px solid var(--border)',
          backgroundColor: 'var(--background)',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ marginBottom: '1rem', fontSize: '1.125rem', fontWeight: '500' }}>
            {editingId ? 'Edit Store' : 'Add New Store'}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Store Name
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter store name"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="location" className="text-sm font-medium">
                Location
              </label>
              <Input
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="Enter store location"
                required
              />
            </div>
            <div className="flex gap-2">
              <Button type="submit">
                {editingId ? 'Update' : 'Save'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {loading ? (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#667eea' }} />
          <span style={{ marginLeft: '0.5rem', color: '#6b7280' }}>Loading stores...</span>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {stores.length > 0 ? (
            stores.map((store) => (
              <div
                key={store.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  border: '1px solid #e5e7eb',
                  padding: '1.5rem',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                  e.currentTarget.style.borderColor = '#667eea';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                  e.currentTarget.style.borderColor = '#e5e7eb';
                }}
              >
                {/* Store Icon */}
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  borderRadius: '12px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '1rem'
                }}>
                  <svg
                    style={{ width: '1.5rem', height: '1.5rem', color: 'white' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                </div>

                {/* Store Info */}
                <div style={{ marginBottom: '1.5rem' }}>
                  <h3 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    color: '#111827',
                    marginBottom: '0.5rem'
                  }}>
                    {store.name}
                  </h3>

                  <p style={{
                    fontSize: '0.875rem',
                    color: '#6b7280',
                    marginBottom: '0.75rem',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <svg
                      style={{ width: '1rem', height: '1rem', marginRight: '0.5rem' }}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    {store.location}
                  </p>

                  <p style={{
                    fontSize: '0.75rem',
                    color: '#9ca3af'
                  }}>
                    Created: {new Date(store.createdAt).toLocaleDateString()}
                  </p>
                </div>

                {/* Action Buttons */}
                <div style={{
                  display: 'flex',
                  gap: '0.5rem'
                }}>
                  <PermissionGuard permission="stores.edit">
                    <Button
                      variant="outline"
                      onClick={() => handleEdit(store)}
                      style={{
                        flex: 1,
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        backgroundColor: 'white',
                        color: '#374151',
                        transition: 'all 0.2s',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '0.5rem'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#667eea';
                        e.currentTarget.style.borderColor = '#667eea';
                        e.currentTarget.style.color = 'white';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.color = '#374151';
                      }}
                    >
                      <Edit style={{ height: '1rem', width: '1rem' }} />
                      Edit
                    </Button>
                  </PermissionGuard>

                  <PermissionGuard permission="stores.manage_access">
                    <Button
                      variant="outline"
                      onClick={() => handleStoreAccess(store)}
                      style={{
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                        backgroundColor: 'white',
                        color: '#10b981',
                        transition: 'all 0.2s',
                        padding: '0.5rem'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#10b981';
                        e.currentTarget.style.borderColor = '#10b981';
                        e.currentTarget.style.color = 'white';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.color = '#10b981';
                      }}
                    >
                      <Users style={{ height: '1rem', width: '1rem' }} />
                    </Button>
                  </PermissionGuard>

                  <PermissionGuard permission="stores.delete">
                    <Button
                      variant="outline"
                      onClick={() => handleDelete(store.id)}
                      style={{
                        borderRadius: '8px',
                        border: '1px solid #fecaca',
                        backgroundColor: 'white',
                        color: '#dc2626',
                        transition: 'all 0.2s',
                        padding: '0.5rem'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#dc2626';
                        e.currentTarget.style.borderColor = '#dc2626';
                        e.currentTarget.style.color = 'white';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#fecaca';
                        e.currentTarget.style.color = '#dc2626';
                      }}
                    >
                      <Trash style={{ height: '1rem', width: '1rem' }} />
                    </Button>
                  </PermissionGuard>
                </div>
              </div>
            ))
          ) : (
            <div style={{
              gridColumn: '1 / -1',
              textAlign: 'center',
              padding: '3rem',
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '2px dashed #d1d5db'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '1rem'
              }}>
                <div style={{
                  width: '4rem',
                  height: '4rem',
                  borderRadius: '50%',
                  backgroundColor: '#f3f4f6',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg
                    style={{ height: '1.5rem', width: '1.5rem', color: '#9ca3af' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                </div>
                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.5rem'
                  }}>
                    No stores found
                  </h3>
                  <p style={{
                    fontSize: '0.875rem',
                    color: '#6b7280',
                    marginBottom: '1rem'
                  }}>
                    Create your first store to start managing locations.
                  </p>
                  <Button
                    onClick={() => {
                      setFormData({ name: '', location: '' });
                      setEditingId(null);
                      setShowForm(true);
                    }}
                    style={{
                      backgroundColor: '#667eea',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem 1rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <Plus style={{ height: '1rem', width: '1rem' }} />
                    Add Store
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Store Access Management Modal */}
      {showStoreAccess && selectedStore && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '1rem'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '2rem',
            maxWidth: '800px',
            width: '100%',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
          }}>
            {/* Modal Header */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '2rem',
              paddingBottom: '1rem',
              borderBottom: '1px solid #e5e7eb'
            }}>
              <div>
                <h2 style={{
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  color: '#111827',
                  marginBottom: '0.5rem'
                }}>
                  Store Access Management
                </h2>
                <p style={{
                  color: '#6b7280',
                  fontSize: '0.875rem'
                }}>
                  Manage user access for {selectedStore.name}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowStoreAccess(false)}
                style={{
                  padding: '0.5rem',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}
              >
                <X style={{ height: '1rem', width: '1rem' }} />
              </Button>
            </div>

            {loadingUsers ? (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '3rem'
              }}>
                <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite' }} />
                <span style={{ marginLeft: '0.5rem' }}>Loading users...</span>
              </div>
            ) : (
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '2rem'
              }}>
                {/* Assigned Users */}
                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827',
                    marginBottom: '1rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    <Users style={{ height: '1.25rem', width: '1.25rem' }} />
                    Assigned Users ({storeUsers.length})
                  </h3>
                  <div style={{
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {storeUsers.length === 0 ? (
                      <div style={{
                        padding: '2rem',
                        textAlign: 'center',
                        color: '#6b7280'
                      }}>
                        No users assigned to this store
                      </div>
                    ) : (
                      storeUsers.map((user) => (
                        <div
                          key={user.id}
                          style={{
                            padding: '1rem',
                            borderBottom: '1px solid #f3f4f6',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}
                        >
                          <div>
                            <div style={{
                              fontWeight: '500',
                              color: '#111827',
                              marginBottom: '0.25rem'
                            }}>
                              {user.name}
                            </div>
                            <div style={{
                              fontSize: '0.875rem',
                              color: '#6b7280'
                            }}>
                              {user.email} • {user.role}
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            onClick={() => removeUserFromStore(user.id)}
                            style={{
                              padding: '0.25rem 0.5rem',
                              fontSize: '0.75rem',
                              borderColor: '#fecaca',
                              color: '#dc2626'
                            }}
                          >
                            Remove
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* Available Users */}
                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827',
                    marginBottom: '1rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    <UserPlus style={{ height: '1.25rem', width: '1.25rem' }} />
                    Available Users ({availableUsers.length})
                  </h3>
                  <div style={{
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {availableUsers.length === 0 ? (
                      <div style={{
                        padding: '2rem',
                        textAlign: 'center',
                        color: '#6b7280'
                      }}>
                        No available users to assign
                      </div>
                    ) : (
                      availableUsers.map((user) => (
                        <div
                          key={user.id}
                          style={{
                            padding: '1rem',
                            borderBottom: '1px solid #f3f4f6',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}
                        >
                          <div>
                            <div style={{
                              fontWeight: '500',
                              color: '#111827',
                              marginBottom: '0.25rem'
                            }}>
                              {user.name}
                            </div>
                            <div style={{
                              fontSize: '0.875rem',
                              color: '#6b7280'
                            }}>
                              {user.email} • {user.role}
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            onClick={() => assignUserToStore(user.id)}
                            style={{
                              padding: '0.25rem 0.5rem',
                              fontSize: '0.75rem',
                              borderColor: '#d1fae5',
                              color: '#065f46',
                              backgroundColor: '#ecfdf5'
                            }}
                          >
                            Assign
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Modal Footer */}
            <div style={{
              marginTop: '2rem',
              paddingTop: '1rem',
              borderTop: '1px solid #e5e7eb',
              display: 'flex',
              justifyContent: 'flex-end'
            }}>
              <Button
                onClick={() => setShowStoreAccess(false)}
                style={{
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem'
                }}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
