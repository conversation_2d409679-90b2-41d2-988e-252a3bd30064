'use client';

import { useState, useEffect } from 'react';
import { Plus, Eye, Trash, Check, X, Loader2, ArrowRight, Package, Truck, Search, Filter, Calendar, MapPin } from 'lucide-react';
import { formatDate } from '@/lib/utils';

// Define types for our data
type Warehouse = {
  id: string;
  name: string;
};

type Store = {
  id: string;
  name: string;
};

type Product = {
  id: string;
  name: string;
  warehouseStock?: Record<string, number>;
};

type TransferItem = {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
};

type Transfer = {
  id: string;
  transferNumber: string;
  date: string;
  warehouseId: string;
  warehouseName: string;
  storeId: string;
  storeName: string;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  items: TransferItem[];
  createdAt: string;
  updatedAt: string;
};

export default function TransfersPage() {
  // State for data
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [products, setProducts] = useState<Product[]>([]);

  // State for UI
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null);
  const [formData, setFormData] = useState({
    warehouseId: '',
    storeId: '',
  });
  const [transferItems, setTransferItems] = useState<Array<{
    productId: string;
    productName: string;
    quantity: string;
    availableStock: number;
  }>>([]);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch transfers
        const transfersResponse = await fetch('/api/transfers');
        const transfersData = await transfersResponse.json();
        setTransfers(transfersData);

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        const warehousesData = await warehousesResponse.json();
        setWarehouses(warehousesData);

        // Fetch stores
        const storesResponse = await fetch('/api/stores');
        const storesData = await storesResponse.json();
        setStores(storesData);

        // Fetch products with warehouse inventory
        const productsResponse = await fetch('/api/products');
        const productsData = await productsResponse.json();

        // Fetch warehouse inventory for each product
        const productsWithStock = await Promise.all(productsData.map(async (product: Product) => {
          const inventoryResponse = await fetch(`/api/warehouse-inventory?productId=${product.id}`);
          const inventoryData = await inventoryResponse.json();

          // Create a map of warehouseId -> quantity
          const warehouseStock: Record<string, number> = {};
          inventoryData.forEach((item: any) => {
            warehouseStock[item.warehouseId] = item.quantity;
          });

          return {
            ...product,
            warehouseStock,
          };
        }));

        setProducts(productsWithStock);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // If warehouse changes, reset the transfer items
    if (name === 'warehouseId') {
      setTransferItems([]);
    }
  };

  const handleAddItem = () => {
    setTransferItems([
      ...transferItems,
      {
        productId: '',
        productName: '',
        quantity: '',
        availableStock: 0,
      },
    ]);
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const updatedItems = [...transferItems];

    if (name === 'productId') {
      const product = products.find(p => p.id === value);
      if (product) {
        const availableStock = product.warehouseStock?.[formData.warehouseId] || 0;
        updatedItems[index] = {
          ...updatedItems[index],
          productId: value,
          productName: product.name,
          availableStock,
        };
      }
    } else {
      updatedItems[index] = {
        ...updatedItems[index],
        [name]: value,
      };
    }

    setTransferItems(updatedItems);
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...transferItems];
    updatedItems.splice(index, 1);
    setTransferItems(updatedItems);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (transferItems.length === 0) {
      alert('Please add at least one item to the transfer');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/transfers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          warehouseId: formData.warehouseId,
          storeId: formData.storeId,
          items: transferItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
          })),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create transfer');
      }

      const newTransfer = await response.json();

      // Add the new transfer to the list
      setTransfers([newTransfer, ...transfers]);

      // Reset form
      setShowForm(false);
      setFormData({
        warehouseId: '',
        storeId: '',
      });
      setTransferItems([]);
    } catch (error) {
      console.error('Error creating transfer:', error);
      alert('Failed to create transfer. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewTransfer = (transfer: Transfer) => {
    setSelectedTransfer(transfer);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this transfer?')) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/transfers/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete transfer');
      }

      // Remove the transfer from the list
      setTransfers(transfers.filter(transfer => transfer.id !== id));

      // Close the transfer details if it's the one being deleted
      if (selectedTransfer && selectedTransfer.id === id) {
        setSelectedTransfer(null);
      }
    } catch (error) {
      console.error('Error deleting transfer:', error);
      alert('Failed to delete transfer. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleUpdateStatus = async (id: string, status: 'COMPLETED' | 'CANCELLED') => {
    setIsUpdatingStatus(true);

    try {
      const response = await fetch(`/api/transfers/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update transfer status');
      }

      const updatedTransfer = await response.json();

      // Update the transfer in the list
      setTransfers(transfers.map(transfer =>
        transfer.id === id ? updatedTransfer : transfer
      ));

      // Update the selected transfer if it's the one being updated
      if (selectedTransfer && selectedTransfer.id === id) {
        setSelectedTransfer(updatedTransfer);
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      alert('Failed to update transfer status. Please try again.');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Filter transfers based on selected status and search term
  const filteredTransfers = transfers.filter(transfer => {
    const matchesStatus = filterStatus === 'all' || transfer.status === filterStatus;
    const matchesSearch = searchTerm === '' ||
      transfer.transferNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.warehouseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.storeName.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Inventory Transfers
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Manage transfers between warehouses and stores • {filteredTransfers.length} transfers
            </p>
          </div>
          <button
            onClick={() => {
              setShowForm(!showForm);
              setSelectedTransfer(null);
            }}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            New Transfer
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '1.5rem 2rem'
      }}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          {/* Search */}
          <div style={{ position: 'relative', flex: 1, minWidth: '320px' }}>
            <Search style={{
              position: 'absolute',
              left: '14px',
              top: '50%',
              transform: 'translateY(-50%)',
              height: '1.25rem',
              width: '1.25rem',
              color: '#64748b'
            }} />
            <input
              type="text"
              placeholder="Search transfers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                paddingLeft: '3rem',
                paddingRight: '1rem',
                paddingTop: '0.875rem',
                paddingBottom: '0.875rem',
                borderRadius: '10px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                transition: 'all 0.2s',
                backgroundColor: '#f8fafc'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.backgroundColor = '#f8fafc';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            style={{
              padding: '0.625rem 1rem',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            <option value="all">All Status</option>
            <option value="PENDING">Pending</option>
            <option value="COMPLETED">Completed</option>
            <option value="CANCELLED">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#3b82f6' }} />
          <span style={{ marginLeft: '0.5rem', color: '#64748b' }}>Loading transfers...</span>
        </div>
      )}

      {/* New Transfer Form */}
      {showForm && (
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                Create New Transfer
              </h2>
              <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                Transfer inventory from warehouse to store
              </p>
            </div>
            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '1.5rem'
              }}>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    <MapPin style={{ display: 'inline', height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    Source Warehouse
                  </label>
                  <select
                    name="warehouseId"
                    value={formData.warehouseId}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <option value="">Select a warehouse</option>
                    {warehouses.map((warehouse: Warehouse) => (
                      <option key={warehouse.id} value={warehouse.id}>
                        {warehouse.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    <Truck style={{ display: 'inline', height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    Destination Store
                  </label>
                  <select
                    name="storeId"
                    value={formData.storeId}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <option value="">Select a store</option>
                    {stores.map((store: Store) => (
                      <option key={store.id} value={store.id}>
                        {store.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {formData.warehouseId && (
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                    <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                      <Package style={{ display: 'inline', height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                      Transfer Items
                    </label>
                    <button
                      type="button"
                      onClick={handleAddItem}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        backgroundColor: '#f3f4f6',
                        color: '#374151',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        padding: '0.5rem 1rem',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#e5e7eb';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                      }}
                    >
                      <Plus style={{ height: '1rem', width: '1rem' }} />
                      Add Item
                    </button>
                  </div>

                  {transferItems.length > 0 ? (
                    <div style={{
                      borderRadius: '6px',
                      border: '1px solid #e2e8f0'
                    }}>
                      <table style={{ width: '100%', fontSize: '0.875rem' }}>
                        <thead>
                          <tr style={{
                            borderBottom: '1px solid #e2e8f0',
                            backgroundColor: '#f8fafc'
                          }}>
                            <th style={{
                              padding: '0.5rem 1rem',
                              textAlign: 'left',
                              fontWeight: '500'
                            }}>Product</th>
                            <th style={{
                              padding: '0.5rem 1rem',
                              textAlign: 'left',
                              fontWeight: '500'
                            }}>Available Stock</th>
                            <th style={{
                              padding: '0.5rem 1rem',
                              textAlign: 'left',
                              fontWeight: '500'
                            }}>Quantity to Transfer</th>
                            <th style={{
                              padding: '0.5rem 1rem',
                              textAlign: 'right',
                              fontWeight: '500'
                            }}>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {transferItems.map((item, index) => (
                            <tr key={index} style={{ borderBottom: '1px solid #e2e8f0' }}>
                              <td style={{ padding: '0.5rem 1rem' }}>
                                <select
                                  name="productId"
                                  value={item.productId}
                                  onChange={(e) => handleItemChange(index, e)}
                                  style={{
                                    width: '100%',
                                    borderRadius: '6px',
                                    border: '1px solid #d1d5db',
                                    backgroundColor: 'white',
                                    fontSize: '0.875rem',
                                    padding: '0.5rem',
                                    cursor: 'pointer'
                                  }}
                                  required
                                >
                                  <option value="">Select a product</option>
                                  {products.map((product: Product) => (
                                    <option
                                      key={product.id}
                                      value={product.id}
                                      disabled={!product.warehouseStock?.[formData.warehouseId]}
                                    >
                                      {product.name}
                                    </option>
                                  ))}
                                </select>
                              </td>
                              <td style={{ padding: '0.5rem 1rem' }}>
                                {item.availableStock}
                              </td>
                              <td style={{ padding: '0.5rem 1rem' }}>
                                <input
                                  name="quantity"
                                  type="number"
                                  min="1"
                                  max={item.availableStock.toString()}
                                  value={item.quantity}
                                  onChange={(e) => handleItemChange(index, e)}
                                  style={{
                                    width: '100%',
                                    height: '2rem',
                                    padding: '0.25rem 0.5rem',
                                    borderRadius: '6px',
                                    border: '1px solid #d1d5db',
                                    fontSize: '0.875rem'
                                  }}
                                  required
                                />
                              </td>
                              <td style={{ padding: '0.5rem 1rem', textAlign: 'right' }}>
                                <button
                                  type="button"
                                  onClick={() => handleRemoveItem(index)}
                                  style={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '2rem',
                                    height: '2rem',
                                    borderRadius: '6px',
                                    border: 'none',
                                    backgroundColor: 'transparent',
                                    color: '#dc2626',
                                    cursor: 'pointer',
                                    transition: 'all 0.2s'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.backgroundColor = '#fee2e2';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.backgroundColor = 'transparent';
                                  }}
                                >
                                  <Trash style={{ height: '1rem', width: '1rem' }} />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div style={{
                      borderRadius: '6px',
                      border: '2px dashed #d1d5db',
                      padding: '2rem',
                      textAlign: 'center',
                      color: '#64748b'
                    }}>
                      No items added. Click "Add Item" to add products to this transfer.
                    </div>
                  )}
                </div>
              )}

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', paddingTop: '1.5rem', borderTop: '1px solid #e2e8f0' }}>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    backgroundColor: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!formData.warehouseId || !formData.storeId || transferItems.length === 0 || isSubmitting}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    border: 'none',
                    backgroundColor: isSubmitting ? '#9ca3af' : '#3b82f6',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: isSubmitting ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    if (!isSubmitting) {
                      e.currentTarget.style.backgroundColor = '#2563eb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSubmitting) {
                      e.currentTarget.style.backgroundColor = '#3b82f6';
                    }
                  }}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite' }} />
                      Creating...
                    </>
                  ) : (
                    'Create Transfer'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Transfer Details Modal */}
      {selectedTransfer && (
        <div style={{
          padding: '2rem',
          margin: '2rem',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          backgroundColor: 'white',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '1.5rem'
          }}>
            <h2 style={{
              fontSize: '1.125rem',
              fontWeight: '500',
              color: '#0f172a'
            }}>Transfer Details</h2>
            <button
              onClick={() => setSelectedTransfer(null)}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                backgroundColor: 'white',
                color: '#374151',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
              }}
            >
              Close
            </button>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div>
              <h3 style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#0f172a',
                marginBottom: '0.75rem'
              }}>Transfer Information</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Transfer Number:</span>
                  <span style={{ color: '#0f172a' }}>{selectedTransfer.transferNumber}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Date:</span>
                  <span style={{ color: '#0f172a' }}>{formatDate(selectedTransfer.date)}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>Status:</span>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '9999px',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    backgroundColor: selectedTransfer.status === 'COMPLETED' ? '#dcfce7' : selectedTransfer.status === 'PENDING' ? '#fef3c7' : '#fee2e2',
                    color: selectedTransfer.status === 'COMPLETED' ? '#166534' : selectedTransfer.status === 'PENDING' ? '#92400e' : '#991b1b'
                  }}>
                    {selectedTransfer.status}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#0f172a',
                marginBottom: '0.75rem'
              }}>Location Information</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>From Warehouse:</span>
                  <span style={{ color: '#0f172a' }}>{selectedTransfer.warehouseName}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: '#64748b' }}>To Store:</span>
                  <span style={{ color: '#0f172a' }}>{selectedTransfer.storeName}</span>
                </div>
              </div>
            </div>
          </div>

          <h3 style={{
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#0f172a',
            marginBottom: '0.75rem'
          }}>Transfer Items</h3>
          <div style={{
            borderRadius: '6px',
            border: '1px solid #e2e8f0'
          }}>
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
              <thead>
                <tr style={{
                  borderBottom: '1px solid #e2e8f0',
                  backgroundColor: '#f8fafc'
                }}>
                  <th style={{
                    padding: '0.5rem 1rem',
                    textAlign: 'left',
                    fontWeight: '500'
                  }}>Product</th>
                  <th style={{
                    padding: '0.5rem 1rem',
                    textAlign: 'right',
                    fontWeight: '500'
                  }}>Quantity</th>
                </tr>
              </thead>
              <tbody>
                {selectedTransfer.items.map((item) => (
                  <tr key={item.id} style={{ borderBottom: '1px solid #e2e8f0' }}>
                    <td style={{ padding: '0.5rem 1rem' }}>{item.productName}</td>
                    <td style={{ padding: '0.5rem 1rem', textAlign: 'right' }}>{item.quantity}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {selectedTransfer.status === 'PENDING' && (
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '0.75rem',
              marginTop: '1.5rem'
            }}>
              <button
                onClick={() => handleUpdateStatus(selectedTransfer.id, 'COMPLETED')}
                disabled={isUpdatingStatus}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: isUpdatingStatus ? '#9ca3af' : '#3b82f6',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: isUpdatingStatus ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                  }
                }}
              >
                {isUpdatingStatus ? (
                  <>
                    <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem' }} />
                    Updating...
                  </>
                ) : (
                  <>
                    <Check style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    Complete Transfer
                  </>
                )}
              </button>
              <button
                onClick={() => handleUpdateStatus(selectedTransfer.id, 'CANCELLED')}
                disabled={isUpdatingStatus}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  border: '1px solid #d1d5db',
                  backgroundColor: isUpdatingStatus ? '#f3f4f6' : 'white',
                  color: isUpdatingStatus ? '#9ca3af' : '#374151',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: isUpdatingStatus ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isUpdatingStatus) {
                    e.currentTarget.style.backgroundColor = 'white';
                  }
                }}
              >
                {isUpdatingStatus ? (
                  <>
                    <Loader2 style={{ height: '1rem', width: '1rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem' }} />
                    Updating...
                  </>
                ) : (
                  <>
                    <X style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    Cancel Transfer
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Transfers List */}
      <div style={{ padding: '2rem' }}>
        {filteredTransfers.length === 0 ? (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '2px dashed #d1d5db',
            padding: '4rem 2rem',
            textAlign: 'center'
          }}>
            <Package style={{ height: '3rem', width: '3rem', color: '#9ca3af', margin: '0 auto 1rem' }} />
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
              No transfers found
            </h3>
            <p style={{ color: '#64748b', fontSize: '0.875rem', marginBottom: '1.5rem' }}>
              {searchTerm || filterStatus !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'Create your first transfer to get started'
              }
            </p>
            {(!searchTerm && filterStatus === 'all') && (
              <button
                onClick={() => setShowForm(true)}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#2563eb';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#3b82f6';
                }}
              >
                <Plus style={{ height: '1rem', width: '1rem' }} />
                Create First Transfer
              </button>
            )}
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
            gap: '1.5rem'
          }}>
            {filteredTransfers.map((transfer) => (
              <div
                key={transfer.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  border: '1px solid #e2e8f0',
                  padding: '1.5rem',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.2s',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
                onClick={() => handleViewTransfer(transfer)}
              >
                {/* Header */}
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <div>
                    <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.25rem' }}>
                      {transfer.transferNumber}
                    </h3>
                    <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
                      <Calendar style={{ display: 'inline', height: '0.875rem', width: '0.875rem', marginRight: '0.25rem' }} />
                      {formatDate(transfer.date)}
                    </p>
                  </div>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '9999px',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    backgroundColor: transfer.status === 'COMPLETED' ? '#dcfce7' : transfer.status === 'PENDING' ? '#fef3c7' : '#fee2e2',
                    color: transfer.status === 'COMPLETED' ? '#166534' : transfer.status === 'PENDING' ? '#92400e' : '#991b1b'
                  }}>
                    {transfer.status}
                  </span>
                </div>

                {/* Transfer Route */}
                <div style={{ marginBottom: '1rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', flex: 1 }}>
                      <MapPin style={{ height: '1rem', width: '1rem', color: '#64748b' }} />
                      <span style={{ fontSize: '0.875rem', color: '#374151', fontWeight: '500' }}>
                        {transfer.warehouseName}
                      </span>
                    </div>
                    <ArrowRight style={{ height: '1rem', width: '1rem', color: '#9ca3af' }} />
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', flex: 1 }}>
                      <Truck style={{ height: '1rem', width: '1rem', color: '#64748b' }} />
                      <span style={{ fontSize: '0.875rem', color: '#374151', fontWeight: '500' }}>
                        {transfer.storeName}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Items Count */}
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <Package style={{ height: '1rem', width: '1rem', color: '#64748b' }} />
                    <span style={{ fontSize: '0.875rem', color: '#64748b' }}>
                      {transfer.items.length} item{transfer.items.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div style={{ display: 'flex', gap: '0.5rem', paddingTop: '1rem', borderTop: '1px solid #f1f5f9' }}>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewTransfer(transfer);
                    }}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      flex: 1,
                      justifyContent: 'center',
                      padding: '0.5rem',
                      borderRadius: '6px',
                      border: '1px solid #d1d5db',
                      backgroundColor: 'white',
                      color: '#374151',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                    }}
                  >
                    <Eye style={{ height: '0.875rem', width: '0.875rem' }} />
                    View
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(transfer.id);
                    }}
                    disabled={isDeleting}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem',
                      borderRadius: '6px',
                      border: '1px solid #fca5a5',
                      backgroundColor: '#fef2f2',
                      color: '#dc2626',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: isDeleting ? 'not-allowed' : 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      if (!isDeleting) {
                        e.currentTarget.style.backgroundColor = '#fee2e2';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isDeleting) {
                        e.currentTarget.style.backgroundColor = '#fef2f2';
                      }
                    }}
                  >
                    {isDeleting && transfer.id === selectedTransfer?.id ? (
                      <Loader2 style={{ height: '0.875rem', width: '0.875rem', animation: 'spin 1s linear infinite' }} />
                    ) : (
                      <Trash style={{ height: '0.875rem', width: '0.875rem' }} />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
