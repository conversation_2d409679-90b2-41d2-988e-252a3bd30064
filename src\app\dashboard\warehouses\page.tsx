'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, Loader2, RefreshCw } from 'lucide-react';

type Warehouse = {
  id: string;
  name: string;
  location: string;
  createdAt: string;
  updatedAt: string;
};

export default function WarehousesPage() {
  // Add CSS for spin animation
  const spinKeyframes = `
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `;

  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = spinKeyframes;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({ name: '', location: '' });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch warehouses data
  const fetchWarehouses = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }
    setError(null);

    try {
      console.log('🔄 Fetching warehouses...');
      const response = await fetch('/api/warehouses', {
        cache: 'no-store', // Ensure fresh data
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch warehouses');
      }

      const data = await response.json();
      console.log('📦 Fetched warehouses:', data);
      setWarehouses(data);
    } catch (err) {
      console.error('Error fetching warehouses:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchWarehouses();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingId) {
        // Update existing warehouse
        const response = await fetch(`/api/warehouses/${editingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            location: formData.location,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update warehouse');
        }

        const updatedWarehouse = await response.json();
        setWarehouses(warehouses.map(warehouse => warehouse.id === editingId ? updatedWarehouse : warehouse));
        setEditingId(null);
      } else {
        // Add new warehouse
        const response = await fetch('/api/warehouses', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            location: formData.location,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create warehouse');
        }

        const newWarehouse = await response.json();
        setWarehouses([...warehouses, newWarehouse]);
      }

      setFormData({ name: '', location: '' });
      setShowForm(false);
    } catch (err) {
      console.error('Error saving warehouse:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while saving the warehouse');
    }
  };

  const handleEdit = (warehouse: Warehouse) => {
    setFormData({ name: warehouse.name, location: warehouse.location });
    setEditingId(warehouse.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    const warehouseToDelete = warehouses.find(w => w.id === id);
    if (!confirm(`Are you sure you want to delete "${warehouseToDelete?.name}"?`)) {
      return;
    }

    try {
      console.log(`🗑️ Attempting to delete warehouse: ${id}`);

      const response = await fetch(`/api/warehouses/${id}`, {
        method: 'DELETE',
      });

      const responseData = await response.json();
      console.log('Delete response:', responseData);

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to delete warehouse');
      }

      // Update local state immediately
      setWarehouses(warehouses.filter(warehouse => warehouse.id !== id));

      // Show success message
      const message = responseData.message || `Warehouse "${warehouseToDelete?.name}" has been deleted successfully!`;
      alert(message);

      // Refresh the list to ensure consistency with database
      setTimeout(() => {
        fetchWarehouses(true);
      }, 1000);

      console.log(`✅ Successfully deleted warehouse: ${id}`, responseData);
    } catch (err) {
      console.error('Error deleting warehouse:', err);
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the warehouse');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '700', letterSpacing: '-0.025em' }}>Warehouses</h1>
          <p style={{ color: '#6b7280' }}>
            Manage your warehouses and their locations
          </p>
        </div>
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <Button
            variant="outline"
            onClick={() => fetchWarehouses(true)}
            disabled={loading || refreshing}
          >
            <RefreshCw style={{
              marginRight: '0.5rem',
              height: '1rem',
              width: '1rem',
              animation: refreshing ? 'spin 1s linear infinite' : 'none'
            }} />
            Refresh
          </Button>
          <Button
            onClick={() => {
              setFormData({ name: '', location: '' });
              setEditingId(null);
              setShowForm(!showForm);
            }}
            disabled={loading}
          >
            <Plus style={{ marginRight: '0.5rem', height: '1rem', width: '1rem' }} />
            Add Warehouse
          </Button>
        </div>
      </div>

      {error && (
        <div style={{
          borderRadius: '6px',
          backgroundColor: '#fef2f2',
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {showForm && (
        <div style={{
          borderRadius: '8px',
          border: '1px solid var(--border)',
          backgroundColor: 'var(--background)',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ marginBottom: '1rem', fontSize: '1.125rem', fontWeight: '500' }}>
            {editingId ? 'Edit Warehouse' : 'Add New Warehouse'}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Warehouse Name
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter warehouse name"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="location" className="text-sm font-medium">
                Location
              </label>
              <Input
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="Enter warehouse location"
                required
              />
            </div>
            <div className="flex gap-2">
              <Button type="submit">
                {editingId ? 'Update' : 'Save'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {loading ? (
        <div style={{
          display: 'flex',
          height: '10rem',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', color: '#667eea' }} />
          <span style={{ marginLeft: '0.5rem', color: '#6b7280' }}>Loading warehouses...</span>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {warehouses.length > 0 ? (
            warehouses.map((warehouse) => (
              <div
                key={warehouse.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  border: '1px solid #e5e7eb',
                  padding: '1.5rem',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                  e.currentTarget.style.borderColor = '#667eea';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                  e.currentTarget.style.borderColor = '#e5e7eb';
                }}
              >
                {/* Warehouse Icon */}
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  borderRadius: '12px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '1rem'
                }}>
                  <svg
                    style={{ width: '1.5rem', height: '1.5rem', color: 'white' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                </div>

                {/* Warehouse Info */}
                <div style={{ marginBottom: '1.5rem' }}>
                  <h3 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    color: '#111827',
                    marginBottom: '0.5rem'
                  }}>
                    {warehouse.name}
                  </h3>

                  <p style={{
                    fontSize: '0.875rem',
                    color: '#6b7280',
                    marginBottom: '0.75rem',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <svg
                      style={{ width: '1rem', height: '1rem', marginRight: '0.5rem' }}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    {warehouse.location}
                  </p>

                  <p style={{
                    fontSize: '0.75rem',
                    color: '#9ca3af'
                  }}>
                    Created: {new Date(warehouse.createdAt).toLocaleDateString()}
                  </p>
                </div>

                {/* Action Buttons */}
                <div style={{
                  display: 'flex',
                  gap: '0.5rem'
                }}>
                  <Button
                    variant="outline"
                    onClick={() => handleEdit(warehouse)}
                    style={{
                      flex: 1,
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: 'white',
                      color: '#374151',
                      transition: 'all 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#667eea';
                      e.currentTarget.style.borderColor = '#667eea';
                      e.currentTarget.style.color = 'white';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                      e.currentTarget.style.color = '#374151';
                    }}
                  >
                    <Edit style={{ height: '1rem', width: '1rem' }} />
                    Edit
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleDelete(warehouse.id)}
                    style={{
                      borderRadius: '8px',
                      border: '1px solid #fecaca',
                      backgroundColor: 'white',
                      color: '#dc2626',
                      transition: 'all 0.2s',
                      padding: '0.5rem'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#dc2626';
                      e.currentTarget.style.borderColor = '#dc2626';
                      e.currentTarget.style.color = 'white';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.borderColor = '#fecaca';
                      e.currentTarget.style.color = '#dc2626';
                    }}
                  >
                    <Trash style={{ height: '1rem', width: '1rem' }} />
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <div style={{
              gridColumn: '1 / -1',
              textAlign: 'center',
              padding: '3rem',
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '2px dashed #d1d5db'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '1rem'
              }}>
                <div style={{
                  width: '4rem',
                  height: '4rem',
                  borderRadius: '50%',
                  backgroundColor: '#f3f4f6',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg
                    style={{ height: '1.5rem', width: '1.5rem', color: '#9ca3af' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                </div>
                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.5rem'
                  }}>
                    No warehouses found
                  </h3>
                  <p style={{
                    fontSize: '0.875rem',
                    color: '#6b7280',
                    marginBottom: '1rem'
                  }}>
                    Create your first warehouse to start managing inventory locations.
                  </p>
                  <Button
                    onClick={() => {
                      setFormData({ name: '', location: '' });
                      setEditingId(null);
                      setShowForm(true);
                    }}
                    style={{
                      backgroundColor: '#667eea',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem 1rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <Plus style={{ height: '1rem', width: '1rem' }} />
                    Add Warehouse
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
