'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, Loader2 } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';



// Wastage reasons
const wastageReasons = [
  'Expired',
  'Damaged during transport',
  'Quality issues',
  'Production error',
  'Customer return',
  'Other',
];

// Define interface for wastage object
interface Wastage {
  id: string;
  date: string;
  productId: string;
  productName: string;
  quantity: number;
  reason: string;
  storeId: string;
  storeName: string;
  productPrice?: number;
  productUnit?: string;
}

// Define interface for product object
interface Product {
  id: string;
  name: string;
  price: number;
  unit: string;
  category?: string;
}

// Define interface for store object
interface Store {
  id: string;
  name: string;
}

export default function WastagePage() {
  const [wastage, setWastage] = useState<Wastage[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    productId: '',
    quantity: '',
    reason: '',
    storeId: '',
    otherReason: '',
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [filterStore, setFilterStore] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch wastage, products, and stores when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch all data in parallel for better performance
        const [wastageResponse, productsResponse, storesResponse] = await Promise.all([
          fetch('/api/wastage'),
          fetch('/api/products'),
          fetch('/api/stores')
        ]);

        // Handle wastage response
        if (!wastageResponse.ok) {
          const errorData = await wastageResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch wastage records');
        }

        // Handle products response
        if (!productsResponse.ok) {
          const errorData = await productsResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch products');
        }

        // Handle stores response
        if (!storesResponse.ok) {
          const errorData = await storesResponse.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch stores');
        }

        // Parse response data
        const [wastageData, productsData, storesData] = await Promise.all([
          wastageResponse.json(),
          productsResponse.json(),
          storesResponse.json()
        ]);

        // Update state with fetched data
        setWastage(wastageData);
        setProducts(productsData);
        setStores(storesData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch filtered wastage when filters change
  useEffect(() => {
    const fetchFilteredWastage = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters for filtering
        const params = new URLSearchParams();

        if (filterStore !== 'all') {
          params.append('storeId', filterStore);
        }

        if (dateRange.startDate) {
          params.append('startDate', dateRange.startDate);
        }

        if (dateRange.endDate) {
          params.append('endDate', dateRange.endDate);
        }

        // Fetch filtered wastage
        const response = await fetch(`/api/wastage?${params.toString()}`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch wastage records');
        }

        const data = await response.json();
        setWastage(data);
      } catch (err) {
        console.error('Error fetching filtered wastage:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if we're not in the initial loading state
    if (!loading) {
      fetchFilteredWastage();
    }
  }, [filterStore, dateRange.startDate, dateRange.endDate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange({ ...dateRange, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.productId || !formData.storeId || !formData.reason || !formData.quantity) {
      alert('Please fill in all required fields');
      return;
    }

    if (isNaN(parseFloat(formData.quantity)) || parseFloat(formData.quantity) <= 0) {
      alert('Please enter a valid quantity');
      return;
    }

    const finalReason = formData.reason === 'Other' ? formData.otherReason : formData.reason;

    // Find the store name for display purposes
    const store = stores.find(s => s.id === formData.storeId);
    if (!store) {
      alert('Selected store not found');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (editingId) {
        // Update existing wastage
        const response = await fetch(`/api/wastage/${editingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            date: formData.date,
            productId: formData.productId,
            quantity: formData.quantity,
            reason: finalReason,
            storeId: formData.storeId,
            storeName: store.name,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update wastage record');
        }

        const updatedWastage = await response.json();

        // Update the wastage list
        setWastage(wastage.map(item =>
          item.id === editingId ? updatedWastage : item
        ));

        setEditingId(null);
        alert('Wastage record updated successfully!');
      } else {
        // Add new wastage
        const response = await fetch('/api/wastage', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            date: formData.date,
            productId: formData.productId,
            quantity: formData.quantity,
            reason: finalReason,
            storeId: formData.storeId,
            storeName: store.name,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create wastage record');
        }

        const newWastage = await response.json();

        // Add the new wastage to the list
        setWastage([newWastage, ...wastage]);
        alert('Wastage record added successfully!');
      }

      // Reset form
      setFormData({
        date: new Date().toISOString().split('T')[0],
        productId: '',
        quantity: '',
        reason: '',
        storeId: '',
        otherReason: '',
      });
      setShowForm(false);
    } catch (err) {
      console.error('Error saving wastage record:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while saving the wastage record');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (item: Wastage) => {
    setFormData({
      date: item.date,
      productId: item.productId,
      quantity: item.quantity.toString(),
      reason: wastageReasons.includes(item.reason) ? item.reason : 'Other',
      storeId: item.storeId,
      otherReason: wastageReasons.includes(item.reason) ? '' : item.reason,
    });
    setEditingId(item.id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this wastage record?')) {
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/wastage/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete wastage record');
      }

      // Remove the wastage from the list
      setWastage(wastage.filter(item => item.id !== id));
      alert('Wastage record deleted successfully!');
    } catch (err) {
      console.error('Error deleting wastage record:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(err instanceof Error ? err.message : 'An error occurred while deleting the wastage record');
    } finally {
      setIsDeleting(false);
    }
  };

  // Filter wastage based on selected filters
  const filteredWastage = wastage.filter(item => {
    // Filter by store
    if (filterStore !== 'all' && item.storeId !== filterStore) {
      return false;
    }

    // Filter by date range
    if (dateRange.startDate && item.date < dateRange.startDate) {
      return false;
    }

    if (dateRange.endDate && item.date > dateRange.endDate) {
      return false;
    }

    return true;
  });

  // Calculate total wastage value
  const totalWastageValue = filteredWastage.reduce((sum, item) => {
    // Use the product price from the wastage record if available, otherwise use 0
    return sum + ((item.productPrice || 0) * item.quantity);
  }, 0);

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Wastage Tracking
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              Track and manage product wastage • ₹{formatCurrency(totalWastageValue)} total loss
            </p>
          </div>
          <button
            onClick={() => {
              setFormData({
                date: new Date().toISOString().split('T')[0],
                productId: '',
                quantity: '',
                reason: '',
                storeId: '',
                otherReason: '',
              });
              setEditingId(null);
              setShowForm(!showForm);
            }}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#b91c1c';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#dc2626';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            Record Wastage
          </button>
        </div>
      </div>

      {/* Content */}
      <div style={{ padding: '2rem' }}>

      <div className="grid gap-4 md:grid-cols-2">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground">Total Wastage Value</h3>
          </div>
          <div className="mt-2">
            <p className="text-2xl font-bold">{formatCurrency(totalWastageValue)}</p>
            <p className="text-xs text-muted-foreground">Total value of wasted products</p>
          </div>
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground">Total Items Wasted</h3>
          </div>
          <div className="mt-2">
            <p className="text-2xl font-bold">{filteredWastage.reduce((sum, item) => sum + item.quantity, 0)}</p>
            <p className="text-xs text-muted-foreground">Number of products wasted</p>
          </div>
        </div>
      </div>

      {showForm && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '2rem',
          marginBottom: '2rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ fontSize: '1.125rem', fontWeight: '500', marginBottom: '1.5rem', color: '#0f172a' }}>
            {editingId ? 'Edit Wastage Record' : 'Record New Wastage'}
          </h2>
          <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="date" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Date
                </label>
                <input
                  id="date"
                  name="date"
                  type="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="storeId" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Store
                </label>
                <select
                  id="storeId"
                  name="storeId"
                  value={formData.storeId}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="">Select a store</option>
                  {stores.map(store => (
                    <option key={store.id} value={store.id}>
                      {store.name}
                    </option>
                  ))}
                </select>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="productId" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Product
                </label>
                <select
                  id="productId"
                  name="productId"
                  value={formData.productId}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="">Select a product</option>
                  {products.map(product => (
                    <option key={product.id} value={product.id}>
                      {product.name} (₹{product.price}/{product.unit})
                    </option>
                  ))}
                </select>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="quantity" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Quantity
                </label>
                <input
                  id="quantity"
                  name="quantity"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  placeholder="Enter quantity"
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <label htmlFor="reason" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                  Reason
                </label>
                <select
                  id="reason"
                  name="reason"
                  value={formData.reason}
                  onChange={handleInputChange}
                  style={{
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                  required
                >
                  <option value="">Select a reason</option>
                  {wastageReasons.map(reason => (
                    <option key={reason} value={reason}>
                      {reason}
                    </option>
                  ))}
                </select>
              </div>
              {formData.reason === 'Other' && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  <label htmlFor="otherReason" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                    Specify Reason
                  </label>
                  <input
                    id="otherReason"
                    name="otherReason"
                    value={formData.otherReason}
                    onChange={handleInputChange}
                    placeholder="Enter specific reason"
                    style={{
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      backgroundColor: 'white',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#3b82f6';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    required
                  />
                </div>
              )}
            </div>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                type="submit"
                disabled={isSubmitting}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: isSubmitting ? '#94a3b8' : '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
                onMouseEnter={(e) => {
                  if (!isSubmitting) {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSubmitting) {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }
                }}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 style={{ height: '16px', width: '16px', animation: 'spin 1s linear infinite' }} />
                    {editingId ? 'Updating...' : 'Saving...'}
                  </>
                ) : (
                  editingId ? 'Update' : 'Save'
                )}
              </button>
              <button
                type="button"
                onClick={() => setShowForm(false)}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#64748b',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  padding: '0.875rem 1.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                }}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '1.5rem',
        marginBottom: '2rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '1rem', color: '#0f172a' }}>
          Filter Wastage Records
        </h3>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <label htmlFor="filterStore" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
              Filter by Store
            </label>
            <select
              id="filterStore"
              value={filterStore}
              onChange={(e) => setFilterStore(e.target.value)}
              style={{
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <option value="all">All Stores</option>
              {stores.map(store => (
                <option key={store.id} value={store.id}>
                  {store.name}
                </option>
              ))}
            </select>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <label htmlFor="startDate" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
              From
            </label>
            <input
              id="startDate"
              name="startDate"
              type="date"
              value={dateRange.startDate}
              onChange={handleDateRangeChange}
              style={{
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white',
                transition: 'all 0.2s'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <label htmlFor="endDate" style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
              To
            </label>
            <input
              id="endDate"
              name="endDate"
              type="date"
              value={dateRange.endDate}
              onChange={handleDateRangeChange}
              style={{
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                backgroundColor: 'white',
                transition: 'all 0.2s'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
          </div>
        </div>
      </div>

      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          {loading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '3rem',
              color: '#64748b'
            }}>
              <Loader2 style={{ height: '2rem', width: '2rem', animation: 'spin 1s linear infinite', marginRight: '0.5rem', color: '#3b82f6' }} />
              <span>Loading wastage records...</span>
            </div>
          ) : error ? (
            <div style={{ padding: '3rem', textAlign: 'center' }}>
              <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
              <button
                onClick={() => {
                  setLoading(true);
                  setError(null);
                  fetch('/api/wastage')
                    .then(res => res.json())
                    .then(data => setWastage(data))
                    .catch(err => setError(err instanceof Error ? err.message : 'An error occurred'))
                    .finally(() => setLoading(false));
                }}
                style={{
                  backgroundColor: '#f1f5f9',
                  color: '#3b82f6',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer'
                }}
              >
                Retry
              </button>
            </div>
          ) : (
            <table style={{ width: '100%', fontSize: '0.875rem' }}>
            <thead>
              <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Date</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Store</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Product</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Quantity</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Value</th>
                <th style={{ padding: '1rem', textAlign: 'left', fontWeight: '500', color: '#374151' }}>Reason</th>
                <th style={{ padding: '1rem', textAlign: 'right', fontWeight: '500', color: '#374151' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredWastage.map((item) => {
                const value = (item.productPrice || 0) * item.quantity;

                return (
                  <tr
                    key={item.id}
                    style={{
                      borderBottom: '1px solid #f1f5f9',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <td style={{ padding: '1rem', color: '#64748b' }}>{formatDate(item.date)}</td>
                    <td style={{ padding: '1rem' }}>{item.storeName}</td>
                    <td style={{ padding: '1rem' }}>{item.productName}</td>
                    <td style={{ padding: '1rem', fontWeight: '500' }}>{item.quantity}</td>
                    <td style={{ padding: '1rem', fontWeight: '500', color: '#dc2626' }}>₹{value.toFixed(2)}</td>
                    <td style={{ padding: '1rem' }}>
                      <span style={{
                        backgroundColor: '#fef3c7',
                        color: '#92400e',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '12px',
                        fontSize: '0.75rem',
                        fontWeight: '500'
                      }}>
                        {item.reason}
                      </span>
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'right' }}>
                      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.5rem' }}>
                        <button
                          onClick={() => handleEdit(item)}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#3b82f6',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#eff6ff';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }}
                        >
                          <Edit style={{ height: '16px', width: '16px' }} />
                        </button>
                        <button
                          onClick={() => handleDelete(item.id)}
                          disabled={isDeleting}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#dc2626',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#fef2f2';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }}
                        >
                          {isDeleting ? (
                            <Loader2 style={{ height: '16px', width: '16px', animation: 'spin 1s linear infinite' }} />
                          ) : (
                            <Trash style={{ height: '16px', width: '16px' }} />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
              {filteredWastage.length === 0 && (
                <tr>
                  <td colSpan={7} style={{ padding: '3rem', textAlign: 'center', color: '#64748b' }}>
                    <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🗑️</div>
                    <p>No wastage records found. Add one to get started or adjust your filters.</p>
                  </td>
                </tr>
              )}
            </tbody>
            <tfoot>
              <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                <td colSpan={4} style={{ padding: '1rem', textAlign: 'right', fontWeight: '600', color: '#374151' }}>
                  Total Wastage Value:
                </td>
                <td style={{ padding: '1rem', fontWeight: '700', color: '#dc2626', fontSize: '1rem' }}>
                  ₹{totalWastageValue.toFixed(2)}
                </td>
                <td colSpan={2}></td>
              </tr>
            </tfoot>
          </table>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
