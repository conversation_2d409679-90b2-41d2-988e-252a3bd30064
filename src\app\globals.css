@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
}

/* Basic utility classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: var(--primary);
  color: white;
  text-decoration: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn:hover {
  background: var(--primary-hover);
}

.btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--background);
  color: var(--foreground);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.error-message {
  padding: 1rem;
  margin-bottom: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: var(--error);
  font-size: 0.875rem;
}

@media (prefers-color-scheme: dark) {
  .error-message {
    background: #7f1d1d;
    border-color: #dc2626;
    color: #fecaca;
  }
}

.card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Layout utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

.min-h-screen {
  min-height: 100vh;
}

.w-full {
  width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* Typography */
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.font-bold {
  font-weight: 700;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-800 {
  color: #1f2937;
}

@media (prefers-color-scheme: dark) {
  .text-gray-600 {
    color: #d1d5db;
  }

  .text-gray-800 {
    color: #f9fafb;
  }
}

.text-blue-600 {
  color: var(--primary);
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

/* Custom animations for login page */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.8s ease-in-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

/* Spinner animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Button Visibility Fix */
button {
  color: inherit !important;
}

button[style*="background"] {
  color: #374151 !important;
}

button[style*="background-color: white"] {
  color: #374151 !important;
}

button[style*="background-color: #667eea"] {
  color: white !important;
}

button[style*="background-color: #dc2626"] {
  color: white !important;
}

button[style*="background-color: #059669"] {
  color: white !important;
}

button[style*="background-color: #10b981"] {
  color: white !important;
}

/* Ensure all buttons have visible text */
button:not([style*="color"]) {
  color: #374151 !important;
}

/* Header styling */
header {
  background-color: #5F9EA0 !important;
}

header span, header div {
  color: white !important;
}

header button {
  color: white !important;
}

header button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Sidebar styling */
aside {
  background-color: #5F9EA0 !important;
}

aside h2, aside h3 {
  color: white !important;
  opacity: 0.8;
}

aside a {
  color: rgba(255, 255, 255, 0.8) !important;
}

aside a:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

aside button {
  color: white !important;
}

/* Dashboard Dark Mode Support */
.dashboard-bg {
  background-color: #f8fafc;
}

.dashboard-card {
  background-color: white;
  border: 1px solid #e2e8f0;
}

.dashboard-header {
  background-color: #5F9EA0;
  border-bottom: 1px solid #4a8a8d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-text-primary {
  color: #0f172a;
}

.dashboard-text-secondary {
  color: #64748b;
}

.dashboard-border {
  border-color: #e2e8f0;
}

.dashboard-hover:hover {
  background-color: #f8fafc;
}

@media (prefers-color-scheme: dark) {
  .dashboard-bg {
    background-color: #0f172a;
  }

  .dashboard-card {
    background-color: #1e293b;
    border-color: #334155;
  }

  .dashboard-header {
    background-color: #1e293b;
    border-bottom-color: #334155;
  }

  .dashboard-text-primary {
    color: #f1f5f9;
  }

  .dashboard-text-secondary {
    color: #cbd5e1;
  }

  .dashboard-border {
    border-color: #334155;
  }

  .dashboard-hover:hover {
    background-color: #334155;
  }
}
