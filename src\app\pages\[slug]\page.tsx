import { NextRequest } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { notFound } from 'next/navigation';
import Link from 'next/link';

const prisma = new PrismaClient();

interface PageData {
  id: string;
  title: string;
  slug: string;
  content: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

async function getPage(slug: string): Promise<PageData | null> {
  try {
    console.log('Fetching page for slug:', slug);

    const page = await prisma.systemSetting.findFirst({
      where: {
        category: 'static_pages',
        key: slug
      }
    });

    if (!page) {
      console.log('Page not found for slug:', slug);
      return null;
    }

    let data;
    try {
      data = JSON.parse(page.value);
    } catch (error) {
      console.error('Error parsing page data:', error);
      return null;
    }

    // Only return active pages
    if (!data.isActive) {
      console.log('Page is inactive for slug:', slug);
      return null;
    }

    const pageData = {
      id: page.key,
      title: data.title,
      slug: data.slug,
      content: data.content,
      isActive: data.isActive,
      createdAt: page.createdAt.toISOString(),
      updatedAt: page.updatedAt.toISOString()
    };

    console.log('Successfully fetched page:', pageData.title);
    return pageData;
  } catch (error) {
    console.error('Error fetching page:', error);
    return null;
  }
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const page = await getPage(params.slug);

  if (!page) {
    return {
      title: 'Page Not Found',
    };
  }

  return {
    title: `${page.title} | Mispri`,
    description: page.content.replace(/<[^>]*>/g, '').substring(0, 160),
  };
}

export default async function StaticPage({ params }: { params: { slug: string } }) {
  const page = await getPage(params.slug);

  if (!page || !page.isActive) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">Mispri</h1>
            </div>
            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-gray-600 hover:text-gray-900">Home</Link>
              <Link href="/pages/about-us" className="text-gray-600 hover:text-gray-900">About Us</Link>
              <Link href="/pages/contact-us" className="text-gray-600 hover:text-gray-900">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            ← Back to Home
          </Link>
        </div>

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {page.title}
          </h1>
          <div className="h-1 w-20 bg-blue-600 rounded"></div>
        </div>

        {/* Page Content */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div
            className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: page.content }}
          />
        </div>

        {/* Footer */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Last updated: {new Date(page.updatedAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">Mispri</h3>
              <p className="text-gray-400 text-sm">
                Your trusted partner for flowers, cakes, gifts, and special moments.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/pages/about-us" className="text-gray-400 hover:text-white">About Us</Link></li>
                <li><Link href="/pages/contact-us" className="text-gray-400 hover:text-white">Contact Us</Link></li>
                <li><Link href="/pages/faq" className="text-gray-400 hover:text-white">FAQ</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Policies</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/pages/privacy-policy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
                <li><Link href="/pages/terms-conditions" className="text-gray-400 hover:text-white">Terms & Conditions</Link></li>
                <li><Link href="/pages/cancellation-refund" className="text-gray-400 hover:text-white">Refund Policy</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">More</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/pages/blog" className="text-gray-400 hover:text-white">Blog</Link></li>
                <li><Link href="/pages/reviews" className="text-gray-400 hover:text-white">Reviews</Link></li>
                <li><Link href="/pages/coupons-deals" className="text-gray-400 hover:text-white">Deals</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2024 Mispri. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
