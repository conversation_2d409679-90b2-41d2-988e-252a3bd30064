'use client';

import { useState } from 'react';

export default function TestAPIPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAPI = async (endpoint: string) => {
    setLoading(true);
    setResult('Testing...');
    
    try {
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-email': '<EMAIL>'
        }
      });

      const status = response.status;
      const statusText = response.statusText;
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ SUCCESS (${status})\n${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        setResult(`❌ ERROR (${status} ${statusText})\n${errorText}`);
      }
    } catch (error) {
      setResult(`❌ NETWORK ERROR\n${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreateProduct = async () => {
    setLoading(true);
    setResult('Creating test product...');
    
    const testData = {
      name: 'Test API Product',
      description: 'Testing API functionality',
      category: 'test-category',
      warehouseId: 'test-warehouse',
      sku: `TEST-${Date.now()}`,
      unit: 'piece',
      lowStockThreshold: '10',
      initialStock: '50',
      isActive: true,
      variants: [
        {
          weight: '0.5 Kg',
          price: 595,
          costPrice: 300,
          isDefault: true,
          isActive: true,
          sortOrder: 1
        }
      ]
    };

    try {
      const response = await fetch('/api/products-with-variants', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-email': '<EMAIL>'
        },
        body: JSON.stringify(testData)
      });

      const status = response.status;
      const statusText = response.statusText;
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ PRODUCT CREATED (${status})\n${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        setResult(`❌ CREATE ERROR (${status} ${statusText})\n${errorText}`);
      }
    } catch (error) {
      setResult(`❌ CREATE NETWORK ERROR\n${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Testing Page</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={() => testAPI('/api/products-with-variants')}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test GET /api/products-with-variants
        </button>
        
        <button
          onClick={() => testAPI('/api/products')}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test GET /api/products
        </button>
        
        <button
          onClick={() => testAPI('/api/categories')}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test GET /api/categories
        </button>
        
        <button
          onClick={testCreateProduct}
          disabled={loading}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50"
        >
          Test POST /api/products-with-variants
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="font-bold mb-2">Result:</h2>
        <pre className="whitespace-pre-wrap text-sm">
          {loading ? 'Loading...' : result || 'Click a button to test an API endpoint'}
        </pre>
      </div>
    </div>
  );
}
