'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function TestStoreLoginPage() {
  const [selectedUser, setSelectedUser] = useState('');
  const router = useRouter();

  const testUsers = [
    {
      id: 'admin-1',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'ADMIN',
      storeId: null,
      store: null,
    },
    {
      id: 'store-manager-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'STORE_MANAGER',
      storeId: 'main-store',
      store: { name: 'Mispri Main Store' },
    },
    {
      id: 'store-manager-2',
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'STORE_MANAGER',
      storeId: 'branch-store',
      store: { name: 'Mispri Branch Store' },
    },
    {
      id: 'staff-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'STAFF',
      storeId: 'main-store',
      store: { name: 'Mispri Main Store' },
    },
  ];

  const handleLogin = (userType: string) => {
    const user = testUsers.find(u => u.id === userType);
    if (user) {
      // Store user in localStorage
      localStorage.setItem('user', JSON.stringify(user));
      
      // Redirect to orders page
      router.push('/dashboard/orders');
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '2rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        maxWidth: '500px',
        width: '100%'
      }}>
        <h1 style={{ 
          fontSize: '1.5rem', 
          fontWeight: '600', 
          marginBottom: '1rem',
          textAlign: 'center'
        }}>
          🧪 Test Role-Based Order Access
        </h1>
        
        <p style={{ 
          color: '#64748b', 
          marginBottom: '2rem',
          textAlign: 'center'
        }}>
          Select a user role to test the order visibility system
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {testUsers.map((user) => (
            <button
              key={user.id}
              onClick={() => handleLogin(user.id)}
              style={{
                padding: '1rem',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.2s',
                ':hover': {
                  backgroundColor: '#f8fafc',
                  borderColor: '#3b82f6'
                }
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f8fafc';
                e.currentTarget.style.borderColor = '#3b82f6';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.borderColor = '#e2e8f0';
              }}
            >
              <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>
                {user.name}
              </div>
              <div style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                {user.email}
              </div>
              <div style={{ fontSize: '0.75rem', color: '#059669', fontWeight: '500' }}>
                {user.role} {user.store ? `• ${user.store.name}` : '• All Stores'}
              </div>
            </button>
          ))}
        </div>

        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          backgroundColor: '#fef3c7',
          borderRadius: '8px',
          fontSize: '0.875rem'
        }}>
          <div style={{ fontWeight: '600', marginBottom: '0.5rem' }}>Expected Results:</div>
          <ul style={{ margin: 0, paddingLeft: '1rem' }}>
            <li><strong>Admin:</strong> Sees all 5 orders (including PENDING_ASSIGNMENT)</li>
            <li><strong>Main Store Manager:</strong> Sees 2 orders (assigned to main-store)</li>
            <li><strong>Branch Store Manager:</strong> Sees 1 order (assigned to branch-store)</li>
            <li><strong>Staff:</strong> Same as their store manager</li>
          </ul>
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '8px',
          fontSize: '0.875rem'
        }}>
          <div style={{ fontWeight: '600', marginBottom: '0.5rem' }}>Test Orders:</div>
          <ul style={{ margin: 0, paddingLeft: '1rem', fontSize: '0.75rem' }}>
            <li>Order 1: PENDING_ASSIGNMENT (Admin only)</li>
            <li>Order 2: ASSIGNED to main-store</li>
            <li>Order 3: IN_PROGRESS at branch-store</li>
            <li>Order 4: COMPLETED at main-store</li>
            <li>Order 5: PENDING_ASSIGNMENT (Admin only)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
