'use client';

import { useState, useEffect } from 'react';

interface ProductVariant {
  id: string;
  weight: string;
  price: number;
  costPrice: number;
  isDefault: boolean;
  isActive: boolean;
  sortOrder: number;
}

interface Product {
  id: string;
  name: string;
  price: number;
  variants: ProductVariant[];
}

export default function TestVariantsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products-with-variants');
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('📦 API Response:', data);
      
      setProducts(data.products || []);
    } catch (err) {
      console.error('❌ Error fetching products:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Testing Product Variants</h1>
        <div className="text-center">Loading products...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Testing Product Variants</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Error:</strong> {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🧪 Product Variants Test Page</h1>
      
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 className="font-semibold text-blue-800 mb-2">📊 Summary</h2>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium">Total Products:</span> {products.length}
          </div>
          <div>
            <span className="font-medium">With Variants:</span> {products.filter(p => p.variants && p.variants.length > 0).length}
          </div>
          <div>
            <span className="font-medium">Without Variants:</span> {products.filter(p => !p.variants || p.variants.length === 0).length}
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {products.map((product) => (
          <div key={product.id} className="border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                <p className="text-sm text-gray-600">ID: {product.id}</p>
                <p className="text-sm text-gray-600">Base Price: ₹{product.price}</p>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  product.variants && product.variants.length > 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {product.variants && product.variants.length > 0 
                    ? `${product.variants.length} Variants` 
                    : 'No Variants'
                  }
                </span>
              </div>
            </div>

            {product.variants && product.variants.length > 0 ? (
              <div>
                <h4 className="font-medium text-gray-700 mb-3">🎛️ Variants:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {product.variants
                    .sort((a, b) => a.sortOrder - b.sortOrder)
                    .map((variant, index) => (
                    <div 
                      key={variant.id} 
                      className={`border rounded-lg p-3 ${
                        variant.isDefault 
                          ? 'border-blue-300 bg-blue-50' 
                          : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-medium text-gray-900">{variant.weight}</span>
                        <div className="flex gap-1">
                          {variant.isDefault && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Default</span>
                          )}
                          <span className={`px-2 py-1 text-xs rounded ${
                            variant.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {variant.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        <div>Price: ₹{variant.price}</div>
                        <div>Cost: ₹{variant.costPrice}</div>
                        <div>Order: {variant.sortOrder}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-gray-500 italic">
                ⚠️ This product has no variants. It will use fallback static weights on the website.
              </div>
            )}
          </div>
        ))}
      </div>

      {products.length === 0 && (
        <div className="text-center text-gray-500 py-8">
          No products found. Create some products with variants in the Products section.
        </div>
      )}
    </div>
  );
}
