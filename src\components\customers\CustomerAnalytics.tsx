'use client';

import { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  ShoppingBag,
  Calendar,
  MapPin,
  Star,
  Clock,
  Target,
  Zap,
  Heart,
  Award
} from 'lucide-react';
import { Customer } from '@/app/dashboard/customers/page';

interface CustomerAnalyticsProps {
  customers: Customer[];
}

export function CustomerAnalytics({ customers }: CustomerAnalyticsProps) {
  const analytics = useMemo(() => {
    const totalCustomers = customers.length;
    const totalRevenue = customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
    const totalOrders = customers.reduce((sum, customer) => sum + customer.totalOrders, 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const avgCustomerValue = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;

    // Customer status distribution
    const statusDistribution = customers.reduce((acc, customer) => {
      const daysSinceLastOrder = customer.lastOrderDate 
        ? Math.floor((new Date().getTime() - new Date(customer.lastOrderDate).getTime()) / (1000 * 3600 * 24))
        : null;
      
      let status = 'new';
      if (daysSinceLastOrder !== null) {
        if (daysSinceLastOrder <= 30) status = 'active';
        else if (daysSinceLastOrder <= 90) status = 'inactive';
        else status = 'dormant';
      }
      
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // VIP customers
    const vipCustomers = customers.filter(customer => 
      customer.totalSpent > 10000 || customer.totalOrders > 20
    ).length;

    // Newsletter subscribers
    const newsletterSubscribers = customers.filter(customer => 
      customer.isSubscribedToNewsletter
    ).length;

    // Top customers by spending
    const topCustomers = [...customers]
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 5);

    // Geographic distribution
    const geoDistribution = customers.reduce((acc, customer) => {
      if (customer.addresses && customer.addresses.length > 0) {
        const city = customer.addresses[0].city;
        acc[city] = (acc[city] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    // Monthly trends (simulated data)
    const monthlyTrends = [
      { month: 'Jan', customers: 45, revenue: 125000 },
      { month: 'Feb', customers: 52, revenue: 142000 },
      { month: 'Mar', customers: 48, revenue: 138000 },
      { month: 'Apr', customers: 61, revenue: 165000 },
      { month: 'May', customers: 58, revenue: 158000 },
      { month: 'Jun', customers: 67, revenue: 182000 },
    ];

    return {
      totalCustomers,
      totalRevenue,
      totalOrders,
      avgOrderValue,
      avgCustomerValue,
      statusDistribution,
      vipCustomers,
      newsletterSubscribers,
      topCustomers,
      geoDistribution,
      monthlyTrends
    };
  }, [customers]);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      new: 'bg-green-100 text-green-800',
      active: 'bg-blue-100 text-blue-800',
      inactive: 'bg-yellow-100 text-yellow-800',
      dormant: 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Customer Value</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.avgCustomerValue)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +5% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VIP Customers</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.vipCustomers}</div>
            <p className="text-xs text-muted-foreground">
              {((analytics.vipCustomers / analytics.totalCustomers) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Customer Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Customer Status Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(analytics.statusDistribution).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge className={`${getStatusColor(status)} border-0`}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Badge>
                    <span className="text-sm text-gray-600">{count} customers</span>
                  </div>
                  <div className="text-sm font-medium">
                    {((count / analytics.totalCustomers) * 100).toFixed(1)}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Key Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ShoppingBag className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-gray-600">Avg Order Value</span>
                </div>
                <span className="font-medium">{formatCurrency(analytics.avgOrderValue)}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Heart className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-gray-600">Newsletter Subscribers</span>
                </div>
                <span className="font-medium">{analytics.newsletterSubscribers}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-amber-600" />
                  <span className="text-sm text-gray-600">VIP Rate</span>
                </div>
                <span className="font-medium">
                  {((analytics.vipCustomers / analytics.totalCustomers) * 100).toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-gray-600">Active Rate</span>
                </div>
                <span className="font-medium">
                  {(((analytics.statusDistribution.active || 0) / analytics.totalCustomers) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Top Customers by Spending
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.topCustomers.map((customer, index) => (
              <div key={customer.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full p-2">
                    <span className="text-white font-bold text-sm">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {customer.firstName} {customer.lastName}
                    </p>
                    <p className="text-sm text-gray-600">{customer.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">{formatCurrency(customer.totalSpent)}</p>
                  <p className="text-sm text-gray-600">{customer.totalOrders} orders</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Geographic Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Geographic Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(analytics.geoDistribution)
              .sort(([, a], [, b]) => b - a)
              .slice(0, 6)
              .map(([city, count]) => (
                <div key={city} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-600" />
                    <span className="font-medium text-gray-900">{city}</span>
                  </div>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Monthly Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Chart visualization would go here</p>
              <p className="text-sm text-gray-400 mt-1">
                Showing customer acquisition and revenue trends
              </p>
            </div>
          </div>
          
          {/* Simple data display */}
          <div className="mt-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {analytics.monthlyTrends.map((trend) => (
              <div key={trend.month} className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-900">{trend.month}</p>
                <p className="text-xs text-gray-600">{trend.customers} customers</p>
                <p className="text-xs text-gray-600">{formatCurrency(trend.revenue)}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Customer Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Customer Lifecycle
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">New Customers</span>
                <span className="font-medium">{analytics.statusDistribution.new || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Customers</span>
                <span className="font-medium">{analytics.statusDistribution.active || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">At Risk</span>
                <span className="font-medium">{analytics.statusDistribution.inactive || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Churned</span>
                <span className="font-medium">{analytics.statusDistribution.dormant || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Customer Retention</span>
                <span className="font-medium text-green-600">85%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Repeat Purchase Rate</span>
                <span className="font-medium text-blue-600">72%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Order Frequency</span>
                <span className="font-medium">2.3/month</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Customer Satisfaction</span>
                <span className="font-medium text-yellow-600">4.8/5</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Growth Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Monthly Growth</span>
                <span className="font-medium text-green-600">+12%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Revenue Growth</span>
                <span className="font-medium text-green-600">+8%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Churn Rate</span>
                <span className="font-medium text-red-600">3.2%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">LTV Growth</span>
                <span className="font-medium text-green-600">+15%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
