'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  User,
  ShoppingBag,
  DollarSign,
  Plus,
  X,
  Tag,
  Clock,
  Star,
  TrendingUp,
  Activity,
  MessageSquare,
  Gift,
  CreditCard,
  Users,
  Heart,
  Award,
  Target,
  Zap,
  Eye,
  Download,
  Share2,
  MoreHorizontal,
  BarChart3
} from 'lucide-react';
import { Customer, CustomerOrder } from '@/app/dashboard/customers/page';

interface CustomerDetailProps {
  customer: Customer;
  orders: CustomerOrder[];
  onBack: () => void;
  onEdit: (customer: Customer) => void;
  onDelete: (id: string) => void;
  onAddNote: (id: string, note: string) => void;
  onAddTag: (id: string, tag: string) => void;
  onRemoveTag: (id: string, tag: string) => void;
}

export function CustomerDetail({
  customer,
  orders,
  onBack,
  onEdit,
  onDelete,
  onAddNote,
  onAddTag,
  onRemoveTag
}: CustomerDetailProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [newNote, setNewNote] = useState('');
  const [newTag, setNewTag] = useState('');
  const [showAddTag, setShowAddTag] = useState(false);

  const fullName = `${customer.firstName} ${customer.lastName}`;

  const handleAddNote = () => {
    if (newNote.trim()) {
      onAddNote(customer.id, newNote);
      setNewNote('');
    }
  };

  const handleAddTag = () => {
    if (newTag.trim()) {
      onAddTag(customer.id, newTag);
      setNewTag('');
      setShowAddTag(false);
    }
  };

  // Enhanced calculations
  const totalOrders = orders.length;
  const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);
  const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
  const lastOrderDate = orders.length > 0 ? orders[0].createdAt : null;

  // Customer status and insights
  const getCustomerStatus = () => {
    const daysSinceLastOrder = lastOrderDate
      ? Math.floor((new Date().getTime() - new Date(lastOrderDate).getTime()) / (1000 * 3600 * 24))
      : null;

    if (!lastOrderDate) return { status: 'new', color: 'green' };
    if (daysSinceLastOrder && daysSinceLastOrder <= 30) return { status: 'active', color: 'blue' };
    if (daysSinceLastOrder && daysSinceLastOrder <= 90) return { status: 'inactive', color: 'yellow' };
    return { status: 'dormant', color: 'red' };
  };

  const customerStatus = getCustomerStatus();
  const isVipCustomer = totalSpent > 10000 || totalOrders > 20;
  const customerLifetimeValue = totalSpent;

  // Recent activity simulation
  const recentActivities = [
    { type: 'order', description: 'Placed order #1234', date: '2 days ago', icon: ShoppingBag },
    { type: 'login', description: 'Logged into account', date: '1 week ago', icon: User },
    { type: 'review', description: 'Left a 5-star review', date: '2 weeks ago', icon: Star },
    { type: 'support', description: 'Contacted customer support', date: '1 month ago', icon: MessageSquare },
  ];

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-gray-200 shadow-sm overflow-hidden">
        {/* Navigation Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <Button variant="ghost" onClick={onBack} className="text-gray-600 hover:text-gray-900">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Button>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" className="border-gray-300">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm" className="border-gray-300">
                <Share2 className="mr-2 h-4 w-4" />
                Share
              </Button>
              <Button variant="outline" onClick={() => onEdit(customer)} className="border-gray-300">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => {
                  if (window.confirm(`Are you sure you want to delete ${fullName}?`)) {
                    onDelete(customer.id);
                  }
                }}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>
          </div>
        </div>

        {/* Customer Profile Header */}
        <div className="p-8">
          <div className="flex items-start gap-6">
            {/* Avatar and Status */}
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full p-6">
                <User className="h-12 w-12 text-white" />
              </div>
              {/* Status Badge */}
              <div className="absolute -bottom-2 -right-2">
                <Badge className={`${
                  customerStatus.color === 'green' ? 'bg-green-100 text-green-800' :
                  customerStatus.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                  customerStatus.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                } border-0`}>
                  {customerStatus.status.charAt(0).toUpperCase() + customerStatus.status.slice(1)}
                </Badge>
              </div>
            </div>

            {/* Customer Info */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <h1 className="text-3xl font-bold text-gray-900">{fullName}</h1>
                {isVipCustomer && (
                  <Badge className="bg-amber-100 text-amber-800 border-0">
                    <Star className="h-4 w-4 mr-1" />
                    VIP Customer
                  </Badge>
                )}
                {customer.isSubscribedToNewsletter && (
                  <Badge className="bg-green-100 text-green-800 border-0">
                    <Mail className="h-4 w-4 mr-1" />
                    Subscribed
                  </Badge>
                )}
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center gap-2 text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span className="text-sm">{customer.email}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span className="text-sm">{customer.phone}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span className="text-sm">Joined {formatDate(customer.createdAt)}</span>
                </div>
                {customer.birthdate && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Gift className="h-4 w-4" />
                    <span className="text-sm">Birthday {formatDate(customer.birthdate)}</span>
                  </div>
                )}
                {lastOrderDate && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">Last order {formatDate(lastOrderDate)}</span>
                  </div>
                )}
                {customer.addresses && customer.addresses.length > 0 && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span className="text-sm">{customer.addresses[0].city}, {customer.addresses[0].state}</span>
                  </div>
                )}
              </div>

              {/* Quick Stats */}
              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <div className="bg-blue-100 rounded-full p-1">
                    <ShoppingBag className="h-3 w-3 text-blue-600" />
                  </div>
                  <span className="text-gray-600">{totalOrders} orders</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-green-100 rounded-full p-1">
                    <DollarSign className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-gray-600">{formatCurrency(customerLifetimeValue)} lifetime value</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-purple-100 rounded-full p-1">
                    <TrendingUp className="h-3 w-3 text-purple-600" />
                  </div>
                  <span className="text-gray-600">{formatCurrency(avgOrderValue)} avg order</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-col gap-2">
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <MessageSquare className="mr-2 h-4 w-4" />
                Message
              </Button>
              <Button variant="outline" size="sm" className="border-gray-300">
                <Eye className="mr-2 h-4 w-4" />
                View Orders
              </Button>
              <Button variant="outline" size="sm" className="border-gray-300">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-blue-100 rounded-full p-3">
              <ShoppingBag className="h-6 w-6 text-blue-600" />
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{customer.totalOrders}</p>
            </div>
          </div>
          <div className="flex items-center text-sm">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-green-600 font-medium">+12%</span>
            <span className="text-gray-500 ml-1">vs last month</span>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-green-100 rounded-full p-3">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Lifetime Value</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(customer.totalSpent)}</p>
            </div>
          </div>
          <div className="flex items-center text-sm">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-green-600 font-medium">+8%</span>
            <span className="text-gray-500 ml-1">vs last month</span>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-purple-100 rounded-full p-3">
              <BarChart3 className="h-6 w-6 text-purple-600" />
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Avg Order Value</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(avgOrderValue)}</p>
            </div>
          </div>
          <div className="flex items-center text-sm">
            <Target className="h-4 w-4 text-blue-500 mr-1" />
            <span className="text-blue-600 font-medium">Above avg</span>
            <span className="text-gray-500 ml-1">customer</span>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-orange-100 rounded-full p-3">
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Last Activity</p>
              <p className="text-lg font-bold text-gray-900">
                {lastOrderDate ? formatDate(lastOrderDate) : 'Never'}
              </p>
            </div>
          </div>
          <div className="flex items-center text-sm">
            <Activity className="h-4 w-4 text-orange-500 mr-1" />
            <span className="text-orange-600 font-medium">{customerStatus.status}</span>
            <span className="text-gray-500 ml-1">customer</span>
          </div>
        </div>
      </div>

      {/* Customer Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Score */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Customer Score</h3>
            <Award className="h-5 w-5 text-amber-500" />
          </div>
          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <div className="w-24 h-24 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-2xl font-bold text-white">
                  {Math.min(100, Math.round((totalSpent / 100) + (totalOrders * 5)))}
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              Based on purchase history and engagement
            </p>
          </div>
        </div>

        {/* Loyalty Status */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Loyalty Status</h3>
            <Heart className="h-5 w-5 text-red-500" />
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Tier</span>
              <span className="font-medium text-gray-900">
                {isVipCustomer ? 'VIP' : totalOrders > 10 ? 'Gold' : totalOrders > 5 ? 'Silver' : 'Bronze'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Points Balance</span>
              <span className="font-medium text-gray-900">{Math.round(totalSpent / 10)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Next Reward</span>
              <span className="font-medium text-blue-600">₹500 off</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <Zap className="h-5 w-5 text-yellow-500" />
          </div>
          <div className="space-y-3">
            <Button variant="outline" className="w-full justify-start" size="sm">
              <Mail className="mr-2 h-4 w-4" />
              Send Email
            </Button>
            <Button variant="outline" className="w-full justify-start" size="sm">
              <Gift className="mr-2 h-4 w-4" />
              Send Coupon
            </Button>
            <Button variant="outline" className="w-full justify-start" size="sm">
              <CreditCard className="mr-2 h-4 w-4" />
              Process Refund
            </Button>
            <Button variant="outline" className="w-full justify-start" size="sm">
              <Users className="mr-2 h-4 w-4" />
              View Similar
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Tab Navigation */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="orders" className="flex items-center gap-2">
              <ShoppingBag className="h-4 w-4" />
              Orders
              {orders.length > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {orders.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="addresses" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Addresses
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Activity
            </TabsTrigger>
          </TabsList>

          <div className="p-6">
            <TabsContent value="overview" className="space-y-6">
              {/* Tags Section */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Tags</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddTag(true)}
                    className="border-gray-300"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Tag
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {customer.tags && customer.tags.length > 0 ? (
                    customer.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="gap-1"
                      >
                        <Tag className="h-3 w-3" />
                        {tag}
                        <button
                          onClick={() => onRemoveTag(customer.id, tag)}
                          className="ml-1 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))
                  ) : (
                    <p className="text-gray-500 text-sm">No tags added yet.</p>
                  )}
                </div>

                {showAddTag && (
                  <div className="mt-4 flex gap-2">
                    <Input
                      type="text"
                      placeholder="Enter tag name"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      className="max-w-xs"
                      onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                    />
                    <Button onClick={handleAddTag} size="sm">Add</Button>
                    <Button variant="outline" onClick={() => setShowAddTag(false)} size="sm">
                      Cancel
                    </Button>
                  </div>
                )}
              </div>

              {/* Notes Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <Textarea
                      placeholder="Add a note about this customer..."
                      value={newNote}
                      onChange={(e) => setNewNote(e.target.value)}
                      className="flex-1"
                      rows={3}
                    />
                    <Button onClick={handleAddNote} className="self-start">
                      Add Note
                    </Button>
                  </div>

                  {customer.notes ? (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-gray-700">{customer.notes}</p>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No notes added yet.</p>
                  )}
                </div>
              </div>

              {/* Newsletter Subscription */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-600" />
                    <span className="text-sm text-gray-700">
                      Newsletter: {customer.isSubscribedToNewsletter ? 'Subscribed' : 'Not subscribed'}
                    </span>
                  </div>
                  {customer.birthdate && (
                    <div className="flex items-center gap-2 mt-2">
                      <Calendar className="h-4 w-4 text-gray-600" />
                      <span className="text-sm text-gray-700">
                        Birthday: {formatDate(customer.birthdate)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="orders">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order History</h3>
                {orders.length > 0 ? (
                  <div className="space-y-4">
                    {orders.map((order) => (
                      <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <p className="font-medium text-gray-900">Order #{order.id}</p>
                            <p className="text-sm text-gray-500">{formatDate(order.createdAt)}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">{formatCurrency(order.total)}</p>
                            <Badge className={`${
                              order.status === 'completed' ? 'bg-green-100 text-green-800' :
                              order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            } border-0`}>
                              {order.status}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-sm text-gray-600">
                          {order.items.length} item(s)
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No orders found for this customer.</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="addresses">
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Addresses</h3>
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Address
                  </Button>
                </div>
                {customer.addresses && customer.addresses.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {customer.addresses.map((address, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="bg-blue-100 rounded-full p-2">
                              <MapPin className="h-4 w-4 text-blue-600" />
                            </div>
                            <span className="font-medium text-gray-900 capitalize">{address.type} Address</span>
                          </div>
                          {address.isDefault && (
                            <Badge className="bg-green-100 text-green-800 border-0">
                              Default
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 space-y-1 mb-3">
                          <p className="font-medium text-gray-900">{address.street}</p>
                          <p>{address.city}, {address.state} {address.postalCode}</p>
                          <p>{address.country}</p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="mr-1 h-3 w-3" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="mr-1 h-3 w-3" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                      <MapPin className="h-8 w-8 text-gray-400" />
                    </div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">No addresses found</h4>
                    <p className="text-gray-500 mb-4">This customer hasn't added any addresses yet.</p>
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Address
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="analytics">
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Customer Analytics</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Export Report
                    </Button>
                  </div>
                </div>

                {/* Purchase Patterns */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Purchase Patterns</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Most Active Day</span>
                        <span className="font-medium text-gray-900">Saturday</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Preferred Time</span>
                        <span className="font-medium text-gray-900">2:00 PM - 4:00 PM</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Avg Days Between Orders</span>
                        <span className="font-medium text-gray-900">15 days</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Seasonal Preference</span>
                        <span className="font-medium text-gray-900">Winter</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Product Preferences</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Top Category</span>
                        <span className="font-medium text-gray-900">Flowers</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Favorite Product</span>
                        <span className="font-medium text-gray-900">Red Roses</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Price Range</span>
                        <span className="font-medium text-gray-900">₹500 - ₹2000</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Repeat Purchase Rate</span>
                        <span className="font-medium text-green-600">85%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Order Trends */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Order Trends (Last 6 Months)</h4>
                  <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">Chart visualization would go here</p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="activity">
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    Export Activity
                  </Button>
                </div>

                {/* Activity Timeline */}
                <div className="space-y-4">
                  {recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                      <div className="bg-white rounded-full p-2 shadow-sm">
                        <activity.icon className="h-4 w-4 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{activity.description}</p>
                        <p className="text-sm text-gray-500">{activity.date}</p>
                      </div>
                      <div className="text-xs text-gray-400">
                        {activity.type}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Communication History */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Communication History</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-900">Welcome Email Sent</p>
                          <p className="text-sm text-gray-600">2 weeks ago</p>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800 border-0">Opened</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <MessageSquare className="h-4 w-4 text-yellow-600" />
                        <div>
                          <p className="font-medium text-gray-900">Support Ticket</p>
                          <p className="text-sm text-gray-600">1 month ago</p>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800 border-0">Resolved</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Gift className="h-4 w-4 text-purple-600" />
                        <div>
                          <p className="font-medium text-gray-900">Birthday Coupon Sent</p>
                          <p className="text-sm text-gray-600">3 months ago</p>
                        </div>
                      </div>
                      <Badge className="bg-blue-100 text-blue-800 border-0">Used</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
