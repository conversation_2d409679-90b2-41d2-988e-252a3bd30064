'use client';

import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package,
  Calendar,
  Target,
  Award,
  Zap,
  BarChart3,
  PieChart,
  Activity,
  Star
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface AnalyticsData {
  revenue: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    percentage: number;
  };
  orders: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    percentage: number;
  };
  customers: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    percentage: number;
  };
  products: {
    current: number;
    lowStock: number;
    trending: string[];
  };
  performance: {
    efficiency: number;
    satisfaction: number;
    growth: number;
  };
}

export function AdvancedAnalytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    revenue: { current: 45250, previous: 38900, trend: 'up', percentage: 16.3 },
    orders: { current: 342, previous: 298, trend: 'up', percentage: 14.8 },
    customers: { current: 156, previous: 142, trend: 'up', percentage: 9.9 },
    products: { current: 89, lowStock: 7, trending: ['Chocolate Cake', 'Red Velvet', 'Croissants'] },
    performance: { efficiency: 94.5, satisfaction: 98.2, growth: 23.7 }
  });

  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp style={{ height: '1rem', width: '1rem', color: '#10b981' }} />;
      case 'down': return <TrendingDown style={{ height: '1rem', width: '1rem', color: '#ef4444' }} />;
      default: return <Activity style={{ height: '1rem', width: '1rem', color: '#6b7280' }} />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return '#10b981';
      case 'down': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (isLoading) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e5e7eb',
        padding: '2rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        textAlign: 'center'
      }}>
        <div style={{
          display: 'inline-block',
          width: '2rem',
          height: '2rem',
          border: '3px solid #f3f4f6',
          borderTop: '3px solid #6366f1',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <p style={{
          marginTop: '1rem',
          color: '#6b7280',
          fontSize: '0.875rem'
        }}>
          Loading advanced analytics...
        </p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '2rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: '#111827',
            marginBottom: '0.5rem'
          }}>
            📊 Advanced Analytics
          </h2>
          <p style={{
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            Comprehensive business insights and performance metrics
          </p>
        </div>
        
        <select
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value)}
          style={{
            padding: '0.5rem 1rem',
            borderRadius: '8px',
            border: '1px solid #e5e7eb',
            fontSize: '0.875rem',
            backgroundColor: 'white'
          }}
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>

      {/* Key Metrics Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        {/* Revenue Card */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '12px',
          padding: '1.5rem',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{
            position: 'absolute',
            top: '-20px',
            right: '-20px',
            width: '80px',
            height: '80px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%'
          }} />
          <div style={{ position: 'relative', zIndex: 2 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
              <DollarSign style={{ height: '1.25rem', width: '1.25rem' }} />
              <span style={{ fontSize: '0.875rem', opacity: 0.9 }}>Total Revenue</span>
            </div>
            <div style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '0.5rem' }}>
              {formatCurrency(analytics.revenue.current)}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              {getTrendIcon(analytics.revenue.trend)}
              <span style={{ fontSize: '0.875rem', opacity: 0.9 }}>
                +{analytics.revenue.percentage}% from last period
              </span>
            </div>
          </div>
        </div>

        {/* Orders Card */}
        <div style={{
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          borderRadius: '12px',
          padding: '1.5rem',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{
            position: 'absolute',
            top: '-20px',
            right: '-20px',
            width: '80px',
            height: '80px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%'
          }} />
          <div style={{ position: 'relative', zIndex: 2 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
              <ShoppingCart style={{ height: '1.25rem', width: '1.25rem' }} />
              <span style={{ fontSize: '0.875rem', opacity: 0.9 }}>Total Orders</span>
            </div>
            <div style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '0.5rem' }}>
              {analytics.orders.current.toLocaleString()}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              {getTrendIcon(analytics.orders.trend)}
              <span style={{ fontSize: '0.875rem', opacity: 0.9 }}>
                +{analytics.orders.percentage}% from last period
              </span>
            </div>
          </div>
        </div>

        {/* Customers Card */}
        <div style={{
          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          borderRadius: '12px',
          padding: '1.5rem',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{
            position: 'absolute',
            top: '-20px',
            right: '-20px',
            width: '80px',
            height: '80px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%'
          }} />
          <div style={{ position: 'relative', zIndex: 2 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
              <Users style={{ height: '1.25rem', width: '1.25rem' }} />
              <span style={{ fontSize: '0.875rem', opacity: 0.9 }}>Active Customers</span>
            </div>
            <div style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '0.5rem' }}>
              {analytics.customers.current.toLocaleString()}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              {getTrendIcon(analytics.customers.trend)}
              <span style={{ fontSize: '0.875rem', opacity: 0.9 }}>
                +{analytics.customers.percentage}% from last period
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Indicators */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          padding: '1rem',
          backgroundColor: '#f0fdf4',
          borderRadius: '8px',
          border: '1px solid #bbf7d0'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
            <Zap style={{ height: '1rem', width: '1rem', color: '#16a34a' }} />
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#16a34a' }}>
              Operational Efficiency
            </span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#16a34a' }}>
            {analytics.performance.efficiency}%
          </div>
        </div>

        <div style={{
          padding: '1rem',
          backgroundColor: '#fef3c7',
          borderRadius: '8px',
          border: '1px solid #fcd34d'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
            <Star style={{ height: '1rem', width: '1rem', color: '#d97706' }} />
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#d97706' }}>
              Customer Satisfaction
            </span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#d97706' }}>
            {analytics.performance.satisfaction}%
          </div>
        </div>

        <div style={{
          padding: '1rem',
          backgroundColor: '#ede9fe',
          borderRadius: '8px',
          border: '1px solid #c4b5fd'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
            <TrendingUp style={{ height: '1rem', width: '1rem', color: '#7c3aed' }} />
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#7c3aed' }}>
              Growth Rate
            </span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#7c3aed' }}>
            {analytics.performance.growth}%
          </div>
        </div>
      </div>

      {/* Trending Products */}
      <div style={{
        padding: '1.5rem',
        backgroundColor: '#f9fafb',
        borderRadius: '8px',
        border: '1px solid #e5e7eb'
      }}>
        <h3 style={{
          fontSize: '1.125rem',
          fontWeight: '600',
          color: '#111827',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          🔥 Trending Products
        </h3>
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '0.5rem'
        }}>
          {analytics.products.trending.map((product, index) => (
            <div
              key={index}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: 'white',
                borderRadius: '20px',
                border: '1px solid #e5e7eb',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}
            >
              {product}
            </div>
          ))}
        </div>
      </div>

      {/* Success Message */}
      <div style={{
        marginTop: '2rem',
        padding: '1rem',
        backgroundColor: '#f0f9ff',
        borderRadius: '8px',
        border: '1px solid #bae6fd',
        textAlign: 'center'
      }}>
        <Award style={{ 
          height: '2rem', 
          width: '2rem', 
          color: '#0284c7', 
          margin: '0 auto 0.5rem' 
        }} />
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#0284c7',
          marginBottom: '0.5rem'
        }}>
          🎉 Excellent Performance!
        </h4>
        <p style={{
          fontSize: '0.875rem',
          color: '#0369a1',
          margin: 0
        }}>
          Your bakery is performing exceptionally well across all key metrics. Keep up the great work!
        </p>
      </div>
    </div>
  );
}
