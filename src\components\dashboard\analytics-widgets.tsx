'use client';

import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// Enhanced Metric Card with Animations
interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon: React.ReactNode;
  color: string;
  description?: string;
}

export function MetricCard({ title, value, change, icon, color, description }: MetricCardProps) {
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      transform: isVisible ? 'translateY(0) scale(1)' : 'translateY(20px) scale(0.95)',
      opacity: isVisible ? 1 : 0,
      position: 'relative',
      overflow: 'hidden'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';
      e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.transform = 'translateY(0) scale(1)';
      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
    }}>
      {/* Background Gradient */}
      <div style={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: '100px',
        height: '100px',
        background: `linear-gradient(135deg, ${color}20, ${color}10)`,
        borderRadius: '50%',
        transform: 'translate(30px, -30px)'
      }} />
      
      {/* Content */}
      <div style={{ position: 'relative', zIndex: 2 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            borderRadius: '12px',
            backgroundColor: `${color}15`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div style={{ color: color }}>
              {icon}
            </div>
          </div>
          {change && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: change.type === 'increase' ? '#10b981' : change.type === 'decrease' ? '#ef4444' : '#6b7280'
            }}>
              {change.type === 'increase' ? (
                <TrendingUp style={{ height: '1rem', width: '1rem' }} />
              ) : change.type === 'decrease' ? (
                <TrendingDown style={{ height: '1rem', width: '1rem' }} />
              ) : null}
              {change.value}%
            </div>
          )}
        </div>
        
        <h3 style={{
          fontSize: '0.875rem',
          fontWeight: '500',
          color: '#6b7280',
          marginBottom: '0.5rem'
        }}>
          {title}
        </h3>
        
        <p style={{
          fontSize: '2rem',
          fontWeight: '700',
          color: '#111827',
          marginBottom: description ? '0.5rem' : 0
        }}>
          {value}
        </p>
        
        {description && (
          <p style={{
            fontSize: '0.75rem',
            color: '#9ca3af'
          }}>
            {description}
          </p>
        )}
      </div>
    </div>
  );
}

// Quick Actions Widget
interface QuickAction {
  label: string;
  icon: React.ReactNode;
  color: string;
  onClick: () => void;
}

interface QuickActionsProps {
  actions: QuickAction[];
}

export function QuickActions({ actions }: QuickActionsProps) {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{
        fontSize: '1.125rem',
        fontWeight: '600',
        color: '#111827',
        marginBottom: '1rem'
      }}>
        Quick Actions
      </h3>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
        gap: '1rem'
      }}>
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={action.onClick}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '1rem',
              borderRadius: '8px',
              border: '1px solid #e5e7eb',
              backgroundColor: 'white',
              cursor: 'pointer',
              transition: 'all 0.2s',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = `${action.color}10`;
              e.currentTarget.style.borderColor = action.color;
              e.currentTarget.style.transform = 'translateY(-2px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'white';
              e.currentTarget.style.borderColor = '#e5e7eb';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            <div style={{ color: action.color }}>
              {action.icon}
            </div>
            {action.label}
          </button>
        ))}
      </div>
    </div>
  );
}

// Recent Activity Widget
interface ActivityItem {
  id: string;
  type: 'order' | 'product' | 'user' | 'inventory';
  message: string;
  timestamp: string;
  status?: 'success' | 'warning' | 'error';
}

interface RecentActivityProps {
  activities: ActivityItem[];
}

export function RecentActivity({ activities }: RecentActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order': return <ShoppingCart style={{ height: '1rem', width: '1rem' }} />;
      case 'product': return <Package style={{ height: '1rem', width: '1rem' }} />;
      case 'user': return <Users style={{ height: '1rem', width: '1rem' }} />;
      case 'inventory': return <AlertTriangle style={{ height: '1rem', width: '1rem' }} />;
      default: return <Clock style={{ height: '1rem', width: '1rem' }} />;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'success': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{
        fontSize: '1.125rem',
        fontWeight: '600',
        color: '#111827',
        marginBottom: '1rem'
      }}>
        Recent Activity
      </h3>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
        {activities.length > 0 ? activities.map((activity) => (
          <div
            key={activity.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem',
              padding: '0.75rem',
              borderRadius: '8px',
              backgroundColor: '#f9fafb',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#f9fafb';
            }}
          >
            <div style={{
              width: '2rem',
              height: '2rem',
              borderRadius: '50%',
              backgroundColor: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: getStatusColor(activity.status)
            }}>
              {getActivityIcon(activity.type)}
            </div>
            
            <div style={{ flex: 1 }}>
              <p style={{
                fontSize: '0.875rem',
                color: '#111827',
                marginBottom: '0.25rem'
              }}>
                {activity.message}
              </p>
              <p style={{
                fontSize: '0.75rem',
                color: '#6b7280'
              }}>
                {activity.timestamp}
              </p>
            </div>
          </div>
        )) : (
          <div style={{
            textAlign: 'center',
            padding: '2rem',
            color: '#6b7280'
          }}>
            <Clock style={{ height: '2rem', width: '2rem', margin: '0 auto 0.5rem' }} />
            <p>No recent activity</p>
          </div>
        )}
      </div>
    </div>
  );
}

// Performance Summary Widget
interface PerformanceMetric {
  label: string;
  value: number;
  target: number;
  color: string;
}

interface PerformanceSummaryProps {
  metrics: PerformanceMetric[];
}

export function PerformanceSummary({ metrics }: PerformanceSummaryProps) {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{
        fontSize: '1.125rem',
        fontWeight: '600',
        color: '#111827',
        marginBottom: '1rem'
      }}>
        Performance Summary
      </h3>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
        {metrics.map((metric, index) => {
          const percentage = Math.min((metric.value / metric.target) * 100, 100);
          
          return (
            <div key={index}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '0.5rem'
              }}>
                <span style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  {metric.label}
                </span>
                <span style={{
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: '#111827'
                }}>
                  {metric.value} / {metric.target}
                </span>
              </div>
              
              <div style={{
                width: '100%',
                height: '8px',
                backgroundColor: '#f3f4f6',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div
                  style={{
                    width: `${percentage}%`,
                    height: '100%',
                    backgroundColor: metric.color,
                    borderRadius: '4px',
                    transition: 'width 1s ease-out'
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
