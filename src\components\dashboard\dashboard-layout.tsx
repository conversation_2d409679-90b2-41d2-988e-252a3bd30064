'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Store,
  Warehouse,
  Package,
  TruckIcon,
  ShoppingCart,
  CreditCard,
  DollarSign,
  BarChart2,
  Users,
  Settings,
  Menu,
  X,
  LogOut,
  Utensils,
  Wheat,
  Trash2,
  UserRound,
  BookOpen,
  Building,
  ReceiptText,
  Tag,
  ShoppingBag,
  Shield,
  Edit3
} from 'lucide-react';

import { useAuth } from '@/lib/auth/auth-context';
import { usePermission } from '@/lib/permissions/hooks';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/useIsClient';

interface SidebarItemProps {
  icon: React.ReactNode;
  title: string;
  href: string;
  active?: boolean;
  permission?: string;
}

const SidebarItem = ({ icon, title, href, active, permission }: SidebarItemProps) => {
  const hasPermission = usePermission(permission as any);

  // If permission is specified and user doesn't have it, don't render
  if (permission && !hasPermission) {
    return null;
  }

  const itemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    borderRadius: '6px',
    padding: '0.5rem 0.75rem',
    fontSize: '0.875rem',
    textDecoration: 'none',
    transition: 'all 0.2s',
    backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
    color: active ? 'white' : 'rgba(255, 255, 255, 0.8)',
  };

  return (
    <Link
      href={href}
      style={itemStyle}
      onMouseEnter={(e) => {
        if (!active) {
          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
          e.currentTarget.style.color = 'white';
        }
      }}
      onMouseLeave={(e) => {
        if (!active) {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
        }
      }}
    >
      {icon}
      <span>{title}</span>
    </Link>
  );
};

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    minHeight: '100vh',
    flexDirection: 'column',
  };

  const headerStyle: React.CSSProperties = {
    position: 'sticky',
    top: 0,
    zIndex: 30,
    display: 'flex',
    height: '4rem',
    alignItems: 'center',
    gap: '1rem',
    borderBottom: '1px solid #4a8a8d',
    backgroundColor: '#5F9EA0',
    padding: '0 1rem',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  };

  // Use custom hook to prevent hydration mismatch
  const isMobile = useIsMobile();

  const sidebarStyle: React.CSSProperties = {
    position: isMobile ? 'fixed' : 'fixed',
    top: '4rem', // Account for header height
    left: 0,
    bottom: 0,
    zIndex: 20,
    display: 'flex',
    width: '16rem',
    flexDirection: 'column',
    borderRight: '1px solid #4a8a8d',
    backgroundColor: '#5F9EA0',
    transform: isMobile ? (sidebarOpen ? 'translateX(0)' : 'translateX(-100%)') : 'translateX(0)',
    transition: 'transform 0.3s ease',
  };

  const mainContentStyle: React.CSSProperties = {
    display: 'flex',
    flex: 1,
    marginTop: '4rem', // Account for header height
  };

  return (
    <div style={containerStyle}>
      {/* Header */}
      <header style={headerStyle}>
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            style={{ color: 'white' }}
          >
            <Menu style={{ height: '1.25rem', width: '1.25rem' }} />
          </Button>
        )}
        <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
          <img src="/LOGO.png" alt="Mispri Logo" style={{ height: '2.5rem', width: 'auto', objectFit: 'contain' }} />
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {!isMobile && (
            <div style={{ fontSize: '0.875rem', color: 'white', opacity: 0.9 }}>
              {user?.name} ({user?.role})
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={logout}
            style={{ color: 'white' }}
          >
            <LogOut style={{ height: '1.25rem', width: '1.25rem' }} />
          </Button>
        </div>
      </header>

      <div style={mainContentStyle}>
        {/* Sidebar */}
        <aside style={sidebarStyle}>
          {/* Mobile close button */}
          {isMobile && (
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              padding: '1rem',
              borderBottom: '1px solid #4a8a8d',
            }}>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                style={{ color: 'white' }}
              >
                <X style={{ height: '1.25rem', width: '1.25rem' }} />
              </Button>
            </div>
          )}

          <nav style={{ flex: 1, overflow: 'auto', padding: '1rem 0' }}>
            <div style={{ padding: '0 1rem' }}>
              <h2 style={{
                marginBottom: '0.5rem',
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em'
              }}>
                Dashboard
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                <SidebarItem
                  icon={<LayoutDashboard style={{ height: '1rem', width: '1rem' }} />}
                  title="Overview"
                  href="/dashboard"
                  active={pathname === '/dashboard'}
                  permission="dashboard.view"
                />
                <SidebarItem
                  icon={<Warehouse style={{ height: '1rem', width: '1rem' }} />}
                  title="Warehouses"
                  href="/dashboard/warehouses"
                  active={pathname.startsWith('/dashboard/warehouses')}
                  permission="stores.view"
                />
                <SidebarItem
                  icon={<Store style={{ height: '1rem', width: '1rem' }} />}
                  title="Stores"
                  href="/dashboard/stores"
                  active={pathname.startsWith('/dashboard/stores')}
                  permission="stores.view"
                />
                <SidebarItem
                  icon={<Package style={{ height: '1rem', width: '1rem' }} />}
                  title="Products"
                  href="/dashboard/products"
                  active={pathname.startsWith('/dashboard/products')}
                  permission="products.view"
                />
                <SidebarItem
                  icon={<Tag style={{ height: '1rem', width: '1rem' }} />}
                  title="Categories"
                  href="/dashboard/categories"
                  active={pathname.startsWith('/dashboard/categories')}
                  permission="categories.view"
                />
                <SidebarItem
                  icon={<Package style={{ height: '1rem', width: '1rem' }} />}
                  title="Inventory"
                  href="/dashboard/inventory"
                  active={pathname.startsWith('/dashboard/inventory')}
                  permission="inventory.view"
                />
                <SidebarItem
                  icon={<Edit3 style={{ height: '1rem', width: '1rem' }} />}
                  title="Stock Management"
                  href="/dashboard/stock-management"
                  active={pathname.startsWith('/dashboard/stock-management')}
                  permission="inventory.manage"
                />
                <SidebarItem
                  icon={<TruckIcon style={{ height: '1rem', width: '1rem' }} />}
                  title="Transfers"
                  href="/dashboard/transfers"
                  active={pathname.startsWith('/dashboard/transfers')}
                  permission="inventory.transfer"
                />
                {/* Commented out for now - Raw Materials, Production, and Recipes */}
                {/*
                <SidebarItem
                  icon={<Wheat style={{ height: '1rem', width: '1rem' }} />}
                  title="Raw Materials"
                  href="/dashboard/raw-materials"
                  active={pathname.startsWith('/dashboard/raw-materials')}
                  permission="raw_materials.view"
                />
                <SidebarItem
                  icon={<Utensils style={{ height: '1rem', width: '1rem' }} />}
                  title="Production"
                  href="/dashboard/production"
                  active={pathname.startsWith('/dashboard/production')}
                  permission="production.view"
                />
                <SidebarItem
                  icon={<BookOpen style={{ height: '1rem', width: '1rem' }} />}
                  title="Recipes"
                  href="/dashboard/recipes"
                  active={pathname.startsWith('/dashboard/recipes')}
                  permission="recipes.view"
                />
                */}
              </div>

              <h2 style={{
                marginTop: '1.5rem',
                marginBottom: '0.5rem',
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em'
              }}>
                Transactions
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                <SidebarItem
                  icon={<ShoppingCart style={{ height: '1rem', width: '1rem' }} />}
                  title="Sales"
                  href="/dashboard/sales"
                  active={pathname.startsWith('/dashboard/sales')}
                  permission="sales.view"
                />
                <SidebarItem
                  icon={<ShoppingBag style={{ height: '1rem', width: '1rem' }} />}
                  title="Orders"
                  href="/dashboard/orders"
                  active={pathname.startsWith('/dashboard/orders')}
                  permission="orders.view"
                />
                <SidebarItem
                  icon={<ReceiptText style={{ height: '1rem', width: '1rem' }} />}
                  title="Point of Sale"
                  href="/dashboard/pos"
                  active={pathname.startsWith('/dashboard/pos')}
                  permission="sales.process"
                />
                <SidebarItem
                  icon={<CreditCard style={{ height: '1rem', width: '1rem' }} />}
                  title="Purchases"
                  href="/dashboard/purchases"
                  active={pathname.startsWith('/dashboard/purchases')}
                  permission="purchases.view"
                />
                <SidebarItem
                  icon={<DollarSign style={{ height: '1rem', width: '1rem' }} />}
                  title="Expenses"
                  href="/dashboard/expenses"
                  active={pathname.startsWith('/dashboard/expenses')}
                  permission="expenses.view"
                />
                <SidebarItem
                  icon={<Trash2 style={{ height: '1rem', width: '1rem' }} />}
                  title="Wastage"
                  href="/dashboard/wastage"
                  active={pathname.startsWith('/dashboard/wastage')}
                  permission="wastage.view"
                />
              </div>

              <h2 style={{
                marginTop: '1.5rem',
                marginBottom: '0.5rem',
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em'
              }}>
                Reports
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                <SidebarItem
                  icon={<BarChart2 style={{ height: '1rem', width: '1rem' }} />}
                  title="Reports & Analytics"
                  href="/dashboard/reports"
                  active={pathname.startsWith('/dashboard/reports')}
                  permission="reports.view"
                />
              </div>

              <h2 style={{
                marginTop: '1.5rem',
                marginBottom: '0.5rem',
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em'
              }}>
                Management
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                <SidebarItem
                  icon={<UserRound style={{ height: '1rem', width: '1rem' }} />}
                  title="Customers"
                  href="/dashboard/customers"
                  active={pathname.startsWith('/dashboard/customers')}
                  permission="customers.view"
                />
                <SidebarItem
                  icon={<Building style={{ height: '1rem', width: '1rem' }} />}
                  title="Suppliers"
                  href="/dashboard/suppliers"
                  active={pathname.startsWith('/dashboard/suppliers')}
                  permission="purchases.view"
                />
                <SidebarItem
                  icon={<Users style={{ height: '1rem', width: '1rem' }} />}
                  title="Users"
                  href="/dashboard/users"
                  active={pathname.startsWith('/dashboard/users')}
                  permission="users.view"
                />
                <SidebarItem
                  icon={<Shield style={{ height: '1rem', width: '1rem' }} />}
                  title="Permissions"
                  href="/dashboard/permissions"
                  active={pathname.startsWith('/dashboard/permissions')}
                  permission="users.manage_roles"
                />
                <SidebarItem
                  icon={<BookOpen style={{ height: '1rem', width: '1rem' }} />}
                  title="Pages"
                  href="/dashboard/pages"
                  active={pathname.startsWith('/dashboard/pages')}
                  permission="settings.view"
                />
                <SidebarItem
                  icon={<Settings style={{ height: '1rem', width: '1rem' }} />}
                  title="Settings"
                  href="/dashboard/settings"
                  active={pathname.startsWith('/dashboard/settings')}
                  permission="settings.view"
                />
              </div>
            </div>
          </nav>
        </aside>

        {/* Main content */}
        <main style={{
          flex: 1,
          overflow: 'auto',
          padding: '1.5rem',
          marginLeft: isMobile ? '0' : '16rem',
          minHeight: 'calc(100vh - 4rem)', // Account for header height
        }}>
          {children}
        </main>
      </div>

      {/* Mobile overlay */}
      {isMobile && sidebarOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 15,
          }}
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
}
