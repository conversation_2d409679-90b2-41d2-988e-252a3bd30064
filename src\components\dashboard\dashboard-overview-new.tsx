'use client';

import { TrendingUp, TrendingDown, Package, ShoppingCart, Users, DollarSign, AlertTriangle, CheckCircle2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface DashboardStats {
  totalProducts: number;
  activeProducts: number;
  totalOrders: number;
  pendingOrders: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalCustomers: number;
  newCustomers: number;
  lowStockProducts: number;
  outOfStockProducts: number;
}

interface RecentOrder {
  id: string;
  customerName: string;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered';
  createdAt: string;
}

interface TopProduct {
  id: string;
  name: string;
  sales: number;
  revenue: number;
}

interface DashboardOverviewProps {
  stats: DashboardStats;
  recentOrders: RecentOrder[];
  topProducts: TopProduct[];
}

export function DashboardOverview({ stats, recentOrders, topProducts }: DashboardOverviewProps) {
  const statCards = [
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      change: '+12.5%',
      trend: 'up' as const,
      icon: DollarSign,
      color: '#10b981',
      bgColor: '#dcfce7'
    },
    {
      title: 'Total Orders',
      value: stats.totalOrders.toLocaleString(),
      change: '+8.2%',
      trend: 'up' as const,
      icon: ShoppingCart,
      color: '#3b82f6',
      bgColor: '#dbeafe'
    },
    {
      title: 'Total Products',
      value: stats.totalProducts.toLocaleString(),
      change: '+3.1%',
      trend: 'up' as const,
      icon: Package,
      color: '#8b5cf6',
      bgColor: '#ede9fe'
    },
    {
      title: 'Total Customers',
      value: stats.totalCustomers.toLocaleString(),
      change: '+15.3%',
      trend: 'up' as const,
      icon: Users,
      color: '#f59e0b',
      bgColor: '#fef3c7'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f59e0b';
      case 'processing': return '#3b82f6';
      case 'shipped': return '#8b5cf6';
      case 'delivered': return '#10b981';
      default: return '#64748b';
    }
  };

  const getStatusBgColor = (status: string) => {
    switch (status) {
      case 'pending': return '#fef3c7';
      case 'processing': return '#dbeafe';
      case 'shipped': return '#ede9fe';
      case 'delivered': return '#dcfce7';
      default: return '#f1f5f9';
    }
  };

  return (
    <div className="dashboard-bg" style={{ minHeight: '100vh' }}>
      {/* Header */}
      <div className="dashboard-header" style={{ padding: '2rem' }}>
        <div>
          <h1 className="dashboard-text-primary" style={{ fontSize: '2.25rem', fontWeight: '800', marginBottom: '0.5rem' }}>
            Dashboard
          </h1>
          <p className="dashboard-text-secondary" style={{ fontSize: '1.125rem' }}>
            Welcome back! Here's what's happening with your business today.
          </p>
        </div>
      </div>

      <div style={{ padding: '2rem' }}>
        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          {statCards.map((card, index) => (
            <div
              key={index}
              className="dashboard-card"
              style={{
                borderRadius: '12px',
                padding: '1.5rem',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
                <div style={{
                  backgroundColor: card.bgColor,
                  borderRadius: '10px',
                  padding: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <card.icon style={{ height: '1.5rem', width: '1.5rem', color: card.color }} />
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: card.trend === 'up' ? '#10b981' : '#ef4444'
                }}>
                  {card.trend === 'up' ? (
                    <TrendingUp style={{ height: '1rem', width: '1rem' }} />
                  ) : (
                    <TrendingDown style={{ height: '1rem', width: '1rem' }} />
                  )}
                  {card.change}
                </div>
              </div>
              <div>
                <h3 className="dashboard-text-primary" style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '0.25rem' }}>
                  {card.value}
                </h3>
                <p className="dashboard-text-secondary" style={{ fontSize: '0.875rem' }}>
                  {card.title}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Content Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '2rem'
        }}>
          {/* Recent Orders */}
          <div className="dashboard-card" style={{
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            <div className="dashboard-hover" style={{
              padding: '1.5rem',
              borderBottom: '1px solid #e2e8f0'
            }}>
              <h3 className="dashboard-text-primary" style={{ fontSize: '1.125rem', fontWeight: '600' }}>
                Recent Orders
              </h3>
              <p className="dashboard-text-secondary" style={{ fontSize: '0.875rem', marginTop: '0.25rem' }}>
                Latest customer orders
              </p>
            </div>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {recentOrders.map((order, index) => (
                <div
                  key={order.id}
                  style={{
                    padding: '1rem 1.5rem',
                    borderBottom: index < recentOrders.length - 1 ? '1px solid #f1f5f9' : 'none',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                    <h4 className="dashboard-text-primary" style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                      {order.customerName}
                    </h4>
                    <span className="dashboard-text-primary" style={{ fontSize: '0.875rem', fontWeight: '600' }}>
                      {formatCurrency(order.total)}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: getStatusColor(order.status),
                      backgroundColor: getStatusBgColor(order.status),
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      textTransform: 'capitalize'
                    }}>
                      {order.status}
                    </span>
                    <span className="dashboard-text-secondary" style={{ fontSize: '0.75rem' }}>
                      {new Date(order.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Products */}
          <div className="dashboard-card" style={{
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            <div className="dashboard-hover" style={{
              padding: '1.5rem',
              borderBottom: '1px solid #e2e8f0'
            }}>
              <h3 className="dashboard-text-primary" style={{ fontSize: '1.125rem', fontWeight: '600' }}>
                Top Products
              </h3>
              <p className="dashboard-text-secondary" style={{ fontSize: '0.875rem', marginTop: '0.25rem' }}>
                Best performing products
              </p>
            </div>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {topProducts.map((product, index) => (
                <div
                  key={product.id}
                  style={{
                    padding: '1rem 1.5rem',
                    borderBottom: index < topProducts.length - 1 ? '1px solid #f1f5f9' : 'none',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8fafc';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <div style={{
                      backgroundColor: '#ede9fe',
                      borderRadius: '8px',
                      padding: '0.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <Package style={{ height: '1.25rem', width: '1.25rem', color: '#8b5cf6' }} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <h4 className="dashboard-text-primary" style={{ fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.25rem' }}>
                        {product.name}
                      </h4>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span className="dashboard-text-secondary" style={{ fontSize: '0.75rem' }}>
                          {product.sales} sales
                        </span>
                        <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#10b981' }}>
                          {formatCurrency(product.revenue)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem',
          marginTop: '2rem'
        }}>
          {/* Low Stock Alert */}
          {stats.lowStockProducts > 0 && (
            <div className="dashboard-card" style={{
              borderRadius: '12px',
              border: '1px solid #fbbf24',
              padding: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.75rem' }}>
                <AlertTriangle style={{ height: '1.25rem', width: '1.25rem', color: '#f59e0b' }} />
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#92400e' }}>
                  Low Stock Alert
                </h3>
              </div>
              <p className="dashboard-text-secondary" style={{ fontSize: '0.875rem' }}>
                {stats.lowStockProducts} products are running low on stock
              </p>
            </div>
          )}

          {/* Out of Stock Alert */}
          {stats.outOfStockProducts > 0 && (
            <div className="dashboard-card" style={{
              borderRadius: '12px',
              border: '1px solid #f87171',
              padding: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.75rem' }}>
                <AlertTriangle style={{ height: '1.25rem', width: '1.25rem', color: '#ef4444' }} />
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#991b1b' }}>
                  Out of Stock
                </h3>
              </div>
              <p className="dashboard-text-secondary" style={{ fontSize: '0.875rem' }}>
                {stats.outOfStockProducts} products are out of stock
              </p>
            </div>
          )}

          {/* All Good */}
          {stats.lowStockProducts === 0 && stats.outOfStockProducts === 0 && (
            <div className="dashboard-card" style={{
              borderRadius: '12px',
              border: '1px solid #34d399',
              padding: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.75rem' }}>
                <CheckCircle2 style={{ height: '1.25rem', width: '1.25rem', color: '#10b981' }} />
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#065f46' }}>
                  All Systems Good
                </h3>
              </div>
              <p className="dashboard-text-secondary" style={{ fontSize: '0.875rem' }}>
                All products are well stocked
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
