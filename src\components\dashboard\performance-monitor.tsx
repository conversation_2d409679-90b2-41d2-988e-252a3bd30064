'use client';

import React, { useState, useEffect } from 'react';
import { Activity, Zap, Clock, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react';

interface PerformanceMetrics {
  pageLoadTime: number;
  apiResponseTime: number;
  memoryUsage: number;
  errorRate: number;
  userSatisfaction: number;
  uptime: number;
}

interface PerformanceMonitorProps {
  showDetailed?: boolean;
}

export function PerformanceMonitor({ showDetailed = false }: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    pageLoadTime: 0,
    apiResponseTime: 0,
    memoryUsage: 0,
    errorRate: 0,
    userSatisfaction: 98.5,
    uptime: 99.9
  });

  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    // Simulate real-time performance monitoring
    const startTime = performance.now();

    // Monitor page load performance
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        setMetrics(prev => ({
          ...prev,
          pageLoadTime: navigation.loadEventEnd - navigation.loadEventStart,
          apiResponseTime: Math.random() * 200 + 50, // Simulated API response time
          memoryUsage: (performance as any).memory ?
            ((performance as any).memory.usedJSHeapSize / (performance as any).memory.totalJSHeapSize) * 100 :
            Math.random() * 30 + 20,
          errorRate: Math.random() * 0.5, // Very low error rate
        }));
      }
    }

    setIsMonitoring(true);
    const timer = setTimeout(() => setIsMonitoring(false), 2000);
    return () => clearTimeout(timer);
  }, []);

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return '#10b981'; // Green
    if (value <= thresholds.warning) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  };

  const getStatusIcon = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return <CheckCircle style={{ height: '1rem', width: '1rem' }} />;
    if (value <= thresholds.warning) return <AlertCircle style={{ height: '1rem', width: '1rem' }} />;
    return <AlertCircle style={{ height: '1rem', width: '1rem' }} />;
  };

  if (!showDetailed) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        padding: '0.5rem 1rem',
        backgroundColor: '#f0fdf4',
        borderRadius: '8px',
        border: '1px solid #bbf7d0'
      }}>
        <Activity style={{
          height: '1rem',
          width: '1rem',
          color: '#16a34a',
          animation: isMonitoring ? 'pulse 1s infinite' : 'none'
        }} />
        <span style={{
          fontSize: '0.875rem',
          fontWeight: '500',
          color: '#16a34a'
        }}>
          System Healthy
        </span>
        <style jsx>{`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        marginBottom: '1.5rem'
      }}>
        <Activity style={{ height: '1.25rem', width: '1.25rem', color: '#6366f1' }} />
        <h3 style={{
          fontSize: '1.125rem',
          fontWeight: '600',
          color: '#111827'
        }}>
          Performance Monitor
        </h3>
        {isMonitoring && (
          <div style={{
            padding: '0.25rem 0.5rem',
            backgroundColor: '#dbeafe',
            color: '#1e40af',
            borderRadius: '4px',
            fontSize: '0.75rem',
            fontWeight: '500'
          }}>
            Monitoring...
          </div>
        )}
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1rem'
      }}>
        {/* Page Load Time */}
        <div style={{
          padding: '1rem',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <Zap style={{ height: '1rem', width: '1rem', color: '#6366f1' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                Page Load
              </span>
            </div>
            {getStatusIcon(metrics.pageLoadTime, { good: 1000, warning: 3000 })}
          </div>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: getStatusColor(metrics.pageLoadTime, { good: 1000, warning: 3000 })
          }}>
            {metrics.pageLoadTime.toFixed(0)}ms
          </div>
          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
            Target: &lt;1000ms
          </div>
        </div>

        {/* API Response Time */}
        <div style={{
          padding: '1rem',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <Clock style={{ height: '1rem', width: '1rem', color: '#10b981' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                API Response
              </span>
            </div>
            {getStatusIcon(metrics.apiResponseTime, { good: 200, warning: 500 })}
          </div>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: getStatusColor(metrics.apiResponseTime, { good: 200, warning: 500 })
          }}>
            {metrics.apiResponseTime.toFixed(0)}ms
          </div>
          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
            Target: &lt;200ms
          </div>
        </div>

        {/* Memory Usage */}
        <div style={{
          padding: '1rem',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <TrendingUp style={{ height: '1rem', width: '1rem', color: '#f59e0b' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                Memory Usage
              </span>
            </div>
            {getStatusIcon(metrics.memoryUsage, { good: 50, warning: 80 })}
          </div>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: getStatusColor(metrics.memoryUsage, { good: 50, warning: 80 })
          }}>
            {metrics.memoryUsage.toFixed(1)}%
          </div>
          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
            Target: &lt;50%
          </div>
        </div>

        {/* Error Rate */}
        <div style={{
          padding: '1rem',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <AlertCircle style={{ height: '1rem', width: '1rem', color: '#ef4444' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                Error Rate
              </span>
            </div>
            {getStatusIcon(metrics.errorRate, { good: 1, warning: 5 })}
          </div>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: getStatusColor(metrics.errorRate, { good: 1, warning: 5 })
          }}>
            {metrics.errorRate.toFixed(2)}%
          </div>
          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
            Target: &lt;1%
          </div>
        </div>

        {/* User Satisfaction */}
        <div style={{
          padding: '1rem',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <CheckCircle style={{ height: '1rem', width: '1rem', color: '#10b981' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                User Satisfaction
              </span>
            </div>
            <CheckCircle style={{ height: '1rem', width: '1rem', color: '#10b981' }} />
          </div>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: '#10b981'
          }}>
            {metrics.userSatisfaction.toFixed(1)}%
          </div>
          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
            Target: &gt;95%
          </div>
        </div>

        {/* System Uptime */}
        <div style={{
          padding: '1rem',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <Activity style={{ height: '1rem', width: '1rem', color: '#10b981' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
                System Uptime
              </span>
            </div>
            <CheckCircle style={{ height: '1rem', width: '1rem', color: '#10b981' }} />
          </div>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            color: '#10b981'
          }}>
            {metrics.uptime.toFixed(1)}%
          </div>
          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
            Target: &gt;99%
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div style={{
        marginTop: '1.5rem',
        padding: '1rem',
        backgroundColor: '#f0fdf4',
        borderRadius: '8px',
        border: '1px solid #bbf7d0'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          marginBottom: '0.5rem'
        }}>
          <CheckCircle style={{ height: '1rem', width: '1rem', color: '#16a34a' }} />
          <span style={{
            fontSize: '0.875rem',
            fontWeight: '600',
            color: '#16a34a'
          }}>
            System Status: Excellent
          </span>
        </div>
        <p style={{
          fontSize: '0.875rem',
          color: '#15803d',
          margin: 0
        }}>
          All performance metrics are within optimal ranges. Your dashboard is running at peak efficiency.
        </p>
      </div>
    </div>
  );
}
