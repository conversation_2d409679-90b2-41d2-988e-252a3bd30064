'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Store,
  Package,
  ShoppingCart,
  BarChart2,
  Users,
  Settings,
  Menu,
  X,
  LogOut,
} from 'lucide-react';

import { useAuth } from '@/lib/auth/auth-context';
import { usePermission } from '@/lib/permissions/hooks';
import { Button } from '@/components/ui/button';

interface SidebarItemProps {
  icon: React.ReactNode;
  title: string;
  href: string;
  active?: boolean;
  permission?: string;
}

const SidebarItem = ({ icon, title, href, active, permission }: SidebarItemProps) => {
  const hasPermission = usePermission(permission as any);

  // If permission is specified and user doesn't have it, don't render
  if (permission && !hasPermission) {
    return null;
  }
  const itemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    borderRadius: '6px',
    padding: '0.75rem',
    fontSize: '0.875rem',
    textDecoration: 'none',
    transition: 'all 0.2s',
    backgroundColor: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
    color: active ? 'white' : 'rgba(255, 255, 255, 0.8)',
    marginBottom: '0.25rem',
  };

  return (
    <Link
      href={href}
      style={itemStyle}
      onMouseEnter={(e) => {
        if (!active) {
          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
          e.currentTarget.style.color = 'white';
        }
      }}
      onMouseLeave={(e) => {
        if (!active) {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
        }
      }}
    >
      {icon}
      <span>{title}</span>
    </Link>
  );
};

export function SimpleDashboardLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* Sidebar */}
      <aside style={{
        width: sidebarOpen ? '16rem' : '0',
        position: 'fixed',
        top: 0,
        left: 0,
        height: '100vh',
        backgroundColor: '#5F9EA0',
        borderRight: '1px solid #4a8a8d',
        transition: 'width 0.3s ease',
        overflow: 'hidden',
        zIndex: 1000,
      }}>
        <div style={{
          width: '16rem',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}>
          {/* Sidebar Header */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '1rem',
            borderBottom: '1px solid #4a8a8d',
          }}>
            <Link href="/dashboard" style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              fontWeight: '600',
              textDecoration: 'none',
              color: 'white',
            }}>
              <img src="/LOGO.png" alt="Mispri Logo" style={{ height: '2rem', width: 'auto', objectFit: 'contain' }} />
            </Link>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              style={{ color: 'white' }}
            >
              <X style={{ height: '1.25rem', width: '1.25rem' }} />
            </Button>
          </div>

          {/* Navigation */}
          <nav style={{ flex: 1, padding: '1rem', overflow: 'auto' }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
                marginBottom: '0.75rem',
              }}>
                Main
              </h3>
              <SidebarItem
                icon={<LayoutDashboard style={{ height: '1rem', width: '1rem' }} />}
                title="Dashboard"
                href="/dashboard"
                active={pathname === '/dashboard'}
                permission="dashboard.view"
              />
              <SidebarItem
                icon={<Package style={{ height: '1rem', width: '1rem' }} />}
                title="Products"
                href="/dashboard/products"
                active={pathname.startsWith('/dashboard/products')}
                permission="products.view"
              />
              <SidebarItem
                icon={<Store style={{ height: '1rem', width: '1rem' }} />}
                title="Stores"
                href="/dashboard/stores"
                active={pathname.startsWith('/dashboard/stores')}
                permission="stores.view"
              />
              <SidebarItem
                icon={<ShoppingCart style={{ height: '1rem', width: '1rem' }} />}
                title="Orders"
                href="/dashboard/orders"
                active={pathname.startsWith('/dashboard/orders')}
                permission="orders.view"
              />
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
                marginBottom: '0.75rem',
              }}>
                Analytics
              </h3>
              <SidebarItem
                icon={<BarChart2 style={{ height: '1rem', width: '1rem' }} />}
                title="Reports"
                href="/dashboard/reports"
                active={pathname.startsWith('/dashboard/reports')}
                permission="reports.view"
              />
            </div>

            <div>
              <h3 style={{
                fontSize: '0.75rem',
                fontWeight: '600',
                color: 'white',
                opacity: 0.8,
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
                marginBottom: '0.75rem',
              }}>
                Management
              </h3>
              <SidebarItem
                icon={<Users style={{ height: '1rem', width: '1rem' }} />}
                title="Users"
                href="/dashboard/users"
                active={pathname.startsWith('/dashboard/users')}
                permission="users.view"
              />
              <SidebarItem
                icon={<BookOpen style={{ height: '1rem', width: '1rem' }} />}
                title="Pages"
                href="/dashboard/pages"
                active={pathname.startsWith('/dashboard/pages')}
                permission="settings.view"
              />
              <SidebarItem
                icon={<Settings style={{ height: '1rem', width: '1rem' }} />}
                title="Settings"
                href="/dashboard/settings"
                active={pathname.startsWith('/dashboard/settings')}
                permission="settings.view"
              />
            </div>
          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        marginLeft: sidebarOpen ? '16rem' : '0',
        transition: 'margin-left 0.3s ease',
      }}>
        {/* Header */}
        <header style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '1rem 1.5rem',
          borderBottom: '1px solid #4a8a8d',
          backgroundColor: '#5F9EA0',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              style={{ color: 'white' }}
            >
              <Menu style={{ height: '1.25rem', width: '1.25rem' }} />
            </Button>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <img src="/LOGO.png" alt="Mispri Logo" style={{ height: '2.5rem', width: 'auto', objectFit: 'contain' }} />
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{ fontSize: '0.875rem', color: 'white', opacity: 0.9 }}>
              Welcome, {user?.name}
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={logout}
              style={{ color: 'white' }}
            >
              <LogOut style={{ height: '1.25rem', width: '1.25rem' }} />
            </Button>
          </div>
        </header>

        {/* Page Content */}
        <main style={{
          flex: 1,
          padding: '1.5rem',
          overflow: 'auto',
          backgroundColor: '#f9fafb',
        }}>
          {children}
        </main>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 999,
          }}
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
}
