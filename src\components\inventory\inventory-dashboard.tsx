'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertTriangle,
  ArrowUpDown,
  BarChart3,
  DollarSign,
  Package,
  Search,
  TrendingDown,
  TrendingUp
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface InventoryItem {
  id: string;
  name: string;
  sku: string;
  category: string;
  description?: string;
  unitOfMeasure: string;
  costPerUnit: number;
  currentStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  location?: string;
  supplier?: string;
  lastRestocked?: string;
  expiryDate?: string;
  imageUrl?: string;
}

interface InventoryDashboardProps {
  items: InventoryItem[];
  categories: string[];
  onViewItem: (id: string) => void;
}

export function InventoryDashboard({ items = [], categories = [], onViewItem }: InventoryDashboardProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter items based on search term and category
  const filteredItems = items.filter(item => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Sort items based on sort field and direction
  const sortedItems = [...filteredItems].sort((a, b) => {
    let aValue: any = a[sortField as keyof InventoryItem];
    let bValue: any = b[sortField as keyof InventoryItem];

    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Calculate inventory statistics
  const totalItems = items.length;
  const totalValue = items.reduce((sum, item) => sum + (item.currentStock * item.costPerUnit), 0);
  const lowStockItems = items.filter(item => item.currentStock <= item.reorderPoint).length;
  const outOfStockItems = items.filter(item => item.currentStock === 0).length;

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header Section */}
      <div style={{
        background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
        borderRadius: '12px',
        padding: '2rem',
        color: 'white',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{ position: 'relative', zIndex: 2 }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: '700', marginBottom: '0.5rem' }}>
            Inventory Overview
          </h2>
          <p style={{ opacity: 0.9, fontSize: '0.875rem' }}>
            {totalItems} items • {formatCurrency(totalValue)} total value
          </p>
        </div>
        <div style={{
          position: 'absolute',
          top: '-50%',
          right: '-10%',
          width: '200px',
          height: '200px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          zIndex: 1
        }} />
      </div>

      {/* Statistics Cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              borderRadius: '8px',
              backgroundColor: '#f0f9ff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Package style={{ height: '1.25rem', width: '1.25rem', color: '#0ea5e9' }} />
            </div>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280' }}>Total Items</h3>
          </div>
          <p style={{ fontSize: '2rem', fontWeight: '700', color: '#111827' }}>{totalItems}</p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              borderRadius: '8px',
              backgroundColor: '#f0fdf4',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <DollarSign style={{ height: '1.25rem', width: '1.25rem', color: '#22c55e' }} />
            </div>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280' }}>Inventory Value</h3>
          </div>
          <p style={{ fontSize: '2rem', fontWeight: '700', color: '#111827' }}>{formatCurrency(totalValue)}</p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              borderRadius: '8px',
              backgroundColor: '#fffbeb',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <AlertTriangle style={{ height: '1.25rem', width: '1.25rem', color: '#f59e0b' }} />
            </div>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280' }}>Low Stock Items</h3>
          </div>
          <p style={{ fontSize: '2rem', fontWeight: '700', color: '#111827' }}>{lowStockItems}</p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              borderRadius: '8px',
              backgroundColor: '#fef2f2',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <TrendingDown style={{ height: '1.25rem', width: '1.25rem', color: '#ef4444' }} />
            </div>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280' }}>Out of Stock</h3>
          </div>
          <p style={{ fontSize: '2rem', fontWeight: '700', color: '#111827' }}>{outOfStockItems}</p>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search by name, SKU, or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 sm:w-[200px]"
        >
          <option value="all">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </div>

      <div className="rounded-lg border bg-card shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50">
                <th
                  className="px-4 py-3 text-left font-medium"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex cursor-pointer items-center">
                    Name
                    {sortField === 'name' && (
                      <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === 'asc' ? 'rotate-0' : 'rotate-180'}`} />
                    )}
                  </div>
                </th>
                <th className="px-4 py-3 text-left font-medium">SKU</th>
                <th className="px-4 py-3 text-left font-medium">Category</th>
                <th
                  className="px-4 py-3 text-right font-medium"
                  onClick={() => handleSort('currentStock')}
                >
                  <div className="flex cursor-pointer items-center justify-end">
                    Current Stock
                    {sortField === 'currentStock' && (
                      <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === 'asc' ? 'rotate-0' : 'rotate-180'}`} />
                    )}
                  </div>
                </th>
                <th
                  className="px-4 py-3 text-right font-medium"
                  onClick={() => handleSort('costPerUnit')}
                >
                  <div className="flex cursor-pointer items-center justify-end">
                    Unit Cost
                    {sortField === 'costPerUnit' && (
                      <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === 'asc' ? 'rotate-0' : 'rotate-180'}`} />
                    )}
                  </div>
                </th>
                <th className="px-4 py-3 text-right font-medium">Total Value</th>
                <th className="px-4 py-3 text-right font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              {sortedItems.map(item => {
                const isLowStock = item.currentStock <= item.reorderPoint;
                const isOutOfStock = item.currentStock === 0;
                const totalValue = item.currentStock * item.costPerUnit;

                return (
                  <tr
                    key={item.id}
                    className="cursor-pointer border-b hover:bg-muted/50"
                    onClick={() => onViewItem(item.id)}
                  >
                    <td className="px-4 py-3 font-medium">{item.name}</td>
                    <td className="px-4 py-3 text-muted-foreground">{item.sku}</td>
                    <td className="px-4 py-3">{item.category}</td>
                    <td className="px-4 py-3 text-right">
                      {item.currentStock} {item.unitOfMeasure}
                    </td>
                    <td className="px-4 py-3 text-right">
                      {formatCurrency(item.costPerUnit)}
                    </td>
                    <td className="px-4 py-3 text-right">
                      {formatCurrency(totalValue)}
                    </td>
                    <td className="px-4 py-3 text-right">
                      {isOutOfStock ? (
                        <span className="inline-flex rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
                          Out of Stock
                        </span>
                      ) : isLowStock ? (
                        <span className="inline-flex rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                          Low Stock
                        </span>
                      ) : (
                        <span className="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                          In Stock
                        </span>
                      )}
                    </td>
                  </tr>
                );
              })}

              {sortedItems.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-muted-foreground">
                    No inventory items found. Adjust your search or add new items.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-lg border bg-card p-4 shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="font-medium">Low Stock Items</h3>
            <Button variant="link" className="h-auto p-0 text-sm">
              View All
            </Button>
          </div>

          {items.filter(item => item.currentStock <= item.reorderPoint && item.currentStock > 0).length > 0 ? (
            <div className="space-y-2">
              {items
                .filter(item => item.currentStock <= item.reorderPoint && item.currentStock > 0)
                .slice(0, 5)
                .map(item => (
                  <div
                    key={item.id}
                    className="flex cursor-pointer items-center justify-between rounded-md p-2 hover:bg-muted"
                    onClick={() => onViewItem(item.id)}
                  >
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {item.currentStock} / {item.reorderPoint} {item.unitOfMeasure}
                      </p>
                    </div>
                    <div className="text-right">
                      <Button variant="outline" size="sm">
                        <TrendingUp className="mr-1 h-3 w-3" />
                        Restock
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground">
              No low stock items at the moment.
            </p>
          )}
        </div>

        <div className="rounded-lg border bg-card p-4 shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="font-medium">Inventory by Category</h3>
            <Button variant="link" className="h-auto p-0 text-sm">
              <BarChart3 className="mr-1 h-4 w-4" />
              View Report
            </Button>
          </div>

          <div className="space-y-4">
            {categories.map(category => {
              const categoryItems = items.filter(item => item.category === category);
              const categoryValue = categoryItems.reduce((sum, item) => sum + (item.currentStock * item.costPerUnit), 0);
              const categoryPercentage = totalValue > 0 ? (categoryValue / totalValue) * 100 : 0;

              return (
                <div key={category}>
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{category}</p>
                    <p className="text-sm">{formatCurrency(categoryValue)}</p>
                  </div>
                  <div className="mt-1 h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-primary"
                      style={{ width: `${categoryPercentage}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
