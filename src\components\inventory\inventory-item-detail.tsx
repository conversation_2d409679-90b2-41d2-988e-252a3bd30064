'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  Edit, 
  Trash, 
  Plus, 
  TrendingUp, 
  TrendingDown, 
  History, 
  AlertTriangle 
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

interface InventoryItem {
  id: string;
  name: string;
  sku: string;
  category: string;
  description?: string;
  unitOfMeasure: string;
  costPerUnit: number;
  currentStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  location?: string;
  supplier?: string;
  lastRestocked?: string;
  expiryDate?: string;
  imageUrl?: string;
}

interface InventoryTransaction {
  id: string;
  itemId: string;
  date: string;
  type: 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'production' | 'wastage';
  quantity: number;
  unitCost?: number;
  totalCost?: number;
  reference?: string;
  notes?: string;
}

interface InventoryItemDetailProps {
  item: InventoryItem;
  transactions: InventoryTransaction[];
  onBack: () => void;
  onEdit: (item: InventoryItem) => void;
  onDelete: (id: string) => void;
  onAddStock: (itemId: string, quantity: number, unitCost: number, notes?: string) => void;
}

export function InventoryItemDetail({
  item,
  transactions,
  onBack,
  onEdit,
  onDelete,
  onAddStock
}: InventoryItemDetailProps) {
  const [showAddStock, setShowAddStock] = useState(false);
  const [stockQuantity, setStockQuantity] = useState('');
  const [stockUnitCost, setStockUnitCost] = useState(item.costPerUnit.toString());
  const [stockNotes, setStockNotes] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  
  const handleAddStock = () => {
    const quantity = parseFloat(stockQuantity);
    const unitCost = parseFloat(stockUnitCost);
    
    if (isNaN(quantity) || quantity <= 0) {
      alert('Please enter a valid quantity');
      return;
    }
    
    if (isNaN(unitCost) || unitCost <= 0) {
      alert('Please enter a valid unit cost');
      return;
    }
    
    onAddStock(item.id, quantity, unitCost, stockNotes);
    setShowAddStock(false);
    setStockQuantity('');
    setStockUnitCost(item.costPerUnit.toString());
    setStockNotes('');
  };
  
  const isLowStock = item.currentStock <= item.reorderPoint;
  const stockValue = item.currentStock * item.costPerUnit;
  
  // Group transactions by type
  const transactionsByType = transactions.reduce((acc, transaction) => {
    if (!acc[transaction.type]) {
      acc[transaction.type] = [];
    }
    acc[transaction.type].push(transaction);
    return acc;
  }, {} as Record<string, InventoryTransaction[]>);
  
  // Calculate total inflow and outflow
  const totalInflow = transactions
    .filter(t => ['purchase', 'adjustment', 'transfer', 'production'].includes(t.type) && t.quantity > 0)
    .reduce((sum, t) => sum + t.quantity, 0);
    
  const totalOutflow = transactions
    .filter(t => ['sale', 'adjustment', 'transfer', 'wastage'].includes(t.type) && t.quantity < 0)
    .reduce((sum, t) => sum + Math.abs(t.quantity), 0);
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Inventory
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => onEdit(item)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => {
              if (window.confirm(`Are you sure you want to delete ${item.name}?`)) {
                onDelete(item.id);
              }
            }}
          >
            <Trash className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-[2fr_1fr]">
        <div>
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold">{item.name}</h1>
              <p className="text-muted-foreground">SKU: {item.sku}</p>
            </div>
            {isLowStock && (
              <div className="flex items-center rounded-md bg-amber-100 px-3 py-1 text-sm text-amber-800">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Low Stock
              </div>
            )}
          </div>
          
          <div className="mt-6">
            <div className="flex border-b">
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'transactions' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('transactions')}
              >
                Transactions
              </button>
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'analytics' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('analytics')}
              >
                Analytics
              </button>
            </div>
            
            {activeTab === 'overview' && (
              <div className="mt-4 space-y-6">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="rounded-lg border p-4">
                    <p className="text-sm font-medium text-muted-foreground">Current Stock</p>
                    <p className="text-2xl font-bold">{item.currentStock} {item.unitOfMeasure}</p>
                    <p className="text-sm text-muted-foreground">Value: {formatCurrency(stockValue)}</p>
                  </div>
                  
                  <div className="rounded-lg border p-4">
                    <p className="text-sm font-medium text-muted-foreground">Reorder Settings</p>
                    <div className="flex items-baseline justify-between">
                      <p className="text-lg font-medium">Point: {item.reorderPoint} {item.unitOfMeasure}</p>
                      <p className="text-sm">Qty: {item.reorderQuantity} {item.unitOfMeasure}</p>
                    </div>
                    <div className="mt-1 h-2 w-full rounded-full bg-muted">
                      <div 
                        className={`h-2 rounded-full ${
                          item.currentStock <= item.reorderPoint / 2 ? 'bg-red-500' :
                          item.currentStock <= item.reorderPoint ? 'bg-amber-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(100, (item.currentStock / (item.reorderPoint * 2)) * 100)}%` }}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="rounded-lg border">
                  <h3 className="border-b px-4 py-3 font-medium">Item Details</h3>
                  <div className="p-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div>
                        <p className="text-sm font-medium">Category</p>
                        <p>{item.category}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Unit of Measure</p>
                        <p>{item.unitOfMeasure}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Cost per Unit</p>
                        <p>{formatCurrency(item.costPerUnit)}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Location</p>
                        <p>{item.location || 'Not specified'}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Supplier</p>
                        <p>{item.supplier || 'Not specified'}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Last Restocked</p>
                        <p>{item.lastRestocked ? formatDate(item.lastRestocked) : 'Never'}</p>
                      </div>
                      {item.expiryDate && (
                        <div>
                          <p className="text-sm font-medium">Expiry Date</p>
                          <p>{formatDate(item.expiryDate)}</p>
                        </div>
                      )}
                    </div>
                    
                    {item.description && (
                      <div className="mt-4">
                        <p className="text-sm font-medium">Description</p>
                        <p className="text-muted-foreground">{item.description}</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {item.imageUrl && (
                  <div className="rounded-lg border p-4">
                    <p className="mb-2 text-sm font-medium">Product Image</p>
                    <img 
                      src={item.imageUrl} 
                      alt={item.name} 
                      className="max-h-60 rounded-md object-contain"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://placehold.co/400x300?text=Image+Not+Available';
                      }}
                    />
                  </div>
                )}
              </div>
            )}
            
            {activeTab === 'transactions' && (
              <div className="mt-4 space-y-4">
                <div className="rounded-lg border">
                  <div className="border-b px-4 py-3">
                    <h3 className="font-medium">Transaction History</h3>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="px-4 py-2 text-left">Date</th>
                          <th className="px-4 py-2 text-left">Type</th>
                          <th className="px-4 py-2 text-right">Quantity</th>
                          <th className="px-4 py-2 text-right">Unit Cost</th>
                          <th className="px-4 py-2 text-right">Total</th>
                          <th className="px-4 py-2 text-left">Reference</th>
                        </tr>
                      </thead>
                      <tbody>
                        {transactions.length > 0 ? (
                          transactions.map(transaction => (
                            <tr key={transaction.id} className="border-b">
                              <td className="px-4 py-2">{formatDate(transaction.date)}</td>
                              <td className="px-4 py-2">
                                <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                  transaction.type === 'purchase' ? 'bg-green-100 text-green-800' :
                                  transaction.type === 'sale' ? 'bg-blue-100 text-blue-800' :
                                  transaction.type === 'adjustment' ? 'bg-purple-100 text-purple-800' :
                                  transaction.type === 'transfer' ? 'bg-amber-100 text-amber-800' :
                                  transaction.type === 'production' ? 'bg-indigo-100 text-indigo-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                                </span>
                              </td>
                              <td className={`px-4 py-2 text-right ${transaction.quantity < 0 ? 'text-red-600' : 'text-green-600'}`}>
                                {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {item.unitOfMeasure}
                              </td>
                              <td className="px-4 py-2 text-right">
                                {transaction.unitCost ? formatCurrency(transaction.unitCost) : '-'}
                              </td>
                              <td className="px-4 py-2 text-right">
                                {transaction.totalCost ? formatCurrency(transaction.totalCost) : '-'}
                              </td>
                              <td className="px-4 py-2">{transaction.reference || '-'}</td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={6} className="px-4 py-4 text-center text-muted-foreground">
                              No transactions found for this item.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'analytics' && (
              <div className="mt-4 space-y-6">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="rounded-lg border p-4">
                    <div className="flex items-center">
                      <TrendingUp className="mr-2 h-5 w-5 text-green-500" />
                      <p className="text-sm font-medium">Total Inflow</p>
                    </div>
                    <p className="mt-2 text-2xl font-bold">{totalInflow} {item.unitOfMeasure}</p>
                  </div>
                  
                  <div className="rounded-lg border p-4">
                    <div className="flex items-center">
                      <TrendingDown className="mr-2 h-5 w-5 text-red-500" />
                      <p className="text-sm font-medium">Total Outflow</p>
                    </div>
                    <p className="mt-2 text-2xl font-bold">{totalOutflow} {item.unitOfMeasure}</p>
                  </div>
                </div>
                
                <div className="rounded-lg border">
                  <h3 className="border-b px-4 py-3 font-medium">Transaction Breakdown</h3>
                  <div className="p-4">
                    <div className="space-y-4">
                      {Object.entries(transactionsByType).map(([type, typeTransactions]) => {
                        const totalQuantity = typeTransactions.reduce((sum, t) => sum + t.quantity, 0);
                        const totalCost = typeTransactions.reduce((sum, t) => sum + (t.totalCost || 0), 0);
                        
                        return (
                          <div key={type} className="rounded-md bg-muted/30 p-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <span className={`mr-2 inline-flex h-6 w-6 items-center justify-center rounded-full ${
                                  type === 'purchase' ? 'bg-green-100 text-green-800' :
                                  type === 'sale' ? 'bg-blue-100 text-blue-800' :
                                  type === 'adjustment' ? 'bg-purple-100 text-purple-800' :
                                  type === 'transfer' ? 'bg-amber-100 text-amber-800' :
                                  type === 'production' ? 'bg-indigo-100 text-indigo-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {type.charAt(0).toUpperCase()}
                                </span>
                                <span className="font-medium">
                                  {type.charAt(0).toUpperCase() + type.slice(1)}s
                                </span>
                              </div>
                              <span className="text-sm">
                                {typeTransactions.length} transactions
                              </span>
                            </div>
                            <div className="mt-2 grid grid-cols-2 gap-2">
                              <div>
                                <p className="text-xs text-muted-foreground">Total Quantity</p>
                                <p className="font-medium">{totalQuantity} {item.unitOfMeasure}</p>
                              </div>
                              <div>
                                <p className="text-xs text-muted-foreground">Total Value</p>
                                <p className="font-medium">{formatCurrency(totalCost)}</p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                      
                      {Object.keys(transactionsByType).length === 0 && (
                        <p className="text-center text-muted-foreground">
                          No transaction data available for analysis.
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="rounded-lg border">
            <div className="border-b px-4 py-3">
              <h3 className="font-medium">Quick Actions</h3>
            </div>
            <div className="p-4">
              <Button 
                className="mb-2 w-full justify-start"
                onClick={() => setShowAddStock(!showAddStock)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Stock
              </Button>
              
              {showAddStock && (
                <div className="mt-4 space-y-3 rounded-md bg-muted/30 p-3">
                  <div>
                    <label htmlFor="quantity" className="mb-1 block text-sm font-medium">
                      Quantity
                    </label>
                    <div className="flex items-center">
                      <Input
                        id="quantity"
                        type="number"
                        min="0.01"
                        step="0.01"
                        value={stockQuantity}
                        onChange={(e) => setStockQuantity(e.target.value)}
                        className="flex-1"
                        required
                      />
                      <span className="ml-2 text-sm">{item.unitOfMeasure}</span>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="unitCost" className="mb-1 block text-sm font-medium">
                      Unit Cost
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        $
                      </span>
                      <Input
                        id="unitCost"
                        type="number"
                        min="0.01"
                        step="0.01"
                        value={stockUnitCost}
                        onChange={(e) => setStockUnitCost(e.target.value)}
                        className="pl-7"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="notes" className="mb-1 block text-sm font-medium">
                      Notes (Optional)
                    </label>
                    <Input
                      id="notes"
                      value={stockNotes}
                      onChange={(e) => setStockNotes(e.target.value)}
                      placeholder="Purchase order reference, etc."
                    />
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAddStock(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleAddStock}
                    >
                      Add
                    </Button>
                  </div>
                </div>
              )}
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => onEdit(item)}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Item
              </Button>
            </div>
          </div>
          
          <div className="rounded-lg border">
            <div className="border-b px-4 py-3">
              <h3 className="font-medium">Recent Activity</h3>
            </div>
            <div className="p-4">
              {transactions.length > 0 ? (
                <div className="space-y-3">
                  {transactions.slice(0, 5).map(transaction => (
                    <div key={transaction.id} className="flex items-start gap-3">
                      <div className={`mt-0.5 rounded-full p-1 ${
                        transaction.type === 'purchase' ? 'bg-green-100 text-green-800' :
                        transaction.type === 'sale' ? 'bg-blue-100 text-blue-800' :
                        transaction.type === 'adjustment' ? 'bg-purple-100 text-purple-800' :
                        transaction.type === 'transfer' ? 'bg-amber-100 text-amber-800' :
                        transaction.type === 'production' ? 'bg-indigo-100 text-indigo-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        <History className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                          <span className={`ml-1 ${transaction.quantity < 0 ? 'text-red-600' : 'text-green-600'}`}>
                            {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {item.unitOfMeasure}
                          </span>
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(transaction.date)}
                          {transaction.reference && ` • Ref: ${transaction.reference}`}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-muted-foreground">
                  No recent activity for this item.
                </p>
              )}
              
              {transactions.length > 5 && (
                <Button
                  variant="link"
                  className="mt-2 h-auto p-0 text-sm"
                  onClick={() => setActiveTab('transactions')}
                >
                  View all transactions
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
