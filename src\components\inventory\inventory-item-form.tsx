'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Save, X, Upload, Image } from 'lucide-react';

interface InventoryItem {
  id?: string;
  name: string;
  sku: string;
  category: string;
  description?: string;
  unitOfMeasure: string;
  costPerUnit: number;
  currentStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  location?: string;
  supplier?: string;
  lastRestocked?: string;
  expiryDate?: string;
  imageUrl?: string;
}

interface InventoryItemFormProps {
  initialData?: InventoryItem;
  onSubmit: (data: InventoryItem) => void;
  onCancel: () => void;
  categories: string[];
  unitOptions: string[];
  suppliers: Array<{ id: string; name: string }>;
  locations: Array<{ id: string; name: string }>;
}

const defaultItem: InventoryItem = {
  name: '',
  sku: '',
  category: '',
  description: '',
  unitOfMeasure: '',
  costPerUnit: 0,
  currentStock: 0,
  reorderPoint: 0,
  reorderQuantity: 0,
  location: '',
  supplier: '',
  imageUrl: '',
};

export function InventoryItemForm({
  initialData = defaultItem,
  onSubmit,
  onCancel,
  categories,
  unitOptions,
  suppliers,
  locations
}: InventoryItemFormProps) {
  const [formData, setFormData] = useState<InventoryItem>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isGeneratingSku, setIsGeneratingSku] = useState(!initialData.sku);

  // Generate SKU when name or category changes if auto-generation is enabled
  useEffect(() => {
    if (isGeneratingSku && formData.name && formData.category) {
      const categoryPrefix = formData.category.substring(0, 3).toUpperCase();
      const nameWords = formData.name.split(' ');
      let namePrefix = '';

      if (nameWords.length === 1) {
        namePrefix = nameWords[0].substring(0, 3).toUpperCase();
      } else {
        namePrefix = nameWords.map(word => word.charAt(0).toUpperCase()).join('');
        if (namePrefix.length > 3) {
          namePrefix = namePrefix.substring(0, 3);
        }
      }

      const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      const generatedSku = `${categoryPrefix}-${namePrefix}-${randomNum}`;

      setFormData(prev => ({ ...prev, sku: generatedSku }));
    }
  }, [isGeneratingSku, formData.name, formData.category]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = value === '' ? 0 : parseFloat(value);
    setFormData({ ...formData, [name]: numValue });

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.unitOfMeasure) {
      newErrors.unitOfMeasure = 'Unit of measure is required';
    }

    if (formData.costPerUnit < 0) {
      newErrors.costPerUnit = 'Cost must be a positive number';
    }

    if (formData.currentStock < 0) {
      newErrors.currentStock = 'Stock cannot be negative';
    }

    if (formData.reorderPoint < 0) {
      newErrors.reorderPoint = 'Reorder point cannot be negative';
    }

    if (formData.reorderQuantity < 0) {
      newErrors.reorderQuantity = 'Reorder quantity cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Basic Information</h3>

          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Item Name *
            </label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={errors.name ? 'border-red-500' : ''}
              required
            />
            {errors.name && (
              <p className="text-xs text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label htmlFor="sku" className="text-sm font-medium">
                SKU *
              </label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="generateSku"
                  checked={isGeneratingSku}
                  onChange={() => setIsGeneratingSku(!isGeneratingSku)}
                  className="mr-2 h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="generateSku" className="text-xs">
                  Auto-generate
                </label>
              </div>
            </div>
            <Input
              id="sku"
              name="sku"
              value={formData.sku}
              onChange={handleInputChange}
              disabled={isGeneratingSku}
              className={errors.sku ? 'border-red-500' : ''}
              required
            />
            {errors.sku && (
              <p className="text-xs text-red-500">{errors.sku}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="category" className="text-sm font-medium">
              Category *
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${
                errors.category ? 'border-red-500' : ''
              }`}
              required
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="text-xs text-red-500">{errors.category}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleInputChange}
              className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Inventory Details</h3>

          <div className="space-y-2">
            <label htmlFor="unitOfMeasure" className="text-sm font-medium">
              Unit of Measure *
            </label>
            <select
              id="unitOfMeasure"
              name="unitOfMeasure"
              value={formData.unitOfMeasure}
              onChange={handleInputChange}
              className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${
                errors.unitOfMeasure ? 'border-red-500' : ''
              }`}
              required
            >
              <option value="">Select a unit</option>
              {unitOptions.map(unit => (
                <option key={unit} value={unit}>
                  {unit}
                </option>
              ))}
            </select>
            {errors.unitOfMeasure && (
              <p className="text-xs text-red-500">{errors.unitOfMeasure}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="costPerUnit" className="text-sm font-medium">
              Cost per Unit *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                ₹
              </span>
              <Input
                id="costPerUnit"
                name="costPerUnit"
                type="number"
                min="0"
                step="0.01"
                value={formData.costPerUnit}
                onChange={handleNumberInputChange}
                className={`pl-7 ${errors.costPerUnit ? 'border-red-500' : ''}`}
                required
              />
            </div>
            {errors.costPerUnit && (
              <p className="text-xs text-red-500">{errors.costPerUnit}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="currentStock" className="text-sm font-medium">
              Current Stock *
            </label>
            <div className="flex items-center">
              <Input
                id="currentStock"
                name="currentStock"
                type="number"
                min="0"
                step="0.01"
                value={formData.currentStock}
                onChange={handleNumberInputChange}
                className={errors.currentStock ? 'border-red-500' : ''}
                required
              />
              <span className="ml-2 text-sm">{formData.unitOfMeasure}</span>
            </div>
            {errors.currentStock && (
              <p className="text-xs text-red-500">{errors.currentStock}</p>
            )}
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <label htmlFor="reorderPoint" className="text-sm font-medium">
                Reorder Point *
              </label>
              <div className="flex items-center">
                <Input
                  id="reorderPoint"
                  name="reorderPoint"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.reorderPoint}
                  onChange={handleNumberInputChange}
                  className={errors.reorderPoint ? 'border-red-500' : ''}
                  required
                />
                <span className="ml-2 text-sm">{formData.unitOfMeasure}</span>
              </div>
              {errors.reorderPoint && (
                <p className="text-xs text-red-500">{errors.reorderPoint}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="reorderQuantity" className="text-sm font-medium">
                Reorder Quantity *
              </label>
              <div className="flex items-center">
                <Input
                  id="reorderQuantity"
                  name="reorderQuantity"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.reorderQuantity}
                  onChange={handleNumberInputChange}
                  className={errors.reorderQuantity ? 'border-red-500' : ''}
                  required
                />
                <span className="ml-2 text-sm">{formData.unitOfMeasure}</span>
              </div>
              {errors.reorderQuantity && (
                <p className="text-xs text-red-500">{errors.reorderQuantity}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Additional Information</h3>

          <div className="space-y-2">
            <label htmlFor="supplier" className="text-sm font-medium">
              Supplier
            </label>
            <select
              id="supplier"
              name="supplier"
              value={formData.supplier || ''}
              onChange={handleInputChange}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a supplier</option>
              {suppliers.map(supplier => (
                <option key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label htmlFor="location" className="text-sm font-medium">
              Storage Location
            </label>
            <select
              id="location"
              name="location"
              value={formData.location || ''}
              onChange={handleInputChange}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a location</option>
              {locations.map(location => (
                <option key={location.id} value={location.id}>
                  {location.name}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label htmlFor="expiryDate" className="text-sm font-medium">
              Expiry Date
            </label>
            <Input
              id="expiryDate"
              name="expiryDate"
              type="date"
              value={formData.expiryDate || ''}
              onChange={handleInputChange}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Product Image</h3>

          <div className="space-y-2">
            <label htmlFor="imageUrl" className="text-sm font-medium">
              Product Image
            </label>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  value={formData.imageUrl || ''}
                  onChange={handleInputChange}
                  placeholder="Enter image URL or use the upload button"
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  // Open file input dialog
                  const fileInput = document.createElement('input');
                  fileInput.type = 'file';
                  fileInput.accept = 'image/*';
                  fileInput.onchange = async (e) => {
                    const target = e.target as HTMLInputElement;
                    if (target.files && target.files[0]) {
                      const file = target.files[0];

                      // Create a preview URL
                      const reader = new FileReader();
                      reader.onloadend = async () => {
                        const base64Data = reader.result as string;

                        try {
                          const response = await fetch('/api/upload', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              image: base64Data,
                              folder: 'inventory'
                            }),
                          });

                          if (!response.ok) {
                            throw new Error('Failed to upload image');
                          }

                          const data = await response.json();
                          // Update the imageUrl using the handleInputChange function
                          const syntheticEvent = {
                            target: {
                              name: 'imageUrl',
                              value: data.imageUrl
                            }
                          } as React.ChangeEvent<HTMLInputElement>;
                          handleInputChange(syntheticEvent);
                        } catch (error) {
                          console.error('Error uploading image:', error);
                        }
                      };
                      reader.readAsDataURL(file);
                    }
                  };
                  fileInput.click();
                }}
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload Image
              </Button>
            </div>
          </div>

          {formData.imageUrl && (
            <div className="rounded-md border p-2">
              <img
                src={formData.imageUrl}
                alt="Product preview"
                className="mx-auto max-h-40 object-contain"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://placehold.co/400x300?text=Invalid+Image+URL';
                }}
              />
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button type="submit">
          <Save className="mr-2 h-4 w-4" />
          {initialData.id ? 'Update' : 'Save'} Item
        </Button>
      </div>
    </form>
  );
}
