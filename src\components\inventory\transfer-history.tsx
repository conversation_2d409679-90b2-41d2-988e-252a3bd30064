'use client';

import { useState, useEffect } from 'react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface TransferHistoryProps {
  warehouseId?: string;
  storeId?: string;
  productId?: string;
  limit?: number;
}

interface Transfer {
  id: string;
  productId: string;
  productName: string;
  sourceWarehouseId: string;
  sourceWarehouseName: string;
  destinationType: string;
  destinationId: string;
  destinationName: string;
  quantity: number;
  notes: string | null;
  createdAt: string;
}

export function TransferHistory({ warehouseId, storeId, productId, limit }: TransferHistoryProps) {
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTransfers = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        if (warehouseId) params.append('warehouseId', warehouseId);
        if (storeId) params.append('storeId', storeId);
        if (productId) params.append('productId', productId);
        if (limit) params.append('limit', limit.toString());

        // Use a try-catch block to handle any network errors
        try {
          const response = await fetch(`/api/inventory/transfers?${params.toString()}`);

          // Even if the response is not OK, we'll try to parse it
          const data = await response.json();

          // If we got an array, use it
          if (Array.isArray(data)) {
            setTransfers(data);
          } else if (data.error) {
            // If we got an error message, log it but don't show to user
            console.error('API returned error:', data.error);
            setTransfers([]);
          } else {
            // Unexpected response format
            console.error('Unexpected API response format:', data);
            setTransfers([]);
          }
        } catch (fetchErr) {
          // Handle fetch errors (network issues, etc.)
          console.error('Fetch error:', fetchErr);
          setTransfers([]);
        }
      } catch (err) {
        // This catch block handles any other errors in our code
        console.error('Error in transfer history component:', err);
        setTransfers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTransfers();
  }, [warehouseId, storeId, productId, limit]);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        height: '10rem',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e5e7eb'
      }}>
        <Loader2 style={{ height: '1.5rem', width: '1.5rem', animation: 'spin 1s linear infinite', color: '#059669' }} />
        <span style={{ marginLeft: '0.5rem', color: '#6b7280' }}>Loading transfer history...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        borderRadius: '8px',
        backgroundColor: '#fef2f2',
        padding: '1rem',
        fontSize: '0.875rem',
        color: '#dc2626'
      }}>
        {error}
      </div>
    );
  }

  if (transfers.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e5e7eb',
        padding: '2rem',
        textAlign: 'center',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          fontSize: '3rem',
          marginBottom: '1rem'
        }}>
          📋
        </div>
        <h3 style={{
          fontSize: '1.125rem',
          fontWeight: '600',
          color: '#111827',
          marginBottom: '0.5rem'
        }}>
          No transfer history found
        </h3>
        <p style={{
          fontSize: '0.875rem',
          color: '#6b7280'
        }}>
          Transfer history will appear here once you start moving inventory.
        </p>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{
        fontSize: '1.125rem',
        fontWeight: '600',
        color: '#111827',
        marginBottom: '1.5rem',
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem'
      }}>
        📋 Transfer History
      </h3>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
        gap: '1rem'
      }}>
        {transfers.map((transfer) => (
          <div
            key={transfer.id}
            style={{
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              border: '1px solid #e5e7eb',
              padding: '1rem',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
              e.currentTarget.style.transform = 'translateY(-1px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#f9fafb';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            {/* Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '0.75rem'
            }}>
              <div>
                <h4 style={{
                  fontSize: '1rem',
                  fontWeight: '600',
                  color: '#111827',
                  marginBottom: '0.25rem'
                }}>
                  {transfer.productName}
                </h4>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#6b7280'
                }}>
                  {formatDate(transfer.createdAt)}
                </p>
              </div>
              <div style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '4px',
                backgroundColor: '#dcfce7',
                color: '#166534',
                fontSize: '0.75rem',
                fontWeight: '500'
              }}>
                {transfer.quantity} units
              </div>
            </div>

            {/* Transfer Route */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              marginBottom: '0.75rem'
            }}>
              <div style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '4px',
                backgroundColor: '#dbeafe',
                color: '#1e40af',
                fontSize: '0.75rem',
                fontWeight: '500'
              }}>
                📦 {transfer.sourceWarehouseName}
              </div>
              <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>→</span>
              <div style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '4px',
                backgroundColor: '#fef3c7',
                color: '#92400e',
                fontSize: '0.75rem',
                fontWeight: '500'
              }}>
                {transfer.destinationType === 'WAREHOUSE' ? '🏭' : '🏪'} {transfer.destinationName}
              </div>
            </div>

            {/* Notes */}
            {transfer.notes && (
              <div style={{
                borderTop: '1px solid #e5e7eb',
                paddingTop: '0.75rem'
              }}>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  fontStyle: 'italic'
                }}>
                  "{transfer.notes}"
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
