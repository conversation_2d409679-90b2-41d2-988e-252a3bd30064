'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { OrderStatusBadge, PaymentStatusBadge } from './order-status-badge';
import { OrderStatusUpdater } from './order-status-updater';
import { useAuth } from '@/lib/auth/auth-context';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  User, 
  MapPin, 
  Phone, 
  Mail, 
  Package, 
  Store,
  Calendar,
  CreditCard,
  Hash
} from 'lucide-react';

interface OrderDetailModalProps {
  order: any;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdate?: () => void;
}

export function OrderDetailModal({ 
  order, 
  isOpen, 
  onClose, 
  onOrderUpdate 
}: OrderDetailModalProps) {
  const { user } = useAuth();
  const [currentStatus, setCurrentStatus] = useState(order?.status);

  if (!order) return null;

  const handleStatusUpdate = (newStatus: string) => {
    setCurrentStatus(newStatus);
    onOrderUpdate?.();
  };

  const isAdmin = user?.role === 'ADMIN';
  const isStoreUser = user?.role === 'STORE_MANAGER' || user?.role === 'STAFF';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Order Details - {order.orderNumber || order.id}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Order Information */}
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Order Information
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Number:</span>
                  <span className="font-medium">{order.orderNumber || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Date:</span>
                  <span>{formatDate(order.createdAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Type:</span>
                  <span className="capitalize">{order.orderType || 'Online'}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Status:</span>
                  <OrderStatusBadge status={currentStatus} />
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Payment Status:</span>
                  <PaymentStatusBadge status={order.paymentStatus?.toLowerCase() || 'pending'} />
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Method:</span>
                  <span className="capitalize">{order.paymentMethod || 'COD'}</span>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <User className="h-4 w-4" />
                Customer Information
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <User className="h-3 w-3 text-gray-400" />
                  <span>{order.customer?.user?.name || order.customerName || 'N/A'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-3 w-3 text-gray-400" />
                  <span>{order.customer?.user?.email || order.customerEmail || 'N/A'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-3 w-3 text-gray-400" />
                  <span>{order.customer?.phone || order.customerPhone || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Delivery Address */}
            {order.address && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Delivery Address
                </h3>
                <div className="text-sm space-y-1">
                  <div>{order.address.firstName} {order.address.lastName}</div>
                  <div>{order.address.street}</div>
                  <div>{order.address.city}, {order.address.state} {order.address.postalCode}</div>
                  <div>{order.address.country}</div>
                  {order.address.phone && (
                    <div className="flex items-center gap-2 mt-2">
                      <Phone className="h-3 w-3 text-gray-400" />
                      <span>{order.address.phone}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Order Items and Store Information */}
          <div className="space-y-4">
            {/* Store Assignment (Admin only) */}
            {isAdmin && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  Store Assignment
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Assigned Store:</span>
                    <span className="font-medium">{order.store?.name || 'Not Assigned'}</span>
                  </div>
                  {order.assignedByUser && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Assigned By:</span>
                      <span>{order.assignedByUser.name}</span>
                    </div>
                  )}
                  {order.store?.location && (
                    <div className="flex items-start gap-2">
                      <MapPin className="h-3 w-3 text-gray-400 mt-0.5" />
                      <span>{order.store.location}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Store Information (Store users only) */}
            {isStoreUser && order.store && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  Your Store
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="font-medium">{order.store.name}</div>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-3 w-3 text-gray-400 mt-0.5" />
                    <span>{order.store.location}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Order Items */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Package className="h-4 w-4" />
                Order Items
              </h3>
              <div className="space-y-3">
                {order.orderItems?.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                    <div>
                      <div className="font-medium">{item.product?.name || item.productName || 'Unknown Product'}</div>
                      <div className="text-sm text-gray-600">Qty: {item.quantity}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(item.unitPrice * item.quantity)}</div>
                      <div className="text-sm text-gray-600">{formatCurrency(item.unitPrice)} each</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Total */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Order Total
              </h3>
              <div className="space-y-2 text-sm">
                {order.subtotal && (
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>{formatCurrency(order.subtotal)}</span>
                  </div>
                )}
                {order.shipping && (
                  <div className="flex justify-between">
                    <span>Shipping:</span>
                    <span>{formatCurrency(order.shipping)}</span>
                  </div>
                )}
                {order.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount:</span>
                    <span>-{formatCurrency(order.discount)}</span>
                  </div>
                )}
                <div className="flex justify-between font-semibold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>{formatCurrency(order.totalAmount)}</span>
                </div>
              </div>
            </div>

            {/* Status Update (Store users only) */}
            {isStoreUser && order.storeId === user?.storeId && (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3">Update Order Status</h3>
                <OrderStatusUpdater
                  orderId={order.id}
                  currentStatus={currentStatus}
                  onStatusUpdate={handleStatusUpdate}
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
