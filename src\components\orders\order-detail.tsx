'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, 
  Printer, 
  Mail, 
  Phone, 
  MapPin, 
  CreditCard, 
  Truck, 
  Package,
  ShoppingBag,
  Clock,
  CalendarClock,
  FileText,
  MessageSquare
} from 'lucide-react';
import { OrderStatusBadge, PaymentStatusBadge } from './order-status-badge';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Order, OrderStatus, PaymentStatus } from './order-list';

interface OrderDetailProps {
  order: Order;
  onBack: () => void;
  onUpdateStatus: (id: string, status: OrderStatus) => void;
  onUpdatePaymentStatus: (id: string, status: PaymentStatus) => void;
}

export function OrderDetail({
  order,
  onBack,
  onUpdateStatus,
  onUpdatePaymentStatus
}: OrderDetailProps) {
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showPaymentStatusDropdown, setShowPaymentStatusDropdown] = useState(false);
  
  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'credit_card':
        return 'Credit Card';
      case 'paypal':
        return 'PayPal';
      case 'cash_on_delivery':
        return 'Cash on Delivery';
      case 'bank_transfer':
        return 'Bank Transfer';
      default:
        return method;
    }
  };
  
  const getDeliveryMethodLabel = (method: string) => {
    switch (method) {
      case 'pickup':
        return 'Store Pickup';
      case 'delivery':
        return 'Home Delivery';
      default:
        return method;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Orders
        </Button>
        <div className="flex gap-2">
          <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline">
            <Mail className="mr-2 h-4 w-4" />
            Email Receipt
          </Button>
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-[2fr_1fr]">
        <div>
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold">Order #{order.orderNumber}</h1>
              <p className="text-muted-foreground">
                Placed on {formatDate(order.createdAt)}
              </p>
            </div>
            <div className="text-right">
              <div className="flex flex-col items-end gap-1">
                <div className="relative">
                  <Button 
                    variant="outline" 
                    className="flex gap-2"
                    onClick={() => setShowStatusDropdown(!showStatusDropdown)}
                  >
                    <OrderStatusBadge status={order.status} />
                    <span className="sr-only">Change Status</span>
                  </Button>
                  {showStatusDropdown && (
                    <div className="absolute right-0 top-full z-10 mt-1 w-48 rounded-md border bg-background shadow-md">
                      <div className="p-1">
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'pending' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'pending');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="pending" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'processing' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'processing');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="processing" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'ready' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'ready');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="ready" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'out-for-delivery' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'out-for-delivery');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="out-for-delivery" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'delivered' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'delivered');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="delivered" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'completed' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'completed');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="completed" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'cancelled' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'cancelled');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="cancelled" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.status === 'refunded' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdateStatus(order.id, 'refunded');
                            setShowStatusDropdown(false);
                          }}
                        >
                          <OrderStatusBadge status="refunded" size="sm" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="relative">
                  <Button 
                    variant="outline" 
                    className="flex gap-2"
                    onClick={() => setShowPaymentStatusDropdown(!showPaymentStatusDropdown)}
                  >
                    <PaymentStatusBadge status={order.paymentStatus} />
                    <span className="sr-only">Change Payment Status</span>
                  </Button>
                  {showPaymentStatusDropdown && (
                    <div className="absolute right-0 top-full z-10 mt-1 w-40 rounded-md border bg-background shadow-md">
                      <div className="p-1">
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.paymentStatus === 'pending' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdatePaymentStatus(order.id, 'pending');
                            setShowPaymentStatusDropdown(false);
                          }}
                        >
                          <PaymentStatusBadge status="pending" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.paymentStatus === 'paid' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdatePaymentStatus(order.id, 'paid');
                            setShowPaymentStatusDropdown(false);
                          }}
                        >
                          <PaymentStatusBadge status="paid" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.paymentStatus === 'failed' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdatePaymentStatus(order.id, 'failed');
                            setShowPaymentStatusDropdown(false);
                          }}
                        >
                          <PaymentStatusBadge status="failed" size="sm" />
                        </button>
                        <button
                          className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                            order.paymentStatus === 'refunded' ? 'bg-muted' : ''
                          }`}
                          onClick={() => {
                            onUpdatePaymentStatus(order.id, 'refunded');
                            setShowPaymentStatusDropdown(false);
                          }}
                        >
                          <PaymentStatusBadge status="refunded" size="sm" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6 space-y-6">
            <div className="rounded-lg border">
              <h3 className="border-b px-4 py-3 font-medium">Order Items</h3>
              <div className="divide-y">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4">
                    <div className="flex items-center">
                      <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted">
                        <Package className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div className="ml-4">
                        <div className="font-medium">{item.productName}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.quantity} x {formatCurrency(item.unitPrice)}
                        </div>
                      </div>
                    </div>
                    <div className="font-medium">
                      {formatCurrency(item.total)}
                    </div>
                  </div>
                ))}
              </div>
              <div className="border-t p-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>{formatCurrency(order.subtotal)}</span>
                  </div>
                  {order.discount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Discount</span>
                      <span className="text-green-600">-{formatCurrency(order.discount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Tax</span>
                    <span>{formatCurrency(order.tax)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Shipping</span>
                    <span>{formatCurrency(order.shipping)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-2 font-medium">
                    <span>Total</span>
                    <span>{formatCurrency(order.total)}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2">
              <div className="rounded-lg border">
                <h3 className="border-b px-4 py-3 font-medium">Customer Information</h3>
                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{order.customerName}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                      <a href={`mailto:${order.customerEmail}`} className="hover:underline">
                        {order.customerEmail}
                      </a>
                    </div>
                    <div className="flex items-center text-sm">
                      <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                      <a href={`tel:${order.customerPhone}`} className="hover:underline">
                        {order.customerPhone}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="rounded-lg border">
                <h3 className="border-b px-4 py-3 font-medium">Shipping Information</h3>
                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Truck className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{getDeliveryMethodLabel(order.deliveryMethod)}</span>
                    </div>
                    {order.deliveryMethod === 'delivery' && order.shippingAddress && (
                      <div className="flex items-start text-sm">
                        <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div>
                          <p>{order.shippingAddress.street}</p>
                          <p>
                            {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}
                          </p>
                          <p>{order.shippingAddress.country}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2">
              <div className="rounded-lg border">
                <h3 className="border-b px-4 py-3 font-medium">Payment Information</h3>
                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CreditCard className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{getPaymentMethodLabel(order.paymentMethod)}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Payment Status:</span>
                      <span className="ml-1">
                        <PaymentStatusBadge status={order.paymentStatus} size="sm" />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="rounded-lg border">
                <h3 className="border-b px-4 py-3 font-medium">Order Timeline</h3>
                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center text-sm">
                      <ShoppingBag className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Order Placed:</span>
                      <span className="ml-1">{formatDate(order.createdAt)}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <CalendarClock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Last Updated:</span>
                      <span className="ml-1">{formatDate(order.updatedAt)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {order.notes && (
              <div className="rounded-lg border">
                <h3 className="border-b px-4 py-3 font-medium">Order Notes</h3>
                <div className="p-4">
                  <div className="flex items-start">
                    <MessageSquare className="mr-2 h-4 w-4 text-muted-foreground" />
                    <p className="text-sm">{order.notes}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="rounded-lg border">
            <div className="border-b px-4 py-3">
              <h3 className="font-medium">Order Summary</h3>
            </div>
            <div className="p-4">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Order Number</span>
                  <span className="font-medium">{order.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Order Date</span>
                  <span>{formatDate(order.createdAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status</span>
                  <OrderStatusBadge status={order.status} size="sm" />
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Status</span>
                  <PaymentStatusBadge status={order.paymentStatus} size="sm" />
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method</span>
                  <span>{getPaymentMethodLabel(order.paymentMethod)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Delivery Method</span>
                  <span>{getDeliveryMethodLabel(order.deliveryMethod)}</span>
                </div>
                <div className="flex justify-between border-t pt-2 font-medium">
                  <span>Total</span>
                  <span>{formatCurrency(order.total)}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="rounded-lg border">
            <div className="border-b px-4 py-3">
              <h3 className="font-medium">Quick Actions</h3>
            </div>
            <div className="p-4">
              <div className="space-y-2">
                <Button className="w-full justify-start">
                  <Printer className="mr-2 h-4 w-4" />
                  Print Invoice
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="mr-2 h-4 w-4" />
                  Email Customer
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Packing Slip
                </Button>
                {order.status !== 'cancelled' && order.status !== 'refunded' && (
                  <Button variant="outline" className="w-full justify-start text-red-500 hover:text-red-500">
                    <XCircle className="mr-2 h-4 w-4" />
                    Cancel Order
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// User icon component
function User(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="8" r="5" />
      <path d="M20 21a8 8 0 1 0-16 0" />
    </svg>
  );
}

// XCircle icon component
function XCircle(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <path d="m15 9-6 6" />
      <path d="m9 9 6 6" />
    </svg>
  );
}
