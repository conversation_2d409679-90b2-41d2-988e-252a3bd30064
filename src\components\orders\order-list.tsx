'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ArrowUpDown,
  Search,
  Filter,
  Calendar,
  User,
  ShoppingBag
} from 'lucide-react';
import { OrderStatusBadge, PaymentStatusBadge } from './order-status-badge';
import { formatCurrency, formatDate } from '@/lib/utils';

export type OrderStatus =
  | 'PENDING_ASSIGNMENT'
  | 'ASSIGNED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'DELIVERED'
  | 'CANCELLED'
  | 'pending'
  | 'processing'
  | 'ready'
  | 'out-for-delivery'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'refunded';

export type PaymentStatus =
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded';

export type PaymentMethod =
  | 'credit_card'
  | 'paypal'
  | 'cash_on_delivery'
  | 'bank_transfer';

export type DeliveryMethod =
  | 'pickup'
  | 'delivery';

export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  deliveryMethod: DeliveryMethod;
  shippingAddress?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface OrderListProps {
  orders: Order[];
  onViewOrder: (id: string) => void;
}

export function OrderList({
  orders,
  onViewOrder
}: OrderListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Order>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<OrderStatus | 'all'>('all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<PaymentStatus | 'all'>('all');
  const [dateFilter, setDateFilter] = useState<'today' | 'yesterday' | 'this-week' | 'this-month' | 'all'>('all');

  // State to track which dropdown is open
  const [openDropdown, setOpenDropdown] = useState<'status' | 'payment' | 'date' | null>(null);

  // Ref for the dropdown container
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Filter orders based on search term, status, payment status, and date
  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerPhone.includes(searchTerm);

    const matchesStatus =
      statusFilter === 'all' ||
      order.status === statusFilter;

    const matchesPaymentStatus =
      paymentStatusFilter === 'all' ||
      order.paymentStatus === paymentStatusFilter;

    // Date filtering
    const orderDate = new Date(order.createdAt);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(thisWeekStart.getDate() - thisWeekStart.getDay());

    const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);

    const matchesDate =
      dateFilter === 'all' ||
      (dateFilter === 'today' && orderDate >= today) ||
      (dateFilter === 'yesterday' && orderDate >= yesterday && orderDate < today) ||
      (dateFilter === 'this-week' && orderDate >= thisWeekStart) ||
      (dateFilter === 'this-month' && orderDate >= thisMonthStart);

    return matchesSearch && matchesStatus && matchesPaymentStatus && matchesDate;
  });

  // Sort orders based on sort field and direction
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    // Handle date comparison
    if (sortField === 'createdAt' || sortField === 'updatedAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const handleSort = (field: keyof Order) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header Section */}
      <div style={{
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        borderRadius: '12px',
        padding: '2rem',
        color: 'white',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{ position: 'relative', zIndex: 2 }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: '700', marginBottom: '0.5rem' }}>
            Order Management
          </h2>
          <p style={{ opacity: 0.9, fontSize: '0.875rem' }}>
            {sortedOrders.length} orders • {sortedOrders.filter(o => o.status === 'pending').length} pending
          </p>
        </div>
        <div style={{
          position: 'absolute',
          top: '-50%',
          right: '-10%',
          width: '200px',
          height: '200px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          zIndex: 1
        }} />
      </div>

      {/* Search and Filters */}
      <div style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '1rem',
        flexWrap: 'wrap'
      }}>
        <div style={{ position: 'relative', flex: 1 }}>
          <Search style={{
            position: 'absolute',
            left: '12px',
            top: '50%',
            transform: 'translateY(-50%)',
            height: '1rem',
            width: '1rem',
            color: '#6b7280'
          }} />
          <Input
            placeholder="Search orders by number, customer name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              paddingLeft: '2.5rem',
              borderRadius: '8px',
              border: '2px solid #e5e7eb',
              transition: 'all 0.2s',
              fontSize: '0.875rem'
            }}
          />
        </div>

        <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }} ref={dropdownRef}>
          <div className="relative">
            <Button
              variant="outline"
              className="flex gap-2"
              onClick={() => setOpenDropdown(openDropdown === 'status' ? null : 'status')}
            >
              <Filter className="h-4 w-4" />
              Status: {statusFilter === 'all' ? 'All' : statusFilter}
            </Button>
            {openDropdown === 'status' && (
              <div className="absolute right-0 top-full z-10 mt-1 w-48 rounded-md border bg-background shadow-md">
                <div className="p-1">
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'all' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('all');
                      setOpenDropdown(null);
                    }}
                  >
                    All Statuses
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'pending' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('pending');
                      setOpenDropdown(null);
                    }}
                  >
                    Pending
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'processing' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('processing');
                      setOpenDropdown(null);
                    }}
                  >
                    Processing
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'ready' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('ready');
                      setOpenDropdown(null);
                    }}
                  >
                    Ready
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'out-for-delivery' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('out-for-delivery');
                      setOpenDropdown(null);
                    }}
                  >
                    Out for Delivery
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'delivered' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('delivered');
                      setOpenDropdown(null);
                    }}
                  >
                    Delivered
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'completed' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('completed');
                      setOpenDropdown(null);
                    }}
                  >
                    Completed
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'cancelled' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('cancelled');
                      setOpenDropdown(null);
                    }}
                  >
                    Cancelled
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      statusFilter === 'refunded' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setStatusFilter('refunded');
                      setOpenDropdown(null);
                    }}
                  >
                    Refunded
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="relative">
            <Button
              variant="outline"
              className="flex gap-2"
              onClick={() => setOpenDropdown(openDropdown === 'payment' ? null : 'payment')}
            >
              <Filter className="h-4 w-4" />
              Payment: {paymentStatusFilter === 'all' ? 'All' : paymentStatusFilter}
            </Button>
            {openDropdown === 'payment' && (
              <div className="absolute right-0 top-full z-10 mt-1 w-40 rounded-md border bg-background shadow-md">
                <div className="p-1">
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      paymentStatusFilter === 'all' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setPaymentStatusFilter('all');
                      setOpenDropdown(null);
                    }}
                  >
                    All
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      paymentStatusFilter === 'pending' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setPaymentStatusFilter('pending');
                      setOpenDropdown(null);
                    }}
                  >
                    Pending
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      paymentStatusFilter === 'paid' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setPaymentStatusFilter('paid');
                      setOpenDropdown(null);
                    }}
                  >
                    Paid
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      paymentStatusFilter === 'failed' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setPaymentStatusFilter('failed');
                      setOpenDropdown(null);
                    }}
                  >
                    Failed
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      paymentStatusFilter === 'refunded' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setPaymentStatusFilter('refunded');
                      setOpenDropdown(null);
                    }}
                  >
                    Refunded
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="relative">
            <Button
              variant="outline"
              className="flex gap-2"
              onClick={() => setOpenDropdown(openDropdown === 'date' ? null : 'date')}
            >
              <Calendar className="h-4 w-4" />
              Date: {dateFilter === 'all' ? 'All Time' :
                     dateFilter === 'today' ? 'Today' :
                     dateFilter === 'yesterday' ? 'Yesterday' :
                     dateFilter === 'this-week' ? 'This Week' :
                     'This Month'}
            </Button>
            {openDropdown === 'date' && (
              <div className="absolute right-0 top-full z-10 mt-1 w-40 rounded-md border bg-background shadow-md">
                <div className="p-1">
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      dateFilter === 'all' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setDateFilter('all');
                      setOpenDropdown(null);
                    }}
                  >
                    All Time
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      dateFilter === 'today' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setDateFilter('today');
                      setOpenDropdown(null);
                    }}
                  >
                    Today
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      dateFilter === 'yesterday' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setDateFilter('yesterday');
                      setOpenDropdown(null);
                    }}
                  >
                    Yesterday
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      dateFilter === 'this-week' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setDateFilter('this-week');
                      setOpenDropdown(null);
                    }}
                  >
                    This Week
                  </button>
                  <button
                    className={`flex w-full items-center rounded-sm px-2 py-1.5 text-sm ${
                      dateFilter === 'this-month' ? 'bg-muted' : ''
                    }`}
                    onClick={() => {
                      setDateFilter('this-month');
                      setOpenDropdown(null);
                    }}
                  >
                    This Month
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Orders Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
        gap: '1.5rem'
      }}>
        {sortedOrders.length > 0 ? (
          sortedOrders.map((order) => (
            <div
              key={order.id}
              onClick={() => onViewOrder(order.id)}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                padding: '1.5rem',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
                e.currentTarget.style.borderColor = '#f59e0b';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.borderColor = '#e5e7eb';
              }}
            >
              {/* Order Header */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <ShoppingBag style={{ height: '1.25rem', width: '1.25rem', color: '#f59e0b' }} />
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827'
                  }}>
                    {order.orderNumber}
                  </h3>
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <OrderStatusBadge status={order.status} />
                  <PaymentStatusBadge status={order.paymentStatus} />
                </div>
              </div>

              {/* Customer Info */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                marginBottom: '1rem',
                padding: '0.75rem',
                backgroundColor: '#f9fafb',
                borderRadius: '8px'
              }}>
                <User style={{ height: '1rem', width: '1rem', color: '#6b7280' }} />
                <div style={{ flex: 1 }}>
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#111827',
                    marginBottom: '0.25rem'
                  }}>
                    {order.customerName}
                  </p>
                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280'
                  }}>
                    {order.customerEmail}
                  </p>
                </div>
              </div>

              {/* Order Details */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '1rem',
                marginBottom: '1rem'
              }}>
                <div>
                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    marginBottom: '0.25rem'
                  }}>
                    Order Date
                  </p>
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#111827'
                  }}>
                    {formatDate(order.createdAt)}
                  </p>
                </div>
                <div>
                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    marginBottom: '0.25rem'
                  }}>
                    Items
                  </p>
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#111827'
                  }}>
                    {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                  </p>
                </div>
              </div>

              {/* Delivery Method */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                marginBottom: '1rem'
              }}>
                <div style={{
                  padding: '0.25rem 0.5rem',
                  backgroundColor: order.deliveryMethod === 'delivery' ? '#dbeafe' : '#f3f4f6',
                  color: order.deliveryMethod === 'delivery' ? '#1e40af' : '#374151',
                  borderRadius: '4px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {order.deliveryMethod === 'delivery' ? '🚚 Delivery' : '🏪 Pickup'}
                </div>
                <div style={{
                  padding: '0.25rem 0.5rem',
                  backgroundColor: '#f0fdf4',
                  color: '#166534',
                  borderRadius: '4px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {order.paymentMethod === 'credit_card' ? '💳 Card' :
                   order.paymentMethod === 'paypal' ? '🅿️ PayPal' :
                   order.paymentMethod === 'cash_on_delivery' ? '💵 COD' : '🏦 Bank'}
                </div>
              </div>

              {/* Total and Action */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb'
              }}>
                <div>
                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    marginBottom: '0.25rem'
                  }}>
                    Total Amount
                  </p>
                  <p style={{
                    fontSize: '1.25rem',
                    fontWeight: '700',
                    color: '#111827'
                  }}>
                    {formatCurrency(order.total)}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewOrder(order.id);
                  }}
                  style={{
                    borderRadius: '8px',
                    border: '2px solid #e5e7eb',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f59e0b';
                    e.currentTarget.style.borderColor = '#f59e0b';
                    e.currentTarget.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                    e.currentTarget.style.color = '#374151';
                  }}
                >
                  View Details
                </Button>
              </div>
            </div>
          ))
        ) : (
          <div style={{
            gridColumn: '1 / -1',
            textAlign: 'center',
            padding: '3rem',
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '2px dashed #d1d5db'
          }}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '1rem'
            }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                borderRadius: '50%',
                backgroundColor: '#f3f4f6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <ShoppingBag style={{ height: '1.5rem', width: '1.5rem', color: '#9ca3af' }} />
              </div>
              <div>
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  No orders found
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280'
                }}>
                  Adjust your search or filters to see more results.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
