'use client';

import {
  Clock,
  CheckCircle2,
  Truck,
  ShoppingBag,
  XCircle,
  AlertTriangle,
  CreditCard
} from 'lucide-react';

type OrderStatus =
  | 'PENDING_ASSIGNMENT'
  | 'ASSIGNED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'DELIVERED'
  | 'CANCELLED'
  | 'pending'
  | 'processing'
  | 'ready'
  | 'out-for-delivery'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'refunded';

type PaymentStatus =
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded';

interface OrderStatusBadgeProps {
  status: OrderStatus;
  size?: 'sm' | 'md';
}

interface PaymentStatusBadgeProps {
  status: PaymentStatus;
  size?: 'sm' | 'md';
}

export function OrderStatusBadge({ status, size = 'md' }: OrderStatusBadgeProps) {
  const sizeClasses = size === 'sm'
    ? 'text-xs px-2 py-0.5'
    : 'text-xs px-2.5 py-0.5';

  const getStatusConfig = (status: OrderStatus) => {
    switch (status) {
      case 'PENDING_ASSIGNMENT':
        return {
          label: 'Pending Assignment',
          icon: <Clock className="mr-1 h-3 w-3" />,
          classes: `bg-orange-100 text-orange-800 ${sizeClasses}`
        };
      case 'ASSIGNED':
        return {
          label: 'Assigned',
          icon: <ShoppingBag className="mr-1 h-3 w-3" />,
          classes: `bg-blue-100 text-blue-800 ${sizeClasses}`
        };
      case 'IN_PROGRESS':
        return {
          label: 'In Progress',
          icon: <ShoppingBag className="mr-1 h-3 w-3" />,
          classes: `bg-indigo-100 text-indigo-800 ${sizeClasses}`
        };
      case 'COMPLETED':
        return {
          label: 'Completed',
          icon: <CheckCircle2 className="mr-1 h-3 w-3" />,
          classes: `bg-green-100 text-green-800 ${sizeClasses}`
        };
      case 'DELIVERED':
        return {
          label: 'Delivered',
          icon: <Truck className="mr-1 h-3 w-3" />,
          classes: `bg-green-100 text-green-800 ${sizeClasses}`
        };
      case 'CANCELLED':
        return {
          label: 'Cancelled',
          icon: <XCircle className="mr-1 h-3 w-3" />,
          classes: `bg-red-100 text-red-800 ${sizeClasses}`
        };
      case 'pending':
        return {
          label: 'Pending',
          icon: <Clock className="mr-1 h-3 w-3" />,
          classes: `bg-yellow-100 text-yellow-800 ${sizeClasses}`
        };
      case 'processing':
        return {
          label: 'Processing',
          icon: <ShoppingBag className="mr-1 h-3 w-3" />,
          classes: `bg-blue-100 text-blue-800 ${sizeClasses}`
        };
      case 'ready':
        return {
          label: 'Ready',
          icon: <CheckCircle2 className="mr-1 h-3 w-3" />,
          classes: `bg-indigo-100 text-indigo-800 ${sizeClasses}`
        };
      case 'out-for-delivery':
        return {
          label: 'Out for Delivery',
          icon: <Truck className="mr-1 h-3 w-3" />,
          classes: `bg-purple-100 text-purple-800 ${sizeClasses}`
        };
      case 'delivered':
        return {
          label: 'Delivered',
          icon: <CheckCircle2 className="mr-1 h-3 w-3" />,
          classes: `bg-green-100 text-green-800 ${sizeClasses}`
        };
      case 'completed':
        return {
          label: 'Completed',
          icon: <CheckCircle2 className="mr-1 h-3 w-3" />,
          classes: `bg-green-100 text-green-800 ${sizeClasses}`
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          icon: <XCircle className="mr-1 h-3 w-3" />,
          classes: `bg-red-100 text-red-800 ${sizeClasses}`
        };
      case 'refunded':
        return {
          label: 'Refunded',
          icon: <AlertTriangle className="mr-1 h-3 w-3" />,
          classes: `bg-orange-100 text-orange-800 ${sizeClasses}`
        };
      default:
        return {
          label: status,
          icon: <Clock className="mr-1 h-3 w-3" />,
          classes: `bg-gray-100 text-gray-800 ${sizeClasses}`
        };
    }
  };

  const { label, icon, classes } = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center rounded-full font-medium ${classes}`}>
      {icon}
      {label}
    </span>
  );
}

export function PaymentStatusBadge({ status, size = 'md' }: PaymentStatusBadgeProps) {
  const sizeClasses = size === 'sm'
    ? 'text-xs px-2 py-0.5'
    : 'text-xs px-2.5 py-0.5';

  const getStatusConfig = (status: PaymentStatus) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          icon: <CreditCard className="mr-1 h-3 w-3" />,
          classes: `bg-yellow-100 text-yellow-800 ${sizeClasses}`
        };
      case 'paid':
        return {
          label: 'Paid',
          icon: <CheckCircle2 className="mr-1 h-3 w-3" />,
          classes: `bg-green-100 text-green-800 ${sizeClasses}`
        };
      case 'failed':
        return {
          label: 'Failed',
          icon: <XCircle className="mr-1 h-3 w-3" />,
          classes: `bg-red-100 text-red-800 ${sizeClasses}`
        };
      case 'refunded':
        return {
          label: 'Refunded',
          icon: <AlertTriangle className="mr-1 h-3 w-3" />,
          classes: `bg-orange-100 text-orange-800 ${sizeClasses}`
        };
      default:
        return {
          label: status,
          icon: <CreditCard className="mr-1 h-3 w-3" />,
          classes: `bg-gray-100 text-gray-800 ${sizeClasses}`
        };
    }
  };

  const { label, icon, classes } = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center rounded-full font-medium ${classes}`}>
      {icon}
      {label}
    </span>
  );
}
