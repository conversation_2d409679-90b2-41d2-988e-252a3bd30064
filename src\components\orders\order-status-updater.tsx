'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/lib/auth/auth-context';
import { toast } from 'sonner';

interface OrderStatusUpdaterProps {
  orderId: string;
  currentStatus: string;
  onStatusUpdate?: (newStatus: string) => void;
}

const statusOptions = [
  { value: 'ASSIGNED', label: 'Assigned', description: 'Order assigned to store' },
  { value: 'IN_PROGRESS', label: 'In Progress', description: 'Store is working on the order' },
  { value: 'COMPLETED', label: 'Completed', description: 'Order completed by store' },
  { value: 'DELIVERED', label: 'Delivered', description: 'Order delivered to customer' },
  { value: 'CANCELLED', label: 'Cancelled', description: 'Order cancelled' },
];

export function OrderStatusUpdater({ 
  orderId, 
  currentStatus, 
  onStatusUpdate 
}: OrderStatusUpdaterProps) {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [isUpdating, setIsUpdating] = useState(false);
  const { user } = useAuth();

  const handleStatusUpdate = async () => {
    if (!selectedStatus || selectedStatus === currentStatus) {
      toast.error('Please select a different status');
      return;
    }

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: selectedStatus,
          userRole: user?.role,
          storeId: user?.storeId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update order status');
      }

      toast.success('Order status updated successfully');
      onStatusUpdate?.(selectedStatus);
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update order status');
      setSelectedStatus(currentStatus); // Reset to original status
    } finally {
      setIsUpdating(false);
    }
  };

  // Filter status options based on current status and user role
  const getAvailableStatuses = () => {
    if (user?.role === 'ADMIN') {
      return statusOptions; // Admin can set any status
    }

    // Store managers can only progress forward
    switch (currentStatus) {
      case 'ASSIGNED':
        return statusOptions.filter(s => ['IN_PROGRESS', 'CANCELLED'].includes(s.value));
      case 'IN_PROGRESS':
        return statusOptions.filter(s => ['COMPLETED', 'CANCELLED'].includes(s.value));
      case 'COMPLETED':
        return statusOptions.filter(s => ['DELIVERED'].includes(s.value));
      case 'DELIVERED':
        return []; // Cannot change from delivered
      case 'CANCELLED':
        return []; // Cannot change from cancelled
      default:
        return statusOptions;
    }
  };

  const availableStatuses = getAvailableStatuses();

  if (availableStatuses.length === 0) {
    return (
      <div className="text-sm text-gray-500">
        No status updates available
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3">
      <Select value={selectedStatus} onValueChange={setSelectedStatus}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Select status" />
        </SelectTrigger>
        <SelectContent>
          {availableStatuses.map((status) => (
            <SelectItem key={status.value} value={status.value}>
              <div>
                <div className="font-medium">{status.label}</div>
                <div className="text-xs text-gray-500">{status.description}</div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Button
        onClick={handleStatusUpdate}
        disabled={isUpdating || selectedStatus === currentStatus}
        size="sm"
      >
        {isUpdating ? 'Updating...' : 'Update Status'}
      </Button>
    </div>
  );
}
