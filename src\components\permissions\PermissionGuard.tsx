'use client';

import { ReactNode } from 'react';
import { Permission } from '@/lib/permissions/permissions';
import { usePermission, useAnyPermission, useAllPermissions } from '@/lib/permissions/hooks';

interface PermissionGuardProps {
  children: ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallback?: ReactNode;
  showFallback?: boolean;
}

// Component to conditionally render content based on permissions
export function PermissionGuard({
  children,
  permission,
  permissions,
  requireAll = false,
  fallback = null,
  showFallback = false
}: PermissionGuardProps) {
  let hasAccess = false;

  if (permission) {
    hasAccess = usePermission(permission);
  } else if (permissions && permissions.length > 0) {
    if (requireAll) {
      hasAccess = useAllPermissions(permissions);
    } else {
      hasAccess = useAnyPermission(permissions);
    }
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  return null;
}

// Component for role-based access
interface RoleGuardProps {
  children: ReactNode;
  allowedRoles: string[];
  fallback?: ReactNode;
  showFallback?: boolean;
}

export function RoleGuard({
  children,
  allowedRoles,
  fallback = null,
  showFallback = false
}: RoleGuardProps) {
  const { user } = useAuth();

  const hasAccess = user && allowedRoles.includes(user.role);

  if (hasAccess) {
    return <>{children}</>;
  }

  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  return null;
}

// Component for store-based access
interface StoreGuardProps {
  children: ReactNode;
  storeId: string;
  fallback?: ReactNode;
  showFallback?: boolean;
}

export function StoreGuard({
  children,
  storeId,
  fallback = null,
  showFallback = false
}: StoreGuardProps) {
  const { user } = useAuth();

  let hasAccess = false;

  if (user) {
    // Admins and warehouse managers can access all stores
    if (user.role === 'ADMIN' || user.role === 'WAREHOUSE_MANAGER') {
      hasAccess = true;
    } else {
      // Store managers and staff can only access their assigned store
      hasAccess = user.storeId === storeId;
    }
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  return null;
}

// Higher-order component for page-level protection
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  permission: Permission,
  fallback?: ReactNode
) {
  return function PermissionProtectedComponent(props: T) {
    const hasAccess = usePermission(permission);

    if (!hasAccess) {
      return (
        fallback || (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px',
            textAlign: 'center',
            padding: '2rem'
          }}>
            <div style={{
              fontSize: '3rem',
              marginBottom: '1rem'
            }}>
              🔒
            </div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.5rem'
            }}>
              Access Denied
            </h2>
            <p style={{
              color: '#6b7280',
              fontSize: '1rem'
            }}>
              You don't have permission to access this page.
            </p>
          </div>
        )
      );
    }

    return <Component {...props} />;
  };
}

// Higher-order component for role-based page protection
export function withRole<T extends object>(
  Component: React.ComponentType<T>,
  allowedRoles: string[],
  fallback?: ReactNode
) {
  return function RoleProtectedComponent(props: T) {
    const { user } = useAuth();
    const hasAccess = user && allowedRoles.includes(user.role);

    if (!hasAccess) {
      return (
        fallback || (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px',
            textAlign: 'center',
            padding: '2rem'
          }}>
            <div style={{
              fontSize: '3rem',
              marginBottom: '1rem'
            }}>
              🚫
            </div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.5rem'
            }}>
              Insufficient Privileges
            </h2>
            <p style={{
              color: '#6b7280',
              fontSize: '1rem'
            }}>
              Your role doesn't have access to this functionality.
            </p>
          </div>
        )
      );
    }

    return <Component {...props} />;
  };
}

// Import useAuth hook
import { useAuth } from '@/lib/auth/auth-context';
