'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Minus, Plus, Trash } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  discount?: number;
  notes?: string;
}

interface CartItemProps {
  item: CartItem;
  onUpdateQuantity: (id: string, change: number) => void;
  onRemoveItem: (id: string) => void;
  onUpdateDiscount: (id: string, discount: number) => void;
  onUpdateNotes: (id: string, notes: string) => void;
}

export function CartItemComponent({
  item,
  onUpdateQuantity,
  onRemoveItem,
  onUpdateDiscount,
  onUpdateNotes
}: CartItemProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [discountValue, setDiscountValue] = useState(item.discount?.toString() || '');
  const [notes, setNotes] = useState(item.notes || '');

  const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDiscountValue(value);
  };

  const handleDiscountBlur = () => {
    const discount = parseFloat(discountValue);
    if (!isNaN(discount) && discount >= 0) {
      onUpdateDiscount(item.id, discount);
    } else {
      setDiscountValue(item.discount?.toString() || '');
    }
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNotes(value);
  };

  const handleNotesBlur = () => {
    onUpdateNotes(item.id, notes);
  };

  const itemTotal = (item.price * item.quantity) - (item.discount || 0);

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div
            className="cursor-pointer font-medium"
            onClick={() => setShowDetails(!showDetails)}
          >
            {item.name}
          </div>
          <div className="text-sm text-muted-foreground">
            {formatCurrency(item.price)} × {item.quantity}
            {item.discount && item.discount > 0 && (
              <span className="ml-2 text-green-600">
                (-{formatCurrency(item.discount)})
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-right font-medium">
            {formatCurrency(itemTotal)}
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onUpdateQuantity(item.id, -1)}
            >
              <Minus className="h-4 w-4" />
              <span className="sr-only">Decrease</span>
            </Button>
            <span className="w-8 text-center">{item.quantity}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onUpdateQuantity(item.id, 1)}
            >
              <Plus className="h-4 w-4" />
              <span className="sr-only">Increase</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-destructive"
              onClick={() => onRemoveItem(item.id)}
            >
              <Trash className="h-4 w-4" />
              <span className="sr-only">Remove</span>
            </Button>
          </div>
        </div>
      </div>

      {showDetails && (
        <div className="rounded-md bg-muted/50 p-2 space-y-2">
          <div className="flex items-center gap-2">
            <label htmlFor={`discount-${item.id}`} className="text-xs font-medium">
              Discount:
            </label>
            <div className="relative flex-1">
              <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                $
              </span>
              <Input
                id={`discount-${item.id}`}
                type="number"
                min="0"
                step="0.01"
                value={discountValue}
                onChange={handleDiscountChange}
                onBlur={handleDiscountBlur}
                className="h-7 pl-5 text-xs"
              />
            </div>
          </div>
          <div>
            <label htmlFor={`notes-${item.id}`} className="text-xs font-medium">
              Notes:
            </label>
            <Input
              id={`notes-${item.id}`}
              value={notes}
              onChange={handleNotesChange}
              onBlur={handleNotesBlur}
              placeholder="Special instructions"
              className="h-7 mt-1 text-xs"
            />
          </div>
        </div>
      )}
    </div>
  );
}
