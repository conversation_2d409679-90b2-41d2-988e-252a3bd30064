'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { UserRound, Plus, Search, X, Loader2 } from 'lucide-react';

// Customer interface
interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  loyaltyPoints?: number;
  totalSpent?: number;
}

interface CustomerSelectorProps {
  onSelectCustomer: (customer: Customer | null) => void;
  selectedCustomer: Customer | null;
}

export function CustomerSelector({ onSelectCustomer, selectedCustomer }: CustomerSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: '',
  });

  // Fetch customers when the dropdown is opened
  useEffect(() => {
    if (isOpen && customers.length === 0) {
      fetchCustomers();
    }
  }, [isOpen]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/pos/customers');

      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }

      const data = await response.json();
      setCustomers(data);
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (customer.phone && customer.phone.includes(searchTerm))
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewCustomer({ ...newCustomer, [name]: value });
  };

  const handleAddCustomer = async () => {
    if (newCustomer.name) {
      try {
        setLoading(true);
        setError(null);

        // Send the new customer data to the API
        const response = await fetch('/api/pos/customers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: newCustomer.name,
            email: newCustomer.email,
            phone: newCustomer.phone
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create customer');
        }

        const customer = await response.json();

        setCustomers([...customers, customer]);
        onSelectCustomer(customer);
        setIsOpen(false);
        setShowAddForm(false);
        setNewCustomer({
          name: '',
          email: '',
          phone: '',
        });
      } catch (err) {
        console.error('Error creating customer:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        alert('Failed to add customer: ' + (err instanceof Error ? err.message : 'An error occurred'));
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSelectCustomer = (customer: Customer) => {
    onSelectCustomer(customer);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleClearCustomer = () => {
    onSelectCustomer(null);
  };

  return (
    <div className="relative">
      {selectedCustomer ? (
        <div className="flex items-center justify-between rounded-md border bg-background p-2">
          <div className="flex items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
              <UserRound className="h-4 w-4" />
            </div>
            <div className="ml-2">
              <p className="text-sm font-medium">{selectedCustomer.name}</p>
              <p className="text-xs text-muted-foreground">
                {selectedCustomer.phone || selectedCustomer.email}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClearCustomer}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={() => setIsOpen(true)}
        >
          <UserRound className="mr-2 h-4 w-4" />
          Select Customer
        </Button>
      )}

      {isOpen && (
        <div className="absolute left-0 top-full z-10 mt-1 w-full rounded-md border bg-background p-2 shadow-md">
          <div className="mb-2 flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setShowAddForm(!showAddForm);
                setSearchTerm('');
              }}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {showAddForm ? (
            <div className="space-y-2 rounded-md border p-2">
              <h3 className="text-sm font-medium">Add New Customer</h3>
              <Input
                placeholder="Name"
                name="name"
                value={newCustomer.name}
                onChange={handleInputChange}
                className="mb-2"
              />
              <Input
                placeholder="Email"
                name="email"
                type="email"
                value={newCustomer.email}
                onChange={handleInputChange}
                className="mb-2"
              />
              <Input
                placeholder="Phone"
                name="phone"
                value={newCustomer.phone}
                onChange={handleInputChange}
                className="mb-2"
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddForm(false)}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleAddCustomer}
                  disabled={!newCustomer.name || loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    'Add'
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="max-h-60 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  <span className="ml-2">Loading customers...</span>
                </div>
              ) : error ? (
                <div className="p-2 text-center text-sm text-red-500">
                  {error}
                  <Button
                    variant="link"
                    size="sm"
                    onClick={fetchCustomers}
                    className="ml-2"
                  >
                    Retry
                  </Button>
                </div>
              ) : filteredCustomers.length > 0 ? (
                filteredCustomers.map(customer => (
                  <div
                    key={customer.id}
                    className="flex cursor-pointer items-center justify-between rounded-md p-2 hover:bg-muted"
                    onClick={() => handleSelectCustomer(customer)}
                  >
                    <div className="flex items-center">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                        <UserRound className="h-4 w-4" />
                      </div>
                      <div className="ml-2">
                        <p className="text-sm font-medium">{customer.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {customer.phone || customer.email}
                        </p>
                      </div>
                    </div>
                    {customer.loyaltyPoints !== undefined && (
                      <div className="text-right text-xs">
                        <p className="font-medium">{customer.loyaltyPoints} points</p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="p-2 text-center text-sm text-muted-foreground">
                  No customers found
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
