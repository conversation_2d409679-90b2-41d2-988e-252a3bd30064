'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CreditCard, Banknote, Smartphone, Receipt, X, Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  loyaltyPoints?: number;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  totalAmount: number;
  onCompletePayment: (paymentDetails: {
    method: string;
    amountPaid: number;
    change: number;
    reference?: string;
  }) => void;
  customer: Customer | null;
  isSubmitting?: boolean;
}

export function PaymentModal({
  isOpen,
  onClose,
  totalAmount,
  onCompletePayment,
  customer,
  isSubmitting = false
}: PaymentModalProps) {
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [amountReceived, setAmountReceived] = useState('');
  const [reference, setReference] = useState('');
  const [applyLoyaltyDiscount, setApplyLoyaltyDiscount] = useState(false);
  const [loyaltyDiscount, setLoyaltyDiscount] = useState(0);
  const [finalAmount, setFinalAmount] = useState(totalAmount);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setPaymentMethod('cash');
      setAmountReceived(totalAmount.toFixed(2));
      setReference('');
      setApplyLoyaltyDiscount(false);
      setLoyaltyDiscount(0);
      setFinalAmount(totalAmount);
    }
  }, [isOpen, totalAmount]);

  // Calculate loyalty discount when customer changes or loyalty toggle changes
  useEffect(() => {
    if (customer?.loyaltyPoints && applyLoyaltyDiscount) {
      // Example: 10 points = $1 discount
      const maxDiscount = Math.floor(customer.loyaltyPoints / 10);
      // Don't allow discount greater than total
      const appliedDiscount = Math.min(maxDiscount, totalAmount);
      setLoyaltyDiscount(appliedDiscount);
      setFinalAmount(totalAmount - appliedDiscount);
    } else {
      setLoyaltyDiscount(0);
      setFinalAmount(totalAmount);
    }
  }, [customer, applyLoyaltyDiscount, totalAmount]);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAmountReceived(e.target.value);
  };

  const handleReferenceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setReference(e.target.value);
  };

  const handleCompletePayment = () => {
    const amountPaid = parseFloat(amountReceived) || finalAmount;
    const change = paymentMethod === 'cash' ? amountPaid - finalAmount : 0;

    onCompletePayment({
      method: paymentMethod,
      amountPaid,
      change,
      reference: reference || undefined,
    });
  };

  if (!isOpen) return null;

  const paymentMethods = [
    { id: 'cash', name: 'Cash', icon: <Banknote className="h-5 w-5" /> },
    { id: 'card', name: 'Credit/Debit Card', icon: <CreditCard className="h-5 w-5" /> },
    { id: 'mobile', name: 'Mobile Payment', icon: <Smartphone className="h-5 w-5" /> },
  ];

  const change = paymentMethod === 'cash'
    ? Math.max(0, parseFloat(amountReceived) - finalAmount)
    : 0;

  const isPaymentValid =
    (paymentMethod === 'cash' && parseFloat(amountReceived) >= finalAmount) ||
    (paymentMethod !== 'cash');

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md rounded-lg bg-background p-6 shadow-lg">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-medium">Payment</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="mb-6 rounded-md bg-muted p-4">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>{formatCurrency(totalAmount)}</span>
          </div>

          {customer && customer.loyaltyPoints && customer.loyaltyPoints > 0 && (
            <div className="mt-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="applyLoyaltyPoints"
                  checked={applyLoyaltyDiscount}
                  onChange={() => setApplyLoyaltyDiscount(!applyLoyaltyDiscount)}
                  className="mr-2 h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="applyLoyaltyPoints" className="text-sm">
                  Apply loyalty points ({customer.loyaltyPoints} points available)
                </label>
              </div>

              {applyLoyaltyDiscount && loyaltyDiscount > 0 && (
                <div className="mt-1 flex justify-between text-sm text-green-600">
                  <span>Loyalty Discount:</span>
                  <span>-{formatCurrency(loyaltyDiscount)}</span>
                </div>
              )}
            </div>
          )}

          <div className="mt-2 border-t pt-2 font-medium">
            <div className="flex justify-between">
              <span>Total:</span>
              <span>{formatCurrency(finalAmount)}</span>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label className="mb-2 block text-sm font-medium">
            Payment Method
          </label>
          <div className="grid grid-cols-3 gap-2">
            {paymentMethods.map(method => (
              <Button
                key={method.id}
                type="button"
                variant={paymentMethod === method.id ? 'default' : 'outline'}
                className="flex flex-col items-center justify-center py-3"
                onClick={() => setPaymentMethod(method.id)}
              >
                {method.icon}
                <span className="mt-1 text-xs">{method.name}</span>
              </Button>
            ))}
          </div>
        </div>

        {paymentMethod === 'cash' && (
          <div className="mb-4 space-y-4">
            <div>
              <label htmlFor="amountReceived" className="mb-2 block text-sm font-medium">
                Amount Received
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                  $
                </span>
                <Input
                  id="amountReceived"
                  type="number"
                  min={finalAmount}
                  step="0.01"
                  value={amountReceived}
                  onChange={handleAmountChange}
                  className="pl-7"
                  required
                />
              </div>
            </div>

            {parseFloat(amountReceived) >= finalAmount && (
              <div className="rounded-md bg-muted p-3">
                <div className="flex justify-between font-medium">
                  <span>Change:</span>
                  <span>{formatCurrency(change)}</span>
                </div>
              </div>
            )}
          </div>
        )}

        {(paymentMethod === 'card' || paymentMethod === 'mobile') && (
          <div className="mb-4">
            <label htmlFor="reference" className="mb-2 block text-sm font-medium">
              Reference / Transaction ID
            </label>
            <Input
              id="reference"
              value={reference}
              onChange={handleReferenceChange}
              placeholder="Optional reference number"
            />
          </div>
        )}

        <div className="flex gap-2">
          <Button
            className="flex-1"
            onClick={handleCompletePayment}
            disabled={!isPaymentValid || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Receipt className="mr-2 h-4 w-4" />
                Complete Payment
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
