'use client';

import { formatCurrency } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  image?: string;
  description?: string;
  inStock?: boolean;
}

interface ProductCardProps {
  product: Product;
  onAddToCart: (product: Product) => void;
}

export function ProductCard({ product, onAddToCart }: ProductCardProps) {
  const isOutOfStock = product.inStock === false;
  
  return (
    <div
      className={`flex cursor-pointer flex-col justify-between rounded-lg border bg-card p-4 shadow-sm transition-colors hover:bg-muted/50 ${
        isOutOfStock ? 'opacity-50' : ''
      }`}
      onClick={() => !isOutOfStock && onAddToCart(product)}
    >
      {product.image && (
        <div className="mb-2 h-24 w-full overflow-hidden rounded-md">
          <img 
            src={product.image} 
            alt={product.name} 
            className="h-full w-full object-cover"
            onError={(e) => {
              (e.target as HTMLImageElement).src = 'https://placehold.co/100x100?text=No+Image';
            }}
          />
        </div>
      )}
      <div>
        <h3 className="font-medium line-clamp-1">{product.name}</h3>
        {product.description && (
          <p className="text-xs text-muted-foreground line-clamp-2">{product.description}</p>
        )}
      </div>
      <div className="mt-2 flex items-center justify-between">
        <div className="font-medium">
          {formatCurrency(product.price)}
        </div>
        {isOutOfStock && (
          <span className="rounded-full bg-destructive/10 px-2 py-0.5 text-xs font-medium text-destructive">
            Out of stock
          </span>
        )}
      </div>
    </div>
  );
}
