'use client';

import { formatCurrency, formatDate } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Printer, Download } from 'lucide-react';

interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  discount?: number;
  notes?: string;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  loyaltyPoints?: number;
}

interface ReceiptProps {
  items: CartItem[];
  customer: Customer | null;
  paymentDetails: {
    method: string;
    amountPaid: number;
    change: number;
    reference?: string;
  };
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  receiptNumber: string;
  date: string;
  onClose: () => void;
  onPrint: () => void;
}

export function Receipt({
  items,
  customer,
  paymentDetails,
  subtotal,
  tax,
  discount,
  total,
  receiptNumber,
  date,
  onClose,
  onPrint
}: ReceiptProps) {
  const handleDownloadPDF = () => {
    // In a real app, this would generate a PDF
    alert('PDF download functionality would be implemented here');
  };
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md rounded-lg bg-background p-6 shadow-lg">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-medium">Receipt</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={onPrint}>
              <Printer className="h-4 w-4" />
              <span className="sr-only">Print</span>
            </Button>
            <Button variant="outline" size="icon" onClick={handleDownloadPDF}>
              <Download className="h-4 w-4" />
              <span className="sr-only">Download</span>
            </Button>
          </div>
        </div>
        
        <div className="mb-6 rounded-md border p-4 print:border-none">
          <div className="text-center">
            <h3 className="text-lg font-bold">Bakery Market</h3>
            <p className="text-sm text-muted-foreground">123 Bakery Street, City</p>
            <p className="text-sm text-muted-foreground">Tel: +1234567890</p>
          </div>
          
          <div className="my-4 border-t border-dashed pt-4">
            <div className="flex justify-between text-sm">
              <span>Receipt #:</span>
              <span>{receiptNumber}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Date:</span>
              <span>{formatDate(date)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Time:</span>
              <span>{new Date(date).toLocaleTimeString()}</span>
            </div>
            
            {customer && (
              <div className="mt-2 border-t pt-2 text-sm">
                <div className="font-medium">Customer:</div>
                <div>{customer.name}</div>
                {customer.phone && <div>{customer.phone}</div>}
              </div>
            )}
          </div>
          
          <div className="my-4 border-t border-dashed pt-4">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="pb-1 text-left">Item</th>
                  <th className="pb-1 text-right">Qty</th>
                  <th className="pb-1 text-right">Price</th>
                  <th className="pb-1 text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                {items.map(item => {
                  const itemTotal = (item.price * item.quantity) - (item.discount || 0);
                  return (
                    <tr key={item.id} className="border-b">
                      <td className="py-1">
                        {item.name}
                        {item.notes && (
                          <div className="text-xs text-muted-foreground">
                            Note: {item.notes}
                          </div>
                        )}
                      </td>
                      <td className="py-1 text-right">{item.quantity}</td>
                      <td className="py-1 text-right">{formatCurrency(item.price)}</td>
                      <td className="py-1 text-right">
                        {formatCurrency(itemTotal)}
                        {item.discount && item.discount > 0 && (
                          <div className="text-xs text-green-600">
                            (-{formatCurrency(item.discount)})
                          </div>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          <div className="border-t border-dashed pt-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal:</span>
              <span>{formatCurrency(subtotal)}</span>
            </div>
            {tax > 0 && (
              <div className="flex justify-between text-sm">
                <span>Tax (10%):</span>
                <span>{formatCurrency(tax)}</span>
              </div>
            )}
            {discount > 0 && (
              <div className="flex justify-between text-sm text-green-600">
                <span>Discount:</span>
                <span>-{formatCurrency(discount)}</span>
              </div>
            )}
            <div className="mt-1 flex justify-between border-t font-medium">
              <span>Total:</span>
              <span>{formatCurrency(total)}</span>
            </div>
          </div>
          
          <div className="mt-4 border-t border-dashed pt-2">
            <div className="flex justify-between text-sm">
              <span>Payment Method:</span>
              <span>{paymentDetails.method === 'cash' ? 'Cash' : 
                     paymentDetails.method === 'card' ? 'Card' : 'Mobile Payment'}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Amount Paid:</span>
              <span>{formatCurrency(paymentDetails.amountPaid)}</span>
            </div>
            {paymentDetails.method === 'cash' && paymentDetails.change > 0 && (
              <div className="flex justify-between text-sm">
                <span>Change:</span>
                <span>{formatCurrency(paymentDetails.change)}</span>
              </div>
            )}
            {paymentDetails.reference && (
              <div className="flex justify-between text-sm">
                <span>Reference:</span>
                <span>{paymentDetails.reference}</span>
              </div>
            )}
          </div>
          
          <div className="mt-6 text-center">
            <p className="text-sm font-medium">Thank you for your purchase!</p>
            <p className="text-xs text-muted-foreground">www.bakerymarket.com</p>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
