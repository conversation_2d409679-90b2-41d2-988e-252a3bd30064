'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash, Image, Check, X, Tag } from 'lucide-react';

interface CategoryProductInfo {
  productContains: Array<{ label: string; value: string; placeholder?: string }>;
  careInstructions: string[];
  badges: string[];
  showNameField: boolean;
  weightLabel: string;
  specifications: Array<{ label: string; placeholder: string; required: boolean }>;
}

interface Category {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  slug: string;
  displayOrder: number;
  isActive: boolean;
  productCount: number;
  createdAt: string;
  updatedAt: string;
  productInfo?: CategoryProductInfo;
}

interface CategoryManagementProps {
  categories: Category[];
  onAddCategory: (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>) => void;
  onUpdateCategory: (id: string, category: Partial<Category>) => void;
  onDeleteCategory: (id: string) => void;
}

export function CategoryManagement({
  categories,
  onAddCategory,
  onUpdateCategory,
  onDeleteCategory
}: CategoryManagementProps) {
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const getDefaultProductInfo = (categoryName: string): CategoryProductInfo => {
    const name = categoryName.toLowerCase();

    if (name.includes('cake') || name.includes('dessert') || name.includes('sweet')) {
      return {
        productContains: [
          { label: 'Flavour', value: '', placeholder: 'e.g., Chocolate, Vanilla, Strawberry' },
          { label: 'Shape', value: '', placeholder: 'e.g., Round, Square, Heart' },
          { label: 'Type', value: '', placeholder: 'e.g., Cream Cake, Fondant Cake' },
          { label: 'Ingredients', value: '', placeholder: 'List main ingredients' },
          { label: 'Allergens', value: '', placeholder: 'e.g., Milk, Nuts, Wheat (Gluten)' }
        ],
        careInstructions: [
          'Store in refrigerator until serving',
          'Bring to room temperature 30 minutes before serving',
          'Use a serrated knife for clean slices',
          'Consume within 24 hours for best freshness'
        ],
        badges: ['EGGLESS', 'FRESH', 'QUALITY', 'FSSAI'],
        showNameField: true,
        weightLabel: 'Weight',
        specifications: [
          { label: 'Shelf Life', placeholder: 'e.g., 2-3 days', required: false },
          { label: 'Serving Size', placeholder: 'e.g., 8-10 people', required: false },
          { label: 'Storage Temperature', placeholder: 'e.g., 2-8°C', required: false }
        ]
      };
    }

    if (name.includes('flower') || name.includes('bouquet') || name.includes('rose')) {
      return {
        productContains: [
          { label: 'Flower Type', value: '', placeholder: 'e.g., Roses, Lilies, Carnations' },
          { label: 'Color', value: '', placeholder: 'e.g., Red, Pink, Mixed' },
          { label: 'Arrangement', value: '', placeholder: 'e.g., Bouquet, Basket, Vase' },
          { label: 'Stems Count', value: '', placeholder: 'e.g., 12 stems, 24 stems' },
          { label: 'Wrapping', value: '', placeholder: 'e.g., Premium Paper, Cellophane' }
        ],
        careInstructions: [
          'Cut stems at 45° angle under running water',
          'Use clean vase with fresh, cool water',
          'Change water every 2-3 days',
          'Keep away from direct sunlight and heat'
        ],
        badges: ['FRESH', 'PREMIUM', 'HAND-PICKED'],
        showNameField: false,
        weightLabel: 'Size',
        specifications: [
          { label: 'Freshness', placeholder: 'e.g., 5-7 days', required: false },
          { label: 'Occasion', placeholder: 'e.g., Birthday, Anniversary', required: false }
        ]
      };
    }

    if (name.includes('plant') || name.includes('green') || name.includes('indoor')) {
      return {
        productContains: [
          { label: 'Plant Type', value: '', placeholder: 'e.g., Indoor Plant, Succulent' },
          { label: 'Pot Material', value: '', placeholder: 'e.g., Ceramic, Plastic, Terracotta' },
          { label: 'Light Requirement', value: '', placeholder: 'e.g., Bright Indirect Light' },
          { label: 'Watering', value: '', placeholder: 'e.g., Weekly, Bi-weekly' },
          { label: 'Care Level', value: '', placeholder: 'e.g., Easy, Moderate, Expert' }
        ],
        careInstructions: [
          'Place in bright, indirect sunlight',
          'Water when top inch of soil feels dry',
          'Ensure pot has proper drainage holes',
          'Fertilize monthly during growing season'
        ],
        badges: ['AIR-PURIFYING', 'LOW-MAINTENANCE', 'INDOOR'],
        showNameField: false,
        weightLabel: 'Size',
        specifications: [
          { label: 'Growth Rate', placeholder: 'e.g., Slow, Medium, Fast', required: false },
          { label: 'Mature Size', placeholder: 'e.g., 30-50 cm', required: false }
        ]
      };
    }

    // Default for gifts and other categories
    return {
      productContains: [
        { label: 'Material', value: '', placeholder: 'e.g., Wood, Metal, Fabric' },
        { label: 'Dimensions', value: '', placeholder: 'e.g., 20x15x10 cm' },
        { label: 'Color', value: '', placeholder: 'e.g., Red, Blue, Multi-color' },
        { label: 'Packaging', value: '', placeholder: 'e.g., Gift Box, Bag' }
      ],
      careInstructions: [
        'Handle with care during unpacking',
        'Store in cool, dry place',
        'Clean gently when needed',
        'Keep away from moisture'
      ],
      badges: ['PREMIUM', 'QUALITY', 'GIFT-READY'],
      showNameField: false,
      weightLabel: 'Size',
      specifications: [
        { label: 'Age Group', placeholder: 'e.g., 3+ years, Adults', required: false },
        { label: 'Occasion', placeholder: 'e.g., Birthday, Anniversary', required: false }
      ]
    };
  };

  const [formData, setFormData] = useState<Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>>({
    name: '',
    description: '',
    imageUrl: '',
    slug: '',
    displayOrder: 0,
    isActive: true,
    productInfo: getDefaultProductInfo('')
  });
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData({ ...formData, [name]: checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      console.log('📁 Category: File selected:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        console.error('❌ Category: File too large:', file.size);
        alert('File size must be less than 5MB');
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        console.error('❌ Category: Invalid file type:', file.type);
        alert('Please select an image file (JPG, PNG, GIF, etc.)');
        return;
      }

      console.log('✅ Category: File validation passed');
      setImageFile(file);

      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Data = reader.result as string;
        console.log('📊 Category: FileReader completed, base64 length:', base64Data.length);
        setImagePreview(base64Data);

        // Upload the image to the server when selected
        uploadImage(base64Data);
      };
      reader.onerror = (error) => {
        console.error('❌ Category: FileReader error:', error);
        alert('Error reading file. Please try again.');
      };
      reader.readAsDataURL(file);
    } else {
      console.log('⚠️ Category: No file selected');
    }
  };

  const uploadImage = async (base64Data: string) => {
    try {
      console.log('🖼️ Category: Starting image upload');
      console.log('📊 Category: Image data length:', base64Data.length);
      console.log('📊 Category: Image format:', base64Data.substring(0, 30) + '...');

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: base64Data,
          folder: 'categories'
        }),
      });

      console.log('📊 Category: Upload response status:', response.status);

      const data = await response.json();
      console.log('📊 Category: Upload response data:', {
        success: data.success,
        hasImageUrl: !!data.imageUrl,
        uploadMethod: data.uploadMethod,
        error: data.error
      });

      if (!response.ok) {
        console.warn('❌ Category: Image upload response not OK:', response.status);
        console.warn('❌ Category: Error details:', data);
        // Still use the imageUrl if provided, even in error cases
        if (data && data.imageUrl) {
          console.log('⚠️ Category: Using imageUrl despite error');
          setFormData(prev => ({ ...prev, imageUrl: data.imageUrl }));
          if (data.warning) {
            console.warn('⚠️ Category: Upload warning:', data.warning);
          }
        }
        return;
      }

      console.log('✅ Category: Image upload successful');
      console.log('🔗 Category: Setting imageUrl:', data.imageUrl ? data.imageUrl.substring(0, 50) + '...' : 'No URL');
      setFormData(prev => ({ ...prev, imageUrl: data.imageUrl }));
    } catch (error) {
      console.error('❌ Category: Error uploading image:', error);
      // Keep the preview but don't update the form data
      // Use a placeholder image
      setFormData(prev => ({ ...prev, imageUrl: '/placeholder-categories.jpg' }));
    }
  };

  const generateSlug = (name: string) => {
    return name.toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-'); // Replace multiple hyphens with a single hyphen
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setFormData({
      ...formData,
      name,
      slug: generateSlug(name),
      productInfo: getDefaultProductInfo(name)
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingId) {
        await onUpdateCategory(editingId, formData);
      } else {
        await onAddCategory(formData);
      }

      resetForm();
    } catch (error) {
      console.error('Error submitting category form:', error);
      alert('There was an error saving the category. Please try again.');
      // Don't reset the form so the user can try again
    }
  };

  const handleEdit = (category: Category) => {
    setFormData({
      name: category.name,
      description: category.description || '',
      imageUrl: category.imageUrl || '',
      slug: category.slug,
      displayOrder: category.displayOrder,
      isActive: category.isActive,
      productInfo: category.productInfo || getDefaultProductInfo(category.name)
    });
    setEditingId(category.id);
    setShowForm(true);

    if (category.imageUrl) {
      setImagePreview(category.imageUrl);
    }
  };

  const handleDelete = (id: string, productCount: number) => {
    let confirmMessage = 'Are you sure you want to delete this category?';

    if (productCount > 0) {
      confirmMessage = `This category has ${productCount} product(s). Deleting it will move all products to "Uncategorized". Are you sure you want to continue?`;
    }

    if (window.confirm(confirmMessage)) {
      onDeleteCategory(id);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      imageUrl: '',
      slug: '',
      displayOrder: 0,
      isActive: true,
      productInfo: getDefaultProductInfo('')
    });
    setEditingId(null);
    setShowForm(false);
    setImageFile(null);
    setImagePreview(null);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header Section */}
      <div style={{
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        borderRadius: '12px',
        padding: '2rem',
        color: 'white',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{ position: 'relative', zIndex: 2 }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: '700', marginBottom: '0.5rem' }}>
            Category Management
          </h2>
          <p style={{ opacity: 0.9, fontSize: '0.875rem' }}>
            {categories.length} categories • {categories.filter(c => c.isActive).length} active
          </p>
        </div>
        <div style={{
          position: 'absolute',
          top: '-50%',
          right: '-10%',
          width: '200px',
          height: '200px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          zIndex: 1
        }} />
        <Button
          onClick={() => setShowForm(!showForm)}
          style={{
            position: 'absolute',
            top: '1.5rem',
            right: '1.5rem',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            color: 'white',
            borderRadius: '8px',
            padding: '0.5rem 1rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            transition: 'all 0.2s',
            zIndex: 10
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
          }}
        >
          <Plus style={{ height: '1rem', width: '1rem' }} />
          Add Category
        </Button>
      </div>

      {showForm && (
        <div style={{
          background: 'white',
          borderRadius: '16px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          border: '1px solid #e5e7eb',
          overflow: 'hidden'
        }}>
          {/* Form Header */}
          <div style={{
            background: 'linear-gradient(135deg, #5F9EA0 0%, #4a8a8d 100%)',
            padding: '1.5rem 2rem',
            color: 'white'
          }}>
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <Tag style={{ height: '1.25rem', width: '1.25rem' }} />
              {editingId ? 'Edit Category' : 'Create New Category'}
            </h3>
            <p style={{
              margin: '0.5rem 0 0 0',
              opacity: 0.9,
              fontSize: '0.875rem'
            }}>
              {editingId ? 'Update category information and product settings' : 'Set up a new category with smart product information defaults'}
            </p>
          </div>

          <form onSubmit={handleSubmit} style={{ padding: '2rem' }}>
            {/* Basic Information Section */}
            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '1rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  background: '#5F9EA0'
                }} />
                Basic Information
              </h4>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '1.5rem'
              }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    Category Name *
                  </label>
                  <Input
                    value={formData.name}
                    onChange={handleNameChange}
                    placeholder="e.g., Cakes, Flowers, Plants"
                    required
                    style={{
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '2px solid #e5e7eb',
                      fontSize: '0.875rem',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#5F9EA0';
                      e.target.style.boxShadow = '0 0 0 3px rgba(95, 158, 160, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#e5e7eb';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    URL Slug *
                  </label>
                  <Input
                    value={formData.slug}
                    onChange={handleInputChange}
                    name="slug"
                    placeholder="category-url-slug"
                    required
                    style={{
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '2px solid #e5e7eb',
                      fontSize: '0.875rem',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#5F9EA0';
                      e.target.style.boxShadow = '0 0 0 3px rgba(95, 158, 160, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#e5e7eb';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    Used in website URLs (auto-generated)
                  </p>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    Display Order
                  </label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.displayOrder}
                    onChange={handleInputChange}
                    name="displayOrder"
                    placeholder="0"
                    style={{
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '2px solid #e5e7eb',
                      fontSize: '0.875rem',
                      transition: 'all 0.2s'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#5F9EA0';
                      e.target.style.boxShadow = '0 0 0 3px rgba(95, 158, 160, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#e5e7eb';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    Lower numbers appear first
                  </p>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    Status
                  </label>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '2px solid #e5e7eb',
                    background: '#f9fafb'
                  }}>
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      name="isActive"
                      style={{
                        width: '1.25rem',
                        height: '1.25rem',
                        accentColor: '#5F9EA0'
                      }}
                    />
                    <span style={{
                      fontSize: '0.875rem',
                      color: '#374151',
                      fontWeight: '500'
                    }}>
                      Active (visible on website)
                    </span>
                  </div>
                </div>
              </div>

              <div style={{ marginTop: '1.5rem' }}>
                <label style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  display: 'block',
                  marginBottom: '0.5rem'
                }}>
                  Description
                </label>
                <textarea
                  value={formData.description || ''}
                  onChange={handleInputChange}
                  name="description"
                  placeholder="Enter a brief description of this category..."
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '8px',
                    border: '2px solid #e5e7eb',
                    fontSize: '0.875rem',
                    resize: 'vertical',
                    transition: 'all 0.2s',
                    fontFamily: 'inherit'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#5F9EA0';
                    e.target.style.boxShadow = '0 0 0 3px rgba(95, 158, 160, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#e5e7eb';
                    e.target.style.boxShadow = 'none';
                  }}
                />
              </div>

              {/* Image Upload Section */}
              <div style={{ marginTop: '1.5rem' }}>
                <label style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  display: 'block',
                  marginBottom: '0.5rem'
                }}>
                  Category Image
                </label>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem'
                }}>
                  <div style={{ flex: 1 }}>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      style={{
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '2px dashed #d1d5db',
                        fontSize: '0.875rem',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.borderColor = '#5F9EA0';
                        e.target.style.background = '#f0f9ff';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.borderColor = '#d1d5db';
                        e.target.style.background = 'white';
                      }}
                    />
                    <p style={{
                      fontSize: '0.75rem',
                      color: '#6b7280',
                      margin: '0.5rem 0 0 0'
                    }}>
                      Recommended: 800x600px, max 2MB (JPG, PNG)
                    </p>
                  </div>

                  {imagePreview && (
                    <div style={{
                      position: 'relative',
                      width: '80px',
                      height: '80px',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      border: '2px solid #e5e7eb'
                    }}>
                      <img
                        src={imagePreview}
                        alt="Preview"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setImagePreview(null);
                          setImageFile(null);
                          setFormData({ ...formData, imageUrl: '' });
                        }}
                        style={{
                          position: 'absolute',
                          top: '4px',
                          right: '4px',
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          background: 'rgba(0, 0, 0, 0.7)',
                          color: 'white',
                          border: 'none',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '12px'
                        }}
                      >
                        ×
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Product Information Section */}
            {formData.name && (
              <div style={{
                borderTop: '2px solid #f3f4f6',
                paddingTop: '2rem',
                marginTop: '2rem'
              }}>
                <h4 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '1rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    background: '#10b981'
                  }} />
                  Product Information Settings
                </h4>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  marginBottom: '1.5rem',
                  lineHeight: '1.5'
                }}>
                  Configure how products in this category will be displayed on the website. These settings are automatically applied based on your category name.
                </p>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  {/* Weight Label and Name Field */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                    gap: '1.5rem'
                  }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                      <label style={{
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: '#374151'
                      }}>
                        Weight/Size Label
                      </label>
                      <Input
                        value={formData.productInfo?.weightLabel || ''}
                        onChange={(e) => setFormData({
                          ...formData,
                          productInfo: {
                            ...formData.productInfo!,
                            weightLabel: e.target.value
                          }
                        })}
                        placeholder="e.g., Weight, Size, Volume"
                        style={{
                          padding: '0.75rem',
                          borderRadius: '8px',
                          border: '2px solid #e5e7eb',
                          fontSize: '0.875rem',
                          transition: 'all 0.2s'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#10b981';
                          e.target.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e5e7eb';
                          e.target.style.boxShadow = 'none';
                        }}
                      />
                    </div>

                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                      <label style={{
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: '#374151'
                      }}>
                        Name Customization
                      </label>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '2px solid #e5e7eb',
                        background: '#f9fafb'
                      }}>
                        <input
                          type="checkbox"
                          checked={formData.productInfo?.showNameField || false}
                          onChange={(e) => setFormData({
                            ...formData,
                            productInfo: {
                              ...formData.productInfo!,
                              showNameField: e.target.checked
                            }
                          })}
                          style={{
                            width: '1.25rem',
                            height: '1.25rem',
                            accentColor: '#10b981'
                          }}
                        />
                        <span style={{
                          fontSize: '0.875rem',
                          color: '#374151',
                          fontWeight: '500'
                        }}>
                          Allow name customization (e.g., "Name on Cake")
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Product Badges */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                    <label style={{
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151'
                    }}>
                      Product Badges
                    </label>
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.5rem',
                      marginBottom: '0.75rem'
                    }}>
                      {formData.productInfo?.badges.map((badge, index) => (
                        <span key={index} style={{
                          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                          color: 'white',
                          fontSize: '0.75rem',
                          fontWeight: '600',
                          padding: '0.5rem 0.75rem',
                          borderRadius: '20px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          boxShadow: '0 2px 4px rgba(16, 185, 129, 0.2)'
                        }}>
                          {badge}
                          <button
                            type="button"
                            onClick={() => {
                              const newBadges = [...formData.productInfo!.badges];
                              newBadges.splice(index, 1);
                              setFormData({
                                ...formData,
                                productInfo: {
                                  ...formData.productInfo!,
                                  badges: newBadges
                                }
                              });
                            }}
                            style={{
                              background: 'rgba(255, 255, 255, 0.2)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '50%',
                              width: '18px',
                              height: '18px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer',
                              fontSize: '12px',
                              fontWeight: 'bold'
                            }}
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                    <Input
                      placeholder="Type badge name and press Enter (e.g., FRESH, PREMIUM)"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const value = (e.target as HTMLInputElement).value.trim().toUpperCase();
                          if (value && !formData.productInfo!.badges.includes(value)) {
                            setFormData({
                              ...formData,
                              productInfo: {
                                ...formData.productInfo!,
                                badges: [...formData.productInfo!.badges, value]
                              }
                            });
                            (e.target as HTMLInputElement).value = '';
                          }
                        }
                      }}
                      style={{
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '2px solid #e5e7eb',
                        fontSize: '0.875rem',
                        transition: 'all 0.2s'
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#10b981';
                        e.target.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#e5e7eb';
                        e.target.style.boxShadow = 'none';
                      }}
                    />
                  </div>

                  {/* Care Instructions */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                    <label style={{
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: '#374151'
                    }}>
                      Care Instructions
                    </label>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                      {formData.productInfo?.careInstructions.map((instruction, index) => (
                        <div key={index} style={{
                          display: 'flex',
                          gap: '0.75rem',
                          alignItems: 'center'
                        }}>
                          <Input
                            value={instruction}
                            onChange={(e) => {
                              const newInstructions = [...formData.productInfo!.careInstructions];
                              newInstructions[index] = e.target.value;
                              setFormData({
                                ...formData,
                                productInfo: {
                                  ...formData.productInfo!,
                                  careInstructions: newInstructions
                                }
                              });
                            }}
                            placeholder="Enter care instruction"
                            style={{
                              flex: 1,
                              padding: '0.75rem',
                              borderRadius: '8px',
                              border: '2px solid #e5e7eb',
                              fontSize: '0.875rem',
                              transition: 'all 0.2s'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#10b981';
                              e.target.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = '#e5e7eb';
                              e.target.style.boxShadow = 'none';
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newInstructions = [...formData.productInfo!.careInstructions];
                              newInstructions.splice(index, 1);
                              setFormData({
                                ...formData,
                                productInfo: {
                                  ...formData.productInfo!,
                                  careInstructions: newInstructions
                                }
                              });
                            }}
                            style={{
                              padding: '0.75rem',
                              borderRadius: '8px',
                              border: '2px solid #ef4444',
                              background: '#fef2f2',
                              color: '#dc2626',
                              cursor: 'pointer',
                              fontSize: '0.875rem',
                              fontWeight: '500',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.background = '#fee2e2';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = '#fef2f2';
                            }}
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => {
                          setFormData({
                            ...formData,
                            productInfo: {
                              ...formData.productInfo!,
                              careInstructions: [...formData.productInfo!.careInstructions, '']
                            }
                          });
                        }}
                        style={{
                          padding: '0.75rem',
                          borderRadius: '8px',
                          border: '2px dashed #10b981',
                          background: '#f0fdf4',
                          color: '#059669',
                          cursor: 'pointer',
                          fontSize: '0.875rem',
                          fontWeight: '500',
                          transition: 'all 0.2s',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '0.5rem'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.background = '#dcfce7';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background = '#f0fdf4';
                        }}
                      >
                        + Add Care Instruction
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div style={{
              borderTop: '2px solid #f3f4f6',
              paddingTop: '2rem',
              marginTop: '2rem',
              display: 'flex',
              gap: '1rem',
              justifyContent: 'flex-end'
            }}>
              <button
                type="button"
                onClick={resetForm}
                style={{
                  padding: '0.875rem 1.5rem',
                  borderRadius: '8px',
                  border: '2px solid #d1d5db',
                  background: 'white',
                  color: '#6b7280',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
                onMouseEnter={(e) => {
                  e.target.style.borderColor = '#9ca3af';
                  e.target.style.color = '#374151';
                }}
                onMouseLeave={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.color = '#6b7280';
                }}
              >
                <X style={{ width: '1rem', height: '1rem' }} />
                Cancel
              </button>

              <button
                type="submit"
                style={{
                  padding: '0.875rem 1.5rem',
                  borderRadius: '8px',
                  border: 'none',
                  background: 'linear-gradient(135deg, #5F9EA0 0%, #4a8a8d 100%)',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  boxShadow: '0 4px 6px -1px rgba(95, 158, 160, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = '0 6px 8px -1px rgba(95, 158, 160, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 4px 6px -1px rgba(95, 158, 160, 0.3)';
                }}
              >
                <Tag style={{ width: '1rem', height: '1rem' }} />
                {editingId ? 'Update Category' : 'Create Category'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Categories Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
        gap: '1.5rem'
      }}>
        {categories.length > 0 ? (
          categories.map((category) => (
            <div
              key={category.id}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e5e7eb',
                padding: '1.5rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
                e.currentTarget.style.borderColor = '#10b981';

                // Add hover effect to image
                const img = e.currentTarget.querySelector('img');
                if (img) {
                  img.style.transform = 'scale(1.05)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.borderColor = '#e5e7eb';

                // Remove hover effect from image
                const img = e.currentTarget.querySelector('img');
                if (img) {
                  img.style.transform = 'scale(1)';
                }
              }}
            >
              {/* Category Image */}
              <div style={{
                width: '100%',
                height: '160px',
                borderRadius: '12px',
                overflow: 'hidden',
                marginBottom: '1rem',
                backgroundColor: '#f8fafc',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                border: '2px solid #e2e8f0'
              }}>
                {category.imageUrl ? (
                  <img
                    src={category.imageUrl}
                    alt={category.name}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      transition: 'all 0.3s ease',
                      opacity: '0'
                    }}
                    onLoad={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.opacity = '1';
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = `
                          <div style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            width: 100%;
                            height: 100%;
                            color: #64748b;
                            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                          ">
                            <svg style="height: 3rem; width: 3rem; margin-bottom: 0.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span style="font-size: 0.75rem; font-weight: 500;">Image Failed</span>
                            <span style="font-size: 0.625rem; color: #9ca3af; margin-top: 0.25rem;">Click edit to upload</span>
                          </div>
                        `;
                      }
                    }}
                  />
                ) : (
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '100%',
                    color: '#64748b',
                    background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)'
                  }}>
                    <Image style={{ height: '3rem', width: '3rem', marginBottom: '0.5rem' }} />
                    <span style={{ fontSize: '0.75rem', fontWeight: '500' }}>Add Image</span>
                  </div>
                )}

                {/* Display Order Badge */}
                <div style={{
                  position: 'absolute',
                  top: '0.5rem',
                  left: '0.5rem',
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '4px'
                }}>
                  #{category.displayOrder}
                </div>
              </div>

              {/* Category Info */}
              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  marginBottom: '0.5rem'
                }}>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827',
                    lineHeight: '1.4',
                    flex: 1
                  }}>
                    {category.name}
                  </h3>
                  {category.isActive ? (
                    <span style={{
                      backgroundColor: '#dcfce7',
                      color: '#166534',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '9999px',
                      marginLeft: '0.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem'
                    }}>
                      <Check style={{ height: '0.75rem', width: '0.75rem' }} />
                      Active
                    </span>
                  ) : (
                    <span style={{
                      backgroundColor: '#f3f4f6',
                      color: '#374151',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '9999px',
                      marginLeft: '0.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem'
                    }}>
                      <X style={{ height: '0.75rem', width: '0.75rem' }} />
                      Inactive
                    </span>
                  )}
                </div>

                {category.description && (
                  <p style={{
                    fontSize: '0.875rem',
                    color: '#6b7280',
                    marginBottom: '0.5rem',
                    lineHeight: '1.4'
                  }}>
                    {category.description}
                  </p>
                )}

                <p style={{
                  fontSize: '0.75rem',
                  color: '#9ca3af',
                  marginBottom: '0.5rem'
                }}>
                  Slug: {category.slug}
                </p>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem',
                  color: '#4b5563'
                }}>
                  <svg
                    style={{ width: '1rem', height: '1rem' }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                    />
                  </svg>
                  {category.productCount} products
                </div>
              </div>

              {/* Action Buttons */}
              <div style={{
                display: 'flex',
                gap: '0.5rem'
              }}>
                <Button
                  variant="outline"
                  onClick={() => handleEdit(category)}
                  style={{
                    flex: 1,
                    borderRadius: '8px',
                    border: '1px solid #e5e7eb',
                    backgroundColor: 'white',
                    transition: 'all 0.2s',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#10b981';
                    e.currentTarget.style.borderColor = '#10b981';
                    e.currentTarget.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                    e.currentTarget.style.color = '#374151';
                  }}
                >
                  <Edit style={{ height: '1rem', width: '1rem' }} />
                  Edit
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleDelete(category.id, category.productCount)}
                  title={category.productCount > 0 ? `Delete category (${category.productCount} products will be moved to Uncategorized)` : "Delete category"}
                  style={{
                    borderRadius: '8px',
                    border: '1px solid #fecaca',
                    backgroundColor: 'white',
                    color: '#dc2626',
                    transition: 'all 0.2s',
                    padding: '0.5rem',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#dc2626';
                    e.currentTarget.style.borderColor = '#dc2626';
                    e.currentTarget.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#fecaca';
                    e.currentTarget.style.color = '#dc2626';
                  }}
                >
                  <Trash style={{ height: '1rem', width: '1rem' }} />
                </Button>
              </div>
            </div>
          ))
        ) : (
          <div style={{
            gridColumn: '1 / -1',
            textAlign: 'center',
            padding: '3rem',
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '2px dashed #d1d5db'
          }}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '1rem'
            }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                borderRadius: '50%',
                backgroundColor: '#f3f4f6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <svg
                  style={{ height: '1.5rem', width: '1.5rem', color: '#9ca3af' }}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                  />
                </svg>
              </div>
              <div>
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  No categories found
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  marginBottom: '1rem'
                }}>
                  Create your first category to organize your products.
                </p>
                <Button
                  onClick={() => setShowForm(true)}
                  style={{
                    backgroundColor: '#10b981',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '0.5rem 1rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  <Plus style={{ height: '1rem', width: '1rem' }} />
                  Add Category
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
