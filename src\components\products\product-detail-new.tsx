'use client';

import { useState } from 'react';
import { ArrowLeft, Edit, Trash, Package, DollarSign, Tag, Eye, BarChart, AlertTriangle, CheckCircle2, Image, ShoppingCart } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  categoryName: string;
  price: number;
  costPrice: number;
  unit: string;
  lowStockThreshold: number;
  sku: string;
  barcode?: string;
  weight?: string;
  dimensions?: string;
  tags?: string;
  metaTitle?: string;
  metaDescription?: string;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface InventoryItem {
  productId: string;
  quantity: number;
  warehouseName: string;
}

interface ProductDetailProps {
  product: Product;
  inventory: InventoryItem[];
  onBack: () => void;
  onEdit: (product: Product) => void;
  onDelete: (id: string) => void;
}

export function ProductDetail({ product, inventory, onBack, onEdit, onDelete }: ProductDetailProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'inventory' | 'analytics'>('overview');

  // Calculate totals
  const totalInventory = inventory.reduce((sum, item) => sum + item.quantity, 0);
  const profitMargin = product.price > 0 ? ((product.price - product.costPrice) / product.price) * 100 : 0;
  const isOutOfStock = totalInventory === 0;
  const isLowStock = totalInventory > 0 && totalInventory <= product.lowStockThreshold;

  const getStockStatus = () => {
    if (isOutOfStock) return { label: 'Out of Stock', color: '#ef4444', bgColor: '#fee2e2' };
    if (isLowStock) return { label: 'Low Stock', color: '#f59e0b', bgColor: '#fef3c7' };
    return { label: 'In Stock', color: '#10b981', bgColor: '#dcfce7' };
  };

  const stockStatus = getStockStatus();

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <button
            onClick={onBack}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: 'transparent',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              padding: '0.75rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f9fafb';
              e.currentTarget.style.borderColor = '#9ca3af';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.borderColor = '#d1d5db';
            }}
          >
            <ArrowLeft style={{ height: '1rem', width: '1rem' }} />
            Back to Products
          </button>

          <div style={{ display: 'flex', gap: '0.75rem' }}>
            <button
              onClick={() => onEdit(product)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#2563eb';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#3b82f6';
              }}
            >
              <Edit style={{ height: '1rem', width: '1rem' }} />
              Edit Product
            </button>
            <button
              onClick={() => {
                if (window.confirm(`Are you sure you want to delete "${product.name}"?`)) {
                  onDelete(product.id);
                }
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#dc2626';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#ef4444';
              }}
            >
              <Trash style={{ height: '1rem', width: '1rem' }} />
              Delete
            </button>
          </div>
        </div>
      </div>

      {/* Product Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr auto',
          gap: '2rem',
          alignItems: 'start'
        }}>
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
              <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a' }}>
                {product.name}
              </h1>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                {product.isActive ? (
                  <span style={{
                    backgroundColor: '#dcfce7',
                    color: '#166534',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '6px',
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    Active
                  </span>
                ) : (
                  <span style={{
                    backgroundColor: '#fee2e2',
                    color: '#991b1b',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '6px',
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    Inactive
                  </span>
                )}
                {product.isFeatured && (
                  <span style={{
                    backgroundColor: '#dbeafe',
                    color: '#1d4ed8',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '6px',
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    Featured
                  </span>
                )}
                <span style={{
                  backgroundColor: stockStatus.bgColor,
                  color: stockStatus.color,
                  padding: '0.25rem 0.75rem',
                  borderRadius: '6px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {stockStatus.label}
                </span>
              </div>
            </div>
            <p style={{ color: '#64748b', fontSize: '0.875rem', marginBottom: '1rem' }}>
              {product.description || 'No description available'}
            </p>
            <div style={{ display: 'flex', gap: '2rem', fontSize: '0.875rem', color: '#64748b' }}>
              <span>SKU: <strong style={{ color: '#374151' }}>{product.sku}</strong></span>
              <span>Category: <strong style={{ color: '#374151' }}>{product.categoryName}</strong></span>
              <span>Unit: <strong style={{ color: '#374151' }}>{product.unit}</strong></span>
            </div>
          </div>

          {/* Pricing Card */}
          <div style={{
            backgroundColor: '#f8fafc',
            borderRadius: '12px',
            padding: '1.5rem',
            border: '1px solid #e2e8f0',
            minWidth: '280px'
          }}>
            <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#374151', marginBottom: '1rem' }}>
              Pricing Information
            </h3>
            <div style={{ marginBottom: '1rem' }}>
              <div style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.25rem' }}>
                {formatCurrency(product.price)}
              </div>
              <div style={{ fontSize: '0.875rem', color: '#64748b' }}>
                Selling Price
              </div>
            </div>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <div>
                <div style={{ fontSize: '1.125rem', fontWeight: '600', color: '#374151' }}>
                  {formatCurrency(product.costPrice)}
                </div>
                <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                  Cost Price
                </div>
              </div>
              <div>
                <div style={{ fontSize: '1.125rem', fontWeight: '600', color: '#10b981' }}>
                  {formatCurrency(product.price - product.costPrice)}
                </div>
                <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                  Profit
                </div>
              </div>
            </div>
            <div style={{
              marginTop: '1rem',
              padding: '0.75rem',
              backgroundColor: profitMargin > 30 ? '#dcfce7' : profitMargin > 15 ? '#fef3c7' : '#fee2e2',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: profitMargin > 30 ? '#166534' : profitMargin > 15 ? '#92400e' : '#991b1b'
              }}>
                {profitMargin.toFixed(1)}%
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: profitMargin > 30 ? '#166534' : profitMargin > 15 ? '#92400e' : '#991b1b'
              }}>
                Profit Margin
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0'
      }}>
        <div style={{ padding: '0 2rem' }}>
          <div style={{ display: 'flex', gap: '2rem' }}>
            {[
              { key: 'overview', label: 'Overview', icon: Eye },
              { key: 'inventory', label: `Inventory (${totalInventory})`, icon: Package },
              { key: 'analytics', label: 'Analytics', icon: BarChart }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '1rem 0',
                  borderBottom: activeTab === key ? '2px solid #3b82f6' : '2px solid transparent',
                  color: activeTab === key ? '#3b82f6' : '#64748b',
                  backgroundColor: 'transparent',
                  border: 'none',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== key) {
                    e.currentTarget.style.color = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== key) {
                    e.currentTarget.style.color = '#64748b';
                  }
                }}
              >
                <Icon style={{ height: '1rem', width: '1rem' }} />
                {label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div style={{ padding: '2rem' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '2rem'
        }}>
          {/* Main Content */}
          <div>
            {activeTab === 'overview' && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e2e8f0',
                padding: '2rem'
              }}>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1.5rem' }}>
                  Product Details
                </h3>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '2rem',
                  marginBottom: '2rem'
                }}>
                  <div>
                    <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                      Basic Information
                    </h4>
                    <div style={{ display: 'grid', gap: '0.75rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Category:</span>
                        <span style={{ fontWeight: '500' }}>{product.categoryName}</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Unit:</span>
                        <span style={{ fontWeight: '500' }}>{product.unit}</span>
                      </div>
                      {product.weight && (
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ color: '#64748b' }}>Weight:</span>
                          <span style={{ fontWeight: '500' }}>{product.weight}</span>
                        </div>
                      )}
                      {product.dimensions && (
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ color: '#64748b' }}>Dimensions:</span>
                          <span style={{ fontWeight: '500' }}>{product.dimensions}</span>
                        </div>
                      )}
                      {product.barcode && (
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ color: '#64748b' }}>Barcode:</span>
                          <span style={{ fontWeight: '500', fontFamily: 'monospace' }}>{product.barcode}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                      Stock Information
                    </h4>
                    <div style={{ display: 'grid', gap: '0.75rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Total Stock:</span>
                        <span style={{ fontWeight: '500', color: stockStatus.color }}>
                          {totalInventory} {product.unit}
                        </span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Low Stock Alert:</span>
                        <span style={{ fontWeight: '500' }}>{product.lowStockThreshold} {product.unit}</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Status:</span>
                        <span style={{ fontWeight: '500', color: stockStatus.color }}>
                          {stockStatus.label}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Tags */}
                {product.tags && (
                  <div style={{ marginBottom: '2rem' }}>
                    <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                      Tags
                    </h4>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                      {product.tags.split(',').map((tag, index) => (
                        <span
                          key={index}
                          style={{
                            backgroundColor: '#eff6ff',
                            color: '#1d4ed8',
                            padding: '0.25rem 0.75rem',
                            borderRadius: '6px',
                            fontSize: '0.75rem',
                            fontWeight: '500'
                          }}
                        >
                          {tag.trim()}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* SEO */}
                <div>
                  <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#374151', marginBottom: '1rem' }}>
                    SEO Information
                  </h4>
                  <div style={{ display: 'grid', gap: '1rem' }}>
                    <div>
                      <label style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem', display: 'block' }}>
                        Meta Title
                      </label>
                      <p style={{ fontSize: '0.875rem', color: '#374151' }}>
                        {product.metaTitle || product.name}
                      </p>
                    </div>
                    <div>
                      <label style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem', display: 'block' }}>
                        Meta Description
                      </label>
                      <p style={{ fontSize: '0.875rem', color: '#374151' }}>
                        {product.metaDescription || 'No meta description set.'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'inventory' && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e2e8f0',
                overflow: 'hidden'
              }}>
                <div style={{
                  padding: '1.5rem',
                  borderBottom: '1px solid #e2e8f0',
                  backgroundColor: '#f8fafc'
                }}>
                  <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a' }}>
                    Inventory by Warehouse
                  </h3>
                </div>
                <div>
                  {inventory.map((item, index) => (
                    <div
                      key={index}
                      style={{
                        padding: '1rem 1.5rem',
                        borderBottom: index < inventory.length - 1 ? '1px solid #f1f5f9' : 'none',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <span style={{ fontWeight: '500', color: '#374151' }}>
                        {item.warehouseName}
                      </span>
                      <span style={{ fontWeight: '600', color: '#0f172a' }}>
                        {item.quantity} {product.unit}
                      </span>
                    </div>
                  ))}
                  {inventory.length === 0 && (
                    <div style={{
                      padding: '2rem',
                      textAlign: 'center',
                      color: '#64748b'
                    }}>
                      No inventory data available
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e2e8f0',
                padding: '2rem',
                textAlign: 'center'
              }}>
                <BarChart style={{ height: '4rem', width: '4rem', color: '#94a3b8', margin: '0 auto 1rem' }} />
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Analytics Coming Soon
                </h3>
                <p style={{ color: '#64748b' }}>
                  Product analytics and sales data will be available here.
                </p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div style={{ display: 'grid', gap: '1.5rem' }}>
            {/* Product Image */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              overflow: 'hidden'
            }}>
              <div style={{
                padding: '1.5rem',
                borderBottom: '1px solid #e2e8f0',
                backgroundColor: '#f8fafc'
              }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#0f172a' }}>
                  Product Image
                </h3>
              </div>
              <div style={{ padding: '1.5rem' }}>
                {product.imageUrl ? (
                  <div style={{
                    aspectRatio: '1',
                    borderRadius: '8px',
                    overflow: 'hidden',
                    backgroundColor: '#f1f5f9'
                  }}>
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  </div>
                ) : (
                  <div style={{
                    aspectRatio: '1',
                    borderRadius: '8px',
                    backgroundColor: '#f1f5f9',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px dashed #d1d5db'
                  }}>
                    <div style={{ textAlign: 'center' }}>
                      <Image style={{ height: '3rem', width: '3rem', color: '#94a3b8', margin: '0 auto 0.5rem' }} />
                      <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
                        No image
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              overflow: 'hidden'
            }}>
              <div style={{
                padding: '1.5rem',
                borderBottom: '1px solid #e2e8f0',
                backgroundColor: '#f8fafc'
              }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#0f172a' }}>
                  Quick Actions
                </h3>
              </div>
              <div style={{ padding: '1.5rem', display: 'grid', gap: '0.75rem' }}>
                <button
                  onClick={() => setActiveTab('inventory')}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    width: '100%',
                    padding: '0.75rem',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#2563eb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#3b82f6';
                  }}
                >
                  <Package style={{ height: '1rem', width: '1rem' }} />
                  View Inventory
                </button>
                <button
                  onClick={() => onEdit(product)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    width: '100%',
                    padding: '0.75rem',
                    backgroundColor: 'transparent',
                    color: '#374151',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <Edit style={{ height: '1rem', width: '1rem' }} />
                  Edit Product
                </button>
                <button
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    width: '100%',
                    padding: '0.75rem',
                    backgroundColor: 'transparent',
                    color: '#374151',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <Eye style={{ height: '1rem', width: '1rem' }} />
                  Preview on Website
                </button>
              </div>
            </div>

            {/* Product Metadata */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              overflow: 'hidden'
            }}>
              <div style={{
                padding: '1.5rem',
                borderBottom: '1px solid #e2e8f0',
                backgroundColor: '#f8fafc'
              }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#0f172a' }}>
                  Metadata
                </h3>
              </div>
              <div style={{ padding: '1.5rem', display: 'grid', gap: '1rem' }}>
                <div>
                  <label style={{ fontSize: '0.75rem', color: '#64748b', marginBottom: '0.25rem', display: 'block' }}>
                    Created
                  </label>
                  <p style={{ fontSize: '0.875rem', color: '#374151' }}>
                    {new Date(product.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <label style={{ fontSize: '0.75rem', color: '#64748b', marginBottom: '0.25rem', display: 'block' }}>
                    Last Updated
                  </label>
                  <p style={{ fontSize: '0.875rem', color: '#374151' }}>
                    {new Date(product.updatedAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <label style={{ fontSize: '0.75rem', color: '#64748b', marginBottom: '0.25rem', display: 'block' }}>
                    Image URL
                  </label>
                  <p style={{ fontSize: '0.75rem', color: '#374151', fontFamily: 'monospace', wordBreak: 'break-all' }}>
                    {product.imageUrl || 'No image uploaded'}
                  </p>
                </div>
                <div>
                  <label style={{ fontSize: '0.75rem', color: '#64748b', marginBottom: '0.25rem', display: 'block' }}>
                    Product ID
                  </label>
                  <p style={{ fontSize: '0.75rem', color: '#374151', fontFamily: 'monospace', wordBreak: 'break-all' }}>
                    {product.id}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
