'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  Edit,
  Trash,
  Eye,
  Package,
  DollarSign,
  Tag,
  Truck,
  BarChart,
  ShoppingCart,
  AlertTriangle,
  Image
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  categoryName: string;
  price: number;
  costPrice: number;
  unit: string;
  lowStockThreshold: number;
  sku: string;
  barcode?: string;
  weight?: string;
  dimensions?: string;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl?: string;
  gallery?: string[];
  tags?: string;
  metaTitle?: string;
  metaDescription?: string;
  createdAt: string;
  updatedAt: string;
}

interface InventoryItem {
  productId: string;
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

interface ProductDetailProps {
  product: Product;
  inventory: InventoryItem[];
  onBack: () => void;
  onEdit: (product: Product) => void;
  onDelete: (id: string) => void;
}

export function ProductDetail({
  product,
  inventory,
  onBack,
  onEdit,
  onDelete
}: ProductDetailProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'inventory' | 'sales'>('overview');

  // Calculate total inventory
  const totalInventory = inventory.reduce((sum, item) => sum + item.quantity, 0);
  const isLowStock = totalInventory <= product.lowStockThreshold;
  const isOutOfStock = totalInventory === 0;

  // Calculate profit margin
  const profitMargin = product.price > 0
    ? ((product.price - product.costPrice) / product.price) * 100
    : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="font-medium">Back to Products</span>
              </button>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => onEdit(product)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Product
              </button>
              <button
                onClick={() => {
                  if (window.confirm(`Are you sure you want to delete ${product.name}?`)) {
                    onDelete(product.id);
                  }
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Product Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
                  <div className="flex items-center space-x-2">
                    {product.isActive ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Inactive
                      </span>
                    )}
                    {product.isFeatured && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Featured
                      </span>
                    )}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      isOutOfStock
                        ? 'bg-red-100 text-red-800'
                        : isLowStock
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {isOutOfStock ? 'Out of Stock' : isLowStock ? 'Low Stock' : 'In Stock'}
                    </span>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">{product.description || 'No description available'}</p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <span>SKU: <span className="font-mono text-gray-900">{product.sku}</span></span>
                  <span>Category: <span className="text-gray-900">{product.categoryName}</span></span>
                  <span>Unit: <span className="text-gray-900">{product.unit}</span></span>
                </div>
              </div>
              <div className="text-right ml-6">
                <div className="text-3xl font-bold text-gray-900 mb-1">{formatCurrency(product.price)}</div>
                <div className="text-sm text-gray-500 mb-2">Selling Price</div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Cost:</span>
                    <span className="font-medium">{formatCurrency(product.costPrice)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Profit:</span>
                    <span className="font-medium text-green-600">{formatCurrency(product.price - product.costPrice)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Margin:</span>
                    <span className={`font-medium ${profitMargin > 30 ? 'text-green-600' : profitMargin > 15 ? 'text-yellow-600' : 'text-red-600'}`}>
                      {profitMargin.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">


            {/* Tabs */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6" aria-label="Tabs">
                  <button
                    onClick={() => setActiveTab('overview')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'overview'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-2" />
                      Overview
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('inventory')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'inventory'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <Package className="h-4 w-4 mr-2" />
                      Inventory
                      <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                        {totalInventory}
                      </span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('sales')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'sales'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <BarChart className="h-4 w-4 mr-2" />
                      Sales Analytics
                    </div>
                  </button>
                </nav>
              </div>

              {activeTab === 'overview' && (
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {/* Product Details */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Product Details</h3>
                      <dl className="space-y-3">
                        <div className="flex justify-between">
                          <dt className="text-sm font-medium text-gray-500">Category</dt>
                          <dd className="text-sm text-gray-900">{product.categoryName}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-sm font-medium text-gray-500">Unit</dt>
                          <dd className="text-sm text-gray-900">{product.unit}</dd>
                        </div>
                        {product.weight && (
                          <div className="flex justify-between">
                            <dt className="text-sm font-medium text-gray-500">Weight</dt>
                            <dd className="text-sm text-gray-900">{product.weight}</dd>
                          </div>
                        )}
                        {product.dimensions && (
                          <div className="flex justify-between">
                            <dt className="text-sm font-medium text-gray-500">Dimensions</dt>
                            <dd className="text-sm text-gray-900">{product.dimensions}</dd>
                          </div>
                        )}
                        {product.barcode && (
                          <div className="flex justify-between">
                            <dt className="text-sm font-medium text-gray-500">Barcode</dt>
                            <dd className="text-sm text-gray-900 font-mono">{product.barcode}</dd>
                          </div>
                        )}
                      </dl>
                    </div>

                    {/* Stock Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Stock Information</h3>
                      <dl className="space-y-3">
                        <div className="flex justify-between">
                          <dt className="text-sm font-medium text-gray-500">Total Stock</dt>
                          <dd className={`text-sm font-medium ${
                            isOutOfStock ? 'text-red-600' : isLowStock ? 'text-yellow-600' : 'text-green-600'
                          }`}>
                            {totalInventory} {product.unit}
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-sm font-medium text-gray-500">Low Stock Threshold</dt>
                          <dd className="text-sm text-gray-900">{product.lowStockThreshold} {product.unit}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-sm font-medium text-gray-500">Status</dt>
                          <dd className={`text-sm font-medium ${
                            isOutOfStock ? 'text-red-600' : isLowStock ? 'text-yellow-600' : 'text-green-600'
                          }`}>
                            {isOutOfStock ? 'Out of Stock' : isLowStock ? 'Low Stock' : 'In Stock'}
                          </dd>
                        </div>
                      </dl>
                    </div>
                  </div>

                  {/* Additional Information */}
                  {product.tags && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {product.tags.split(',').map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag.trim()}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* SEO Information */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">SEO Information</h3>
                    <dl className="space-y-3">
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Meta Title</dt>
                        <dd className="mt-1 text-sm text-gray-900">{product.metaTitle || product.name}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Meta Description</dt>
                        <dd className="mt-1 text-sm text-gray-900">{product.metaDescription || 'No meta description set.'}</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              )}

            {activeTab === 'inventory' && (
              <div className="mt-4 space-y-4">
                <div className="rounded-lg border p-4">
                  <h3 className="font-medium">Inventory Summary</h3>
                  <div className="mt-2 grid gap-4 sm:grid-cols-3">
                    <div>
                      <div className="text-sm text-muted-foreground">Total Stock</div>
                      <div className="text-2xl font-bold">{totalInventory} {product.unit}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Low Stock Threshold</div>
                      <div className="text-2xl font-bold">{product.lowStockThreshold} {product.unit}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Stock Status</div>
                      <div className="text-2xl font-bold">
                        {isOutOfStock ? (
                          <span className="text-red-500">Out of Stock</span>
                        ) : isLowStock ? (
                          <span className="text-amber-500">Low Stock</span>
                        ) : (
                          <span className="text-green-500">In Stock</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border">
                  <h3 className="border-b px-4 py-3 font-medium">Inventory by Location</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="px-4 py-3 text-left font-medium">Warehouse</th>
                          <th className="px-4 py-3 text-right font-medium">Quantity</th>
                          <th className="px-4 py-3 text-right font-medium">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {inventory.length > 0 ? (
                          inventory.map((item) => {
                            const isItemLowStock = item.quantity <= product.lowStockThreshold;
                            const isItemOutOfStock = item.quantity === 0;

                            return (
                              <tr key={item.warehouseId} className="border-b">
                                <td className="px-4 py-3 font-medium">{item.warehouseName}</td>
                                <td className="px-4 py-3 text-right">{item.quantity} {product.unit}</td>
                                <td className="px-4 py-3 text-right">
                                  {isItemOutOfStock ? (
                                    <span className="inline-flex items-center text-red-500">
                                      <AlertTriangle className="mr-1 h-4 w-4" />
                                      Out of Stock
                                    </span>
                                  ) : isItemLowStock ? (
                                    <span className="inline-flex items-center text-amber-500">
                                      <AlertTriangle className="mr-1 h-4 w-4" />
                                      Low Stock
                                    </span>
                                  ) : (
                                    <span className="text-green-500">In Stock</span>
                                  )}
                                </td>
                              </tr>
                            );
                          })
                        ) : (
                          <tr>
                            <td colSpan={3} className="px-4 py-4 text-center text-muted-foreground">
                              No inventory data available for this product.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'sales' && (
              <div className="mt-4 space-y-4">
                <div className="rounded-lg border p-4">
                  <h3 className="font-medium">Sales Summary</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Sales data will be displayed here once the product has sales history.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Product Image */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Product Image</h3>
              </div>
              <div className="p-6">
                {product.imageUrl ? (
                  <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="h-full w-full object-cover object-center"
                    />
                  </div>
                ) : (
                  <div className="aspect-square flex items-center justify-center rounded-lg bg-gray-50 border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <Image className="mx-auto h-12 w-12 text-gray-400" />
                      <span className="mt-2 block text-sm font-medium text-gray-900">No image</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
              </div>
              <div className="p-6 space-y-3">
                <button
                  onClick={() => setActiveTab('inventory')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Package className="h-4 w-4 mr-2" />
                  View Inventory
                </button>
                <button
                  onClick={() => onEdit(product)}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Product
                </button>
                <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview on Website
                </button>
                <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Order
                </button>
              </div>
            </div>

            {/* Product Metadata */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Product Metadata</h3>
              </div>
              <div className="p-6">
                <dl className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="mt-1 text-sm text-gray-900">{new Date(product.createdAt).toLocaleDateString()}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900">{new Date(product.updatedAt).toLocaleDateString()}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Product ID</dt>
                    <dd className="mt-1 text-sm text-gray-900 font-mono break-all">{product.id}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
