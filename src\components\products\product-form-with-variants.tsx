'use client';

import { useState, useEffect } from 'react';
import { Save, X, Image, Package, Tag, Search, Plus, Trash2 } from 'lucide-react';

interface Category {
  id: string;
  name: string;
}

interface ProductVariant {
  id?: string;
  weight: string;
  price: number;
  discountedPrice?: number;
  costPrice: number;
  sku?: string;
  isDefault: boolean;
  isActive: boolean;
  sortOrder: number;
}

interface ProductFormProps {
  initialData?: Partial<ProductFormData>;
  categories: Category[];
  onSubmit: (data: ProductFormData) => void;
  onCancel: () => void;
}

export interface ProductFormData {
  id?: string;
  name: string;
  description: string;
  category: string;
  price: string; // Base price (will be overridden by variants)
  discountedPrice?: string; // Base discounted price
  costPrice: string;
  unit: string;
  lowStockThreshold: string;
  initialStock: string;
  warehouseId: string;
  sku: string;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  variants: ProductVariant[];
}

const defaultFormData: ProductFormData = {
  name: '',
  description: '',
  category: '',
  price: '',
  discountedPrice: '',
  costPrice: '',
  unit: 'piece',
  lowStockThreshold: '10',
  initialStock: '0',
  warehouseId: '',
  sku: '',
  isActive: true,
  isFeatured: false,
  imageUrl: '',
  metaTitle: '',
  metaDescription: '',
  variants: [
    { weight: '0.5 Kg', price: 595, discountedPrice: 500, costPrice: 300, isDefault: true, isActive: true, sortOrder: 1 },
    { weight: '1 Kg', price: 1045, discountedPrice: 890, costPrice: 500, isDefault: false, isActive: true, sortOrder: 2 },
    { weight: '1.5 Kg', price: 1545, discountedPrice: 1315, costPrice: 750, isDefault: false, isActive: true, sortOrder: 3 },
    { weight: '2 Kg', price: 2045, discountedPrice: 1740, costPrice: 1000, isDefault: false, isActive: true, sortOrder: 4 }
  ]
};

export function ProductFormWithVariants({
  initialData = {},
  categories,
  onSubmit,
  onCancel
}: ProductFormProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    ...defaultFormData,
    ...initialData,
  });

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(initialData?.imageUrl || null);
  const [imageUploading, setImageUploading] = useState(false);
  const [imageUploadSuccess, setImageUploadSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState<'basic' | 'variants' | 'images' | 'seo'>('basic');
  const [warehouses, setWarehouses] = useState<any[]>([]);

  // Fetch warehouses on component mount
  useEffect(() => {
    const fetchWarehouses = async () => {
      try {
        const response = await fetch('/api/warehouses');
        if (response.ok) {
          const warehousesData = await response.json();
          setWarehouses(warehousesData);
        } else {
          setWarehouses([
            { id: 'warehouse-1', name: 'Main Warehouse', location: 'Bhubaneswar Central' }
          ]);
        }
      } catch (error) {
        setWarehouses([
          { id: 'warehouse-1', name: 'Main Warehouse', location: 'Bhubaneswar Central' }
        ]);
      }
    };
    fetchWarehouses();
  }, []);

  // Generate SKU when name changes
  useEffect(() => {
    if (formData.name && !formData.sku && !initialData?.sku) {
      const generatedSku = generateSku(formData.name, formData.category);
      setFormData(prev => ({ ...prev, sku: generatedSku }));
    }
  }, [formData.name, formData.category, initialData?.sku]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData({ ...formData, [name]: checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleVariantChange = (index: number, field: keyof ProductVariant, value: any) => {
    const updatedVariants = [...formData.variants];
    updatedVariants[index] = { ...updatedVariants[index], [field]: value };

    // If setting as default, unset others
    if (field === 'isDefault' && value === true) {
      updatedVariants.forEach((variant, i) => {
        if (i !== index) variant.isDefault = false;
      });
    }

    setFormData({ ...formData, variants: updatedVariants });
  };

  const addVariant = () => {
    const newVariant: ProductVariant = {
      weight: '',
      price: 0,
      discountedPrice: 0,
      costPrice: 0,
      isDefault: false,
      isActive: true,
      sortOrder: formData.variants.length + 1
    };
    setFormData({ ...formData, variants: [...formData.variants, newVariant] });
  };

  const removeVariant = (index: number) => {
    const updatedVariants = formData.variants.filter((_, i) => i !== index);
    setFormData({ ...formData, variants: updatedVariants });
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Data = reader.result as string;
        setImagePreview(base64Data);
        uploadImage(base64Data);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (base64Data: string) => {
    try {
      setImageUploading(true);
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ image: base64Data, folder: 'products' }),
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      setFormData(prevFormData => ({ ...prevFormData, imageUrl: data.imageUrl }));
      setImageUploadSuccess(true);
      setTimeout(() => setImageUploadSuccess(false), 3000);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setImageUploading(false);
    }
  };

  const generateSku = (name: string, category: string): string => {
    const categoryPrefix = category ? category.substring(0, 3).toUpperCase() : 'PRD';
    const namePrefix = name ? name.substring(0, 3).toUpperCase() : '';
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    return `${categoryPrefix}-${namePrefix}-${randomNum}`;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.category || !formData.warehouseId) {
      alert('Please fill in all required fields');
      return;
    }

    // Validate variants
    if (formData.variants.length === 0) {
      alert('Please add at least one product variant');
      return;
    }

    const hasDefault = formData.variants.some(v => v.isDefault);
    if (!hasDefault) {
      alert('Please set one variant as default');
      return;
    }

    onSubmit(formData);
  };

  const isEditing = !!initialData?.id;

  return (
    <div style={{ backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              {isEditing ? 'Edit Product' : 'Add New Product'}
            </h1>
            <p style={{ color: '#64748b', fontSize: '0.875rem' }}>
              {isEditing ? 'Update product information and variants' : 'Create a new product with weight-based pricing'}
            </p>
          </div>
          <button
            onClick={onCancel}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: 'transparent',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              padding: '0.75rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
          >
            <X style={{ height: '1rem', width: '1rem' }} />
            Cancel
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0'
      }}>
        <div style={{ padding: '0 2rem' }}>
          <div style={{ display: 'flex', gap: '2rem' }}>
            {[
              { key: 'basic', label: 'Basic Information', icon: Package },
              { key: 'variants', label: 'Weight & Pricing', icon: Tag },
              { key: 'images', label: 'Images', icon: Image },
              { key: 'seo', label: 'SEO & Marketing', icon: Search }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '1rem 0',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderBottom: activeTab === key ? '2px solid #3b82f6' : '2px solid transparent',
                  color: activeTab === key ? '#3b82f6' : '#64748b',
                  backgroundColor: 'transparent',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <Icon style={{ height: '1rem', width: '1rem' }} />
                {label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit}>
        <div style={{ padding: '2rem' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '2rem'
          }}>
            {activeTab === 'basic' && (
              <div>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1.5rem' }}>
                  Basic Information
                </h3>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                  gap: '1.5rem',
                  marginBottom: '1.5rem'
                }}>
                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                      Product Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Enter product name"
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.875rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                      Category *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.875rem',
                        backgroundColor: 'white'
                      }}
                    >
                      <option value="">Select a category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                      Warehouse *
                    </label>
                    <select
                      name="warehouseId"
                      value={formData.warehouseId}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.875rem',
                        backgroundColor: 'white'
                      }}
                    >
                      <option value="">Select a warehouse</option>
                      {warehouses.map(warehouse => (
                        <option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name} - {warehouse.location}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                      SKU
                    </label>
                    <input
                      type="text"
                      name="sku"
                      value={formData.sku}
                      onChange={handleInputChange}
                      placeholder="Auto-generated"
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.875rem'
                      }}
                    />
                  </div>
                </div>

                <div>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter product description"
                    rows={4}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem',
                      resize: 'vertical'
                    }}
                  />
                </div>
              </div>
            )}

            {activeTab === 'variants' && (
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
                  <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a' }}>
                    Weight & Pricing Variants
                  </h3>
                  <button
                    type="button"
                    onClick={addVariant}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem 1rem',
                      fontSize: '0.875rem',
                      cursor: 'pointer'
                    }}
                  >
                    <Plus style={{ height: '1rem', width: '1rem' }} />
                    Add Variant
                  </button>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {formData.variants.map((variant, index) => (
                    <div key={index} style={{
                      border: '1px solid #e2e8f0',
                      borderRadius: '8px',
                      padding: '1rem',
                      backgroundColor: '#f8fafc'
                    }}>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                        gap: '1rem',
                        alignItems: 'end'
                      }}>
                        <div>
                          <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                            Weight/Size
                          </label>
                          <input
                            type="text"
                            value={variant.weight}
                            onChange={(e) => handleVariantChange(index, 'weight', e.target.value)}
                            placeholder="e.g., 0.5 Kg"
                            style={{
                              width: '100%',
                              padding: '0.5rem',
                              borderRadius: '6px',
                              border: '1px solid #d1d5db',
                              fontSize: '0.875rem'
                            }}
                          />
                        </div>

                        <div>
                          <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                            Original Price (₹)
                          </label>
                          <input
                            type="number"
                            value={variant.price}
                            onChange={(e) => handleVariantChange(index, 'price', parseFloat(e.target.value) || 0)}
                            placeholder="0"
                            style={{
                              width: '100%',
                              padding: '0.5rem',
                              borderRadius: '6px',
                              border: '1px solid #d1d5db',
                              fontSize: '0.875rem'
                            }}
                          />
                        </div>

                        <div>
                          <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                            Discounted Price (₹)
                          </label>
                          <input
                            type="number"
                            value={variant.discountedPrice || ''}
                            onChange={(e) => handleVariantChange(index, 'discountedPrice', parseFloat(e.target.value) || undefined)}
                            placeholder="Optional"
                            style={{
                              width: '100%',
                              padding: '0.5rem',
                              borderRadius: '6px',
                              border: '1px solid #d1d5db',
                              fontSize: '0.875rem'
                            }}
                          />
                          {variant.price && variant.discountedPrice && variant.discountedPrice < variant.price && (
                            <div style={{ fontSize: '0.75rem', color: '#10b981', marginTop: '0.25rem' }}>
                              {Math.round(((variant.price - variant.discountedPrice) / variant.price) * 100)}% OFF
                            </div>
                          )}
                        </div>

                        <div>
                          <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                            Cost Price (₹)
                          </label>
                          <input
                            type="number"
                            value={variant.costPrice}
                            onChange={(e) => handleVariantChange(index, 'costPrice', parseFloat(e.target.value) || 0)}
                            placeholder="0"
                            style={{
                              width: '100%',
                              padding: '0.5rem',
                              borderRadius: '6px',
                              border: '1px solid #d1d5db',
                              fontSize: '0.875rem'
                            }}
                          />
                        </div>

                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                          <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem' }}>
                            <input
                              type="checkbox"
                              checked={variant.isDefault}
                              onChange={(e) => handleVariantChange(index, 'isDefault', e.target.checked)}
                            />
                            Default
                          </label>

                          <button
                            type="button"
                            onClick={() => removeVariant(index)}
                            style={{
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              padding: '0.5rem',
                              cursor: 'pointer'
                            }}
                          >
                            <Trash2 style={{ height: '1rem', width: '1rem' }} />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'images' && (
              <div>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1.5rem' }}>
                  Product Images
                </h3>

                <div style={{ marginBottom: '1.5rem' }}>
                  <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                    Main Product Image
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      border: '1px solid #d1d5db',
                      fontSize: '0.875rem'
                    }}
                  />
                  {imageUploading && <p style={{ color: '#3b82f6', fontSize: '0.875rem', marginTop: '0.5rem' }}>Uploading...</p>}
                  {imageUploadSuccess && <p style={{ color: '#10b981', fontSize: '0.875rem', marginTop: '0.5rem' }}>Image uploaded successfully!</p>}
                </div>

                {imagePreview && (
                  <div style={{ marginTop: '1rem' }}>
                    <img
                      src={imagePreview}
                      alt="Preview"
                      style={{
                        width: '200px',
                        height: '200px',
                        objectFit: 'cover',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0'
                      }}
                    />
                  </div>
                )}
              </div>
            )}

            {activeTab === 'seo' && (
              <div>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1.5rem' }}>
                  SEO & Marketing
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                      Meta Title
                    </label>
                    <input
                      type="text"
                      name="metaTitle"
                      value={formData.metaTitle}
                      onChange={handleInputChange}
                      placeholder="SEO title for search engines"
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.875rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                      Meta Description
                    </label>
                    <textarea
                      name="metaDescription"
                      value={formData.metaDescription}
                      onChange={handleInputChange}
                      placeholder="SEO description for search engines"
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        border: '1px solid #d1d5db',
                        fontSize: '0.875rem',
                        resize: 'vertical'
                      }}
                    />
                  </div>

                  <div style={{ display: 'flex', gap: '1rem' }}>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem' }}>
                      <input
                        type="checkbox"
                        name="isActive"
                        checked={formData.isActive}
                        onChange={handleInputChange}
                      />
                      Active Product
                    </label>

                    <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem' }}>
                      <input
                        type="checkbox"
                        name="isFeatured"
                        checked={formData.isFeatured}
                        onChange={handleInputChange}
                      />
                      Featured Product
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div style={{ marginTop: '2rem', display: 'flex', justifyContent: 'flex-end', gap: '1rem' }}>
            <button
              type="button"
              onClick={onCancel}
              style={{
                backgroundColor: 'transparent',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              <Save style={{ height: '1rem', width: '1rem' }} />
              {isEditing ? 'Update Product' : 'Create Product'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
