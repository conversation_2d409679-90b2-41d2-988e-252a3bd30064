'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Save, X, Image } from 'lucide-react';

interface Category {
  id: string;
  name: string;
}

interface ProductFormProps {
  initialData?: Partial<ProductFormData>;
  categories: Category[];
  onSubmit: (data: ProductFormData) => void;
  onCancel: () => void;
}

export interface ProductFormData {
  id?: string; // Added for editing existing products
  name: string;
  description: string;
  category: string;
  price: string;
  costPrice: string;
  unit: string;
  lowStockThreshold: string;
  initialStock: string;
  sku: string;
  barcode?: string;
  weight?: string;
  dimensions?: string;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl?: string;
  gallery?: string[];
  tags?: string;
  metaTitle?: string;
  metaDescription?: string;
}

const defaultFormData: ProductFormData = {
  name: '',
  description: '',
  category: '',
  price: '',
  costPrice: '',
  unit: '',
  lowStockThreshold: '10',
  initialStock: '0',
  sku: '',
  barcode: '',
  weight: '',
  dimensions: '',
  isActive: true,
  isFeatured: false,
  imageUrl: '',
  gallery: [],
  tags: '',
  metaTitle: '',
  metaDescription: '',
};

export function ProductForm({
  initialData = {},
  categories,
  onSubmit,
  onCancel
}: ProductFormProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    ...defaultFormData,
    ...initialData,
  });

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(formData.imageUrl || null);
  const [activeTab, setActiveTab] = useState<'basic' | 'details' | 'images' | 'seo'>('basic');

  // Generate SKU when name changes if SKU is empty
  useEffect(() => {
    if (formData.name && !formData.sku && !initialData.sku) {
      const generatedSku = generateSku(formData.name, formData.category);
      setFormData(prev => ({ ...prev, sku: generatedSku }));
    }
  }, [formData.name, formData.category, initialData.sku]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData({ ...formData, [name]: checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Data = reader.result as string;
        setImagePreview(base64Data);

        // Upload the image to the server when selected
        uploadImage(base64Data);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (base64Data: string) => {
    try {
      console.log('🖼️ Uploading image to /api/upload');

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: base64Data,
          folder: 'products'
        }),
      });

      console.log('📊 Upload response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Upload failed:', errorData);
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const data = await response.json();
      console.log('✅ Upload response data:', data);

      setFormData(prevFormData => ({ ...prevFormData, imageUrl: data.imageUrl }));

      // Show success message based on upload method
      if (data.isBase64) {
        console.log('📱 Image stored as base64 data (production mode)');
      } else {
        console.log('💾 Image saved to file system (development mode)');
      }
    } catch (error) {
      console.error('❌ Error uploading image:', error);
      alert(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
      // Keep the preview but don't update the form data
    }
  };

  const generateSku = (name: string, category: string): string => {
    // Create SKU from first 3 letters of category and first 3 letters of product name + random number
    const categoryPrefix = category ? category.substring(0, 3).toUpperCase() : 'PRD';
    const namePrefix = name ? name.substring(0, 3).toUpperCase() : '';
    const randomNum = Math.floor(1000 + Math.random() * 9000); // 4-digit number

    return `${categoryPrefix}-${namePrefix}-${randomNum}`;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex border-b">
        <button
          type="button"
          className={`px-4 py-2 font-medium ${activeTab === 'basic' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('basic')}
        >
          Basic Info
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium ${activeTab === 'details' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('details')}
        >
          Details
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium ${activeTab === 'images' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('images')}
        >
          Images
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium ${activeTab === 'seo' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('seo')}
        >
          SEO
        </button>
      </div>

      {activeTab === 'basic' && (
        <div className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Product Name *
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter product name"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                required
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="price" className="text-sm font-medium">
                Selling Price *
              </label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={handleInputChange}
                placeholder="Enter selling price"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="costPrice" className="text-sm font-medium">
                Cost Price *
              </label>
              <Input
                id="costPrice"
                name="costPrice"
                type="number"
                step="0.01"
                min="0"
                value={formData.costPrice}
                onChange={handleInputChange}
                placeholder="Enter cost price"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="unit" className="text-sm font-medium">
                Unit *
              </label>
              <Input
                id="unit"
                name="unit"
                value={formData.unit}
                onChange={handleInputChange}
                placeholder="e.g., piece, loaf, kg"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="lowStockThreshold" className="text-sm font-medium">
                Low Stock Threshold *
              </label>
              <Input
                id="lowStockThreshold"
                name="lowStockThreshold"
                type="number"
                min="1"
                value={formData.lowStockThreshold}
                onChange={handleInputChange}
                placeholder="Enter threshold"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="initialStock" className="text-sm font-medium">
                Initial Stock Quantity
              </label>
              <Input
                id="initialStock"
                name="initialStock"
                type="number"
                min="0"
                value={formData.initialStock}
                onChange={handleInputChange}
                placeholder="0"
              />
              <p className="text-xs text-muted-foreground">
                How many items to add to inventory when creating this product
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter product description"
              className="flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            />
          </div>

          <div className="flex flex-col gap-2 sm:flex-row">
            <div className="flex items-center space-x-2">
              <input
                id="isActive"
                name="isActive"
                type="checkbox"
                checked={formData.isActive}
                onChange={handleInputChange}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label htmlFor="isActive" className="text-sm">
                Active (visible on website)
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                id="isFeatured"
                name="isFeatured"
                type="checkbox"
                checked={formData.isFeatured}
                onChange={handleInputChange}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label htmlFor="isFeatured" className="text-sm">
                Featured Product
              </label>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'details' && (
        <div className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <label htmlFor="sku" className="text-sm font-medium">
                SKU *
              </label>
              <Input
                id="sku"
                name="sku"
                value={formData.sku}
                onChange={handleInputChange}
                placeholder="Stock Keeping Unit"
                required
              />
              <p className="text-xs text-muted-foreground">
                Auto-generated, but can be edited.
              </p>
            </div>

            <div className="space-y-2">
              <label htmlFor="barcode" className="text-sm font-medium">
                Barcode / UPC
              </label>
              <Input
                id="barcode"
                name="barcode"
                value={formData.barcode || ''}
                onChange={handleInputChange}
                placeholder="Enter barcode or UPC"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="weight" className="text-sm font-medium">
                Weight
              </label>
              <Input
                id="weight"
                name="weight"
                value={formData.weight || ''}
                onChange={handleInputChange}
                placeholder="e.g., 500g, 1kg"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="dimensions" className="text-sm font-medium">
                Dimensions
              </label>
              <Input
                id="dimensions"
                name="dimensions"
                value={formData.dimensions || ''}
                onChange={handleInputChange}
                placeholder="e.g., 10x5x2 cm"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <label htmlFor="tags" className="text-sm font-medium">
                Tags
              </label>
              <Input
                id="tags"
                name="tags"
                value={formData.tags || ''}
                onChange={handleInputChange}
                placeholder="Enter tags separated by commas"
              />
              <p className="text-xs text-muted-foreground">
                Tags help customers find your products more easily.
              </p>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'images' && (
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="mainImage" className="text-sm font-medium">
              Main Product Image
            </label>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  id="mainImage"
                  name="mainImage"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="cursor-pointer"
                />
                <p className="mt-1 text-xs text-muted-foreground">
                  Recommended size: 800x800 pixels, max 2MB.
                </p>
              </div>

              {imagePreview ? (
                <div className="relative h-24 w-24 overflow-hidden rounded-md border">
                  <img
                    src={imagePreview}
                    alt="Product preview"
                    className="h-full w-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview(null);
                      setImageFile(null);
                      setFormData({ ...formData, imageUrl: '' });
                    }}
                    className="absolute right-1 top-1 rounded-full bg-background p-1 shadow-sm"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ) : (
                <div className="flex h-24 w-24 items-center justify-center rounded-md border">
                  <Image className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              Product Gallery
            </label>
            <p className="text-xs text-muted-foreground">
              Coming soon: Add multiple images to showcase your product from different angles.
            </p>
          </div>
        </div>
      )}

      {activeTab === 'seo' && (
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="metaTitle" className="text-sm font-medium">
              Meta Title
            </label>
            <Input
              id="metaTitle"
              name="metaTitle"
              value={formData.metaTitle || ''}
              onChange={handleInputChange}
              placeholder="Enter meta title for SEO"
            />
            <p className="text-xs text-muted-foreground">
              Defaults to product name if left empty. Recommended length: 50-60 characters.
            </p>
          </div>

          <div className="space-y-2">
            <label htmlFor="metaDescription" className="text-sm font-medium">
              Meta Description
            </label>
            <textarea
              id="metaDescription"
              name="metaDescription"
              value={formData.metaDescription || ''}
              onChange={handleInputChange}
              placeholder="Enter meta description for SEO"
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            />
            <p className="text-xs text-muted-foreground">
              Recommended length: 150-160 characters.
            </p>
          </div>
        </div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button type="submit">
          <Save className="mr-2 h-4 w-4" />
          {initialData.name ? 'Update' : 'Save'} Product
        </Button>
      </div>
    </form>
  );
}
