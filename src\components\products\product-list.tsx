'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ArrowUpDown,
  Search,
  Plus,
  Filter,
  Tag,
  AlertTriangle,
  CheckCircle2,
  Image
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  categoryName: string;
  price: number;
  costPrice: number;
  unit: string;
  lowStockThreshold: number;
  sku: string;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: string;
  name: string;
}

interface InventoryItem {
  productId: string;
  quantity: number;
}

interface ProductListProps {
  products: Product[];
  categories: Category[];
  inventory: InventoryItem[];
  onViewProduct: (id: string) => void;
  onAddProduct: () => void;
}

export function ProductList({
  products,
  categories,
  inventory,
  onViewProduct,
  onAddProduct
}: ProductListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Product>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [stockFilter, setStockFilter] = useState<'all' | 'in-stock' | 'low-stock' | 'out-of-stock'>('all');

  // State to track which dropdown is open
  const [openDropdown, setOpenDropdown] = useState<'category' | 'status' | 'stock' | null>(null);

  // Ref for the dropdown container
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Filter products based on search term, category, status, and stock
  const filteredProducts = products.filter(product => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      categoryFilter === 'all' ||
      product.category === categoryFilter;

    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && product.isActive) ||
      (statusFilter === 'inactive' && !product.isActive);

    // Get inventory for this product
    const productInventory = inventory.find(item => item.productId === product.id);
    const stockQuantity = productInventory ? productInventory.quantity : 0;

    const matchesStock =
      stockFilter === 'all' ||
      (stockFilter === 'in-stock' && stockQuantity > product.lowStockThreshold) ||
      (stockFilter === 'low-stock' && stockQuantity > 0 && stockQuantity <= product.lowStockThreshold) ||
      (stockFilter === 'out-of-stock' && stockQuantity === 0);

    return matchesSearch && matchesCategory && matchesStatus && matchesStock;
  });

  // Sort products based on sort field and direction
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const handleSort = (field: keyof Product) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get stock status for a product
  const getStockStatus = (productId: string, lowStockThreshold: number) => {
    const productInventory = inventory.find(item => item.productId === productId);
    const quantity = productInventory ? productInventory.quantity : 0;

    if (quantity === 0) {
      return { status: 'out-of-stock', label: 'Out of Stock', icon: <AlertTriangle className="mr-1 h-3 w-3 text-red-500" /> };
    } else if (quantity <= lowStockThreshold) {
      return { status: 'low-stock', label: 'Low Stock', icon: <AlertTriangle className="mr-1 h-3 w-3 text-amber-500" /> };
    } else {
      return { status: 'in-stock', label: 'In Stock', icon: <CheckCircle2 className="mr-1 h-3 w-3 text-green-500" /> };
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f1f5f9'
    }}>
      {/* Header Section */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h1 style={{
              fontSize: '2.25rem',
              fontWeight: '800',
              color: '#0f172a',
              marginBottom: '0.5rem'
            }}>
              Products
            </h1>
            <p style={{
              color: '#64748b',
              fontSize: '1.125rem'
            }}>
              Manage your product catalog • {sortedProducts.length} total • {sortedProducts.filter(p => p.isActive).length} active
            </p>
          </div>
          <button
            onClick={onAddProduct}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            Add New Product
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '1.5rem 2rem'
      }}>
        <div style={{
          display: 'flex',
          gap: '1rem',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          {/* Search */}
          <div style={{ position: 'relative', flex: 1, minWidth: '320px' }}>
            <Search style={{
              position: 'absolute',
              left: '14px',
              top: '50%',
              transform: 'translateY(-50%)',
              height: '1.25rem',
              width: '1.25rem',
              color: '#64748b'
            }} />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                paddingLeft: '3rem',
                paddingRight: '1rem',
                paddingTop: '0.875rem',
                paddingBottom: '0.875rem',
                borderRadius: '10px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                transition: 'all 0.2s',
                backgroundColor: '#f8fafc'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.backgroundColor = '#f8fafc';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* Filters */}
          <div style={{ display: 'flex', gap: '0.75rem', flexWrap: 'wrap' }} ref={dropdownRef}>
            {/* Category Filter */}
            <div style={{ position: 'relative' }}>
              <button
                onClick={() => setOpenDropdown(openDropdown === 'category' ? null : 'category')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  borderRadius: '8px',
                  border: '1px solid #d1d5db',
                  backgroundColor: categoryFilter !== 'all' ? '#eff6ff' : 'white',
                  color: categoryFilter !== 'all' ? '#1d4ed8' : '#374151',
                  padding: '0.625rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = '#3b82f6';
                  e.currentTarget.style.backgroundColor = categoryFilter !== 'all' ? '#dbeafe' : '#f8fafc';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.backgroundColor = categoryFilter !== 'all' ? '#eff6ff' : 'white';
                }}
              >
                <Tag style={{ height: '1rem', width: '1rem' }} />
                {categoryFilter === 'all' ? 'All Categories' : categories.find(c => c.id === categoryFilter)?.name || 'All Categories'}
              </button>
              {openDropdown === 'category' && (
                <div style={{
                  position: 'absolute',
                  right: 0,
                  top: '100%',
                  zIndex: 10,
                  marginTop: '0.25rem',
                  width: '12rem',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb',
                  backgroundColor: 'white',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                }}>
                  <div style={{ padding: '0.25rem' }}>
                    <button
                      style={{
                        display: 'flex',
                        width: '100%',
                        alignItems: 'center',
                        borderRadius: '4px',
                        padding: '0.5rem 0.75rem',
                        fontSize: '0.875rem',
                        backgroundColor: categoryFilter === 'all' ? '#f3f4f6' : 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        transition: 'background-color 0.2s'
                      }}
                      onClick={() => {
                        setCategoryFilter('all');
                        setOpenDropdown(null);
                      }}
                      onMouseEnter={(e) => {
                        if (categoryFilter !== 'all') {
                          e.currentTarget.style.backgroundColor = '#f9fafb';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (categoryFilter !== 'all') {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }
                      }}
                    >
                      All Categories
                    </button>
                    {categories.map(category => (
                      <button
                        key={category.id}
                        style={{
                          display: 'flex',
                          width: '100%',
                          alignItems: 'center',
                          borderRadius: '4px',
                          padding: '0.5rem 0.75rem',
                          fontSize: '0.875rem',
                          backgroundColor: categoryFilter === category.id ? '#f3f4f6' : 'transparent',
                          border: 'none',
                          cursor: 'pointer',
                          transition: 'background-color 0.2s'
                        }}
                        onClick={() => {
                          setCategoryFilter(category.id);
                          setOpenDropdown(null);
                        }}
                        onMouseEnter={(e) => {
                          if (categoryFilter !== category.id) {
                            e.currentTarget.style.backgroundColor = '#f9fafb';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (categoryFilter !== category.id) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                      >
                        {category.name}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

          <div style={{ position: 'relative' }}>
            <Button
              variant="outline"
              onClick={() => setOpenDropdown(openDropdown === 'status' ? null : 'status')}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                backgroundColor: statusFilter !== 'all' ? '#f3f4f6' : 'white',
                transition: 'all 0.2s',
                fontSize: '0.875rem'
              }}
            >
              <Filter style={{ height: '1rem', width: '1rem' }} />
              Status: {statusFilter === 'all' ? 'All' : statusFilter === 'active' ? 'Active' : 'Inactive'}
            </Button>
            {openDropdown === 'status' && (
              <div style={{
                position: 'absolute',
                right: 0,
                top: '100%',
                zIndex: 10,
                marginTop: '0.25rem',
                width: '10rem',
                borderRadius: '8px',
                border: '1px solid #e5e7eb',
                backgroundColor: 'white',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
              }}>
                <div style={{ padding: '0.25rem' }}>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: statusFilter === 'all' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStatusFilter('all');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (statusFilter !== 'all') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (statusFilter !== 'all') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    All
                  </button>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: statusFilter === 'active' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStatusFilter('active');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (statusFilter !== 'active') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (statusFilter !== 'active') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    Active
                  </button>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: statusFilter === 'inactive' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStatusFilter('inactive');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (statusFilter !== 'inactive') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (statusFilter !== 'inactive') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    Inactive
                  </button>
                </div>
              </div>
            )}
          </div>

          <div style={{ position: 'relative' }}>
            <Button
              variant="outline"
              onClick={() => setOpenDropdown(openDropdown === 'stock' ? null : 'stock')}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                borderRadius: '8px',
                border: '2px solid #e5e7eb',
                backgroundColor: stockFilter !== 'all' ? '#f3f4f6' : 'white',
                transition: 'all 0.2s',
                fontSize: '0.875rem'
              }}
            >
              <Filter style={{ height: '1rem', width: '1rem' }} />
              Stock: {stockFilter === 'all' ? 'All' : stockFilter === 'in-stock' ? 'In Stock' : stockFilter === 'low-stock' ? 'Low Stock' : 'Out of Stock'}
            </Button>
            {openDropdown === 'stock' && (
              <div style={{
                position: 'absolute',
                right: 0,
                top: '100%',
                zIndex: 10,
                marginTop: '0.25rem',
                width: '10rem',
                borderRadius: '8px',
                border: '1px solid #e5e7eb',
                backgroundColor: 'white',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
              }}>
                <div style={{ padding: '0.25rem' }}>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: stockFilter === 'all' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStockFilter('all');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (stockFilter !== 'all') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (stockFilter !== 'all') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    All
                  </button>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: stockFilter === 'in-stock' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStockFilter('in-stock');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (stockFilter !== 'in-stock') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (stockFilter !== 'in-stock') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    In Stock
                  </button>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: stockFilter === 'low-stock' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStockFilter('low-stock');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (stockFilter !== 'low-stock') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (stockFilter !== 'low-stock') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    Low Stock
                  </button>
                  <button
                    style={{
                      display: 'flex',
                      width: '100%',
                      alignItems: 'center',
                      borderRadius: '4px',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      backgroundColor: stockFilter === 'out-of-stock' ? '#f3f4f6' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => {
                      setStockFilter('out-of-stock');
                      setOpenDropdown(null);
                    }}
                    onMouseEnter={(e) => {
                      if (stockFilter !== 'out-of-stock') {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (stockFilter !== 'out-of-stock') {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    Out of Stock
                  </button>
                </div>
              </div>
            )}
          </div>

          <Button
            onClick={onAddProduct}
            style={{
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#5a67d8';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#667eea';
            }}
          >
            <Plus style={{ height: '1rem', width: '1rem' }} />
            Add Product
          </Button>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div style={{ padding: '2rem' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',
          gap: '1.5rem'
        }}>
        {sortedProducts.length > 0 ? (
          sortedProducts.map(product => {
            const stockStatus = getStockStatus(product.id, product.lowStockThreshold);
            const productInventory = inventory.find(item => item.productId === product.id);
            const stockQuantity = productInventory ? productInventory.quantity : 0;

            return (
              <div
                key={product.id}
                onClick={() => onViewProduct(product.id)}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  border: '1px solid #e5e7eb',
                  padding: '1.5rem',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-4px)';
                  e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
                  e.currentTarget.style.borderColor = '#667eea';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                  e.currentTarget.style.borderColor = '#e5e7eb';
                }}
              >
                {/* Product Image */}
                <div style={{
                  width: '100%',
                  height: '160px',
                  borderRadius: '8px',
                  overflow: 'hidden',
                  marginBottom: '1rem',
                  backgroundColor: '#f9fafb',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  ) : (
                    <Image style={{ height: '3rem', width: '3rem', color: '#9ca3af' }} />
                  )}
                </div>

                {/* Product Info */}
                <div style={{ marginBottom: '1rem' }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <h3 style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      color: '#111827',
                      lineHeight: '1.4',
                      flex: 1
                    }}>
                      {product.name}
                    </h3>
                    {product.isActive ? (
                      <span style={{
                        backgroundColor: '#dcfce7',
                        color: '#166534',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '9999px',
                        marginLeft: '0.5rem'
                      }}>
                        Active
                      </span>
                    ) : (
                      <span style={{
                        backgroundColor: '#f3f4f6',
                        color: '#374151',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '9999px',
                        marginLeft: '0.5rem'
                      }}>
                        Inactive
                      </span>
                    )}
                  </div>

                  <p style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    marginBottom: '0.5rem'
                  }}>
                    SKU: {product.sku}
                  </p>

                  <p style={{
                    fontSize: '0.875rem',
                    color: '#4b5563',
                    marginBottom: '0.75rem'
                  }}>
                    {product.categoryName}
                  </p>
                </div>

                {/* Price and Stock */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <div>
                    <p style={{
                      fontSize: '1.25rem',
                      fontWeight: '700',
                      color: '#111827'
                    }}>
                      {formatCurrency(product.price)}
                    </p>
                    <p style={{
                      fontSize: '0.75rem',
                      color: '#6b7280'
                    }}>
                      per {product.unit}
                    </p>
                  </div>

                  <div style={{ textAlign: 'right' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                      marginBottom: '0.25rem'
                    }}>
                      {stockStatus.status === 'out-of-stock' ? (
                        <AlertTriangle style={{ height: '0.875rem', width: '0.875rem', color: '#ef4444', marginRight: '0.25rem' }} />
                      ) : stockStatus.status === 'low-stock' ? (
                        <AlertTriangle style={{ height: '0.875rem', width: '0.875rem', color: '#f59e0b', marginRight: '0.25rem' }} />
                      ) : (
                        <CheckCircle2 style={{ height: '0.875rem', width: '0.875rem', color: '#10b981', marginRight: '0.25rem' }} />
                      )}
                      <span style={{
                        fontSize: '0.875rem',
                        fontWeight: '600',
                        color: stockStatus.status === 'out-of-stock' ? '#ef4444' :
                               stockStatus.status === 'low-stock' ? '#f59e0b' : '#10b981'
                      }}>
                        {stockQuantity} {product.unit}
                      </span>
                    </div>
                    <p style={{
                      fontSize: '0.75rem',
                      color: '#6b7280'
                    }}>
                      {stockStatus.label}
                    </p>
                  </div>
                </div>

                {/* Action Button */}
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewProduct(product.id);
                  }}
                  style={{
                    width: '100%',
                    borderRadius: '8px',
                    border: '2px solid #e5e7eb',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#667eea';
                    e.currentTarget.style.borderColor = '#667eea';
                    e.currentTarget.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                    e.currentTarget.style.color = '#374151';
                  }}
                >
                  View Details
                </Button>
              </div>
            );
          })
        ) : (
          <div style={{
            gridColumn: '1 / -1',
            textAlign: 'center',
            padding: '3rem',
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '2px dashed #d1d5db'
          }}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '1rem'
            }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                borderRadius: '50%',
                backgroundColor: '#f3f4f6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Search style={{ height: '1.5rem', width: '1.5rem', color: '#9ca3af' }} />
              </div>
              <div>
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  No products found
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  marginBottom: '1rem'
                }}>
                  Adjust your search criteria or add a new product to get started.
                </p>
                <Button
                  onClick={onAddProduct}
                  style={{
                    backgroundColor: '#667eea',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '0.5rem 1rem'
                  }}
                >
                  <Plus style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                  Add Product
                </Button>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
}
