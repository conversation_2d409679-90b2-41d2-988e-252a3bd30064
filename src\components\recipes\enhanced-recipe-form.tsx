'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RecipeFormTabs } from './recipe-form-tabs';
import { RecipeFormBasic } from './recipe-form-basic';
import { RecipeFormIngredients } from './recipe-form-ingredients';
import { RecipeFormInstructions } from './recipe-form-instructions';
import { RecipeFormNutrition } from './recipe-form-nutrition';
import { RecipeFormCosting } from './recipe-form-costing';
import { RecipeFormAdditional } from './recipe-form-additional';

interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost?: number;
  category?: string;
  isOptional?: boolean;
  substitutes?: string;
}

interface Recipe {
  id?: string;
  name: string;
  description: string;
  preparationTime: number;
  bakingTime: number;
  restingTime?: number;
  totalTime?: number;
  yield: number;
  yieldUnit: string;
  difficulty: string;
  category: string;
  tags: string[];
  ingredients: Ingredient[];
  instructions: string[];
  notes?: string;
  tips?: string[];
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sugar?: number;
  };
  costPerUnit?: number;
  sellingPrice?: number;
  profitMargin?: number;
  imageUrl?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface EnhancedRecipeFormProps {
  initialData?: Recipe;
  onSubmit: (data: Recipe) => void;
  onCancel: () => void;
  rawMaterials?: Array<{id: string; name: string; unit: string; cost: number}>;
}

const defaultRecipe: Recipe = {
  name: '',
  description: '',
  preparationTime: 0,
  bakingTime: 0,
  restingTime: 0,
  totalTime: 0,
  yield: 1,
  yieldUnit: 'piece',
  difficulty: 'Medium',
  category: 'Other',
  tags: [],
  ingredients: [],
  instructions: [''],
  notes: '',
  tips: [],
  nutritionInfo: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0
  },
  costPerUnit: 0,
  sellingPrice: 0,
  profitMargin: 0,
  imageUrl: '',
  isActive: true
};

export function EnhancedRecipeForm({ 
  initialData = defaultRecipe, 
  onSubmit, 
  onCancel,
  rawMaterials
}: EnhancedRecipeFormProps) {
  const [formData, setFormData] = useState<Recipe>(initialData);
  const [activeTab, setActiveTab] = useState('basic');
  const [newIngredient, setNewIngredient] = useState({
    name: '',
    quantity: '',
    unit: '',
    cost: '',
    category: '',
    isOptional: false,
    substitutes: ''
  });
  const [newTag, setNewTag] = useState('');
  const [newTip, setNewTip] = useState('');

  // Calculate total time whenever prep, baking, or resting time changes
  useEffect(() => {
    const total = (formData.preparationTime || 0) + 
                 (formData.bakingTime || 0) + 
                 (formData.restingTime || 0);
    setFormData(prev => ({ ...prev, totalTime: total }));
  }, [formData.preparationTime, formData.bakingTime, formData.restingTime]);

  // Calculate profit margin whenever cost or selling price changes
  useEffect(() => {
    if (formData.costPerUnit && formData.sellingPrice && formData.costPerUnit > 0) {
      const margin = ((formData.sellingPrice - formData.costPerUnit) / formData.sellingPrice) * 100;
      setFormData(prev => ({ ...prev, profitMargin: parseFloat(margin.toFixed(2)) }));
    }
  }, [formData.costPerUnit, formData.sellingPrice]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: parseFloat(value) || 0 });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({ ...formData, [name]: checked });
  };

  const handleIngredientInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'isOptional' && e.target instanceof HTMLInputElement) {
      setNewIngredient({ ...newIngredient, [name]: e.target.checked });
    } else {
      setNewIngredient({ ...newIngredient, [name]: value });
    }
  };
  
  const handleNutritionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      nutritionInfo: {
        ...formData.nutritionInfo,
        [name]: parseFloat(value) || 0
      }
    });
  };

  const handleAddIngredient = () => {
    if (newIngredient.name && newIngredient.quantity && newIngredient.unit) {
      const ingredient = {
        id: Date.now().toString(),
        name: newIngredient.name,
        quantity: parseFloat(newIngredient.quantity),
        unit: newIngredient.unit,
        cost: parseFloat(newIngredient.cost) || undefined,
        category: newIngredient.category || undefined,
        isOptional: newIngredient.isOptional,
        substitutes: newIngredient.substitutes || undefined
      };
      
      setFormData({
        ...formData,
        ingredients: [...formData.ingredients, ingredient],
      });
      
      // Recalculate recipe cost if we have cost information
      if (ingredient.cost) {
        calculateRecipeCost([...formData.ingredients, ingredient]);
      }
      
      setNewIngredient({
        name: '',
        quantity: '',
        unit: '',
        cost: '',
        category: '',
        isOptional: false,
        substitutes: ''
      });
    }
  };

  const handleRemoveIngredient = (id: string) => {
    setFormData({
      ...formData,
      ingredients: formData.ingredients.filter(ingredient => ingredient.id !== id),
    });
  };

  const handleInstructionChange = (index: number, value: string) => {
    const updatedInstructions = [...formData.instructions];
    updatedInstructions[index] = value;
    setFormData({
      ...formData,
      instructions: updatedInstructions,
    });
  };

  const handleAddInstruction = () => {
    setFormData({
      ...formData,
      instructions: [...formData.instructions, ''],
    });
  };

  const handleRemoveInstruction = (index: number) => {
    const updatedInstructions = [...formData.instructions];
    updatedInstructions.splice(index, 1);
    setFormData({
      ...formData,
      instructions: updatedInstructions,
    });
  };
  
  const handleAddTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag]
      });
      setNewTag('');
    }
  };
  
  const handleRemoveTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    });
  };
  
  const handleAddTip = () => {
    if (newTip) {
      setFormData({
        ...formData,
        tips: [...formData.tips, newTip]
      });
      setNewTip('');
    }
  };
  
  const handleRemoveTip = (index: number) => {
    const updatedTips = [...formData.tips];
    updatedTips.splice(index, 1);
    setFormData({
      ...formData,
      tips: updatedTips
    });
  };
  
  const calculateRecipeCost = (ingredients = formData.ingredients) => {
    // Only calculate if we have yield information
    if (formData.yield <= 0) return;
    
    const totalCost = ingredients.reduce((sum, ingredient) => {
      return sum + (ingredient.cost || 0) * ingredient.quantity;
    }, 0);
    
    const costPerUnit = totalCost / formData.yield;
    
    setFormData(prev => ({
      ...prev,
      costPerUnit: parseFloat(costPerUnit.toFixed(2))
    }));
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      notes: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <RecipeFormTabs activeTab={activeTab} onChange={setActiveTab} />
      
      <div className="space-y-6">
        {/* Basic Info Tab */}
        {activeTab === 'basic' && (
          <RecipeFormBasic 
            formData={formData}
            handleInputChange={handleInputChange}
            handleNumberInputChange={handleNumberInputChange}
          />
        )}
        
        {/* Ingredients Tab */}
        {activeTab === 'ingredients' && (
          <RecipeFormIngredients 
            ingredients={formData.ingredients}
            newIngredient={newIngredient}
            handleIngredientInputChange={handleIngredientInputChange}
            handleAddIngredient={handleAddIngredient}
            handleRemoveIngredient={handleRemoveIngredient}
            rawMaterials={rawMaterials}
          />
        )}
        
        {/* Instructions Tab */}
        {activeTab === 'instructions' && (
          <RecipeFormInstructions 
            instructions={formData.instructions}
            handleInstructionChange={handleInstructionChange}
            handleAddInstruction={handleAddInstruction}
            handleRemoveInstruction={handleRemoveInstruction}
            tips={formData.tips || []}
            newTip={newTip}
            setNewTip={setNewTip}
            handleAddTip={handleAddTip}
            handleRemoveTip={handleRemoveTip}
            notes={formData.notes || ''}
            handleNotesChange={handleNotesChange}
          />
        )}
        
        {/* Nutrition Tab */}
        {activeTab === 'nutrition' && (
          <RecipeFormNutrition 
            nutritionInfo={formData.nutritionInfo || {}}
            handleNutritionChange={handleNutritionChange}
          />
        )}
        
        {/* Costing Tab */}
        {activeTab === 'costing' && (
          <RecipeFormCosting 
            ingredients={formData.ingredients}
            costPerUnit={formData.costPerUnit || 0}
            sellingPrice={formData.sellingPrice || 0}
            profitMargin={formData.profitMargin || 0}
            yield={formData.yield}
            yieldUnit={formData.yieldUnit}
            handleNumberInputChange={handleNumberInputChange}
            calculateRecipeCost={() => calculateRecipeCost()}
          />
        )}
        
        {/* Additional Info Tab */}
        {activeTab === 'additional' && (
          <RecipeFormAdditional 
            tags={formData.tags}
            newTag={newTag}
            setNewTag={setNewTag}
            handleAddTag={handleAddTag}
            handleRemoveTag={handleRemoveTag}
            imageUrl={formData.imageUrl || ''}
            handleInputChange={handleInputChange}
            isActive={formData.isActive}
            handleCheckboxChange={handleCheckboxChange}
          />
        )}
      </div>
      
      <div className="flex justify-end gap-2">
        <Button type="submit">
          {initialData.id ? 'Update Recipe' : 'Save Recipe'}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
}
