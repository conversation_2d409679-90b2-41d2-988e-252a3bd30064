'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Clock,
  ChefHat,
  Utensils,
  IndianRupee,
  Tag,
  FileText,
  Printer,
  Edit,
  ArrowLeft
} from 'lucide-react';

interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost?: number;
  isOptional?: boolean;
  substitutes?: string;
}

interface Recipe {
  id: string;
  name: string;
  description: string;
  preparationTime: number;
  bakingTime: number;
  restingTime?: number;
  totalTime?: number;
  yield: number;
  yieldUnit: string;
  difficulty: string;
  category: string;
  tags: string[];
  ingredients: Ingredient[];
  instructions: string[];
  notes?: string;
  tips?: string[];
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sugar?: number;
  };
  costPerUnit?: number;
  sellingPrice?: number;
  profitMargin?: number;
  imageUrl?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface RecipeDetailProps {
  recipe: Recipe;
  onBack: () => void;
  onEdit: (recipe: Recipe) => void;
}

export function RecipeDetail({ recipe, onBack, onEdit }: RecipeDetailProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [servings, setServings] = useState(recipe.yield);

  const formatTime = (minutes: number): string => {
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours} hr ${mins} min` : `${hours} hr`;
  };

  const calculateScaledQuantity = (quantity: number): number => {
    if (recipe.yield === 0) return quantity;
    const scaleFactor = servings / recipe.yield;
    return parseFloat((quantity * scaleFactor).toFixed(2));
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="space-y-6 print:p-6">
      <div className="flex items-center justify-between print:hidden">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Recipes
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button onClick={() => onEdit(recipe)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Recipe
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold">{recipe.name}</h1>
          <p className="text-muted-foreground">{recipe.description}</p>
        </div>

        <div className="flex flex-wrap gap-2">
          {recipe.tags.map(tag => (
            <span
              key={tag}
              className="rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary"
            >
              {tag}
            </span>
          ))}
        </div>

        <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
          <div className="flex items-center rounded-lg border p-3">
            <Clock className="mr-3 h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Total Time</p>
              <p className="text-lg">{formatTime(recipe.totalTime || (recipe.preparationTime + recipe.bakingTime + (recipe.restingTime || 0)))}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border p-3">
            <ChefHat className="mr-3 h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Difficulty</p>
              <p className="text-lg">{recipe.difficulty}</p>
            </div>
          </div>

          <div className="flex items-center rounded-lg border p-3">
            <Utensils className="mr-3 h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Yield</p>
              <p className="text-lg">{recipe.yield} {recipe.yieldUnit}</p>
            </div>
          </div>

          {recipe.costPerUnit && (
            <div className="flex items-center rounded-lg border p-3">
              <IndianRupee className="mr-3 h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Cost per {recipe.yieldUnit}</p>
                <p className="text-lg">₹{recipe.costPerUnit.toFixed(2)}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex border-b print:hidden">
        <button
          className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`px-4 py-2 font-medium ${activeTab === 'nutrition' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('nutrition')}
        >
          Nutrition
        </button>
        <button
          className={`px-4 py-2 font-medium ${activeTab === 'costing' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
          onClick={() => setActiveTab('costing')}
        >
          Costing
        </button>
      </div>

      {/* Always show ingredients and instructions for printing */}
      <div className={activeTab !== 'overview' && !window.matchMedia('print').matches ? 'hidden' : ''}>
        <div className="grid gap-8 md:grid-cols-[1fr_2fr]">
          <div className="space-y-6">
            <div>
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-semibold">Ingredients</h2>
                <div className="flex items-center space-x-2 print:hidden">
                  <span className="text-sm">Servings:</span>
                  <input
                    type="number"
                    min="1"
                    value={servings}
                    onChange={(e) => setServings(parseInt(e.target.value) || recipe.yield)}
                    className="w-16 rounded-md border px-2 py-1 text-sm"
                  />
                </div>
              </div>

              <ul className="space-y-2">
                {recipe.ingredients.map((ingredient) => (
                  <li key={ingredient.id} className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>
                      {calculateScaledQuantity(ingredient.quantity)} {ingredient.unit} {ingredient.name}
                      {ingredient.isOptional && <span className="text-sm text-muted-foreground"> (optional)</span>}
                      {ingredient.substitutes && (
                        <span className="block text-sm text-muted-foreground">
                          Substitute: {ingredient.substitutes}
                        </span>
                      )}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            {recipe.tips && recipe.tips.length > 0 && (
              <div>
                <h2 className="mb-2 text-xl font-semibold">Tips</h2>
                <ul className="space-y-2">
                  {recipe.tips.map((tip, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-2">•</span>
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="space-y-6">
            <div>
              <h2 className="mb-4 text-xl font-semibold">Instructions</h2>
              <ol className="space-y-4">
                {recipe.instructions.map((instruction, index) => (
                  <li key={index} className="flex">
                    <span className="mr-4 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-primary text-sm font-medium text-primary-foreground">
                      {index + 1}
                    </span>
                    <span>{instruction}</span>
                  </li>
                ))}
              </ol>
            </div>

            {recipe.notes && (
              <div className="rounded-lg bg-muted p-4">
                <h3 className="mb-2 font-medium">Notes</h3>
                <p>{recipe.notes}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Nutrition Tab */}
      <div className={activeTab !== 'nutrition' ? 'hidden' : ''}>
        {recipe.nutritionInfo ? (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Nutrition Information</h2>
            <p className="text-sm text-muted-foreground">Per {recipe.yieldUnit} (approximate values)</p>

            <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
              {recipe.nutritionInfo.calories !== undefined && (
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Calories</p>
                  <p className="text-2xl font-bold">{recipe.nutritionInfo.calories}</p>
                </div>
              )}

              {recipe.nutritionInfo.protein !== undefined && (
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Protein</p>
                  <p className="text-2xl font-bold">{recipe.nutritionInfo.protein}g</p>
                </div>
              )}

              {recipe.nutritionInfo.carbs !== undefined && (
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Carbohydrates</p>
                  <p className="text-2xl font-bold">{recipe.nutritionInfo.carbs}g</p>
                </div>
              )}

              {recipe.nutritionInfo.fat !== undefined && (
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Fat</p>
                  <p className="text-2xl font-bold">{recipe.nutritionInfo.fat}g</p>
                </div>
              )}

              {recipe.nutritionInfo.fiber !== undefined && (
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Fiber</p>
                  <p className="text-2xl font-bold">{recipe.nutritionInfo.fiber}g</p>
                </div>
              )}

              {recipe.nutritionInfo.sugar !== undefined && (
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Sugar</p>
                  <p className="text-2xl font-bold">{recipe.nutritionInfo.sugar}g</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="rounded-lg border border-dashed p-8 text-center">
            <p className="text-muted-foreground">No nutrition information available for this recipe.</p>
          </div>
        )}
      </div>

      {/* Costing Tab */}
      <div className={activeTab !== 'costing' ? 'hidden' : ''}>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Recipe Costing</h2>

          {recipe.costPerUnit ? (
            <>
              <div className="grid gap-4 sm:grid-cols-3">
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Cost per {recipe.yieldUnit}</p>
                  <p className="text-2xl font-bold">₹{recipe.costPerUnit.toFixed(2)}</p>
                </div>

                {recipe.sellingPrice && (
                  <div className="rounded-lg border p-4">
                    <p className="text-sm font-medium text-muted-foreground">Selling Price</p>
                    <p className="text-2xl font-bold">₹{recipe.sellingPrice.toFixed(2)}</p>
                  </div>
                )}

                {recipe.profitMargin && (
                  <div className="rounded-lg border p-4">
                    <p className="text-sm font-medium text-muted-foreground">Profit Margin</p>
                    <p className="text-2xl font-bold">{recipe.profitMargin.toFixed(0)}%</p>
                  </div>
                )}
              </div>

              <div className="rounded-lg border">
                <h3 className="border-b px-4 py-3 font-medium">Ingredient Cost Breakdown</h3>
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="px-4 py-2 text-left">Ingredient</th>
                      <th className="px-4 py-2 text-right">Quantity</th>
                      <th className="px-4 py-2 text-right">Unit Cost</th>
                      <th className="px-4 py-2 text-right">Total Cost</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recipe.ingredients.map(ingredient => {
                      const cost = ingredient.cost || 0;
                      const totalCost = cost * ingredient.quantity;
                      return (
                        <tr key={ingredient.id} className="border-b">
                          <td className="px-4 py-2">{ingredient.name}</td>
                          <td className="px-4 py-2 text-right">{ingredient.quantity} {ingredient.unit}</td>
                          <td className="px-4 py-2 text-right">₹{cost.toFixed(2)}/{ingredient.unit}</td>
                          <td className="px-4 py-2 text-right">₹{totalCost.toFixed(2)}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                  <tfoot>
                    <tr className="font-medium">
                      <td colSpan={3} className="px-4 py-2 text-right">Total Recipe Cost:</td>
                      <td className="px-4 py-2 text-right">
                        ₹{(recipe.costPerUnit * recipe.yield).toFixed(2)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </>
          ) : (
            <div className="rounded-lg border border-dashed p-8 text-center">
              <p className="text-muted-foreground">No costing information available for this recipe.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
