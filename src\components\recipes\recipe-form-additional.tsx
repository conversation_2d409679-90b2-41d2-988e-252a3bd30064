'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X, Upload } from 'lucide-react';

interface AdditionalInfoProps {
  tags: string[];
  newTag: string;
  setNewTag: (value: string) => void;
  handleAddTag: () => void;
  handleRemoveTag: (tag: string) => void;
  imageUrl: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isActive: boolean;
  handleCheckboxChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function RecipeFormAdditional({
  tags,
  newTag,
  setNewTag,
  handleAddTag,
  handleRemoveTag,
  imageUrl,
  handleInputChange,
  isActive,
  handleCheckboxChange
}: AdditionalInfoProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Tags</h3>
        <div className="flex flex-wrap gap-2">
          {tags.map(tag => (
            <div
              key={tag}
              className="flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm text-primary"
            >
              <span>{tag}</span>
              <button
                type="button"
                className="ml-1 rounded-full p-1 hover:bg-primary/20"
                onClick={() => handleRemoveTag(tag)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove</span>
              </button>
            </div>
          ))}

          {tags.length === 0 && (
            <div className="text-sm text-muted-foreground">No tags added yet</div>
          )}
        </div>

        <div className="flex gap-2">
          <Input
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder="Add a tag (e.g., vegan, gluten-free)"
          />
          <Button
            type="button"
            variant="outline"
            onClick={handleAddTag}
            disabled={!newTag}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add
          </Button>
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="imageUrl" className="text-sm font-medium">
          Recipe Image
        </label>
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Input
              id="imageUrl"
              name="imageUrl"
              value={imageUrl}
              onChange={handleInputChange}
              placeholder="Enter URL for recipe image or use the upload button"
            />
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              // Open file input dialog
              const fileInput = document.createElement('input');
              fileInput.type = 'file';
              fileInput.accept = 'image/*';
              fileInput.onchange = async (e) => {
                const target = e.target as HTMLInputElement;
                if (target.files && target.files[0]) {
                  const file = target.files[0];

                  // Create a preview URL
                  const reader = new FileReader();
                  reader.onloadend = async () => {
                    const base64Data = reader.result as string;

                    try {
                      const response = await fetch('/api/upload', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          image: base64Data,
                          folder: 'recipes'
                        }),
                      });

                      if (!response.ok) {
                        throw new Error('Failed to upload image');
                      }

                      const data = await response.json();
                      // Update the imageUrl using the handleInputChange function
                      const syntheticEvent = {
                        target: {
                          name: 'imageUrl',
                          value: data.imageUrl
                        }
                      } as React.ChangeEvent<HTMLInputElement>;
                      handleInputChange(syntheticEvent);
                    } catch (error) {
                      console.error('Error uploading image:', error);
                    }
                  };
                  reader.readAsDataURL(file);
                }
              };
              fileInput.click();
            }}
          >
            Upload Image
          </Button>
        </div>
        {imageUrl && (
          <div className="mt-2 rounded-md border p-2">
            <img
              src={imageUrl}
              alt="Recipe preview"
              className="mx-auto max-h-40 object-contain"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://placehold.co/400x300?text=Invalid+Image+URL';
              }}
            />
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="isActive"
          name="isActive"
          checked={isActive}
          onChange={handleCheckboxChange}
          className="h-4 w-4 rounded border-gray-300"
        />
        <label htmlFor="isActive" className="text-sm font-medium">
          Recipe is active and ready for production
        </label>
      </div>
    </div>
  );
}
