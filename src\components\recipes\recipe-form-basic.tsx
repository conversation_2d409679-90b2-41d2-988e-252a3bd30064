'use client';

import { Input } from '@/components/ui/input';

interface BasicInfoProps {
  formData: any;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleNumberInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function RecipeFormBasic({ formData, handleInputChange, handleNumberInputChange }: BasicInfoProps) {
  const difficultyLevels = ['Easy', 'Medium', 'Hard', 'Expert'];
  const categories = [
    'Bread', 
    'Cakes', 
    'Pastries', 
    'Cookies', 
    'Desserts', 
    'Savory', 
    'Gluten-Free', 
    'Vegan', 
    'Other'
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Basic Information</h3>
      
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <label htmlFor="name" className="text-sm font-medium">
            Recipe Name
          </label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Enter recipe name"
            required
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="category" className="text-sm font-medium">
            Category
          </label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleInputChange}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            required
          >
            <option value="">Select a category</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          placeholder="Enter recipe description"
          className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          required
        />
      </div>
      
      <div className="grid gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <label htmlFor="preparationTime" className="text-sm font-medium">
            Preparation Time (min)
          </label>
          <Input
            id="preparationTime"
            name="preparationTime"
            type="number"
            min="0"
            value={formData.preparationTime}
            onChange={handleNumberInputChange}
            required
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="bakingTime" className="text-sm font-medium">
            Baking Time (min)
          </label>
          <Input
            id="bakingTime"
            name="bakingTime"
            type="number"
            min="0"
            value={formData.bakingTime}
            onChange={handleNumberInputChange}
            required
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="restingTime" className="text-sm font-medium">
            Resting Time (min)
          </label>
          <Input
            id="restingTime"
            name="restingTime"
            type="number"
            min="0"
            value={formData.restingTime}
            onChange={handleNumberInputChange}
          />
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <label htmlFor="yield" className="text-sm font-medium">
            Yield
          </label>
          <Input
            id="yield"
            name="yield"
            type="number"
            min="1"
            step="0.5"
            value={formData.yield}
            onChange={handleNumberInputChange}
            required
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="yieldUnit" className="text-sm font-medium">
            Yield Unit
          </label>
          <Input
            id="yieldUnit"
            name="yieldUnit"
            value={formData.yieldUnit}
            onChange={handleInputChange}
            placeholder="e.g., loaf, cake, pieces"
            required
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="difficulty" className="text-sm font-medium">
            Difficulty
          </label>
          <select
            id="difficulty"
            name="difficulty"
            value={formData.difficulty}
            onChange={handleInputChange}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            required
          >
            <option value="">Select difficulty</option>
            {difficultyLevels.map(level => (
              <option key={level} value={level}>
                {level}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}
