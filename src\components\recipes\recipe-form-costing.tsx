'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calculator } from 'lucide-react';

interface CostingTabProps {
  ingredients: Array<{
    id: string;
    name: string;
    quantity: number;
    unit: string;
    cost?: number;
  }>;
  costPerUnit: number;
  sellingPrice: number;
  profitMargin: number;
  yield: number;
  yieldUnit: string;
  handleNumberInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  calculateRecipeCost: () => void;
}

export function RecipeFormCosting({
  ingredients,
  costPerUnit,
  sellingPrice,
  profitMargin,
  yield: recipeYield,
  yieldUnit,
  handleNumberInputChange,
  calculateRecipeCost
}: CostingTabProps) {
  const totalCost = costPerUnit * recipeYield;
  
  const ingredientsWithCost = ingredients.filter(i => i.cost !== undefined && i.cost > 0);
  const hasIngredientCosts = ingredientsWithCost.length > 0;
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Recipe Costing</h3>
        {hasIngredientCosts && (
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={calculateRecipeCost}
          >
            <Calculator className="mr-2 h-4 w-4" />
            Calculate Cost
          </Button>
        )}
      </div>
      
      {!hasIngredientCosts && (
        <div className="rounded-lg border border-dashed p-4 text-center text-muted-foreground">
          <p>Add ingredient costs in the Ingredients tab to calculate recipe cost.</p>
        </div>
      )}
      
      <div className="grid gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <label htmlFor="costPerUnit" className="text-sm font-medium">
            Cost per {yieldUnit}
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">$</span>
            <Input
              id="costPerUnit"
              name="costPerUnit"
              type="number"
              min="0"
              step="0.01"
              value={costPerUnit}
              onChange={handleNumberInputChange}
              className="pl-7"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <label htmlFor="sellingPrice" className="text-sm font-medium">
            Selling Price
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">$</span>
            <Input
              id="sellingPrice"
              name="sellingPrice"
              type="number"
              min="0"
              step="0.01"
              value={sellingPrice}
              onChange={handleNumberInputChange}
              className="pl-7"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <label htmlFor="profitMargin" className="text-sm font-medium">
            Profit Margin
          </label>
          <div className="relative">
            <Input
              id="profitMargin"
              name="profitMargin"
              type="number"
              min="0"
              max="100"
              value={profitMargin}
              onChange={handleNumberInputChange}
              disabled
              className="pr-7"
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">%</span>
          </div>
        </div>
      </div>
      
      {hasIngredientCosts && (
        <div className="rounded-lg border">
          <h4 className="border-b px-4 py-2 font-medium">Ingredient Cost Breakdown</h4>
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="px-4 py-2 text-left">Ingredient</th>
                <th className="px-4 py-2 text-right">Quantity</th>
                <th className="px-4 py-2 text-right">Unit Cost</th>
                <th className="px-4 py-2 text-right">Total Cost</th>
              </tr>
            </thead>
            <tbody>
              {ingredientsWithCost.map(ingredient => {
                const cost = ingredient.cost || 0;
                const totalCost = cost * ingredient.quantity;
                return (
                  <tr key={ingredient.id} className="border-b">
                    <td className="px-4 py-2">{ingredient.name}</td>
                    <td className="px-4 py-2 text-right">{ingredient.quantity} {ingredient.unit}</td>
                    <td className="px-4 py-2 text-right">${cost.toFixed(2)}/{ingredient.unit}</td>
                    <td className="px-4 py-2 text-right">${totalCost.toFixed(2)}</td>
                  </tr>
                );
              })}
            </tbody>
            <tfoot>
              <tr className="font-medium">
                <td colSpan={3} className="px-4 py-2 text-right">Total Recipe Cost:</td>
                <td className="px-4 py-2 text-right">${totalCost.toFixed(2)}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      )}
    </div>
  );
}
