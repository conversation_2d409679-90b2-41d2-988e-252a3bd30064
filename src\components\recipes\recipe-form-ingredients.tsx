'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Trash } from 'lucide-react';

interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost?: number;
  category?: string;
  isOptional?: boolean;
  substitutes?: string;
}

interface IngredientsTabProps {
  ingredients: Ingredient[];
  newIngredient: {
    name: string;
    quantity: string;
    unit: string;
    cost: string;
    category: string;
    isOptional: boolean;
    substitutes: string;
  };
  handleIngredientInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  handleAddIngredient: () => void;
  handleRemoveIngredient: (id: string) => void;
  rawMaterials?: Array<{id: string; name: string; unit: string; cost: number}>;
}

export function RecipeFormIngredients({ 
  ingredients, 
  newIngredient, 
  handleIngredientInputChange, 
  handleAddIngredient, 
  handleRemoveIngredient,
  rawMaterials
}: IngredientsTabProps) {
  const ingredientCategories = [
    'Flour & Grains',
    'Sweeteners',
    'Dairy',
    'Eggs',
    'Fats & Oils',
    'Leavening Agents',
    'Flavorings',
    'Nuts & Seeds',
    'Fruits',
    'Other'
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Ingredients</h3>
      
      <div className="rounded-md border">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-muted/50">
              <th className="px-4 py-2 text-left font-medium">Ingredient</th>
              <th className="px-4 py-2 text-left font-medium">Quantity</th>
              <th className="px-4 py-2 text-left font-medium">Unit</th>
              <th className="px-4 py-2 text-left font-medium">Cost</th>
              <th className="px-4 py-2 text-left font-medium">Optional</th>
              <th className="px-4 py-2 text-right font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {ingredients.map((ingredient) => (
              <tr key={ingredient.id} className="border-b">
                <td className="px-4 py-2">{ingredient.name}</td>
                <td className="px-4 py-2">{ingredient.quantity}</td>
                <td className="px-4 py-2">{ingredient.unit}</td>
                <td className="px-4 py-2">{ingredient.cost ? `$${ingredient.cost.toFixed(2)}` : '-'}</td>
                <td className="px-4 py-2">{ingredient.isOptional ? 'Yes' : 'No'}</td>
                <td className="px-4 py-2 text-right">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemoveIngredient(ingredient.id)}
                  >
                    <Trash className="h-4 w-4" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </td>
              </tr>
            ))}
            <tr>
              <td className="px-4 py-2">
                {rawMaterials ? (
                  <select
                    name="name"
                    value={newIngredient.name}
                    onChange={handleIngredientInputChange}
                    className="w-full rounded-md border-0 bg-transparent text-sm focus:ring-0"
                  >
                    <option value="">Select an ingredient</option>
                    {rawMaterials.map(material => (
                      <option key={material.id} value={material.name}>
                        {material.name}
                      </option>
                    ))}
                  </select>
                ) : (
                  <Input
                    name="name"
                    value={newIngredient.name}
                    onChange={handleIngredientInputChange}
                    placeholder="Ingredient name"
                  />
                )}
              </td>
              <td className="px-4 py-2">
                <Input
                  name="quantity"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={newIngredient.quantity}
                  onChange={handleIngredientInputChange}
                  placeholder="Quantity"
                />
              </td>
              <td className="px-4 py-2">
                <Input
                  name="unit"
                  value={newIngredient.unit}
                  onChange={handleIngredientInputChange}
                  placeholder="Unit (g, ml, etc.)"
                />
              </td>
              <td className="px-4 py-2">
                <Input
                  name="cost"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newIngredient.cost}
                  onChange={handleIngredientInputChange}
                  placeholder="Cost per unit"
                />
              </td>
              <td className="px-4 py-2">
                <input
                  type="checkbox"
                  name="isOptional"
                  checked={newIngredient.isOptional}
                  onChange={handleIngredientInputChange}
                  className="h-4 w-4 rounded border-gray-300"
                />
              </td>
              <td className="px-4 py-2 text-right">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={handleAddIngredient}
                >
                  <Plus className="h-4 w-4" />
                  <span className="sr-only">Add</span>
                </Button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div className="space-y-2">
        <label htmlFor="substitutes" className="text-sm font-medium">
          Substitutes (for the ingredient being added)
        </label>
        <Input
          id="substitutes"
          name="substitutes"
          value={newIngredient.substitutes}
          onChange={handleIngredientInputChange}
          placeholder="Possible substitutes for this ingredient"
        />
      </div>
      
      <div className="space-y-2">
        <label htmlFor="category" className="text-sm font-medium">
          Category (for the ingredient being added)
        </label>
        <select
          id="category"
          name="category"
          value={newIngredient.category}
          onChange={handleIngredientInputChange}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select a category</option>
          {ingredientCategories.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
