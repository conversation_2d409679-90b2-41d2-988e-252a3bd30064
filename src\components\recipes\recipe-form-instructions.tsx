'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Trash } from 'lucide-react';

interface InstructionsTabProps {
  instructions: string[];
  handleInstructionChange: (index: number, value: string) => void;
  handleAddInstruction: () => void;
  handleRemoveInstruction: (index: number) => void;
  tips: string[];
  newTip: string;
  setNewTip: (value: string) => void;
  handleAddTip: () => void;
  handleRemoveTip: (index: number) => void;
  notes: string;
  handleNotesChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

export function RecipeFormInstructions({
  instructions,
  handleInstructionChange,
  handleAddInstruction,
  handleRemoveInstruction,
  tips,
  newTip,
  setNewTip,
  handleAddTip,
  handleRemoveTip,
  notes,
  handleNotesChange
}: InstructionsTabProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Instructions</h3>
        
        <div className="space-y-2">
          {instructions.map((instruction, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-primary text-sm font-medium text-primary-foreground">
                {index + 1}
              </div>
              <Input
                value={instruction}
                onChange={(e) => handleInstructionChange(index, e.target.value)}
                placeholder={`Step ${index + 1}`}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveInstruction(index)}
              >
                <Trash className="h-4 w-4" />
                <span className="sr-only">Remove</span>
              </Button>
            </div>
          ))}
          
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddInstruction}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Step
          </Button>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Tips & Tricks</h3>
        
        <div className="space-y-2">
          {tips.map((tip, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="flex-shrink-0 text-sm font-medium">•</div>
              <Input
                value={tip}
                onChange={(e) => handleInstructionChange(index, e.target.value)}
                disabled
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveTip(index)}
              >
                <Trash className="h-4 w-4" />
                <span className="sr-only">Remove</span>
              </Button>
            </div>
          ))}
          
          <div className="flex gap-2">
            <Input
              value={newTip}
              onChange={(e) => setNewTip(e.target.value)}
              placeholder="Add a helpful tip"
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleAddTip}
              disabled={!newTip}
            >
              Add
            </Button>
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        <label htmlFor="notes" className="text-sm font-medium">
          Additional Notes
        </label>
        <textarea
          id="notes"
          name="notes"
          value={notes}
          onChange={handleNotesChange}
          placeholder="Any additional notes about the recipe"
          className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        />
      </div>
    </div>
  );
}
