'use client';

import { Input } from '@/components/ui/input';

interface NutritionTabProps {
  nutritionInfo: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sugar?: number;
  };
  handleNutritionChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function RecipeFormNutrition({ nutritionInfo, handleNutritionChange }: NutritionTabProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Nutrition Information</h3>
      <p className="text-sm text-muted-foreground">Enter nutrition information per serving</p>
      
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
        <div className="space-y-2">
          <label htmlFor="calories" className="text-sm font-medium">
            Calories
          </label>
          <Input
            id="calories"
            name="calories"
            type="number"
            min="0"
            value={nutritionInfo.calories || ''}
            onChange={handleNutritionChange}
            placeholder="Calories"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="protein" className="text-sm font-medium">
            Protein (g)
          </label>
          <Input
            id="protein"
            name="protein"
            type="number"
            min="0"
            step="0.1"
            value={nutritionInfo.protein || ''}
            onChange={handleNutritionChange}
            placeholder="Protein in grams"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="carbs" className="text-sm font-medium">
            Carbohydrates (g)
          </label>
          <Input
            id="carbs"
            name="carbs"
            type="number"
            min="0"
            step="0.1"
            value={nutritionInfo.carbs || ''}
            onChange={handleNutritionChange}
            placeholder="Carbs in grams"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="fat" className="text-sm font-medium">
            Fat (g)
          </label>
          <Input
            id="fat"
            name="fat"
            type="number"
            min="0"
            step="0.1"
            value={nutritionInfo.fat || ''}
            onChange={handleNutritionChange}
            placeholder="Fat in grams"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="fiber" className="text-sm font-medium">
            Fiber (g)
          </label>
          <Input
            id="fiber"
            name="fiber"
            type="number"
            min="0"
            step="0.1"
            value={nutritionInfo.fiber || ''}
            onChange={handleNutritionChange}
            placeholder="Fiber in grams"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="sugar" className="text-sm font-medium">
            Sugar (g)
          </label>
          <Input
            id="sugar"
            name="sugar"
            type="number"
            min="0"
            step="0.1"
            value={nutritionInfo.sugar || ''}
            onChange={handleNutritionChange}
            placeholder="Sugar in grams"
          />
        </div>
      </div>
    </div>
  );
}
