'use client';

import { useState } from 'react';

interface TabProps {
  activeTab: string;
  onChange: (tab: string) => void;
}

export function RecipeFormTabs({ activeTab, onChange }: TabProps) {
  const tabs = [
    { id: 'basic', label: 'Basic Info' },
    { id: 'ingredients', label: 'Ingredients' },
    { id: 'instructions', label: 'Instructions' },
    { id: 'nutrition', label: 'Nutrition' },
    { id: 'costing', label: 'Costing' },
    { id: 'additional', label: 'Additional Info' }
  ];

  return (
    <div className="mb-6 flex flex-wrap border-b">
      {tabs.map(tab => (
        <button
          key={tab.id}
          className={`px-4 py-2 font-medium transition-colors ${
            activeTab === tab.id 
              ? 'border-b-2 border-primary text-primary' 
              : 'text-muted-foreground hover:text-foreground'
          }`}
          onClick={() => onChange(tab.id)}
          type="button"
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
}
