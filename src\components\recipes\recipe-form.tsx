'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Trash } from 'lucide-react';

interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost?: number;
  category?: string;
  isOptional?: boolean;
  substitutes?: string;
}

interface Recipe {
  id?: string;
  name: string;
  description: string;
  preparationTime: number;
  bakingTime: number;
  restingTime?: number;
  totalTime?: number;
  yield: number;
  yieldUnit: string;
  difficulty: string;
  category: string;
  tags: string[];
  ingredients: Ingredient[];
  instructions: string[];
  notes?: string;
  tips?: string[];
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sugar?: number;
  };
  costPerUnit?: number;
  sellingPrice?: number;
  profitMargin?: number;
  imageUrl?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface RecipeFormProps {
  initialData?: Recipe;
  onSubmit: (data: Recipe) => void;
  onCancel: () => void;
  rawMaterials?: Array<{id: string; name: string; unit: string; cost: number}>;
}

const defaultRecipe: Recipe = {
  name: '',
  description: '',
  preparationTime: 0,
  bakingTime: 0,
  restingTime: 0,
  totalTime: 0,
  yield: 1,
  yieldUnit: 'piece',
  difficulty: 'Medium',
  category: 'Other',
  tags: [],
  ingredients: [],
  instructions: [''],
  notes: '',
  tips: [],
  nutritionInfo: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0
  },
  costPerUnit: 0,
  sellingPrice: 0,
  profitMargin: 0,
  isActive: true
};

export function RecipeForm({ initialData = defaultRecipe, onSubmit, onCancel }: RecipeFormProps) {
  const [formData, setFormData] = useState(initialData);
  const [newIngredient, setNewIngredient] = useState({
    name: '',
    quantity: '',
    unit: '',
    cost: '',
    category: '',
    isOptional: false,
    substitutes: ''
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [newTag, setNewTag] = useState('');
  const [newTip, setNewTip] = useState('');
  const [calculatingCost, setCalculatingCost] = useState(false);

  // Calculate total time whenever prep, baking, or resting time changes
  useEffect(() => {
    const total = (formData.preparationTime || 0) +
                 (formData.bakingTime || 0) +
                 (formData.restingTime || 0);
    setFormData(prev => ({ ...prev, totalTime: total }));
  }, [formData.preparationTime, formData.bakingTime, formData.restingTime]);

  // Calculate profit margin whenever cost or selling price changes
  useEffect(() => {
    if (formData.costPerUnit && formData.sellingPrice && formData.costPerUnit > 0) {
      const margin = ((formData.sellingPrice - formData.costPerUnit) / formData.sellingPrice) * 100;
      setFormData(prev => ({ ...prev, profitMargin: parseFloat(margin.toFixed(2)) }));
    }
  }, [formData.costPerUnit, formData.sellingPrice]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: parseFloat(value) || 0 });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({ ...formData, [name]: checked });
  };

  const handleIngredientInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'isOptional' && e.target instanceof HTMLInputElement) {
      setNewIngredient({ ...newIngredient, [name]: e.target.checked });
    } else {
      setNewIngredient({ ...newIngredient, [name]: value });
    }
  };

  const handleNutritionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      nutritionInfo: {
        ...formData.nutritionInfo,
        [name]: parseFloat(value) || 0
      }
    });
  };

  const handleAddIngredient = () => {
    if (newIngredient.name && newIngredient.quantity && newIngredient.unit) {
      const ingredient = {
        id: Date.now().toString(),
        name: newIngredient.name,
        quantity: parseFloat(newIngredient.quantity),
        unit: newIngredient.unit,
        cost: parseFloat(newIngredient.cost) || undefined,
        category: newIngredient.category || undefined,
        isOptional: newIngredient.isOptional,
        substitutes: newIngredient.substitutes || undefined
      };

      setFormData({
        ...formData,
        ingredients: [...formData.ingredients, ingredient],
      });

      // Recalculate recipe cost if we have cost information
      if (ingredient.cost) {
        calculateRecipeCost([...formData.ingredients, ingredient]);
      }

      setNewIngredient({
        name: '',
        quantity: '',
        unit: '',
        cost: '',
        category: '',
        isOptional: false,
        substitutes: ''
      });
    }
  };

  const calculateRecipeCost = (ingredients = formData.ingredients) => {
    // Only calculate if we have yield information
    if (formData.yield <= 0) return;

    const totalCost = ingredients.reduce((sum, ingredient) => {
      return sum + (ingredient.cost || 0) * ingredient.quantity;
    }, 0);

    const costPerUnit = totalCost / formData.yield;

    setFormData(prev => ({
      ...prev,
      costPerUnit: parseFloat(costPerUnit.toFixed(2))
    }));
  };

  const handleAddTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag]
      });
      setNewTag('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    });
  };

  const handleAddTip = () => {
    if (newTip) {
      setFormData({
        ...formData,
        tips: [...formData.tips, newTip]
      });
      setNewTip('');
    }
  };

  const handleRemoveTip = (index: number) => {
    const updatedTips = [...formData.tips];
    updatedTips.splice(index, 1);
    setFormData({
      ...formData,
      tips: updatedTips
    });
  };

  const handleRemoveIngredient = (id: string) => {
    setFormData({
      ...formData,
      ingredients: formData.ingredients.filter(ingredient => ingredient.id !== id),
    });
  };

  const handleAddInstruction = () => {
    setFormData({
      ...formData,
      instructions: [...formData.instructions, ''],
    });
  };

  const handleInstructionChange = (index: number, value: string) => {
    const updatedInstructions = [...formData.instructions];
    updatedInstructions[index] = value;
    setFormData({
      ...formData,
      instructions: updatedInstructions,
    });
  };

  const handleRemoveInstruction = (index: number) => {
    const updatedInstructions = [...formData.instructions];
    updatedInstructions.splice(index, 1);
    setFormData({
      ...formData,
      instructions: updatedInstructions,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Recipe Name
            </label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter recipe name"
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="yieldUnit" className="text-sm font-medium">
              Yield Unit
            </label>
            <Input
              id="yieldUnit"
              name="yieldUnit"
              value={formData.yieldUnit}
              onChange={handleInputChange}
              placeholder="e.g., loaf, cake, pieces"
              required
            />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <label htmlFor="preparationTime" className="text-sm font-medium">
              Preparation Time (min)
            </label>
            <Input
              id="preparationTime"
              name="preparationTime"
              type="number"
              min="0"
              value={formData.preparationTime}
              onChange={handleNumberInputChange}
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="bakingTime" className="text-sm font-medium">
              Baking Time (min)
            </label>
            <Input
              id="bakingTime"
              name="bakingTime"
              type="number"
              min="0"
              value={formData.bakingTime}
              onChange={handleNumberInputChange}
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="yield" className="text-sm font-medium">
              Yield
            </label>
            <Input
              id="yield"
              name="yield"
              type="number"
              min="1"
              step="0.5"
              value={formData.yield}
              onChange={handleNumberInputChange}
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="description" className="text-sm font-medium">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter recipe description"
            className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            required
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Ingredients</h3>

        <div className="rounded-md border">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="px-4 py-2 text-left font-medium">Ingredient</th>
                <th className="px-4 py-2 text-left font-medium">Quantity</th>
                <th className="px-4 py-2 text-left font-medium">Unit</th>
                <th className="px-4 py-2 text-right font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {formData.ingredients.map((ingredient) => (
                <tr key={ingredient.id} className="border-b">
                  <td className="px-4 py-2">{ingredient.name}</td>
                  <td className="px-4 py-2">{ingredient.quantity}</td>
                  <td className="px-4 py-2">{ingredient.unit}</td>
                  <td className="px-4 py-2 text-right">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveIngredient(ingredient.id)}
                    >
                      <Trash className="h-4 w-4" />
                      <span className="sr-only">Remove</span>
                    </Button>
                  </td>
                </tr>
              ))}
              <tr>
                <td className="px-4 py-2">
                  <Input
                    name="name"
                    value={newIngredient.name}
                    onChange={handleIngredientInputChange}
                    placeholder="Ingredient name"
                  />
                </td>
                <td className="px-4 py-2">
                  <Input
                    name="quantity"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={newIngredient.quantity}
                    onChange={handleIngredientInputChange}
                    placeholder="Quantity"
                  />
                </td>
                <td className="px-4 py-2">
                  <Input
                    name="unit"
                    value={newIngredient.unit}
                    onChange={handleIngredientInputChange}
                    placeholder="Unit (g, ml, etc.)"
                  />
                </td>
                <td className="px-4 py-2 text-right">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={handleAddIngredient}
                  >
                    <Plus className="h-4 w-4" />
                    <span className="sr-only">Add</span>
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Instructions</h3>

        <div className="space-y-2">
          {formData.instructions.map((instruction, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="flex-shrink-0 text-sm font-medium">{index + 1}.</div>
              <Input
                value={instruction}
                onChange={(e) => handleInstructionChange(index, e.target.value)}
                placeholder={`Step ${index + 1}`}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveInstruction(index)}
              >
                <Trash className="h-4 w-4" />
                <span className="sr-only">Remove</span>
              </Button>
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddInstruction}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Step
          </Button>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="submit">
          {initialData.id ? 'Update Recipe' : 'Save Recipe'}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
}
