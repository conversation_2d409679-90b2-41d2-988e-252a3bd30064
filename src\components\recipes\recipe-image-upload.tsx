'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Upload, X } from 'lucide-react';

interface RecipeImageUploadProps {
  imageUrl: string;
  onImageChange: (url: string) => void;
}

export function RecipeImageUpload({ imageUrl, onImageChange }: RecipeImageUploadProps) {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(imageUrl || null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      setError(null);

      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Data = reader.result as string;
        setImagePreview(base64Data);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!imagePreview || imagePreview === imageUrl) return;
    
    try {
      setIsUploading(true);
      setError(null);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imagePreview,
          folder: 'recipes'
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload image');
      }
      
      const data = await response.json();
      onImageChange(data.imageUrl);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    setImageFile(null);
    onImageChange('');
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label htmlFor="recipeImage" className="text-sm font-medium">
          Recipe Image
        </label>
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Input
              id="recipeImage"
              name="recipeImage"
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="cursor-pointer"
            />
            <p className="mt-1 text-xs text-muted-foreground">
              Recommended size: 1200x800 pixels, max 2MB.
            </p>
          </div>

          {imageFile && (
            <Button 
              type="button" 
              onClick={handleUpload}
              disabled={isUploading}
              size="sm"
            >
              <Upload className="mr-2 h-4 w-4" />
              {isUploading ? 'Uploading...' : 'Upload'}
            </Button>
          )}
        </div>
        
        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
      </div>

      {imagePreview && (
        <div className="relative rounded-md border p-2">
          <img 
            src={imagePreview} 
            alt="Recipe preview" 
            className="mx-auto max-h-60 object-contain" 
          />
          <button
            type="button"
            onClick={handleRemoveImage}
            className="absolute right-2 top-2 rounded-full bg-background p-1 shadow-sm"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
}
