'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import { Users, UserPlus, UserMinus, TrendingUp, ShoppingBag, Calendar } from 'lucide-react';

// Mock data for customer acquisition
const mockCustomerAcquisition = [
  { month: 'Jan', newCustomers: 45, churnedCustomers: 12, totalCustomers: 45 },
  { month: 'Feb', newCustomers: 52, churnedCustomers: 15, totalCustomers: 82 },
  { month: 'Mar', newCustomers: 61, churnedCustomers: 18, totalCustomers: 125 },
  { month: 'Apr', newCustomers: 48, churnedCustomers: 22, totalCustomers: 151 },
  { month: 'May', newCustomers: 55, churnedCustomers: 19, totalCustomers: 187 },
  { month: 'Jun', newCustomers: 67, churnedCustomers: 21, totalCustomers: 233 },
  { month: 'Jul', newCustomers: 72, churnedCustomers: 25, totalCustomers: 280 },
  { month: 'Aug', newCustomers: 58, churnedCustomers: 30, totalCustomers: 308 },
  { month: 'Sep', newCustomers: 63, churnedCustomers: 28, totalCustomers: 343 },
  { month: 'Oct', newCustomers: 75, churnedCustomers: 32, totalCustomers: 386 },
  { month: 'Nov', newCustomers: 85, churnedCustomers: 35, totalCustomers: 436 },
  { month: 'Dec', newCustomers: 92, churnedCustomers: 38, totalCustomers: 490 }
];

// Mock data for customer segments
const mockCustomerSegments = [
  { name: 'New (0-30 days)', value: 92, color: '#0088FE' },
  { name: 'Recent (31-90 days)', value: 143, color: '#00C49F' },
  { name: 'Regular (91-180 days)', value: 156, color: '#FFBB28' },
  { name: 'Loyal (181+ days)', value: 99, color: '#FF8042' }
];

// Mock data for customer lifetime value
const mockCustomerLTV = [
  { segment: 'New', value: 35.50 },
  { segment: 'Recent', value: 120.75 },
  { segment: 'Regular', value: 275.25 },
  { segment: 'Loyal', value: 520.80 }
];

// Mock data for customer purchase frequency
const mockPurchaseFrequency = [
  { frequency: '1 time', customers: 180 },
  { frequency: '2-3 times', customers: 120 },
  { frequency: '4-6 times', customers: 95 },
  { frequency: '7-12 times', customers: 65 },
  { frequency: '13+ times', customers: 30 }
];

interface CustomerAnalyticsProps {
  timeRange?: 'month' | 'quarter' | 'year';
}

export function CustomerAnalytics({ timeRange = 'year' }: CustomerAnalyticsProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'month' | 'quarter' | 'year'>(timeRange);
  
  // Calculate metrics
  const totalCustomers = mockCustomerAcquisition[mockCustomerAcquisition.length - 1].totalCustomers;
  const newCustomers = mockCustomerAcquisition.reduce((sum, month) => sum + month.newCustomers, 0);
  const churnedCustomers = mockCustomerAcquisition.reduce((sum, month) => sum + month.churnedCustomers, 0);
  const churnRate = (churnedCustomers / totalCustomers) * 100;
  
  // Calculate average order value by segment
  const averageOrderValue = mockCustomerLTV.reduce((sum, segment) => sum + segment.value, 0) / mockCustomerLTV.length;
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h2 className="text-xl font-semibold">Customer Analytics</h2>
        <div className="flex gap-2">
          <Button 
            variant={selectedTimeRange === 'month' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setSelectedTimeRange('month')}
          >
            Month
          </Button>
          <Button 
            variant={selectedTimeRange === 'quarter' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setSelectedTimeRange('quarter')}
          >
            Quarter
          </Button>
          <Button 
            variant={selectedTimeRange === 'year' ? 'default' : 'outline'} 
            size="sm"
            onClick={() => setSelectedTimeRange('year')}
          >
            Year
          </Button>
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCustomers}</div>
            <div className="text-xs text-muted-foreground">
              Lifetime customers
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">New Customers</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{newCustomers}</div>
            <div className="text-xs text-muted-foreground">
              In the last {selectedTimeRange}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
            <UserMinus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{churnRate.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              {churnedCustomers} customers lost
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Avg. Customer Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(averageOrderValue)}</div>
            <div className="text-xs text-muted-foreground">
              Per customer
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Customer Growth</CardTitle>
            <CardDescription>
              New vs. churned customers over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={mockCustomerAcquisition}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}`, 'Customers']} />
                  <Legend />
                  <Line type="monotone" dataKey="totalCustomers" name="Total Customers" stroke="#8884d8" strokeWidth={2} />
                  <Line type="monotone" dataKey="newCustomers" name="New Customers" stroke="#82ca9d" />
                  <Line type="monotone" dataKey="churnedCustomers" name="Churned Customers" stroke="#ff7300" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Customer Segments</CardTitle>
            <CardDescription>
              Distribution of customers by segment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={mockCustomerSegments}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {mockCustomerSegments.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} customers`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Customer Lifetime Value</CardTitle>
            <CardDescription>
              Average value by customer segment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={mockCustomerLTV}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="segment" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatCurrency(value as number), 'Lifetime Value']} />
                  <Legend />
                  <Bar dataKey="value" name="Lifetime Value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Purchase Frequency</CardTitle>
            <CardDescription>
              Number of customers by purchase frequency
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={mockPurchaseFrequency}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="frequency" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} customers`, 'Count']} />
                  <Legend />
                  <Bar dataKey="customers" name="Customers" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
