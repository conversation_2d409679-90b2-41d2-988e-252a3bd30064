'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  BarChart,
  Bar
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import { ArrowUpDown, Users } from 'lucide-react';

interface CustomerSegment {
  name: string;
  value: number;
  count: number;
  color: string;
}

interface RetentionData {
  month: string;
  newCustomers: number;
  returningCustomers: number;
  churnRate: number;
}

interface FrequencyData {
  frequency: string;
  customers: number;
  revenue: number;
}

interface CustomerInsightsProps {
  segments?: CustomerSegment[];
  retention?: RetentionData[];
  frequency?: FrequencyData[];
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
}

export function CustomerInsights({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate
}: CustomerInsightsProps) {
  const [activeTab, setActiveTab] = useState<'segments' | 'retention' | 'frequency'>('segments');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [retention, setRetention] = useState<RetentionData[]>([]);
  const [frequency, setFrequency] = useState<FrequencyData[]>([]);
  const [summary, setSummary] = useState({
    totalCustomers: 0,
    totalRevenue: 0,
    averageCustomerValue: 0
  });

  // Fetch customer data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        const response = await fetch(`/api/reports/customers?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch customer insights data');
        }

        const data = await response.json();
        // Ensure we're using real data from the API
        setSegments(data.segments || []);
        setRetention(data.retention || []);
        setFrequency(data.frequency || []);
        setSummary({
          totalCustomers: data.summary?.totalCustomers || 0,
          totalRevenue: data.summary?.totalRevenue || 0,
          averageCustomerValue: data.summary?.averageCustomerValue || 0
        });
      } catch (err) {
        console.error('Error fetching customer insights:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate]);

  // If loading or error, show appropriate UI
  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading customer insights data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button
          onClick={() => onTimeRangeChange(timeRange)}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  // Calculate totals
  const totalCustomers = summary.totalCustomers;
  const totalRevenue = summary.totalRevenue;
  const averageCustomerValue = summary.averageCustomerValue;

  // Custom tooltip for the segments pie chart
  const SegmentTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            Revenue: {formatCurrency(data.value)}
          </p>
          <p className="text-sm text-muted-foreground">
            Customers: {data.count}
          </p>
          <p className="text-sm text-muted-foreground">
            Avg. Value: {formatCurrency(data.value / data.count)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header is now handled by parent component */}

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Customers Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Customers</h3>
            <Users style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a', marginBottom: '0.5rem' }}>
            {totalCustomers}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            For the selected period
          </p>
        </div>

        {/* Customer Revenue Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Customer Revenue</h3>
            <Users style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#10b981', marginBottom: '0.5rem' }}>
            {formatCurrency(totalRevenue)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            For the selected period
          </p>
        </div>

        {/* Average Customer Value Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Avg. Customer Value</h3>
            <Users style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#3b82f6', marginBottom: '0.5rem' }}>
            {formatCurrency(averageCustomerValue)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Revenue per customer
          </p>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle>Customer Analysis</CardTitle>
            <div className="flex gap-2">
              <Button
                variant={activeTab === 'segments' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('segments')}
              >
                Segments
              </Button>
              <Button
                variant={activeTab === 'retention' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('retention')}
              >
                Retention
              </Button>
              <Button
                variant={activeTab === 'frequency' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('frequency')}
              >
                Frequency
              </Button>
            </div>
          </div>
          <CardDescription>
            {activeTab === 'segments' && 'Customer segments by revenue contribution'}
            {activeTab === 'retention' && 'New vs returning customers over time'}
            {activeTab === 'frequency' && 'Purchase frequency analysis'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {activeTab === 'segments' && (
            <div className="grid gap-6 md:grid-cols-2">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={segments || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {segments && segments.length > 0 ? segments.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      )) : null}
                    </Pie>
                    <Tooltip content={<SegmentTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div>
                <h3 className="mb-4 font-medium">Segment Details</h3>
                <div className="space-y-4">
                  {segments.map(segment => (
                    <div key={segment.name} className="rounded-md bg-muted/30 p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="mr-2 h-3 w-3 rounded-full"
                            style={{ backgroundColor: segment.color }}
                          />
                          <span className="font-medium">{segment.name}</span>
                        </div>
                        <span>{((segment.value / totalRevenue) * 100).toFixed(1)}%</span>
                      </div>
                      <div className="mt-2 grid grid-cols-3 gap-2 text-sm">
                        <div>
                          <p className="text-muted-foreground">Customers</p>
                          <p>{segment.count}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Revenue</p>
                          <p>{formatCurrency(segment.value)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Avg. Value</p>
                          <p>{formatCurrency(segment.value / segment.count)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'retention' && (
            <div className="grid gap-6 md:grid-cols-2">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={retention || []}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line yAxisId="left" type="monotone" dataKey="newCustomers" name="New Customers" stroke="#8884d8" />
                    <Line yAxisId="left" type="monotone" dataKey="returningCustomers" name="Returning Customers" stroke="#82ca9d" />
                    <Line yAxisId="right" type="monotone" dataKey="churnRate" name="Churn Rate (%)" stroke="#ff7300" />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <div>
                <h3 className="mb-4 font-medium">Retention Metrics</h3>
                <div className="space-y-4">
                  <div className="rounded-md bg-muted/30 p-3">
                    <p className="font-medium">New vs. Returning</p>
                    <p className="text-sm text-muted-foreground">
                      The chart shows the number of new customers acquired each month compared to returning customers.
                    </p>
                  </div>

                  <div className="rounded-md bg-muted/30 p-3">
                    <p className="font-medium">Churn Rate</p>
                    <p className="text-sm text-muted-foreground">
                      Churn rate represents the percentage of customers who stopped purchasing in a given period.
                    </p>
                  </div>

                  <div className="rounded-md bg-muted/30 p-3">
                    <p className="font-medium">Customer Lifetime</p>
                    <p className="text-sm text-muted-foreground">
                      Based on current retention rates, the average customer makes purchases for approximately
                      {' '}{retention && retention.length > 0
                        ? (100 / ((retention.reduce((sum, item) => sum + (item.churnRate || 0), 0) / retention.length) || 1)).toFixed(1)
                        : 'N/A'}{' '}
                      months before churning.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'frequency' && (
            <div className="grid gap-6 md:grid-cols-2">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={frequency || []}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="frequency" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="customers" name="Customers" fill="#8884d8" />
                    <Bar yAxisId="right" dataKey="revenue" name="Revenue" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div>
                <h3 className="mb-4 font-medium">Purchase Frequency</h3>
                <div className="space-y-4">
                  <div className="rounded-md bg-muted/30 p-3">
                    <p className="font-medium">One-time Buyers</p>
                    <p className="text-sm text-muted-foreground">
                      {frequency && frequency.length > 0 ? frequency[0].customers : 0} customers
                      ({frequency && frequency.length > 0 && totalCustomers > 0 ? ((frequency[0].customers / totalCustomers) * 100).toFixed(1) : '0'}%)
                      made only one purchase, contributing {frequency && frequency.length > 0 ? formatCurrency(frequency[0].revenue) : formatCurrency(0)}
                      ({frequency && frequency.length > 0 && totalRevenue > 0 ? ((frequency[0].revenue / totalRevenue) * 100).toFixed(1) : '0'}%) to revenue.
                    </p>
                  </div>

                  <div className="rounded-md bg-muted/30 p-3">
                    <p className="font-medium">Frequent Buyers</p>
                    <p className="text-sm text-muted-foreground">
                      {frequency && frequency.length > 0 ? frequency[frequency.length - 1].customers : 0} customers
                      ({frequency && frequency.length > 0 && totalCustomers > 0 ? ((frequency[frequency.length - 1].customers / totalCustomers) * 100).toFixed(1) : '0'}%)
                      are frequent buyers, contributing {frequency && frequency.length > 0 ? formatCurrency(frequency[frequency.length - 1].revenue) : formatCurrency(0)}
                      ({frequency && frequency.length > 0 && totalRevenue > 0 ? ((frequency[frequency.length - 1].revenue / totalRevenue) * 100).toFixed(1) : '0'}%) to revenue.
                    </p>
                  </div>

                  <div className="rounded-md bg-muted/30 p-3">
                    <p className="font-medium">Revenue per Purchase</p>
                    <p className="text-sm text-muted-foreground">
                      Customers who purchase more frequently tend to spend
                      {' '}{frequency && frequency.length > 0 &&
                          frequency[frequency.length - 1].customers > 0 &&
                          frequency[0].customers > 0 ?
                          ((frequency[frequency.length - 1].revenue / frequency[frequency.length - 1].customers) /
                          (frequency[0].revenue / frequency[0].customers)).toFixed(1) : 'N/A'}x
                      more per purchase than one-time buyers.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
