'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from 'recharts';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ArrowDown, ArrowUp, DollarSign, TrendingUp } from 'lucide-react';

interface Expense {
  id: string;
  date: string;
  storeId: string;
  storeName: string;
  category: string;
  amount: number;
  description: string;
  userId: string;
  userName: string;
}

interface ExpensesByCategory {
  category: string;
  amount: number;
  percentage: number;
}

interface ExpensesByStore {
  storeId: string;
  storeName: string;
  amount: number;
  percentage: number;
}

interface ExpensesByDate {
  date: string;
  amount: number;
}

interface ExpensesReportProps {
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
  storeId?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function ExpensesReport({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate,
  storeId
}: ExpensesReportProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [expensesByCategory, setExpensesByCategory] = useState<ExpensesByCategory[]>([]);
  const [expensesByStore, setExpensesByStore] = useState<ExpensesByStore[]>([]);
  const [expensesByDate, setExpensesByDate] = useState<ExpensesByDate[]>([]);
  const [summary, setSummary] = useState({
    totalExpenses: 0,
    totalRecords: 0,
  });
  const [activeTab, setActiveTab] = useState<'overview' | 'byCategory' | 'byStore' | 'details'>('overview');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        if (storeId) {
          params.append('storeId', storeId);
        }

        const response = await fetch(`/api/reports/expenses?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch expenses report data');
        }

        const data = await response.json();

        setExpenses(data.expenses);
        setExpensesByCategory(data.expensesByCategory);
        setExpensesByStore(data.expensesByStore);
        setExpensesByDate(data.expensesByDate);
        setSummary(data.summary);
      } catch (err) {
        console.error('Error fetching expenses report:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate, storeId]);

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{payload[0].name}</p>
          <p>Amount: {formatCurrency(payload[0].payload.amount)}</p>
          <p>Percentage: {payload[0].payload.percentage.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading expenses report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button
          onClick={() => onTimeRangeChange(timeRange)}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Expenses Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Expenses</h3>
            <DollarSign style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#ef4444', marginBottom: '0.5rem' }}>
            {formatCurrency(summary.totalExpenses)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            From {summary.totalRecords} expense records
          </p>
        </div>

        {/* Top Expense Category Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Top Expense Category</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#3b82f6', marginBottom: '0.5rem' }}>
            {expensesByCategory.length > 0 ? expensesByCategory[0].category : 'N/A'}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            {expensesByCategory.length > 0
              ? `${formatCurrency(expensesByCategory[0].amount)} (${expensesByCategory[0].percentage.toFixed(1)}%)`
              : 'No expense data available'}
          </p>
        </div>

        {/* Average Daily Expense Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Average Daily Expense</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {expensesByDate.length > 0
              ? formatCurrency(summary.totalExpenses / expensesByDate.length)
              : formatCurrency(0)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Over {expensesByDate.length} days
          </p>
        </div>
      </div>

      {/* Professional Tab Navigation */}
      <div style={{ marginBottom: '1.5rem' }}>
        <div style={{
          display: 'flex',
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '0.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          {['overview', 'byCategory', 'byStore', 'details'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as any)}
              style={{
                flex: 1,
                padding: '0.75rem 1rem',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: activeTab === tab ? '#3b82f6' : 'transparent',
                color: activeTab === tab ? 'white' : '#64748b',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s',
                textTransform: 'capitalize'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = '#f8fafc';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
            >
              {tab === 'byCategory' ? 'By Category' : tab === 'byStore' ? 'By Store' : tab}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '1.5rem'
        }}>
          {/* Expenses Over Time Chart */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Expenses Over Time
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
              Daily expenses over the selected period
            </p>
            <div style={{ height: '300px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={expensesByDate}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                  <XAxis dataKey="date" stroke="#64748b" fontSize={12} />
                  <YAxis stroke="#64748b" fontSize={12} />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Bar dataKey="amount" name="Expense Amount" fill="#ef4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Expenses by Category Chart */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Expenses by Category
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
              Distribution of expenses by category
            </p>
            <div style={{ height: '300px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={expensesByCategory}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#ef4444"
                    dataKey="amount"
                    nameKey="category"
                    label={({ category, percent }) =>
                      `${category}: ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {expensesByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {/* By Category Tab */}
      {activeTab === 'byCategory' && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Expenses by Category
          </h3>
          <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
            Detailed breakdown of expenses by category
          </p>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Category
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Amount
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    % of Total
                  </th>
                </tr>
              </thead>
              <tbody>
                {expensesByCategory.map((item) => (
                  <tr key={item.category} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                      {item.category}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                      {formatCurrency(item.amount)}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {item.percentage.toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <td style={{ padding: '0.75rem 1rem', fontWeight: '600', color: '#0f172a' }}>
                    Total
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {formatCurrency(summary.totalExpenses)}
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    100%
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {/* By Store Tab */}
      {activeTab === 'byStore' && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Expenses by Store
          </h3>
          <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
            Detailed breakdown of expenses by store
          </p>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Store
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Amount
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    % of Total
                  </th>
                </tr>
              </thead>
              <tbody>
                {expensesByStore.map((item) => (
                  <tr key={item.storeId} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                      {item.storeName}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                      {formatCurrency(item.amount)}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {item.percentage.toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <td style={{ padding: '0.75rem 1rem', fontWeight: '600', color: '#0f172a' }}>
                    Total
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {formatCurrency(summary.totalExpenses)}
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    100%
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {/* Details Tab */}
      {activeTab === 'details' && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Expense Details
          </h3>
          <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
            Detailed list of all expense records
          </p>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Date
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Store
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Category
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Description
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody>
                {expenses.map((expense) => (
                  <tr key={expense.id} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                      {formatDate(expense.date)}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                      {expense.storeName}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                      {expense.category}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                      {expense.description}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                      {formatCurrency(expense.amount)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <td colSpan={4} style={{ padding: '0.75rem 1rem', fontWeight: '600', color: '#0f172a' }}>
                    Total
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {formatCurrency(summary.totalExpenses)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
