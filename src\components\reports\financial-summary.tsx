'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import { ArrowDown, ArrowUp, DollarSign, TrendingDown, TrendingUp } from 'lucide-react';

interface FinancialData {
  date: string;
  revenue: number;
  cogs: number;
  expenses: number;
  profit: number;
}

interface ExpenseCategory {
  name: string;
  value: number;
  color: string;
}

interface FinancialSummaryProps {
  financialData?: FinancialData[];
  expenseCategories?: ExpenseCategory[];
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
}

export function FinancialSummary({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate
}: FinancialSummaryProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'profitLoss' | 'expenses'>('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [financialData, setFinancialData] = useState<FinancialData[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<ExpenseCategory[]>([]);
  const [comparison, setComparison] = useState({
    revenueChange: 0,
    profitChange: 0,
    grossMarginChange: 0,
    netMarginChange: 0
  });

  // Fetch financial data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        const response = await fetch(`/api/reports/financial?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch financial data');
        }

        const data = await response.json();
        setFinancialData(data.financialData);
        setExpenseCategories(data.expenseCategories);

        // Use real comparison data if available, otherwise use defaults
        if (data.comparison) {
          setComparison({
            revenueChange: data.comparison.revenueChange,
            profitChange: data.comparison.profitChange,
            grossMarginChange: data.comparison.grossMarginChange,
            netMarginChange: data.comparison.netMarginChange
          });
        } else {
          // Fallback to defaults if comparison data is not available
          setComparison({
            revenueChange: 0,
            profitChange: 0,
            grossMarginChange: 0,
            netMarginChange: 0
          });
        }
      } catch (err) {
        console.error('Error fetching financial data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate]);

  // If loading or error, show appropriate UI
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading financial data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <Button
          variant="outline"
          onClick={() => onTimeRangeChange(timeRange)}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Calculate totals
  const totalRevenue = financialData.reduce((sum, item) => sum + item.revenue, 0);
  const totalCOGS = financialData.reduce((sum, item) => sum + item.cogs, 0);
  const totalExpenses = financialData.reduce((sum, item) => sum + item.expenses, 0);
  const totalProfit = financialData.reduce((sum, item) => sum + item.profit, 0);

  // Calculate gross and net profit margins
  const grossProfit = totalRevenue - totalCOGS;
  const grossMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;
  const netMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

  // For comparison with previous period
  const revenueChange = comparison.revenueChange;
  const profitChange = comparison.profitChange;
  const grossMarginChange = comparison.grossMarginChange;
  const netMarginChange = comparison.netMarginChange;

  // Format data for the profit & loss chart
  const plChartData = financialData.map(item => ({
    date: item.date,
    revenue: item.revenue,
    cogs: item.cogs,
    expenses: item.expenses,
    profit: item.profit,
    grossProfit: item.revenue - item.cogs
  }));

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header is now handled by parent component */}

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Revenue Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Revenue</h3>
            <DollarSign style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#10b981', marginBottom: '0.5rem' }}>
            {formatCurrency(totalRevenue)}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {revenueChange >= 0 ? (
              <>
                <ArrowUp style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#10b981' }} />
                <span style={{ color: '#10b981' }}>{revenueChange}%</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#ef4444' }} />
                <span style={{ color: '#ef4444' }}>{Math.abs(revenueChange)}%</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>

        {/* Net Profit Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Net Profit</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#3b82f6', marginBottom: '0.5rem' }}>
            {formatCurrency(totalProfit)}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {profitChange >= 0 ? (
              <>
                <ArrowUp style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#10b981' }} />
                <span style={{ color: '#10b981' }}>{profitChange}%</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#ef4444' }} />
                <span style={{ color: '#ef4444' }}>{Math.abs(profitChange)}%</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>

        {/* Gross Margin Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Gross Margin</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#8b5cf6', marginBottom: '0.5rem' }}>
            {grossMargin.toFixed(1)}%
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {grossMarginChange >= 0 ? (
              <>
                <ArrowUp style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#10b981' }} />
                <span style={{ color: '#10b981' }}>+{grossMarginChange}pts</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#ef4444' }} />
                <span style={{ color: '#ef4444' }}>{grossMarginChange}pts</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>

        {/* Net Margin Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Net Margin</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {netMargin.toFixed(1)}%
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {netMarginChange >= 0 ? (
              <>
                <ArrowUp style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#10b981' }} />
                <span style={{ color: '#10b981' }}>+{netMarginChange}pts</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ marginRight: '0.25rem', height: '16px', width: '16px', color: '#ef4444' }} />
                <span style={{ color: '#ef4444' }}>{netMarginChange}pts</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle>Financial Analysis</CardTitle>
            <div className="flex gap-2">
              <Button
                variant={activeTab === 'overview' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </Button>
              <Button
                variant={activeTab === 'profitLoss' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('profitLoss')}
              >
                Profit & Loss
              </Button>
              <Button
                variant={activeTab === 'expenses' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('expenses')}
              >
                Expenses
              </Button>
            </div>
          </div>
          <CardDescription>
            {activeTab === 'overview' && 'Financial performance overview'}
            {activeTab === 'profitLoss' && 'Detailed profit and loss analysis'}
            {activeTab === 'expenses' && 'Expense breakdown by category'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {activeTab === 'overview' && (
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={financialData || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" name="Revenue" stroke="#8884d8" />
                  <Line type="monotone" dataKey="profit" name="Net Profit" stroke="#82ca9d" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}

          {activeTab === 'profitLoss' && (
            <div className="grid gap-6 md:grid-cols-2">
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={plChartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Bar dataKey="revenue" name="Revenue" fill="#8884d8" />
                    <Bar dataKey="cogs" name="Cost of Goods" fill="#82ca9d" />
                    <Bar dataKey="expenses" name="Expenses" fill="#ffc658" />
                    <Bar dataKey="profit" name="Net Profit" fill="#ff8042" />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div>
                <h3 className="mb-4 font-medium">Profit & Loss Statement</h3>
                <div className="space-y-2 rounded-md border p-4">
                  <div className="flex justify-between">
                    <span className="font-medium">Revenue</span>
                    <span>{formatCurrency(totalRevenue)}</span>
                  </div>
                  <div className="flex justify-between text-muted-foreground">
                    <span>Cost of Goods Sold</span>
                    <span>- {formatCurrency(totalCOGS)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-medium">Gross Profit</span>
                    <span>{formatCurrency(grossProfit)}</span>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Gross Margin</span>
                    <span>{grossMargin.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between text-muted-foreground">
                    <span>Operating Expenses</span>
                    <span>- {formatCurrency(totalExpenses)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-medium">Net Profit</span>
                    <span>{formatCurrency(totalProfit)}</span>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Net Margin</span>
                    <span>{netMargin.toFixed(1)}%</span>
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <h4 className="font-medium">Key Insights</h4>
                  <ul className="list-inside list-disc space-y-1 text-sm text-muted-foreground">
                    <li>Gross margin is {grossMargin > 50 ? 'healthy' : 'below industry average'} at {grossMargin.toFixed(1)}%</li>
                    <li>Net profit {profitChange >= 0 ? 'increased' : 'decreased'} by {Math.abs(profitChange)}% compared to previous period</li>
                    <li>Cost of goods represents {((totalCOGS / totalRevenue) * 100).toFixed(1)}% of revenue</li>
                    <li>Operating expenses are {((totalExpenses / totalRevenue) * 100).toFixed(1)}% of revenue</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'expenses' && (
            <div className="grid gap-6 md:grid-cols-2">
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={expenseCategories || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {expenseCategories && expenseCategories.length > 0 ? expenseCategories.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      )) : null}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div>
                <h3 className="mb-4 font-medium">Expense Breakdown</h3>
                <div className="space-y-3">
                  {expenseCategories && expenseCategories.length > 0 ? expenseCategories.map(category => (
                    <div key={category.name} className="rounded-md bg-muted/30 p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="mr-2 h-3 w-3 rounded-full"
                            style={{ backgroundColor: category.color }}
                          />
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <span>{formatCurrency(category.value)}</span>
                      </div>
                      <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                        <span>% of Total Expenses</span>
                        <span>{((category.value / totalExpenses) * 100).toFixed(1)}%</span>
                      </div>
                      <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                        <span>% of Revenue</span>
                        <span>{((category.value / totalRevenue) * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  )) : <div className="text-center text-muted-foreground py-4">No expense data available</div>}
                </div>

                <div className="mt-4 rounded-md border p-3">
                  <p className="font-medium">Total Expenses: {formatCurrency(totalExpenses)}</p>
                  <p className="text-sm text-muted-foreground">
                    Representing {((totalExpenses / totalRevenue) * 100).toFixed(1)}% of total revenue
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
