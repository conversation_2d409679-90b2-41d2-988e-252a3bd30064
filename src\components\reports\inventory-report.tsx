'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { formatCurrency } from '@/lib/utils';
import { Package, AlertTriangle, TrendingUp, Warehouse } from 'lucide-react';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  sku: string;
  totalStock: number;
  lowStockThreshold: number;
  unit: string;
  isLowStock: boolean;
  warehouseStock: number;
  storeStock: number;
  locations: Array<{
    type: 'warehouse' | 'store';
    name: string;
    quantity: number;
  }>;
}

interface InventoryByCategory {
  category: string;
  count: number;
  value: number;
  percentage: number;
}

interface InventoryByLocation {
  name: string;
  type: 'warehouse' | 'store';
  count: number;
  value: number;
}

interface InventoryReportProps {
  reportType?: 'all' | 'lowStock';
  onReportTypeChange?: (type: 'all' | 'lowStock') => void;
}

export function InventoryReport({
  reportType = 'all',
  onReportTypeChange
}: InventoryReportProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [inventoryData, setInventoryData] = useState<InventoryItem[]>([]);
  const [inventoryByCategory, setInventoryByCategory] = useState<InventoryByCategory[]>([]);
  const [inventoryByLocation, setInventoryByLocation] = useState<InventoryByLocation[]>([]);
  const [summary, setSummary] = useState({
    totalProducts: 0,
    totalInventoryValue: 0,
    lowStockCount: 0,
    outOfStockCount: 0,
  });

  useEffect(() => {
    fetchInventoryData();
  }, [reportType]);

  const fetchInventoryData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/reports/inventory?type=${reportType}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch inventory data');
      }
      
      const data = await response.json();
      
      setInventoryData(data.reportData || []);
      setInventoryByCategory(data.inventoryByCategory || []);
      setInventoryByLocation(data.inventoryByLocation || []);
      setSummary(data.summary || {
        totalProducts: 0,
        totalInventoryValue: 0,
        lowStockCount: 0,
        outOfStockCount: 0,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading inventory report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button 
          onClick={() => fetchInventoryData()}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Products Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Products</h3>
            <Package style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#3b82f6', marginBottom: '0.5rem' }}>
            {summary.totalProducts}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Products in inventory
          </p>
        </div>
        
        {/* Total Inventory Value Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Inventory Value</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#10b981', marginBottom: '0.5rem' }}>
            {formatCurrency(summary.totalInventoryValue)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Total value of inventory
          </p>
        </div>
        
        {/* Low Stock Items Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Low Stock Items</h3>
            <AlertTriangle style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {summary.lowStockCount}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Items below threshold
          </p>
        </div>
        
        {/* Out of Stock Items Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Out of Stock Items</h3>
            <AlertTriangle style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#ef4444', marginBottom: '0.5rem' }}>
            {summary.outOfStockCount}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Items with zero stock
          </p>
        </div>
      </div>

      {/* Charts Section */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '1.5rem'
      }}>
        {/* Inventory by Category Chart */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1rem' }}>
            Inventory by Category
          </h3>
          <div style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={inventoryByCategory}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#3b82f6"
                  dataKey="value"
                  label={({ category, percentage }) => `${category} ${percentage.toFixed(1)}%`}
                >
                  {inventoryByCategory.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={`hsl(${(index * 137.5) % 360}, 70%, 50%)`} 
                    />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Inventory by Location Chart */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1rem' }}>
            Inventory by Location
          </h3>
          <div style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={inventoryByLocation}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Bar dataKey="value" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Inventory Items Table */}
      {inventoryData.length > 0 && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '1rem' }}>
            {reportType === 'lowStock' ? 'Low Stock Items' : 'All Inventory Items'}
          </h3>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Product Name
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    SKU
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Category
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Total Stock
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Threshold
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'center', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {inventoryData.slice(0, 10).map((item) => (
                  <tr key={item.id} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem', fontWeight: '500', color: '#0f172a' }}>
                      {item.name}
                    </td>
                    <td style={{ padding: '0.75rem', color: '#64748b' }}>
                      {item.sku}
                    </td>
                    <td style={{ padding: '0.75rem', color: '#64748b' }}>
                      {item.category}
                    </td>
                    <td style={{ padding: '0.75rem', textAlign: 'right', color: '#0f172a' }}>
                      {item.totalStock} {item.unit}
                    </td>
                    <td style={{ padding: '0.75rem', textAlign: 'right', color: '#64748b' }}>
                      {item.lowStockThreshold}
                    </td>
                    <td style={{ padding: '0.75rem', textAlign: 'center' }}>
                      <span style={{
                        padding: '0.25rem 0.75rem',
                        borderRadius: '9999px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        backgroundColor: item.totalStock === 0 ? '#fee2e2' : item.isLowStock ? '#fef3c7' : '#dcfce7',
                        color: item.totalStock === 0 ? '#dc2626' : item.isLowStock ? '#d97706' : '#16a34a'
                      }}>
                        {item.totalStock === 0 ? 'Out of Stock' : item.isLowStock ? 'Low Stock' : 'In Stock'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
