'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { formatCurrency } from '@/lib/utils';
import { AlertTriangle, ShoppingCart } from 'lucide-react';

interface ProductInventory {
  storeId: string;
  storeName: string;
  quantity: number;
  threshold: number;
  status: string;
}

interface LowStockProduct {
  id: string;
  name: string;
  category: string;
  price: number;
  unit: string;
  minStockLevel: number;
  inventory: ProductInventory[];
}

interface StoreData {
  storeId: string;
  storeName: string;
  lowStockCount: number;
  outOfStockCount: number;
  products: Array<{
    productId: string;
    productName: string;
    quantity: number;
    threshold: number;
    status: string;
  }>;
}

interface LowStockReportProps {
  storeId?: string;
  onStoreChange?: (storeId: string) => void;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function LowStockReport({
  storeId,
  onStoreChange
}: LowStockReportProps): JSX.Element {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<LowStockProduct[]>([]);
  const [stores, setStores] = useState<StoreData[]>([]);
  const [summary, setSummary] = useState({
    totalLowStockProducts: 0,
    totalLowStockItems: 0,
    outOfStockCount: 0,
    lowStockCount: 0,
  });
  const [threshold, setThreshold] = useState(10);
  const [includeZero, setIncludeZero] = useState(true);
  const [activeTab, setActiveTab] = useState<'byStore' | 'byProduct'>('byStore');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('threshold', threshold.toString());
        params.append('includeZero', includeZero.toString());

        if (storeId) {
          params.append('storeId', storeId);
        }

        const response = await fetch(`/api/reports/low-stock?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch low stock report data');
        }

        const data = await response.json();

        setProducts(data.products);
        setStores(data.stores);
        setSummary(data.summary);
      } catch (err) {
        console.error('Error fetching low stock report:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [threshold, includeZero, storeId]);

  // Prepare data for charts
  const storeChartData = stores.map(store => ({
    name: store.storeName,
    lowStock: store.lowStockCount,
    outOfStock: store.outOfStockCount,
  }));

  const categoryData = products.reduce((acc, product) => {
    const category = product.category || 'Uncategorized';

    if (!acc[category]) {
      acc[category] = { name: category, count: 0 };
    }

    acc[category].count += 1;

    return acc;
  }, {} as Record<string, { name: string; count: number }>);

  const categoryChartData = Object.values(categoryData).sort((a, b) => b.count - a.count);

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading low stock report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button
          onClick={() => setThreshold(threshold)}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Summary Cards */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Low Stock Items</h3>
            <AlertTriangle style={{ height: '20px', width: '20px', color: '#f59e0b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {summary.lowStockCount}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Items below threshold but not zero
          </p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Out of Stock Items</h3>
            <AlertTriangle style={{ height: '20px', width: '20px', color: '#ef4444' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#ef4444', marginBottom: '0.5rem' }}>
            {summary.outOfStockCount}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Items with zero quantity
          </p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Products Affected</h3>
            <AlertTriangle style={{ height: '20px', width: '20px', color: '#8b5cf6' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#8b5cf6', marginBottom: '0.5rem' }}>
            {summary.totalLowStockProducts}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Unique products with low stock
          </p>
        </div>
      </div>

      {/* Filters */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '1.5rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '1rem', alignItems: 'end' }}>
          <div style={{ minWidth: '180px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem', display: 'block' }}>
              Threshold
            </label>
            <select
              value={threshold.toString()}
              onChange={(e) => setThreshold(parseInt(e.target.value, 10))}
              style={{
                width: '100%',
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '0.875rem',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="5">5 units</option>
              <option value="10">10 units</option>
              <option value="20">20 units</option>
              <option value="50">50 units</option>
            </select>
          </div>

          <div style={{ display: 'flex', backgroundColor: '#f8fafc', borderRadius: '8px', border: '1px solid #e2e8f0' }}>
            <button
              onClick={() => setIncludeZero(true)}
              style={{
                padding: '0.5rem 1rem',
                border: 'none',
                borderRadius: '8px 0 0 8px',
                backgroundColor: includeZero ? '#3b82f6' : 'transparent',
                color: includeZero ? 'white' : '#64748b',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
            >
              Include Zero
            </button>
            <button
              onClick={() => setIncludeZero(false)}
              style={{
                padding: '0.5rem 1rem',
                border: 'none',
                borderRadius: '0 8px 8px 0',
                backgroundColor: !includeZero ? '#3b82f6' : 'transparent',
                color: !includeZero ? 'white' : '#64748b',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
            >
              Exclude Zero
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        padding: '0.5rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        {['byStore', 'byProduct'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab as any)}
            style={{
              flex: 1,
              padding: '0.75rem 1rem',
              borderRadius: '8px',
              border: 'none',
              backgroundColor: activeTab === tab ? '#3b82f6' : 'transparent',
              color: activeTab === tab ? 'white' : '#64748b',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
          >
            {tab === 'byStore' ? 'By Store' : 'By Product'}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'byStore' ? (
        <div>
          {/* Charts Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '1.5rem',
            marginBottom: '1.5rem'
          }}>
            {/* Low Stock by Store Chart */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              padding: '1.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                Low Stock by Store
              </h3>
              <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
                Number of low stock and out of stock items by store
              </p>
              <div style={{ height: '300px' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={storeChartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                    <XAxis type="number" stroke="#64748b" fontSize={12} />
                    <YAxis dataKey="name" type="category" width={100} stroke="#64748b" fontSize={12} />
                    <Tooltip />
                    <Bar dataKey="lowStock" name="Low Stock" fill="#f59e0b" stackId="a" />
                    <Bar dataKey="outOfStock" name="Out of Stock" fill="#ef4444" stackId="a" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Low Stock by Category Chart */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              padding: '1.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                Low Stock by Category
              </h3>
              <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
                Distribution of low stock items by product category
              </p>
              <div style={{ height: '300px' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categoryChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#f59e0b"
                      dataKey="count"
                      nameKey="name"
                      label={({ name, percent }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                    >
                      {categoryChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Store Details Section */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {stores.map((store) => (
              <div key={store.storeId} style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid #e2e8f0',
                padding: '1.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ marginBottom: '1rem' }}>
                  <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                    {store.storeName}
                  </h3>
                  <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
                    {store.lowStockCount} low stock items, {store.outOfStockCount} out of stock items
                  </p>
                </div>
                <div style={{ overflowX: 'auto' }}>
                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                      <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                        <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                          Product
                        </th>
                        <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                          Quantity
                        </th>
                        <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                          Threshold
                        </th>
                        <th style={{ padding: '0.75rem 1rem', textAlign: 'center', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                          Status
                        </th>
                        <th style={{ padding: '0.75rem 1rem', textAlign: 'center', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {store.products.map((product) => (
                        <tr key={product.productId} style={{ borderBottom: '1px solid #f1f5f9' }}>
                          <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                            {product.productName}
                          </td>
                          <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                            {product.quantity}
                          </td>
                          <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                            {product.threshold}
                          </td>
                          <td style={{ padding: '0.75rem 1rem', textAlign: 'center' }}>
                            <span style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              borderRadius: '9999px',
                              padding: '0.25rem 0.75rem',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              backgroundColor: product.status === 'Out of Stock' ? '#fee2e2' : '#fef3c7',
                              color: product.status === 'Out of Stock' ? '#dc2626' : '#d97706'
                            }}>
                              {product.status}
                            </span>
                          </td>
                          <td style={{ padding: '0.75rem 1rem', textAlign: 'center' }}>
                            <a
                              href={`/dashboard/purchases?product=${product.productId}`}
                              style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                padding: '0.5rem 1rem',
                                backgroundColor: 'transparent',
                                color: '#3b82f6',
                                border: '1px solid #e2e8f0',
                                borderRadius: '6px',
                                fontSize: '0.875rem',
                                fontWeight: '500',
                                textDecoration: 'none',
                                cursor: 'pointer',
                                transition: 'all 0.2s'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#f8fafc';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                              }}
                            >
                              <ShoppingCart style={{ height: '16px', width: '16px', marginRight: '0.5rem' }} />
                              Order
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
          {/* Product Overview Table */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Low Stock Products
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
              All products with low stock across all stores
            </p>
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                    <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                      Product
                    </th>
                    <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                      Category
                    </th>
                    <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                      Price
                    </th>
                    <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                      Min Stock
                    </th>
                    <th style={{ padding: '0.75rem 1rem', textAlign: 'center', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                      Status
                    </th>
                    <th style={{ padding: '0.75rem 1rem', textAlign: 'center', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {products.map((product) => {
                    // Calculate worst status (Out of Stock > Low Stock)
                    const hasOutOfStock = product.inventory.some(inv => inv.status === 'Out of Stock');
                    const status = hasOutOfStock ? 'Out of Stock' : 'Low Stock';

                    return (
                      <tr key={product.id} style={{ borderBottom: '1px solid #f1f5f9' }}>
                        <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                          {product.name}
                        </td>
                        <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                          {product.category}
                        </td>
                        <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                          {formatCurrency(product.price)}
                        </td>
                        <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                          {product.minStockLevel}
                        </td>
                        <td style={{ padding: '0.75rem 1rem', textAlign: 'center' }}>
                          <span style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            borderRadius: '9999px',
                            padding: '0.25rem 0.75rem',
                            fontSize: '0.75rem',
                            fontWeight: '500',
                            backgroundColor: status === 'Out of Stock' ? '#fee2e2' : '#fef3c7',
                            color: status === 'Out of Stock' ? '#dc2626' : '#d97706'
                          }}>
                            {status}
                          </span>
                        </td>
                        <td style={{ padding: '0.75rem 1rem', textAlign: 'center' }}>
                          <a
                            href={`/dashboard/purchases?product=${product.id}`}
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              padding: '0.5rem 1rem',
                              backgroundColor: 'transparent',
                              color: '#3b82f6',
                              border: '1px solid #e2e8f0',
                              borderRadius: '6px',
                              fontSize: '0.875rem',
                              fontWeight: '500',
                              textDecoration: 'none',
                              cursor: 'pointer',
                              transition: 'all 0.2s'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#f8fafc';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                            }}
                          >
                            <ShoppingCart style={{ height: '16px', width: '16px', marginRight: '0.5rem' }} />
                            Order
                          </a>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
