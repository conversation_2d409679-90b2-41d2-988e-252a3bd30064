'use client';

import { useState, useEffect } from 'react';
import {
  BarC<PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON>hart,
  Line,
  Legend
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { formatCurrency } from '@/lib/utils';
import { ArrowUpDown, Search } from 'lucide-react';

interface ProductData {
  id: string;
  name: string;
  category: string;
  totalSales: number;
  totalRevenue: number;
  costOfGoods: number;
  profit: number;
  profitMargin: number;
  trend: Array<{ date: string; sales: number }>;
}

interface ProductPerformanceProps {
  products?: ProductData[];
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
}

export function ProductPerformance({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate
}: ProductPerformanceProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof ProductData>('totalRevenue');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedProduct, setSelectedProduct] = useState<ProductData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<ProductData[]>([]);

  // Fetch product data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        const response = await fetch(`/api/reports/products?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch product performance data');
        }

        const data = await response.json();
        // Ensure we're using real data from the API
        setProducts(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('Error fetching product performance:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate]);

  // If loading or error, show appropriate UI
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading product performance data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <Button
          variant="outline"
          onClick={() => onTimeRangeChange(timeRange)}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Filter products based on search term
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort products based on sort field and direction
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (sortDirection === 'asc') {
      return a[sortField] > b[sortField] ? 1 : -1;
    } else {
      return a[sortField] < b[sortField] ? 1 : -1;
    }
  });

  // Top 5 products by revenue for the chart
  const topProducts = products && products.length > 0
    ? [...products].sort((a, b) => b.totalRevenue - a.totalRevenue).slice(0, 5)
    : [];

  const handleSort = (field: keyof ProductData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Format data for the top products chart
  const topProductsChartData = topProducts.length > 0
    ? topProducts.map(product => ({
        name: product.name,
        revenue: product.totalRevenue,
        profit: product.profit
      }))
    : [];

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Header is now handled by parent component */}

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '1.5rem'
      }}>
        {/* Top Products Chart */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Top 5 Products
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
              Best performing products by revenue
            </p>
          </div>
          <div style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={topProductsChartData || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis type="number" stroke="#64748b" fontSize={12} />
                <YAxis dataKey="name" type="category" width={100} stroke="#64748b" fontSize={12} />
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Legend />
                <Bar dataKey="revenue" name="Revenue" fill="#3b82f6" radius={[0, 4, 4, 0]} />
                <Bar dataKey="profit" name="Profit" fill="#10b981" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {selectedProduct ? (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1.5rem' }}>
              <div>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                  {selectedProduct.name}
                </h3>
                <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
                  {selectedProduct.category} • Profit Margin: {selectedProduct.profitMargin.toFixed(1)}%
                </p>
              </div>
              <button
                onClick={() => setSelectedProduct(null)}
                style={{
                  backgroundColor: 'transparent',
                  color: '#64748b',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f8fafc';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Back to List
              </button>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem', marginBottom: '1.5rem' }}>
              <div>
                <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>Total Revenue</p>
                <p style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a' }}>{formatCurrency(selectedProduct.totalRevenue)}</p>
              </div>
              <div>
                <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>Total Profit</p>
                <p style={{ fontSize: '1.125rem', fontWeight: '600', color: '#10b981' }}>{formatCurrency(selectedProduct.profit)}</p>
              </div>
              <div>
                <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>Units Sold</p>
                <p style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a' }}>{selectedProduct.totalSales}</p>
              </div>
              <div>
                <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>Cost of Goods</p>
                <p style={{ fontSize: '1.125rem', fontWeight: '600', color: '#ef4444' }}>{formatCurrency(selectedProduct.costOfGoods)}</p>
              </div>
            </div>

            <div style={{ height: '200px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={selectedProduct?.trend || []}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                  <XAxis dataKey="date" stroke="#64748b" fontSize={12} />
                  <YAxis stroke="#64748b" fontSize={12} />
                  <Tooltip />
                  <Line type="monotone" dataKey="sales" stroke="#3b82f6" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        ) : (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
                Product List
              </h3>
              <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1rem' }}>
                Click on a product to see detailed performance
              </p>
              <div style={{ position: 'relative' }}>
                <Search style={{
                  position: 'absolute',
                  left: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  height: '16px',
                  width: '16px',
                  color: '#64748b'
                }} />
                <input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                    borderRadius: '8px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    backgroundColor: 'white',
                    transition: 'all 0.2s'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#3b82f6';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              <table style={{ width: '100%', fontSize: '0.875rem' }}>
                <thead>
                  <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                    <th
                      onClick={() => handleSort('name')}
                      style={{
                        padding: '0.75rem 0.5rem',
                        textAlign: 'left',
                        fontWeight: '500',
                        color: '#374151',
                        cursor: 'pointer',
                        userSelect: 'none'
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        Product
                        {sortField === 'name' && (
                          <ArrowUpDown style={{
                            marginLeft: '0.25rem',
                            height: '16px',
                            width: '16px',
                            transform: sortDirection === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)',
                            transition: 'transform 0.2s'
                          }} />
                        )}
                      </div>
                    </th>
                    <th
                      onClick={() => handleSort('totalSales')}
                      style={{
                        padding: '0.75rem 0.5rem',
                        textAlign: 'right',
                        fontWeight: '500',
                        color: '#374151',
                        cursor: 'pointer',
                        userSelect: 'none'
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        Units
                        {sortField === 'totalSales' && (
                          <ArrowUpDown style={{
                            marginLeft: '0.25rem',
                            height: '16px',
                            width: '16px',
                            transform: sortDirection === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)',
                            transition: 'transform 0.2s'
                          }} />
                        )}
                      </div>
                    </th>
                    <th
                      onClick={() => handleSort('totalRevenue')}
                      style={{
                        padding: '0.75rem 0.5rem',
                        textAlign: 'right',
                        fontWeight: '500',
                        color: '#374151',
                        cursor: 'pointer',
                        userSelect: 'none'
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        Revenue
                        {sortField === 'totalRevenue' && (
                          <ArrowUpDown style={{
                            marginLeft: '0.25rem',
                            height: '16px',
                            width: '16px',
                            transform: sortDirection === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)',
                            transition: 'transform 0.2s'
                          }} />
                        )}
                      </div>
                    </th>
                    <th
                      onClick={() => handleSort('profitMargin')}
                      style={{
                        padding: '0.75rem 0.5rem',
                        textAlign: 'right',
                        fontWeight: '500',
                        color: '#374151',
                        cursor: 'pointer',
                        userSelect: 'none'
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        Margin
                        {sortField === 'profitMargin' && (
                          <ArrowUpDown style={{
                            marginLeft: '0.25rem',
                            height: '16px',
                            width: '16px',
                            transform: sortDirection === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)',
                            transition: 'transform 0.2s'
                          }} />
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedProducts.map(product => (
                    <tr
                      key={product.id}
                      onClick={() => setSelectedProduct(product)}
                      style={{
                        borderBottom: '1px solid #f1f5f9',
                        cursor: 'pointer',
                        transition: 'background-color 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <td style={{ padding: '0.75rem 0.5rem' }}>
                        <div style={{ fontWeight: '500', color: '#0f172a', marginBottom: '0.25rem' }}>{product.name}</div>
                        <div style={{ fontSize: '0.75rem', color: '#64748b' }}>{product.category}</div>
                      </td>
                      <td style={{ padding: '0.75rem 0.5rem', textAlign: 'right', fontWeight: '500' }}>{product.totalSales}</td>
                      <td style={{ padding: '0.75rem 0.5rem', textAlign: 'right', fontWeight: '500', color: '#10b981' }}>{formatCurrency(product.totalRevenue)}</td>
                      <td style={{ padding: '0.75rem 0.5rem', textAlign: 'right', fontWeight: '500' }}>
                        <span style={{
                          backgroundColor: product.profitMargin > 20 ? '#dcfce7' : product.profitMargin > 10 ? '#fef3c7' : '#fee2e2',
                          color: product.profitMargin > 20 ? '#166534' : product.profitMargin > 10 ? '#92400e' : '#991b1b',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '12px',
                          fontSize: '0.75rem'
                        }}>
                          {product.profitMargin.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  ))}
                  {sortedProducts.length === 0 && (
                    <tr>
                      <td colSpan={4} style={{ padding: '2rem', textAlign: 'center', color: '#64748b' }}>
                        <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔍</div>
                        No products found matching your search.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
