'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { formatDate } from '@/lib/utils';
import { Factory, TrendingUp, Package, AlertTriangle } from 'lucide-react';

interface Production {
  id: string;
  productionNumber: string;
  date: string;
  productId: string;
  productName: string;
  quantity: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  rawMaterials: Array<{
    id: string;
    name: string;
    quantity: number;
    unit: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

interface ProductionByProduct {
  productName: string;
  quantity: number;
  percentage: number;
}

interface ProductionByDate {
  date: string;
  quantity: number;
  completed: number;
  pending: number;
  cancelled: number;
}

interface ProductionByStatus {
  status: string;
  count: number;
  percentage: number;
  color: string;
}

interface ProductionReportProps {
  timeRange: 'week' | 'month' | 'quarter' | 'year';
  onTimeRangeChange: (range: 'week' | 'month' | 'quarter' | 'year') => void;
  startDate?: string;
  endDate?: string;
}

export function ProductionReport({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate
}: ProductionReportProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productions, setProductions] = useState<Production[]>([]);
  const [productionByProduct, setProductionByProduct] = useState<ProductionByProduct[]>([]);
  const [productionByDate, setProductionByDate] = useState<ProductionByDate[]>([]);
  const [productionByStatus, setProductionByStatus] = useState<ProductionByStatus[]>([]);
  const [summary, setSummary] = useState({
    totalProduction: 0,
    completedProduction: 0,
    pendingProduction: 0,
    cancelledProduction: 0,
    totalProducts: 0,
  });

  useEffect(() => {
    fetchProductionData();
  }, [timeRange, startDate, endDate]);

  const fetchProductionData = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/production');

      if (!response.ok) {
        throw new Error('Failed to fetch production data');
      }

      const data = await response.json();
      setProductions(data);

      // Process the data for charts and summary
      processProductionData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const processProductionData = (data: Production[]) => {
    // Filter data based on time range
    const filteredData = filterDataByTimeRange(data);

    // Calculate summary
    const totalProduction = filteredData.reduce((sum, prod) => sum + prod.quantity, 0);
    const completedProduction = filteredData
      .filter(prod => prod.status === 'COMPLETED')
      .reduce((sum, prod) => sum + prod.quantity, 0);
    const pendingProduction = filteredData
      .filter(prod => prod.status === 'PENDING')
      .reduce((sum, prod) => sum + prod.quantity, 0);
    const cancelledProduction = filteredData
      .filter(prod => prod.status === 'CANCELLED')
      .reduce((sum, prod) => sum + prod.quantity, 0);
    const totalProducts = new Set(filteredData.map(prod => prod.productId)).size;

    setSummary({
      totalProduction,
      completedProduction,
      pendingProduction,
      cancelledProduction,
      totalProducts,
    });

    // Group by product
    const productMap = new Map();
    filteredData.forEach(prod => {
      if (!productMap.has(prod.productName)) {
        productMap.set(prod.productName, 0);
      }
      productMap.set(prod.productName, productMap.get(prod.productName) + prod.quantity);
    });

    const productionByProductData = Array.from(productMap.entries()).map(([productName, quantity]) => ({
      productName,
      quantity,
      percentage: totalProduction > 0 ? (quantity / totalProduction) * 100 : 0,
    }));

    setProductionByProduct(productionByProductData.sort((a, b) => b.quantity - a.quantity));

    // Group by status
    const statusCounts = {
      COMPLETED: filteredData.filter(prod => prod.status === 'COMPLETED').length,
      PENDING: filteredData.filter(prod => prod.status === 'PENDING').length,
      CANCELLED: filteredData.filter(prod => prod.status === 'CANCELLED').length,
    };

    const totalRecords = filteredData.length;
    const statusData = Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: totalRecords > 0 ? (count / totalRecords) * 100 : 0,
      color: status === 'COMPLETED' ? '#10b981' : status === 'PENDING' ? '#f59e0b' : '#ef4444',
    }));

    setProductionByStatus(statusData);

    // Group by date
    const dateMap = new Map();
    filteredData.forEach(prod => {
      const date = prod.date;
      if (!dateMap.has(date)) {
        dateMap.set(date, { date, quantity: 0, completed: 0, pending: 0, cancelled: 0 });
      }
      const dateData = dateMap.get(date);
      dateData.quantity += prod.quantity;
      if (prod.status === 'COMPLETED') dateData.completed += prod.quantity;
      if (prod.status === 'PENDING') dateData.pending += prod.quantity;
      if (prod.status === 'CANCELLED') dateData.cancelled += prod.quantity;
    });

    const productionByDateData = Array.from(dateMap.values()).sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    setProductionByDate(productionByDateData);
  };

  const filterDataByTimeRange = (data: Production[]) => {
    if (startDate && endDate) {
      return data.filter(prod => {
        const prodDate = new Date(prod.date);
        return prodDate >= new Date(startDate) && prodDate <= new Date(endDate);
      });
    }

    const now = new Date();
    let startFilterDate: Date;

    switch (timeRange) {
      case 'week':
        startFilterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startFilterDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startFilterDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startFilterDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startFilterDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    return data.filter(prod => new Date(prod.date) >= startFilterDate);
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading production report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button
          onClick={() => onTimeRangeChange(timeRange)}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Production Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Production</h3>
            <Factory style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#3b82f6', marginBottom: '0.5rem' }}>
            {summary.totalProduction}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Units produced
          </p>
        </div>

        {/* Completed Production Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Completed Production</h3>
            <Package style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#10b981', marginBottom: '0.5rem' }}>
            {summary.completedProduction}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Units completed
          </p>
        </div>

        {/* Pending Production Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Pending Production</h3>
            <AlertTriangle style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {summary.pendingProduction}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Units pending
          </p>
        </div>

        {/* Total Products Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Products in Production</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#8b5cf6', marginBottom: '0.5rem' }}>
            {summary.totalProducts}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Unique products
          </p>
        </div>
      </div>
    </div>
  );
}
