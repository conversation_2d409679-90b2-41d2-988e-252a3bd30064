'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ArrowDown, ArrowUp, ShoppingCart, TrendingUp } from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface PurchaseItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface Purchase {
  id: string;
  date: string;
  storeId: string;
  storeName: string;
  supplierName: string;
  supplierContact: string;
  amount: number;
  paymentMethod: string;
  status: string;
  items: PurchaseItem[];
}

interface PurchasesBySupplier {
  supplierName: string;
  amount: number;
  count: number;
  percentage: number;
}

interface PurchasesByStore {
  storeId: string;
  storeName: string;
  amount: number;
  count: number;
  percentage: number;
}

interface PurchasesByDate {
  date: string;
  amount: number;
  count: number;
}

interface PurchasesByProduct {
  productId: string;
  productName: string;
  quantity: number;
  amount: number;
}

interface PurchaseReportProps {
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
  storeId?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function PurchaseReport({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate,
  storeId
}: PurchaseReportProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [purchasesBySupplier, setPurchasesBySupplier] = useState<PurchasesBySupplier[]>([]);
  const [purchasesByStore, setPurchasesByStore] = useState<PurchasesByStore[]>([]);
  const [purchasesByDate, setPurchasesByDate] = useState<PurchasesByDate[]>([]);
  const [purchasesByProduct, setPurchasesByProduct] = useState<PurchasesByProduct[]>([]);
  const [summary, setSummary] = useState({
    totalPurchaseAmount: 0,
    totalPurchases: 0,
    uniqueSuppliers: 0,
    uniqueProducts: 0,
  });
  const [activeTab, setActiveTab] = useState<'overview' | 'bySupplier' | 'byProduct' | 'details'>('overview');
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        if (storeId) {
          params.append('storeId', storeId);
        }

        const response = await fetch(`/api/reports/purchases?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch purchase report data');
        }

        const data = await response.json();

        setPurchases(data.purchases);
        setPurchasesBySupplier(data.purchasesBySupplier);
        setPurchasesByStore(data.purchasesByStore);
        setPurchasesByDate(data.purchasesByDate);
        setPurchasesByProduct(data.purchasesByProduct);
        setSummary(data.summary);
      } catch (err) {
        console.error('Error fetching purchase report:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate, storeId]);

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{payload[0].name}</p>
          <p>Amount: {formatCurrency(payload[0].payload.amount)}</p>
          <p>Count: {payload[0].payload.count} purchases</p>
          <p>Percentage: {payload[0].payload.percentage?.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading purchase report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button
          onClick={() => onTimeRangeChange(timeRange)}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  if (selectedPurchase) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Purchase Details</CardTitle>
                <CardDescription>
                  {formatDate(selectedPurchase.date)} - {selectedPurchase.supplierName}
                </CardDescription>
              </div>
              <Button
                variant="outline"
                onClick={() => setSelectedPurchase(null)}
              >
                Back to Report
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3 mb-6">
              <div>
                <p className="text-sm text-muted-foreground">Supplier</p>
                <p className="font-medium">{selectedPurchase.supplierName}</p>
                {selectedPurchase.supplierContact && (
                  <p className="text-sm text-muted-foreground">{selectedPurchase.supplierContact}</p>
                )}
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Store</p>
                <p className="font-medium">{selectedPurchase.storeName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Amount</p>
                <p className="font-medium">{formatCurrency(selectedPurchase.amount)}</p>
                <p className="text-sm text-muted-foreground">
                  {selectedPurchase.paymentMethod} - {selectedPurchase.status}
                </p>
              </div>
            </div>

            <div className="rounded-md border">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="px-4 py-3 text-left font-medium">Product</th>
                      <th className="px-4 py-3 text-right font-medium">Quantity</th>
                      <th className="px-4 py-3 text-right font-medium">Unit Price</th>
                      <th className="px-4 py-3 text-right font-medium">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedPurchase.items.map((item) => (
                      <tr key={item.id} className="border-b">
                        <td className="px-4 py-3">{item.productName}</td>
                        <td className="px-4 py-3 text-right">{item.quantity}</td>
                        <td className="px-4 py-3 text-right">{formatCurrency(item.unitPrice)}</td>
                        <td className="px-4 py-3 text-right">{formatCurrency(item.totalPrice)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t bg-muted/50">
                      <td colSpan={3} className="px-4 py-3 font-medium">Total</td>
                      <td className="px-4 py-3 text-right font-medium">{formatCurrency(selectedPurchase.amount)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Purchase Amount Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Purchase Amount</h3>
            <ShoppingCart style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#3b82f6', marginBottom: '0.5rem' }}>
            {formatCurrency(summary.totalPurchaseAmount)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            From {summary.totalPurchases} purchases
          </p>
        </div>

        {/* Average Purchase Value Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Average Purchase Value</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#10b981', marginBottom: '0.5rem' }}>
            {summary.totalPurchases > 0
              ? formatCurrency(summary.totalPurchaseAmount / summary.totalPurchases)
              : formatCurrency(0)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Per purchase
          </p>
        </div>

        {/* Unique Suppliers Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Unique Suppliers</h3>
            <ShoppingCart style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#8b5cf6', marginBottom: '0.5rem' }}>
            {summary.uniqueSuppliers}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Suppliers worked with
          </p>
        </div>

        {/* Unique Products Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Unique Products</h3>
            <ShoppingCart style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {summary.uniqueProducts}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Products purchased
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="bySupplier">By Supplier</TabsTrigger>
          <TabsTrigger value="byProduct">By Product</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Purchases Over Time</CardTitle>
                <CardDescription>
                  Daily purchase amounts over the selected period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={purchasesByDate}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                      <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                      <Tooltip formatter={(value, name) => {
                        if (name === 'amount') return formatCurrency(value as number);
                        return value;
                      }} />
                      <Legend />
                      <Bar yAxisId="left" dataKey="amount" name="Purchase Amount" fill="#8884d8" />
                      <Bar yAxisId="right" dataKey="count" name="Purchase Count" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Purchases by Supplier</CardTitle>
                <CardDescription>
                  Distribution of purchases by supplier
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={purchasesBySupplier.slice(0, 5)}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="amount"
                        nameKey="supplierName"
                        label={({ supplierName, percent }) =>
                          `${supplierName.substring(0, 10)}${supplierName.length > 10 ? '...' : ''}: ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {purchasesBySupplier.slice(0, 5).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="bySupplier">
          <Card>
            <CardHeader>
              <CardTitle>Purchases by Supplier</CardTitle>
              <CardDescription>
                Detailed breakdown of purchases by supplier
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="px-4 py-3 text-left font-medium">Supplier</th>
                        <th className="px-4 py-3 text-right font-medium">Purchase Count</th>
                        <th className="px-4 py-3 text-right font-medium">Total Amount</th>
                        <th className="px-4 py-3 text-right font-medium">% of Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {purchasesBySupplier.map((item) => (
                        <tr key={item.supplierName} className="border-b">
                          <td className="px-4 py-3">{item.supplierName}</td>
                          <td className="px-4 py-3 text-right">{item.count}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(item.amount)}</td>
                          <td className="px-4 py-3 text-right">{item.percentage.toFixed(1)}%</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="border-t bg-muted/50">
                        <td className="px-4 py-3 font-medium">Total</td>
                        <td className="px-4 py-3 text-right font-medium">{summary.totalPurchases}</td>
                        <td className="px-4 py-3 text-right font-medium">{formatCurrency(summary.totalPurchaseAmount)}</td>
                        <td className="px-4 py-3 text-right font-medium">100%</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="byProduct">
          <Card>
            <CardHeader>
              <CardTitle>Purchases by Product</CardTitle>
              <CardDescription>
                Detailed breakdown of purchases by product
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="px-4 py-3 text-left font-medium">Product</th>
                        <th className="px-4 py-3 text-right font-medium">Quantity</th>
                        <th className="px-4 py-3 text-right font-medium">Total Amount</th>
                        <th className="px-4 py-3 text-right font-medium">% of Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {purchasesByProduct.map((item) => (
                        <tr key={item.productId} className="border-b">
                          <td className="px-4 py-3">{item.productName}</td>
                          <td className="px-4 py-3 text-right">{item.quantity}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(item.amount)}</td>
                          <td className="px-4 py-3 text-right">
                            {((item.amount / summary.totalPurchaseAmount) * 100).toFixed(1)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="border-t bg-muted/50">
                        <td colSpan={2} className="px-4 py-3 font-medium">Total</td>
                        <td className="px-4 py-3 text-right font-medium">{formatCurrency(summary.totalPurchaseAmount)}</td>
                        <td className="px-4 py-3 text-right font-medium">100%</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Purchase Details</CardTitle>
              <CardDescription>
                Detailed list of all purchases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="px-4 py-3 text-left font-medium">Date</th>
                        <th className="px-4 py-3 text-left font-medium">Supplier</th>
                        <th className="px-4 py-3 text-left font-medium">Store</th>
                        <th className="px-4 py-3 text-right font-medium">Amount</th>
                        <th className="px-4 py-3 text-center font-medium">Status</th>
                        <th className="px-4 py-3 text-center font-medium">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {purchases.map((purchase) => (
                        <tr key={purchase.id} className="border-b">
                          <td className="px-4 py-3">{formatDate(purchase.date)}</td>
                          <td className="px-4 py-3">{purchase.supplierName}</td>
                          <td className="px-4 py-3">{purchase.storeName}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(purchase.amount)}</td>
                          <td className="px-4 py-3 text-center">
                            <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              purchase.status === 'COMPLETED'
                                ? 'bg-green-100 text-green-800'
                                : purchase.status === 'PENDING'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {purchase.status}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedPurchase(purchase)}
                            >
                              View
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="border-t bg-muted/50">
                        <td colSpan={3} className="px-4 py-3 font-medium">Total</td>
                        <td className="px-4 py-3 text-right font-medium">{formatCurrency(summary.totalPurchaseAmount)}</td>
                        <td colSpan={2}></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
