'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import { ArrowDown, ArrowUp, DollarSign, ShoppingCart, TrendingUp, Users } from 'lucide-react';

interface SalesData {
  date: string;
  revenue: number;
  orders: number;
  customers: number;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
}

interface SalesOverviewProps {
  salesData?: SalesData[];
  categoryData?: CategoryData[];
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
}

export function SalesOverview({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate
}: SalesOverviewProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const [summary, setSummary] = useState({
    totalRevenue: 0,
    totalOrders: 0,
    totalCustomers: 0,
    averageOrderValue: 0,
    revenueChange: 0,
    ordersChange: 0,
    customersChange: 0,
    aovChange: 0
  });

  // Fetch sales data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        const response = await fetch(`/api/reports/sales?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch sales data');
        }

        const data = await response.json();
        // Ensure we have valid arrays for our data
        setSalesData(Array.isArray(data.salesByDate) ? data.salesByDate : []);
        setCategoryData(Array.isArray(data.salesByCategory) ? data.salesByCategory : []);

        // Check if data.salesData exists (for backward compatibility)
        if (!data.salesByDate && Array.isArray(data.salesData)) {
          setSalesData(data.salesData);
        }

        // Check if data.categoryData exists (for backward compatibility)
        if (!data.salesByCategory && Array.isArray(data.categoryData)) {
          setCategoryData(data.categoryData);
        }
        // Ensure we have valid summary data with defaults
        setSummary({
          totalRevenue: data.summary?.totalRevenue || 0,
          totalOrders: data.summary?.totalOrders || 0,
          totalCustomers: data.summary?.uniqueCustomers || 0,
          averageOrderValue: data.summary?.avgOrderValue || 0,
          revenueChange: data.summary?.revenueChange || 0,
          ordersChange: data.summary?.ordersChange || 0,
          customersChange: data.summary?.customersChange || 0,
          aovChange: data.summary?.avgOrderValueChange || 0
        });
      } catch (err) {
        console.error('Error fetching sales data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate]);

  // If loading or error, show appropriate UI
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading sales data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <Button
          variant="outline"
          onClick={() => onTimeRangeChange(timeRange)}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Calculate totals and comparisons
  const totalRevenue = summary.totalRevenue;
  const totalOrders = summary.totalOrders;
  const totalCustomers = summary.totalCustomers;

  // Calculate average order value
  const averageOrderValue = summary.averageOrderValue;

  // For comparison with previous period
  const revenueChange = summary.revenueChange;
  const ordersChange = summary.ordersChange;
  const customersChange = summary.customersChange;
  const aovChange = summary.aovChange;

  // Custom tooltip for the bar chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '0.75rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          fontSize: '0.875rem'
        }}>
          <p style={{ fontWeight: '600', color: '#0f172a', marginBottom: '0.25rem' }}>{label}</p>
          <p style={{ color: '#64748b', marginBottom: '0.25rem' }}>
            Revenue: {formatCurrency(payload[0].value)}
          </p>
          <p style={{ color: '#64748b' }}>
            Orders: {payload[1].value}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* This header is now handled by the parent component */}

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Revenue Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Revenue</h3>
            <DollarSign style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a', marginBottom: '0.5rem' }}>
            {formatCurrency(totalRevenue)}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {revenueChange >= 0 ? (
              <>
                <ArrowUp style={{ height: '16px', width: '16px', color: '#10b981', marginRight: '0.25rem' }} />
                <span style={{ color: '#10b981' }}>{revenueChange}%</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ height: '16px', width: '16px', color: '#ef4444', marginRight: '0.25rem' }} />
                <span style={{ color: '#ef4444' }}>{Math.abs(revenueChange)}%</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>

        {/* Total Orders Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Total Orders</h3>
            <ShoppingCart style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a', marginBottom: '0.5rem' }}>
            {totalOrders}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {ordersChange >= 0 ? (
              <>
                <ArrowUp style={{ height: '16px', width: '16px', color: '#10b981', marginRight: '0.25rem' }} />
                <span style={{ color: '#10b981' }}>{ordersChange}%</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ height: '16px', width: '16px', color: '#ef4444', marginRight: '0.25rem' }} />
                <span style={{ color: '#ef4444' }}>{Math.abs(ordersChange)}%</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>

        {/* Unique Customers Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Unique Customers</h3>
            <Users style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a', marginBottom: '0.5rem' }}>
            {totalCustomers}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {customersChange >= 0 ? (
              <>
                <ArrowUp style={{ height: '16px', width: '16px', color: '#10b981', marginRight: '0.25rem' }} />
                <span style={{ color: '#10b981' }}>{customersChange}%</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ height: '16px', width: '16px', color: '#ef4444', marginRight: '0.25rem' }} />
                <span style={{ color: '#ef4444' }}>{Math.abs(customersChange)}%</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>

        {/* Average Order Value Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>Avg. Order Value</h3>
            <TrendingUp style={{ height: '20px', width: '20px', color: '#64748b' }} />
          </div>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#0f172a', marginBottom: '0.5rem' }}>
            {formatCurrency(averageOrderValue)}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#64748b' }}>
            {aovChange >= 0 ? (
              <>
                <ArrowUp style={{ height: '16px', width: '16px', color: '#10b981', marginRight: '0.25rem' }} />
                <span style={{ color: '#10b981' }}>{aovChange}%</span>
              </>
            ) : (
              <>
                <ArrowDown style={{ height: '16px', width: '16px', color: '#ef4444', marginRight: '0.25rem' }} />
                <span style={{ color: '#ef4444' }}>{Math.abs(aovChange)}%</span>
              </>
            )}
            <span style={{ marginLeft: '0.25rem' }}>from previous period</span>
          </div>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '1.5rem'
      }}>
        {/* Revenue & Orders Chart */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Revenue & Orders
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
              Daily revenue and order count for the selected period
            </p>
          </div>
          <div style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={salesData || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="date" stroke="#64748b" fontSize={12} />
                <YAxis yAxisId="left" orientation="left" stroke="#3b82f6" fontSize={12} />
                <YAxis yAxisId="right" orientation="right" stroke="#10b981" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar yAxisId="left" dataKey="revenue" name="Revenue" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                <Bar yAxisId="right" dataKey="orders" name="Orders" fill="#10b981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Sales by Category Chart */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Sales by Category
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
              Revenue distribution across product categories
            </p>
          </div>
          <div style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={categoryData || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#3b82f6"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {categoryData && categoryData.length > 0 ? categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  )) : null}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
}
