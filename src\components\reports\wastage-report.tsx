'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>xis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from 'recharts';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ArrowDown, ArrowUp, Trash2, TrendingUp } from 'lucide-react';

interface WastageRecord {
  id: string;
  date: string;
  productId: string;
  productName: string;
  quantity: number;
  reason: string;
  value: number;
  productPrice: number;
  productUnit: string;
}

interface WastageByReason {
  reason: string;
  quantity: number;
  value: number;
}

interface WastageByProduct {
  productId: string;
  productName: string;
  quantity: number;
  value: number;
}

interface WastageByDate {
  date: string;
  quantity: number;
  value: number;
}

interface WastageReportProps {
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  startDate?: string;
  endDate?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function WastageReport({
  timeRange,
  onTimeRangeChange,
  startDate,
  endDate
}: WastageReportProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [wastageRecords, setWastageRecords] = useState<WastageRecord[]>([]);
  const [wastageByReason, setWastageByReason] = useState<WastageByReason[]>([]);
  const [wastageByProduct, setWastageByProduct] = useState<WastageByProduct[]>([]);
  const [wastageByDate, setWastageByDate] = useState<WastageByDate[]>([]);
  const [summary, setSummary] = useState({
    totalWastageValue: 0,
    totalWastageQuantity: 0,
    totalRecords: 0,
  });
  const [activeTab, setActiveTab] = useState<'overview' | 'byReason' | 'byProduct' | 'details'>('overview');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('timeRange', timeRange);

        if (startDate) {
          params.append('startDate', startDate);
        }

        if (endDate) {
          params.append('endDate', endDate);
        }

        const response = await fetch(`/api/reports/wastage?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch wastage report data');
        }

        const data = await response.json();

        setWastageRecords(data.wastageRecords);
        setWastageByReason(data.wastageByReason);
        setWastageByProduct(data.wastageByProduct);
        setWastageByDate(data.wastageByDate);
        setSummary(data.summary);
      } catch (err) {
        console.error('Error fetching wastage report:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, startDate, endDate]);

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '0.5rem',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>{payload[0].name}</p>
          <p style={{ fontSize: '0.875rem', color: '#64748b' }}>Quantity: {payload[0].payload.quantity}</p>
          <p style={{ fontSize: '0.875rem', color: '#64748b' }}>Value: {formatCurrency(payload[0].payload.value)}</p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '3rem' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #f1f5f9',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>Loading wastage report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '3rem', textAlign: 'center' }}>
        <p style={{ color: '#dc2626', marginBottom: '1rem' }}>{error}</p>
        <button
          onClick={() => onTimeRangeChange(timeRange)}
          style={{
            backgroundColor: 'transparent',
            color: '#3b82f6',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem'
      }}>
        {/* Total Wastage Value Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b', marginBottom: '1rem' }}>
            Total Wastage Value
          </h3>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#ef4444', marginBottom: '0.5rem' }}>
            {formatCurrency(summary.totalWastageValue)}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            From {summary.totalRecords} wastage records
          </p>
        </div>

        {/* Total Wastage Quantity Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b', marginBottom: '1rem' }}>
            Total Wastage Quantity
          </h3>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#f59e0b', marginBottom: '0.5rem' }}>
            {summary.totalWastageQuantity}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            Units of products wasted
          </p>
        </div>

        {/* Top Wastage Reason Card */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#64748b', marginBottom: '1rem' }}>
            Top Wastage Reason
          </h3>
          <div style={{ fontSize: '1.875rem', fontWeight: '700', color: '#8b5cf6', marginBottom: '0.5rem' }}>
            {wastageByReason.length > 0 ? wastageByReason[0].reason : 'N/A'}
          </div>
          <p style={{ fontSize: '0.75rem', color: '#64748b' }}>
            {wastageByReason.length > 0
              ? `${formatCurrency(wastageByReason[0].value)} (${wastageByReason[0].quantity} units)`
              : 'No wastage data available'}
          </p>
        </div>
      </div>

      {/* Professional Tab Navigation */}
      <div style={{ marginBottom: '1.5rem' }}>
        <div style={{
          display: 'flex',
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '0.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          {['overview', 'byReason', 'byProduct', 'details'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as any)}
              style={{
                flex: 1,
                padding: '0.75rem 1rem',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: activeTab === tab ? '#3b82f6' : 'transparent',
                color: activeTab === tab ? 'white' : '#64748b',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s',
                textTransform: 'capitalize'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = '#f8fafc';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
            >
              {tab === 'byReason' ? 'By Reason' : tab === 'byProduct' ? 'By Product' : tab}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '1.5rem'
        }}>
          {/* Wastage Over Time Chart */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Wastage Over Time
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
              Value of wastage over the selected period
            </p>
            <div style={{ height: '300px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={wastageByDate}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                  <XAxis dataKey="date" stroke="#64748b" fontSize={12} />
                  <YAxis stroke="#64748b" fontSize={12} />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Bar dataKey="value" name="Wastage Value" fill="#ef4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Wastage by Reason Chart */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
              Wastage by Reason
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
              Distribution of wastage by reason
            </p>
            <div style={{ height: '300px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={wastageByReason}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#ef4444"
                    dataKey="value"
                    nameKey="reason"
                    label={({ reason, percent }) =>
                      `${reason}: ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {wastageByReason.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {/* By Reason Tab */}
      {activeTab === 'byReason' && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Wastage by Reason
          </h3>
          <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
            Detailed breakdown of wastage by reason
          </p>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Reason
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Quantity
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Value
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    % of Total
                  </th>
                </tr>
              </thead>
              <tbody>
                {wastageByReason.map((item) => (
                  <tr key={item.reason} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                      {item.reason}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {item.quantity}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                      {formatCurrency(item.value)}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {((item.value / summary.totalWastageValue) * 100).toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <td style={{ padding: '0.75rem 1rem', fontWeight: '600', color: '#0f172a' }}>
                    Total
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {summary.totalWastageQuantity}
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {formatCurrency(summary.totalWastageValue)}
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    100%
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {/* By Product Tab */}
      {activeTab === 'byProduct' && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Wastage by Product
          </h3>
          <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
            Detailed breakdown of wastage by product
          </p>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Product
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Quantity
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Value
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    % of Total
                  </th>
                </tr>
              </thead>
              <tbody>
                {wastageByProduct.map((item) => (
                  <tr key={item.productId} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                      {item.productName}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {item.quantity}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                      {formatCurrency(item.value)}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {((item.value / summary.totalWastageValue) * 100).toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <td style={{ padding: '0.75rem 1rem', fontWeight: '600', color: '#0f172a' }}>
                    Total
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {summary.totalWastageQuantity}
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {formatCurrency(summary.totalWastageValue)}
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    100%
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {/* Details Tab */}
      {activeTab === 'details' && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#0f172a', marginBottom: '0.5rem' }}>
            Wastage Details
          </h3>
          <p style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '1.5rem' }}>
            Detailed list of all wastage records
          </p>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Date
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Product
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Quantity
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Reason
                  </th>
                  <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontSize: '0.875rem', fontWeight: '500', color: '#64748b' }}>
                    Value
                  </th>
                </tr>
              </thead>
              <tbody>
                {wastageRecords.map((record) => (
                  <tr key={record.id} style={{ borderBottom: '1px solid #f1f5f9' }}>
                    <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                      {formatDate(record.date)}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', fontWeight: '500', color: '#0f172a' }}>
                      {record.productName}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#64748b' }}>
                      {record.quantity}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', color: '#64748b' }}>
                      {record.reason}
                    </td>
                    <td style={{ padding: '0.75rem 1rem', textAlign: 'right', color: '#0f172a' }}>
                      {formatCurrency(record.value)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ borderTop: '2px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
                  <td colSpan={2} style={{ padding: '0.75rem 1rem', fontWeight: '600', color: '#0f172a' }}>
                    Total
                  </td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {summary.totalWastageQuantity}
                  </td>
                  <td style={{ padding: '0.75rem 1rem' }}></td>
                  <td style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '600', color: '#0f172a' }}>
                    {formatCurrency(summary.totalWastageValue)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
