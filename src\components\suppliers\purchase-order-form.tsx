'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Save, X, Plus, Trash, Calculator } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Supplier {
  id: string;
  name: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  unitPrice: number;
}

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  sku: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface PurchaseOrder {
  id?: string;
  orderNumber: string;
  supplierId: string;
  date: string;
  expectedDeliveryDate?: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'unpaid' | 'partial' | 'paid';
  shippingAddress: string;
  notes?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
}

interface PurchaseOrderFormProps {
  initialData?: Partial<PurchaseOrder>;
  supplier: Supplier;
  products: Product[];
  onSubmit: (data: PurchaseOrder) => void;
  onCancel: () => void;
}

export function PurchaseOrderForm({
  initialData,
  supplier,
  products,
  onSubmit,
  onCancel
}: PurchaseOrderFormProps) {
  const [formData, setFormData] = useState<PurchaseOrder>({
    orderNumber: initialData?.orderNumber || generateOrderNumber(),
    supplierId: supplier.id,
    date: initialData?.date || new Date().toISOString().split('T')[0],
    expectedDeliveryDate: initialData?.expectedDeliveryDate || '',
    status: initialData?.status || 'pending',
    paymentStatus: initialData?.paymentStatus || 'unpaid',
    shippingAddress: initialData?.shippingAddress || `${supplier.address}, ${supplier.city}, ${supplier.state} ${supplier.postalCode}, ${supplier.country}`,
    notes: initialData?.notes || '',
    items: initialData?.items || [],
    subtotal: initialData?.subtotal || 0,
    tax: initialData?.tax || 0,
    shipping: initialData?.shipping || 0,
    total: initialData?.total || 0,
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedProductId, setSelectedProductId] = useState('');
  const [quantity, setQuantity] = useState('1');
  
  // Generate a unique order number
  function generateOrderNumber() {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `PO-${year}${month}${day}-${random}`;
  }
  
  // Calculate totals whenever items, tax, or shipping changes
  useEffect(() => {
    const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
    const total = subtotal + formData.tax + formData.shipping;
    
    setFormData(prev => ({
      ...prev,
      subtotal,
      total
    }));
  }, [formData.items, formData.tax, formData.shipping]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle numeric inputs
    if (name === 'tax' || name === 'shipping') {
      const numValue = value === '' ? 0 : parseFloat(value);
      setFormData({ ...formData, [name]: numValue });
    } else {
      setFormData({ ...formData, [name]: value });
    }
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };
  
  const handleAddItem = () => {
    if (!selectedProductId || parseFloat(quantity) <= 0) {
      return;
    }
    
    const product = products.find(p => p.id === selectedProductId);
    if (!product) return;
    
    const newItem: OrderItem = {
      id: Date.now().toString(),
      productId: product.id,
      productName: product.name,
      sku: product.sku,
      quantity: parseFloat(quantity),
      unitPrice: product.unitPrice,
      total: product.unitPrice * parseFloat(quantity)
    };
    
    setFormData({
      ...formData,
      items: [...formData.items, newItem]
    });
    
    // Reset selection
    setSelectedProductId('');
    setQuantity('1');
  };
  
  const handleRemoveItem = (id: string) => {
    setFormData({
      ...formData,
      items: formData.items.filter(item => item.id !== id)
    });
  };
  
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.orderNumber.trim()) {
      newErrors.orderNumber = 'Order number is required';
    }
    
    if (!formData.date.trim()) {
      newErrors.date = 'Order date is required';
    }
    
    if (!formData.shippingAddress.trim()) {
      newErrors.shippingAddress = 'Shipping address is required';
    }
    
    if (formData.items.length === 0) {
      newErrors.items = 'At least one item is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Order Information</h3>
          
          <div className="space-y-2">
            <label htmlFor="orderNumber" className="text-sm font-medium">
              Order Number *
            </label>
            <Input
              id="orderNumber"
              name="orderNumber"
              value={formData.orderNumber}
              onChange={handleInputChange}
              className={errors.orderNumber ? 'border-red-500' : ''}
              required
            />
            {errors.orderNumber && (
              <p className="text-xs text-red-500">{errors.orderNumber}</p>
            )}
          </div>
          
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <label htmlFor="date" className="text-sm font-medium">
                Order Date *
              </label>
              <Input
                id="date"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleInputChange}
                className={errors.date ? 'border-red-500' : ''}
                required
              />
              {errors.date && (
                <p className="text-xs text-red-500">{errors.date}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <label htmlFor="expectedDeliveryDate" className="text-sm font-medium">
                Expected Delivery Date
              </label>
              <Input
                id="expectedDeliveryDate"
                name="expectedDeliveryDate"
                type="date"
                value={formData.expectedDeliveryDate || ''}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <label htmlFor="status" className="text-sm font-medium">
                Order Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            
            <div className="space-y-2">
              <label htmlFor="paymentStatus" className="text-sm font-medium">
                Payment Status
              </label>
              <select
                id="paymentStatus"
                name="paymentStatus"
                value={formData.paymentStatus}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="unpaid">Unpaid</option>
                <option value="partial">Partially Paid</option>
                <option value="paid">Paid</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Supplier Information</h3>
          
          <div className="rounded-lg border p-4">
            <div className="font-medium">{supplier.name}</div>
            <div className="text-sm text-muted-foreground">Contact: {supplier.contactName}</div>
            <div className="text-sm text-muted-foreground">Email: {supplier.email}</div>
            <div className="text-sm text-muted-foreground">Phone: {supplier.phone}</div>
          </div>
          
          <div className="space-y-2">
            <label htmlFor="shippingAddress" className="text-sm font-medium">
              Shipping Address *
            </label>
            <textarea
              id="shippingAddress"
              name="shippingAddress"
              value={formData.shippingAddress}
              onChange={handleInputChange}
              className={`flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${
                errors.shippingAddress ? 'border-red-500' : ''
              }`}
              required
            />
            {errors.shippingAddress && (
              <p className="text-xs text-red-500">{errors.shippingAddress}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label htmlFor="notes" className="text-sm font-medium">
              Order Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes || ''}
              onChange={handleInputChange}
              className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              placeholder="Add any special instructions or notes..."
            />
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Order Items</h3>
        
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="flex-1">
            <select
              value={selectedProductId}
              onChange={(e) => setSelectedProductId(e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a product</option>
              {products.map(product => (
                <option key={product.id} value={product.id}>
                  {product.name} - {product.sku} ({formatCurrency(product.unitPrice)})
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-24">
            <Input
              type="number"
              min="1"
              step="1"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              placeholder="Qty"
            />
          </div>
          
          <Button 
            type="button" 
            onClick={handleAddItem}
            disabled={!selectedProductId || parseFloat(quantity) <= 0}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Item
          </Button>
        </div>
        
        {errors.items && (
          <p className="text-xs text-red-500">{errors.items}</p>
        )}
        
        <div className="rounded-lg border">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="px-4 py-3 text-left font-medium">Product</th>
                  <th className="px-4 py-3 text-left font-medium">SKU</th>
                  <th className="px-4 py-3 text-right font-medium">Unit Price</th>
                  <th className="px-4 py-3 text-right font-medium">Quantity</th>
                  <th className="px-4 py-3 text-right font-medium">Total</th>
                  <th className="px-4 py-3 text-center font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {formData.items.length > 0 ? (
                  formData.items.map(item => (
                    <tr key={item.id} className="border-b">
                      <td className="px-4 py-3 font-medium">{item.productName}</td>
                      <td className="px-4 py-3 text-muted-foreground">{item.sku}</td>
                      <td className="px-4 py-3 text-right">{formatCurrency(item.unitPrice)}</td>
                      <td className="px-4 py-3 text-right">{item.quantity}</td>
                      <td className="px-4 py-3 text-right font-medium">{formatCurrency(item.total)}</td>
                      <td className="px-4 py-3 text-center">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveItem(item.id)}
                          className="text-destructive"
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Remove</span>
                        </Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-4 py-4 text-center text-muted-foreground">
                      No items added to this order yet.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <div></div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Order Summary</h3>
          
          <div className="rounded-lg border p-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Subtotal</span>
                <span>{formatCurrency(formData.subtotal)}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Tax</span>
                <div className="flex w-24 items-center">
                  <span className="mr-2">$</span>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    name="tax"
                    value={formData.tax}
                    onChange={handleInputChange}
                    className="h-8"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Shipping</span>
                <div className="flex w-24 items-center">
                  <span className="mr-2">$</span>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    name="shipping"
                    value={formData.shipping}
                    onChange={handleInputChange}
                    className="h-8"
                  />
                </div>
              </div>
              
              <div className="border-t pt-2">
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>{formatCurrency(formData.total)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button type="submit">
          <Save className="mr-2 h-4 w-4" />
          {initialData?.id ? 'Update' : 'Create'} Purchase Order
        </Button>
      </div>
    </form>
  );
}
