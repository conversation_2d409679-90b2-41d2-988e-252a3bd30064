'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  Edit, 
  Trash, 
  Phone, 
  Mail, 
  MapPin, 
  Package, 
  DollarSign, 
  Calendar, 
  Clock,
  FileText,
  Star,
  StarOff
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

interface Supplier {
  id: string;
  name: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  website?: string;
  notes?: string;
  status: 'active' | 'inactive';
  paymentTerms?: string;
  taxId?: string;
  rating?: number;
  createdAt: string;
  updatedAt: string;
}

interface SupplierProduct {
  id: string;
  name: string;
  category: string;
  sku: string;
  unitPrice: number;
  minOrderQuantity?: number;
  leadTime?: number;
  lastOrderDate?: string;
}

interface PurchaseOrder {
  id: string;
  orderNumber: string;
  date: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  paymentStatus: 'unpaid' | 'partial' | 'paid';
  expectedDeliveryDate?: string;
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
}

interface SupplierDetailProps {
  supplier: Supplier;
  products: SupplierProduct[];
  purchaseOrders: PurchaseOrder[];
  onBack: () => void;
  onEdit: (supplier: Supplier) => void;
  onDelete: (id: string) => void;
  onCreatePurchaseOrder: (supplierId: string) => void;
}

export function SupplierDetail({
  supplier,
  products,
  purchaseOrders,
  onBack,
  onEdit,
  onDelete,
  onCreatePurchaseOrder
}: SupplierDetailProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'orders' | 'history'>('overview');
  
  // Calculate some metrics
  const totalOrders = purchaseOrders.length;
  const totalSpent = purchaseOrders.reduce((sum, order) => sum + order.total, 0);
  const pendingOrders = purchaseOrders.filter(order => order.status === 'pending' || order.status === 'confirmed').length;
  const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
  
  // Get recent orders
  const recentOrders = [...purchaseOrders]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);
  
  // Render supplier rating stars
  const renderRating = (rating: number = 0) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      if (i <= rating) {
        stars.push(<Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />);
      } else {
        stars.push(<StarOff key={i} className="h-4 w-4 text-muted-foreground" />);
      }
    }
    return <div className="flex">{stars}</div>;
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suppliers
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => onEdit(supplier)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => {
              if (window.confirm(`Are you sure you want to delete ${supplier.name}?`)) {
                onDelete(supplier.id);
              }
            }}
          >
            <Trash className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-[2fr_1fr]">
        <div>
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold">{supplier.name}</h1>
              <p className="text-muted-foreground">
                {supplier.status === 'active' ? (
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                    Active
                  </span>
                ) : (
                  <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                    Inactive
                  </span>
                )}
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">Supplier Rating</div>
              {renderRating(supplier.rating)}
            </div>
          </div>
          
          <div className="mt-6">
            <div className="flex border-b">
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'products' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('products')}
              >
                Products ({products.length})
              </button>
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'orders' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('orders')}
              >
                Purchase Orders ({purchaseOrders.length})
              </button>
              <button
                className={`px-4 py-2 font-medium ${activeTab === 'history' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                onClick={() => setActiveTab('history')}
              >
                History
              </button>
            </div>
            
            {activeTab === 'overview' && (
              <div className="mt-4 space-y-6">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium">Contact Information</h3>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center text-sm">
                        <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                        <a href={`mailto:${supplier.email}`} className="hover:underline">
                          {supplier.email}
                        </a>
                      </div>
                      <div className="flex items-center text-sm">
                        <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                        <a href={`tel:${supplier.phone}`} className="hover:underline">
                          {supplier.phone}
                        </a>
                      </div>
                      <div className="flex items-center text-sm">
                        <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>
                          {supplier.address}, {supplier.city}, {supplier.state} {supplier.postalCode}, {supplier.country}
                        </span>
                      </div>
                      {supplier.website && (
                        <div className="flex items-center text-sm">
                          <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                          <a href={supplier.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                            {supplier.website}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium">Business Details</h3>
                    <div className="mt-2 space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Contact Person:</span>
                        <span>{supplier.contactName}</span>
                      </div>
                      {supplier.taxId && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Tax ID:</span>
                          <span>{supplier.taxId}</span>
                        </div>
                      )}
                      {supplier.paymentTerms && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Payment Terms:</span>
                          <span>{supplier.paymentTerms}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Supplier Since:</span>
                        <span>{formatDate(supplier.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="rounded-lg border p-4">
                  <h3 className="font-medium">Supplier Notes</h3>
                  <p className="mt-2 text-sm">
                    {supplier.notes || 'No notes available for this supplier.'}
                  </p>
                </div>
                
                <div className="rounded-lg border">
                  <h3 className="border-b px-4 py-3 font-medium">Recent Purchase Orders</h3>
                  {recentOrders.length > 0 ? (
                    <div className="divide-y">
                      {recentOrders.map(order => (
                        <div key={order.id} className="flex items-center justify-between p-4">
                          <div>
                            <div className="font-medium">{order.orderNumber}</div>
                            <div className="text-sm text-muted-foreground">{formatDate(order.date)}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{formatCurrency(order.total)}</div>
                            <div className="text-sm">
                              <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                                order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                                order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                                order.status === 'confirmed' ? 'bg-purple-100 text-purple-800' :
                                order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      No purchase orders found for this supplier.
                    </div>
                  )}
                  {recentOrders.length > 0 && (
                    <div className="border-t p-4 text-center">
                      <Button variant="link" onClick={() => setActiveTab('orders')}>
                        View All Purchase Orders
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {activeTab === 'products' && (
              <div className="mt-4 space-y-4">
                <div className="flex justify-between">
                  <h3 className="font-medium">Products Supplied</h3>
                  <Button size="sm">
                    <Package className="mr-2 h-4 w-4" />
                    Add Product
                  </Button>
                </div>
                
                <div className="rounded-lg border">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="px-4 py-3 text-left font-medium">Product Name</th>
                          <th className="px-4 py-3 text-left font-medium">Category</th>
                          <th className="px-4 py-3 text-left font-medium">SKU</th>
                          <th className="px-4 py-3 text-right font-medium">Unit Price</th>
                          <th className="px-4 py-3 text-right font-medium">MOQ</th>
                          <th className="px-4 py-3 text-right font-medium">Lead Time</th>
                          <th className="px-4 py-3 text-right font-medium">Last Order</th>
                        </tr>
                      </thead>
                      <tbody>
                        {products.length > 0 ? (
                          products.map(product => (
                            <tr key={product.id} className="border-b">
                              <td className="px-4 py-3 font-medium">{product.name}</td>
                              <td className="px-4 py-3">{product.category}</td>
                              <td className="px-4 py-3 text-muted-foreground">{product.sku}</td>
                              <td className="px-4 py-3 text-right">{formatCurrency(product.unitPrice)}</td>
                              <td className="px-4 py-3 text-right">{product.minOrderQuantity || '-'}</td>
                              <td className="px-4 py-3 text-right">
                                {product.leadTime ? `${product.leadTime} days` : '-'}
                              </td>
                              <td className="px-4 py-3 text-right">
                                {product.lastOrderDate ? formatDate(product.lastOrderDate) : 'Never'}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={7} className="px-4 py-4 text-center text-muted-foreground">
                              No products found for this supplier.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'orders' && (
              <div className="mt-4 space-y-4">
                <div className="flex justify-between">
                  <h3 className="font-medium">Purchase Orders</h3>
                  <Button size="sm" onClick={() => onCreatePurchaseOrder(supplier.id)}>
                    <FileText className="mr-2 h-4 w-4" />
                    Create Purchase Order
                  </Button>
                </div>
                
                <div className="rounded-lg border">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="px-4 py-3 text-left font-medium">Order Number</th>
                          <th className="px-4 py-3 text-left font-medium">Date</th>
                          <th className="px-4 py-3 text-right font-medium">Total</th>
                          <th className="px-4 py-3 text-center font-medium">Status</th>
                          <th className="px-4 py-3 text-center font-medium">Payment</th>
                          <th className="px-4 py-3 text-right font-medium">Expected Delivery</th>
                          <th className="px-4 py-3 text-center font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {purchaseOrders.length > 0 ? (
                          purchaseOrders.map(order => (
                            <tr key={order.id} className="border-b">
                              <td className="px-4 py-3 font-medium">{order.orderNumber}</td>
                              <td className="px-4 py-3">{formatDate(order.date)}</td>
                              <td className="px-4 py-3 text-right">{formatCurrency(order.total)}</td>
                              <td className="px-4 py-3 text-center">
                                <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                                  order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                                  order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                                  order.status === 'confirmed' ? 'bg-purple-100 text-purple-800' :
                                  order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                </span>
                              </td>
                              <td className="px-4 py-3 text-center">
                                <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                                  order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' :
                                  order.paymentStatus === 'partial' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                                </span>
                              </td>
                              <td className="px-4 py-3 text-right">
                                {order.expectedDeliveryDate ? formatDate(order.expectedDeliveryDate) : '-'}
                              </td>
                              <td className="px-4 py-3 text-center">
                                <Button variant="ghost" size="sm">
                                  View
                                </Button>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={7} className="px-4 py-4 text-center text-muted-foreground">
                              No purchase orders found for this supplier.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'history' && (
              <div className="mt-4 space-y-4">
                <h3 className="font-medium">Supplier History</h3>
                
                <div className="rounded-lg border p-4">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Supplier Added</p>
                        <p className="text-sm text-muted-foreground">{formatDate(supplier.createdAt)}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Last Updated</p>
                        <p className="text-sm text-muted-foreground">{formatDate(supplier.updatedAt)}</p>
                      </div>
                    </div>
                    
                    {purchaseOrders.length > 0 && (
                      <div className="flex items-center">
                        <FileText className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">First Order</p>
                          <p className="text-sm text-muted-foreground">
                            {formatDate(purchaseOrders.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())[0].date)}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {purchaseOrders.length > 0 && (
                      <div className="flex items-center">
                        <DollarSign className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Total Spent</p>
                          <p className="text-sm text-muted-foreground">{formatCurrency(totalSpent)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="rounded-lg border">
            <div className="border-b px-4 py-3">
              <h3 className="font-medium">Supplier Metrics</h3>
            </div>
            <div className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Orders</span>
                  <span className="font-medium">{totalOrders}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Spent</span>
                  <span className="font-medium">{formatCurrency(totalSpent)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Pending Orders</span>
                  <span className="font-medium">{pendingOrders}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Avg. Order Value</span>
                  <span className="font-medium">{formatCurrency(averageOrderValue)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Products Supplied</span>
                  <span className="font-medium">{products.length}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="rounded-lg border">
            <div className="border-b px-4 py-3">
              <h3 className="font-medium">Quick Actions</h3>
            </div>
            <div className="p-4">
              <div className="space-y-2">
                <Button className="w-full justify-start" onClick={() => onCreatePurchaseOrder(supplier.id)}>
                  <FileText className="mr-2 h-4 w-4" />
                  Create Purchase Order
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={() => setActiveTab('products')}>
                  <Package className="mr-2 h-4 w-4" />
                  View Products
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={() => onEdit(supplier)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Supplier
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Supplier
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Globe icon component
function Globe(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="2" x2="22" y1="12" y2="12" />
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
    </svg>
  );
}
