'use client';

import * as React from 'react';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', style, ...props }, ref) => {
    const baseStyles: React.CSSProperties = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      whiteSpace: 'nowrap',
      borderRadius: '6px',
      fontSize: '0.875rem',
      fontWeight: '500',
      transition: 'all 0.2s',
      border: 'none',
      cursor: 'pointer',
      textDecoration: 'none',
      ...style,
    };

    const variantStyles: Record<string, React.CSSProperties> = {
      default: {
        backgroundColor: 'hsl(221.2 83.2% 53.3%)', // Blue primary color
        color: 'white',
      },
      destructive: {
        backgroundColor: '#dc2626',
        color: 'white',
      },
      outline: {
        border: '1px solid hsl(214.3 31.8% 91.4%)',
        backgroundColor: 'transparent',
        color: 'hsl(222.2 84% 4.9%)', // Dark text
      },
      secondary: {
        backgroundColor: 'hsl(210 40% 96%)', // Light gray
        color: 'hsl(222.2 84% 4.9%)', // Dark text
      },
      ghost: {
        backgroundColor: 'transparent',
        color: 'hsl(222.2 84% 4.9%)', // Dark text
      },
      link: {
        backgroundColor: 'transparent',
        color: 'hsl(221.2 83.2% 53.3%)', // Blue primary color
        textDecoration: 'underline',
      },
    };

    const sizeStyles: Record<string, React.CSSProperties> = {
      default: {
        height: '2.5rem',
        padding: '0.5rem 1rem',
      },
      sm: {
        height: '2.25rem',
        padding: '0.5rem 0.75rem',
      },
      lg: {
        height: '2.75rem',
        padding: '0.5rem 2rem',
      },
      icon: {
        height: '2.5rem',
        width: '2.5rem',
        padding: '0',
      },
    };

    const combinedStyles = {
      ...baseStyles,
      ...variantStyles[variant],
      ...sizeStyles[size],
      // Ensure text is always visible by setting a fallback color
      color: style?.color || variantStyles[variant].color || 'hsl(222.2 84% 4.9%)',
    };

    return (
      <button
        ref={ref}
        className={className}
        style={combinedStyles}
        onMouseEnter={(e) => {
          if (variant === 'default') {
            e.currentTarget.style.backgroundColor = 'hsl(221.2 83.2% 48%)'; // Darker blue on hover
          } else if (variant === 'secondary') {
            e.currentTarget.style.backgroundColor = 'hsl(210 40% 92%)'; // Darker gray on hover
          } else if (variant === 'outline') {
            e.currentTarget.style.backgroundColor = 'hsl(210 40% 98%)'; // Light background on hover
          } else if (variant === 'ghost') {
            e.currentTarget.style.backgroundColor = 'hsl(210 40% 96%)'; // Light background on hover
          }
        }}
        onMouseLeave={(e) => {
          if (variant === 'default') {
            e.currentTarget.style.backgroundColor = 'hsl(221.2 83.2% 53.3%)'; // Original blue
          } else if (variant === 'secondary') {
            e.currentTarget.style.backgroundColor = 'hsl(210 40% 96%)'; // Original gray
          } else if (variant === 'outline') {
            e.currentTarget.style.backgroundColor = 'transparent'; // Original transparent
          } else if (variant === 'ghost') {
            e.currentTarget.style.backgroundColor = 'transparent'; // Original transparent
          }
        }}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button };
