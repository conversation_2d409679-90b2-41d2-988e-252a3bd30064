'use client';

import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, ExternalLink } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface DatabaseErrorProps {
  message?: string;
}

export function DatabaseError({ message }: DatabaseErrorProps) {
  const router = useRouter();

  const handleRefresh = () => {
    router.refresh();
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-6 text-center">
      <div className="mb-6 text-amber-500">
        <AlertCircle size={64} />
      </div>
      <h2 className="text-2xl font-bold mb-2">Database Connection Error</h2>
      <p className="text-muted-foreground mb-4 max-w-md">
        {message ||
          'The database is currently unavailable. It might be in a paused state. Please try again in a few moments while the database reactivates.'}
      </p>
      <div className="mb-6 text-sm text-muted-foreground max-w-md">
        <p className="mb-2">Connection details:</p>
        <code className="block bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs overflow-auto max-w-full">
          Host: ep-flat-snow-a4nohacn.us-east-1.aws.neon.tech<br />
          Database: neondb<br />
          Schema: bakery
        </code>
      </div>
      <div className="flex flex-col sm:flex-row gap-3">
        <Button onClick={handleRefresh} className="flex items-center gap-2">
          <RefreshCw size={16} />
          Retry Connection
        </Button>
        <Button
          variant="outline"
          onClick={() => window.open('https://console.neon.tech', '_blank')}
          className="flex items-center gap-2"
        >
          <ExternalLink size={16} />
          <span>Open NeonDB Console</span>
        </Button>
      </div>
    </div>
  );
}
