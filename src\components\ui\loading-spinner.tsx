'use client';

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  text?: string;
}

export function LoadingSpinner({ 
  size = 'md', 
  color = '#667eea',
  text = 'Loading...'
}: LoadingSpinnerProps) {
  const sizeMap = {
    sm: '1.5rem',
    md: '2rem',
    lg: '3rem'
  };

  const spinnerSize = sizeMap[size];

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '1rem',
      padding: '2rem'
    }}>
      <div
        style={{
          width: spinnerSize,
          height: spinnerSize,
          border: `3px solid rgba(${color === '#667eea' ? '102, 126, 234' : '0, 0, 0'}, 0.1)`,
          borderTop: `3px solid ${color}`,
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}
      />
      {text && (
        <p style={{
          fontSize: '0.875rem',
          color: '#6b7280',
          fontWeight: '500'
        }}>
          {text}
        </p>
      )}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

// Enhanced Card Component with Animations
interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  hover?: boolean;
}

export function AnimatedCard({ 
  children, 
  className = '', 
  style = {}, 
  onClick,
  hover = true 
}: AnimatedCardProps) {
  const [isHovered, setIsHovered] = React.useState(false);

  const cardStyle: React.CSSProperties = {
    backgroundColor: 'white',
    borderRadius: '12px',
    border: '1px solid #e5e7eb',
    padding: '1.5rem',
    boxShadow: isHovered && hover 
      ? '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      : '0 1px 3px rgba(0, 0, 0, 0.1)',
    transform: isHovered && hover ? 'translateY(-4px)' : 'translateY(0)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    cursor: onClick ? 'pointer' : 'default',
    ...style
  };

  return (
    <div
      className={className}
      style={cardStyle}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
}

// Professional Button Component
interface ProfessionalButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  style?: React.CSSProperties;
}

export function ProfessionalButton({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false,
  loading = false,
  icon,
  style = {}
}: ProfessionalButtonProps) {
  const [isHovered, setIsHovered] = React.useState(false);

  const variants = {
    primary: {
      backgroundColor: isHovered ? '#5a67d8' : '#667eea',
      color: 'white',
      border: 'none'
    },
    secondary: {
      backgroundColor: isHovered ? '#f3f4f6' : '#f9fafb',
      color: '#374151',
      border: '1px solid #e5e7eb'
    },
    outline: {
      backgroundColor: isHovered ? '#f3f4f6' : 'transparent',
      color: '#667eea',
      border: '2px solid #667eea'
    },
    ghost: {
      backgroundColor: isHovered ? '#f3f4f6' : 'transparent',
      color: '#374151',
      border: 'none'
    }
  };

  const sizes = {
    sm: { padding: '0.5rem 0.75rem', fontSize: '0.75rem' },
    md: { padding: '0.75rem 1rem', fontSize: '0.875rem' },
    lg: { padding: '1rem 1.5rem', fontSize: '1rem' }
  };

  const buttonStyle: React.CSSProperties = {
    ...variants[variant],
    ...sizes[size],
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    fontWeight: '500',
    cursor: disabled || loading ? 'not-allowed' : 'pointer',
    opacity: disabled || loading ? 0.6 : 1,
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    transform: isHovered && !disabled && !loading ? 'translateY(-1px)' : 'translateY(0)',
    boxShadow: isHovered && !disabled && !loading 
      ? '0 4px 12px rgba(0, 0, 0, 0.15)' 
      : '0 1px 3px rgba(0, 0, 0, 0.1)',
    ...style
  };

  return (
    <button
      style={buttonStyle}
      onClick={onClick}
      disabled={disabled || loading}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {loading ? (
        <LoadingSpinner size="sm" color={variant === 'primary' ? 'white' : '#667eea'} text="" />
      ) : icon}
      {children}
    </button>
  );
}

// Toast Notification Component
interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  onClose: () => void;
  duration?: number;
}

export function Toast({ message, type = 'info', onClose, duration = 5000 }: ToastProps) {
  React.useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [onClose, duration]);

  const typeStyles = {
    success: { backgroundColor: '#10b981', color: 'white' },
    error: { backgroundColor: '#ef4444', color: 'white' },
    warning: { backgroundColor: '#f59e0b', color: 'white' },
    info: { backgroundColor: '#3b82f6', color: 'white' }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '1rem',
      right: '1rem',
      zIndex: 1000,
      ...typeStyles[type],
      padding: '1rem 1.5rem',
      borderRadius: '8px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      animation: 'slideIn 0.3s ease-out',
      maxWidth: '400px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>{message}</span>
        <button
          onClick={onClose}
          style={{
            marginLeft: '1rem',
            backgroundColor: 'transparent',
            border: 'none',
            color: 'inherit',
            cursor: 'pointer',
            fontSize: '1.25rem'
          }}
        >
          ×
        </button>
      </div>
      <style jsx>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}
