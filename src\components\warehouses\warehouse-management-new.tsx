'use client';

import { useState } from 'react';
import { Search, Plus, Edit, Trash, Warehouse, MapPin, Package, Users, ToggleLeft, ToggleRight } from 'lucide-react';

interface Warehouse {
  id: string;
  name: string;
  code: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  phone?: string;
  email?: string;
  managerName?: string;
  capacity: number;
  currentStock: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface WarehouseManagementProps {
  warehouses: Warehouse[];
  onAddWarehouse: () => void;
  onEditWarehouse: (warehouse: Warehouse) => void;
  onDeleteWarehouse: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  onViewDetails: (id: string) => void;
}

export function WarehouseManagement({
  warehouses,
  onAddWarehouse,
  onEditWarehouse,
  onDeleteWarehouse,
  onToggleStatus,
  onViewDetails
}: WarehouseManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  // Filter warehouses
  const filteredWarehouses = warehouses.filter(warehouse => {
    const matchesSearch = warehouse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         warehouse.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         warehouse.city.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && warehouse.isActive) ||
                         (statusFilter === 'inactive' && !warehouse.isActive);
    
    return matchesSearch && matchesStatus;
  });

  const getCapacityPercentage = (current: number, capacity: number) => {
    return capacity > 0 ? (current / capacity) * 100 : 0;
  };

  const getCapacityColor = (percentage: number) => {
    if (percentage >= 90) return '#ef4444';
    if (percentage >= 70) return '#f59e0b';
    return '#10b981';
  };

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '2.25rem', fontWeight: '800', color: '#0f172a', marginBottom: '0.5rem' }}>
              Warehouses
            </h1>
            <p style={{ color: '#64748b', fontSize: '1.125rem' }}>
              Manage your storage facilities • {filteredWarehouses.length} warehouses
            </p>
          </div>
          <button
            onClick={onAddWarehouse}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: '#8b5cf6',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '0.875rem 1.75rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#7c3aed';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#8b5cf6';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            }}
          >
            <Plus style={{ height: '1.125rem', width: '1.125rem' }} />
            Add Warehouse
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '1.5rem 2rem'
      }}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          {/* Search */}
          <div style={{ position: 'relative', flex: 1, minWidth: '320px' }}>
            <Search style={{
              position: 'absolute',
              left: '14px',
              top: '50%',
              transform: 'translateY(-50%)',
              height: '1.25rem',
              width: '1.25rem',
              color: '#64748b'
            }} />
            <input
              type="text"
              placeholder="Search warehouses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                paddingLeft: '3rem',
                paddingRight: '1rem',
                paddingTop: '0.875rem',
                paddingBottom: '0.875rem',
                borderRadius: '10px',
                border: '1px solid #d1d5db',
                fontSize: '0.875rem',
                transition: 'all 0.2s',
                backgroundColor: '#f8fafc'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#8b5cf6';
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(139, 92, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.backgroundColor = '#f8fafc';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            style={{
              padding: '0.625rem 1rem',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      {/* Warehouses Grid */}
      <div style={{ padding: '2rem' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(380px, 1fr))',
          gap: '1.5rem'
        }}>
          {filteredWarehouses.map(warehouse => {
            const capacityPercentage = getCapacityPercentage(warehouse.currentStock, warehouse.capacity);
            const capacityColor = getCapacityColor(capacityPercentage);

            return (
              <div
                key={warehouse.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  border: '1px solid #e2e8f0',
                  overflow: 'hidden',
                  transition: 'all 0.2s',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-4px)';
                  e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
                onClick={() => onViewDetails(warehouse.id)}
              >
                {/* Header */}
                <div style={{
                  backgroundColor: '#f8fafc',
                  padding: '1.5rem',
                  borderBottom: '1px solid #e2e8f0',
                  position: 'relative'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <div style={{
                      backgroundColor: '#8b5cf6',
                      borderRadius: '10px',
                      padding: '0.75rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <Warehouse style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <h3 style={{
                        fontSize: '1.125rem',
                        fontWeight: '600',
                        color: '#0f172a',
                        marginBottom: '0.25rem'
                      }}>
                        {warehouse.name}
                      </h3>
                      <p style={{
                        color: '#64748b',
                        fontSize: '0.875rem'
                      }}>
                        Code: {warehouse.code}
                      </p>
                    </div>
                    {/* Status Toggle */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleStatus(warehouse.id, !warehouse.isActive);
                      }}
                      style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                      }}
                    >
                      {warehouse.isActive ? (
                        <ToggleRight style={{ height: '1.25rem', width: '1.25rem', color: '#10b981' }} />
                      ) : (
                        <ToggleLeft style={{ height: '1.25rem', width: '1.25rem', color: '#ef4444' }} />
                      )}
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div style={{ padding: '1.5rem' }}>
                  {/* Location */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '0.75rem',
                    marginBottom: '1rem'
                  }}>
                    <MapPin style={{ height: '1rem', width: '1rem', color: '#64748b', marginTop: '0.125rem' }} />
                    <div>
                      <p style={{ fontSize: '0.875rem', color: '#374151', marginBottom: '0.25rem' }}>
                        {warehouse.address}
                      </p>
                      <p style={{ fontSize: '0.875rem', color: '#64748b' }}>
                        {warehouse.city}, {warehouse.state} - {warehouse.pincode}
                      </p>
                    </div>
                  </div>

                  {/* Manager */}
                  {warehouse.managerName && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.75rem',
                      marginBottom: '1rem'
                    }}>
                      <Users style={{ height: '1rem', width: '1rem', color: '#64748b' }} />
                      <p style={{ fontSize: '0.875rem', color: '#374151' }}>
                        Manager: {warehouse.managerName}
                      </p>
                    </div>
                  )}

                  {/* Capacity */}
                  <div style={{ marginBottom: '1rem' }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      <span style={{ fontSize: '0.875rem', color: '#374151', fontWeight: '500' }}>
                        Capacity Utilization
                      </span>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>
                        {warehouse.currentStock} / {warehouse.capacity}
                      </span>
                    </div>
                    <div style={{
                      width: '100%',
                      height: '8px',
                      backgroundColor: '#f1f5f9',
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${Math.min(capacityPercentage, 100)}%`,
                        height: '100%',
                        backgroundColor: capacityColor,
                        transition: 'all 0.3s'
                      }} />
                    </div>
                    <p style={{
                      fontSize: '0.75rem',
                      color: capacityColor,
                      fontWeight: '500',
                      marginTop: '0.25rem'
                    }}>
                      {capacityPercentage.toFixed(1)}% utilized
                    </p>
                  </div>

                  {/* Status */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '1rem'
                  }}>
                    <span style={{
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: warehouse.isActive ? '#10b981' : '#ef4444',
                      backgroundColor: warehouse.isActive ? '#dcfce7' : '#fee2e2',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px'
                    }}>
                      {warehouse.isActive ? 'Active' : 'Inactive'}
                    </span>
                    {warehouse.phone && (
                      <span style={{ fontSize: '0.75rem', color: '#64748b' }}>
                        {warehouse.phone}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div style={{
                    display: 'flex',
                    gap: '0.5rem'
                  }}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditWarehouse(warehouse);
                      }}
                      style={{
                        flex: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem',
                        backgroundColor: '#8b5cf6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#7c3aed';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#8b5cf6';
                      }}
                    >
                      <Edit style={{ height: '0.875rem', width: '0.875rem' }} />
                      Edit
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (window.confirm(`Are you sure you want to delete "${warehouse.name}"?`)) {
                          onDeleteWarehouse(warehouse.id);
                        }
                      }}
                      style={{
                        padding: '0.5rem',
                        backgroundColor: '#ef4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#dc2626';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#ef4444';
                      }}
                    >
                      <Trash style={{ height: '0.875rem', width: '0.875rem' }} />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredWarehouses.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '4rem 2rem',
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e2e8f0'
          }}>
            <Warehouse style={{ height: '4rem', width: '4rem', color: '#94a3b8', margin: '0 auto 1rem' }} />
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
              No warehouses found
            </h3>
            <p style={{ color: '#64748b', marginBottom: '1.5rem' }}>
              Try adjusting your search or create your first warehouse
            </p>
            <button
              onClick={onAddWarehouse}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: '#8b5cf6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#7c3aed';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#8b5cf6';
              }}
            >
              <Plus style={{ height: '1rem', width: '1rem' }} />
              Add Your First Warehouse
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
