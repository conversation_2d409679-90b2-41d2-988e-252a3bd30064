'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to check if we're on the client side
 * Prevents hydration mismatches by ensuring consistent rendering
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to get window dimensions safely
 * Returns default values on server, actual values on client
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: 1024, // Default desktop width
    height: 768, // Default desktop height
  });

  const isClient = useIsClient();

  useEffect(() => {
    if (!isClient) return;

    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // Set initial size
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isClient]);

  return windowSize;
}

/**
 * Hook to check if screen is mobile size
 * Prevents hydration mismatches by using consistent initial state
 */
export function useIsMobile() {
  const { width } = useWindowSize();
  const isClient = useIsClient();
  
  // Return false on server to prevent hydration mismatch
  // Will update to correct value after hydration
  return isClient ? width < 768 : false;
}
