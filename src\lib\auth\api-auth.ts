import { NextRequest } from 'next/server';
import { UserRole, getUserPermissions, UserWithPermissions } from '@/lib/permissions/permissions';
import { userStorage, storeStorage } from '@/lib/storage/user-storage';

// Simple authentication for API routes
export async function getAuthenticatedUser(request: NextRequest): Promise<UserWithPermissions | null> {
  try {
    // For now, we'll use a simple approach with headers
    // In production, you should use proper JWT tokens or session validation

    // Try to get user email from headers (set by frontend)
    const userEmail = request.headers.get('x-user-email');

    if (!userEmail) {
      // Fallback: try to get from hardcoded admin for demo
      // This is temporary - in production, use proper session/JWT validation
      const adminUser = userStorage.getUserByEmail('<EMAIL>');
      if (adminUser) {
        const store = adminUser.storeId ? storeStorage.getStoreById(adminUser.storeId) : null;
        return {
          id: adminUser.id,
          name: adminUser.name,
          email: adminUser.email,
          role: adminUser.role as User<PERSON>ole,
          storeId: adminUser.storeId,
          store: store ? { name: store.name } : null,
          permissions: await getCustomUserPermissions(adminUser.email)
        };
      }
      return null;
    }

    // Get user from storage
    const user = userStorage.getUserByEmail(userEmail);
    if (!user) {
      return null;
    }

    // Get custom permissions for the user
    const customPermissions = await getCustomUserPermissions(userEmail);

    // Get store information
    const store = user.storeId ? storeStorage.getStoreById(user.storeId) : null;

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role as UserRole,
      storeId: user.storeId,
      store: store ? { name: store.name } : null,
      permissions: customPermissions
    };
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}

// Get custom permissions for a user from storage
async function getCustomUserPermissions(userEmail: string): Promise<any[]> {
  try {
    // In production, fetch from database:
    // const permissions = await prisma.userPermission.findMany({
    //   where: { userEmail },
    //   select: { permission: true }
    // });
    // return permissions.map(p => p.permission);

    // For now, use in-memory storage (same as in permissions API)
    const userPermissions: Record<string, string[]> = {
      '<EMAIL>': [
        'dashboard.view', 'products.view', 'products.create', 'products.edit', 'products.delete',
        'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
        'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.view_all_stores',
        'customers.view', 'customers.create', 'customers.edit', 'customers.delete', 'customers.view_all_stores',
        'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles',
        'stores.view', 'stores.create', 'stores.edit', 'stores.delete', 'stores.manage_access',
        'inventory.view', 'inventory.edit', 'inventory.transfer', 'inventory.view_all_stores',
        'raw_materials.view', 'raw_materials.create', 'raw_materials.edit', 'raw_materials.delete',
        'recipes.view', 'recipes.create', 'recipes.edit', 'recipes.delete',
        'production.view', 'production.create', 'production.edit', 'production.delete',
        'sales.view', 'sales.create', 'sales.process',
        'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
        'expenses.view', 'expenses.create', 'expenses.edit', 'expenses.delete',
        'wastage.view', 'wastage.create', 'wastage.edit', 'wastage.delete',
        'reports.view', 'reports.sales', 'reports.inventory', 'reports.financial', 'reports.production', 'reports.all_stores',
        'settings.view', 'settings.edit', 'settings.system'
      ]
    };

    return userPermissions[userEmail] || [];
  } catch (error) {
    console.error('Error getting custom permissions:', error);
    return getUserPermissions('STAFF'); // Fallback to minimal permissions
  }
}

// Check if user has permission
export function hasPermission(user: UserWithPermissions | null, permission: string): boolean {
  if (!user) return false;
  return user.permissions.includes(permission as any);
}

// Check if user can access store data
export function canAccessStore(user: UserWithPermissions | null, storeId: string): boolean {
  if (!user) return false;

  // Admins and warehouse managers can access all stores
  if (user.role === 'ADMIN' || user.role === 'WAREHOUSE_MANAGER') {
    return true;
  }

  // Store managers and staff can only access their assigned store
  return user.storeId === storeId;
}

// Filter data based on user's store access
export function filterByStoreAccess<T extends { storeId?: string | null }>(
  data: T[],
  user: UserWithPermissions
): T[] {
  // Admins and warehouse managers can see all data
  if (user.role === 'ADMIN' || user.role === 'WAREHOUSE_MANAGER') {
    return data;
  }

  // Store managers and staff can only see data from their store
  return data.filter(item => {
    if (!item.storeId) return true; // Global data
    return item.storeId === user.storeId;
  });
}

// API response helper with permission check
export function createProtectedResponse(
  user: UserWithPermissions | null,
  requiredPermission: string,
  data?: any,
  status: number = 200
) {
  if (!user) {
    return new Response(
      JSON.stringify({ error: 'Authentication required' }),
      { status: 401, headers: { 'Content-Type': 'application/json' } }
    );
  }

  if (!hasPermission(user, requiredPermission)) {
    return new Response(
      JSON.stringify({ error: 'Insufficient permissions' }),
      { status: 403, headers: { 'Content-Type': 'application/json' } }
    );
  }

  if (data !== undefined) {
    return new Response(
      JSON.stringify(data),
      { status, headers: { 'Content-Type': 'application/json' } }
    );
  }

  return new Response(null, { status });
}
