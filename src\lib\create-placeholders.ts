import fs from 'fs';
import path from 'path';

// Create SVG placeholder images for different categories
const createSVGPlaceholder = (text: string, color: string = '#e5e7eb'): string => {
  return `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
    <rect width="400" height="300" fill="${color}"/>
    <text x="200" y="150" font-family="Arial, sans-serif" font-size="24" fill="#6b7280" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`;
};

// Create placeholder images for different categories
export function createPlaceholderImages() {
  const publicDir = path.join(process.cwd(), 'public');
  
  const placeholders = [
    { name: 'placeholder-products.jpg', text: 'Product Image', color: '#fef3c7' },
    { name: 'placeholder-categories.jpg', text: 'Category Image', color: '#dbeafe' },
    { name: 'placeholder-uploads.jpg', text: 'Upload Image', color: '#e5e7eb' },
    { name: 'placeholder-recipes.jpg', text: 'Recipe Image', color: '#fecaca' },
    { name: 'placeholder-inventory.jpg', text: 'Inventory Image', color: '#d1fae5' },
  ];

  placeholders.forEach(({ name, text, color }) => {
    const svgContent = createSVGPlaceholder(text, color);
    const filePath = path.join(publicDir, name);
    
    try {
      fs.writeFileSync(filePath, svgContent);
      console.log(`Created placeholder: ${name}`);
    } catch (error) {
      console.error(`Error creating placeholder ${name}:`, error);
    }
  });
}

// Create sample bakery images
export function createSampleImages() {
  const publicDir = path.join(process.cwd(), 'public');
  const productsDir = path.join(publicDir, 'products');
  const categoriesDir = path.join(publicDir, 'categories');

  // Ensure directories exist
  [productsDir, categoriesDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  // Sample product images
  const productImages = [
    { name: 'chocolate-cake.svg', text: '🍰 Chocolate Cake', color: '#8b4513' },
    { name: 'red-roses.svg', text: '🌹 Red Roses', color: '#dc2626' },
    { name: 'birthday-gift.svg', text: '🎁 Birthday Gift', color: '#7c3aed' },
    { name: 'vanilla-cake.svg', text: '🧁 Vanilla Cake', color: '#fbbf24' },
    { name: 'flower-bouquet.svg', text: '💐 Flower Bouquet', color: '#ec4899' },
    { name: 'anniversary-gift.svg', text: '💝 Anniversary Gift', color: '#ef4444' },
  ];

  // Sample category images
  const categoryImages = [
    { name: 'cakes.svg', text: '🍰 Cakes', color: '#f59e0b' },
    { name: 'flowers.svg', text: '🌸 Flowers', color: '#ec4899' },
    { name: 'gifts.svg', text: '🎁 Gifts', color: '#8b5cf6' },
    { name: 'plants.svg', text: '🌱 Plants', color: '#10b981' },
    { name: 'combos.svg', text: '🎉 Combos', color: '#f59e0b' },
    { name: 'occasions.svg', text: '🎊 Occasions', color: '#ef4444' },
  ];

  // Create product images
  productImages.forEach(({ name, text, color }) => {
    const svgContent = createSVGPlaceholder(text, color);
    const filePath = path.join(productsDir, name);
    
    try {
      fs.writeFileSync(filePath, svgContent);
      console.log(`Created product image: ${name}`);
    } catch (error) {
      console.error(`Error creating product image ${name}:`, error);
    }
  });

  // Create category images
  categoryImages.forEach(({ name, text, color }) => {
    const svgContent = createSVGPlaceholder(text, color);
    const filePath = path.join(categoriesDir, name);
    
    try {
      fs.writeFileSync(filePath, svgContent);
      console.log(`Created category image: ${name}`);
    } catch (error) {
      console.error(`Error creating category image ${name}:`, error);
    }
  });
}

// Function to run all placeholder creation
export function initializePlaceholders() {
  console.log('Creating placeholder images...');
  createPlaceholderImages();
  createSampleImages();
  console.log('Placeholder images created successfully!');
}
