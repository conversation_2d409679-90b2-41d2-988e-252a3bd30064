import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

/**
 * Tests the database connection and handles connection errors gracefully
 * @returns A NextResponse object if there's a connection error, or null if the connection is successful
 */
export async function testDatabaseConnection() {
  try {
    // Simple query to test connection
    await prisma.$queryRaw`SELECT 1`;
    return null; // Connection successful
  } catch (dbError: any) {
    console.error('Database connection error:', dbError);
    
    // Check if it's a connection error (NeonDB might be paused)
    if (dbError.message && dbError.message.includes("Can't reach database server")) {
      return NextResponse.json(
        { 
          error: 'Database connection error', 
          message: 'The database is currently unavailable. It might be in a paused state. Please try again in a few moments while the database reactivates.',
          status: 'DB_UNAVAILABLE'
        },
        { status: 503 } // Service Unavailable
      );
    }
    
    throw dbError; // Re-throw if it's not a connection error
  }
}
