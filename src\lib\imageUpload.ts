import fs from 'fs';
import path from 'path';

/**
 * Uploads an image to the public folder
 * @param file The file to upload
 * @param folder The subfolder within public to store the image (e.g., 'products', 'categories')
 * @returns The URL path to the uploaded image
 */
export async function uploadImageToPublic(
  base64Data: string,
  folder: string = 'uploads'
): Promise<string> {
  try {
    console.log(`Starting image upload to folder: ${folder}`);
    console.log(`Base64 data length: ${base64Data.length}`);

    // Extract the file data and extension from the base64 string
    const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      console.error('Invalid image data format. Expected: data:image/[type];base64,[data]');
      console.error('Received format:', base64Data.substring(0, 50) + '...');
      throw new Error('Invalid image data format');
    }

    const extension = matches[1];
    const fileData = matches[2];

    console.log(`Image type: ${extension}`);
    console.log(`File data length: ${fileData.length}`);

    const buffer = Buffer.from(fileData, 'base64');
    console.log(`Buffer size: ${buffer.length} bytes`);

    // Validate buffer size
    if (buffer.length === 0) {
      throw new Error('Empty image buffer');
    }

    // Create folder if it doesn't exist
    const publicDir = path.join(process.cwd(), 'public');
    const uploadDir = path.join(publicDir, folder);

    console.log(`Public directory: ${publicDir}`);
    console.log(`Upload directory: ${uploadDir}`);

    // Generate a unique filename
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${extension}`;
    console.log(`Generated filename: ${fileName}`);

    // Ensure directory exists
    if (!fs.existsSync(uploadDir)) {
      console.log(`Creating directory: ${uploadDir}`);
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log(`Directory created successfully`);
    } else {
      console.log(`Directory already exists: ${uploadDir}`);
    }

    const filePath = path.join(uploadDir, fileName);
    console.log(`Full file path: ${filePath}`);

    // Write the file
    fs.writeFileSync(filePath, buffer);

    // Verify file was written
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`File successfully saved. Size: ${stats.size} bytes`);

      if (stats.size === 0) {
        throw new Error('File was saved but is empty');
      }
    } else {
      throw new Error('File was not saved successfully');
    }

    // Return the URL path (relative to public folder)
    const imageUrl = `/${folder}/${fileName}`;
    console.log(`Returning image URL: ${imageUrl}`);
    return imageUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error; // Re-throw the error instead of returning placeholder
  }
}

/**
 * Deletes an image from the public folder
 * @param imagePath The path of the image to delete (e.g., '/uploads/image.jpg')
 */
export function deleteImageFromPublic(imagePath: string): void {
  try {
    if (!imagePath || imagePath.startsWith('http')) {
      return; // Don't delete external URLs
    }

    // Remove leading slash if present
    const relativePath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
    const filePath = path.join(process.cwd(), 'public', relativePath);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Error deleting image:', error);
  }
}
