import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { Permission, UserRole, hasPermission, canAccessStore, getUserPermissions, UserWithPermissions } from './permissions';

// Extract user from request (you'll need to implement based on your auth system)
export async function getUserFromRequest(request: NextRequest): Promise<UserWithPermissions | null> {
  try {
    // Get user ID from session/token (implement based on your auth system)
    const authHeader = request.headers.get('authorization');
    const sessionCookie = request.cookies.get('session')?.value;
    
    // For now, we'll use a simple approach - you should implement proper JWT/session validation
    if (!authHeader && !sessionCookie) {
      return null;
    }

    // This is a simplified example - implement proper user extraction
    // You might need to decode JW<PERSON> token or validate session
    
    // For demo purposes, let's assume we can extract user email from headers
    const userEmail = request.headers.get('x-user-email') || '<EMAIL>';
    
    // Fetch user from database
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        storeId: true,
        store: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      return null;
    }

    return {
      ...user,
      role: user.role as UserRole,
      permissions: getUserPermissions(user.role as UserRole),
    };
  } catch (error) {
    console.error('Error extracting user from request:', error);
    return null;
  }
}

// Middleware to check permissions for API routes
export function withPermission(permission: Permission) {
  return function (handler: (request: NextRequest, user: UserWithPermissions) => Promise<NextResponse>) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        const user = await getUserFromRequest(request);

        if (!user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        if (!hasPermission(user, permission)) {
          return NextResponse.json(
            { error: 'Insufficient permissions' },
            { status: 403 }
          );
        }

        return handler(request, user);
      } catch (error) {
        console.error('Permission middleware error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

// Middleware to check role-based access
export function withRole(allowedRoles: UserRole[]) {
  return function (handler: (request: NextRequest, user: UserWithPermissions) => Promise<NextResponse>) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        const user = await getUserFromRequest(request);

        if (!user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        if (!allowedRoles.includes(user.role)) {
          return NextResponse.json(
            { error: 'Insufficient role privileges' },
            { status: 403 }
          );
        }

        return handler(request, user);
      } catch (error) {
        console.error('Role middleware error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

// Middleware to check store access
export function withStoreAccess() {
  return function (handler: (request: NextRequest, user: UserWithPermissions) => Promise<NextResponse>) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        const user = await getUserFromRequest(request);

        if (!user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        // Extract store ID from request (URL params, body, etc.)
        const url = new URL(request.url);
        const storeId = url.searchParams.get('storeId') || 
                       url.pathname.split('/').find(segment => segment.startsWith('store-'));

        if (storeId && !canAccessStore(user, storeId)) {
          return NextResponse.json(
            { error: 'Access denied to this store' },
            { status: 403 }
          );
        }

        return handler(request, user);
      } catch (error) {
        console.error('Store access middleware error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

// Filter data based on user's store access
export function filterByStoreAccess<T extends { storeId?: string | null }>(
  data: T[],
  user: UserWithPermissions
): T[] {
  // Admins and warehouse managers can see all data
  if (user.role === 'ADMIN' || user.role === 'WAREHOUSE_MANAGER') {
    return data;
  }

  // Store managers and staff can only see data from their store
  return data.filter(item => {
    if (!item.storeId) return true; // Global data
    return item.storeId === user.storeId;
  });
}

// Check if user can perform action on specific resource
export function canPerformAction(
  user: UserWithPermissions,
  action: Permission,
  resourceStoreId?: string | null
): boolean {
  // Check basic permission
  if (!hasPermission(user, action)) {
    return false;
  }

  // If resource is store-specific, check store access
  if (resourceStoreId && !canAccessStore(user, resourceStoreId)) {
    return false;
  }

  return true;
}

// Validate request data based on user permissions
export function validateRequestData(
  user: UserWithPermissions,
  data: any,
  requiredPermissions: Permission[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if user has required permissions
  for (const permission of requiredPermissions) {
    if (!hasPermission(user, permission)) {
      errors.push(`Missing permission: ${permission}`);
    }
  }

  // Additional validation based on user role
  if (user.role !== 'ADMIN') {
    // Non-admin users cannot modify certain fields
    const restrictedFields = ['role', 'permissions', 'systemSettings'];
    
    for (const field of restrictedFields) {
      if (data[field] !== undefined) {
        errors.push(`Cannot modify restricted field: ${field}`);
      }
    }
  }

  // Store-specific validation
  if (data.storeId && !canAccessStore(user, data.storeId)) {
    errors.push('Cannot access specified store');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
