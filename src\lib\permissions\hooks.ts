import { useAuth } from '@/lib/auth/auth-context';
import { 
  Permission, 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  canAccessStore,
  canViewAllStores,
  getAccessibleNavigation,
  UserWithPermissions,
  getUserPermissions
} from './permissions';

// Hook to check if user has specific permission
export function usePermission(permission: Permission): boolean {
  const { user } = useAuth();
  
  if (!user) return false;
  
  const userWithPermissions: UserWithPermissions = {
    ...user,
    permissions: getUserPermissions(user.role)
  };
  
  return hasPermission(userWithPermissions, permission);
}

// Hook to check if user has any of the specified permissions
export function useAnyPermission(permissions: Permission[]): boolean {
  const { user } = useAuth();
  
  if (!user) return false;
  
  const userWithPermissions: UserWithPermissions = {
    ...user,
    permissions: getUserPermissions(user.role)
  };
  
  return hasAnyPermission(userWithPermissions, permissions);
}

// Hook to check if user has all of the specified permissions
export function useAllPermissions(permissions: Permission[]): boolean {
  const { user } = useAuth();
  
  if (!user) return false;
  
  const userWithPermissions: UserWithPermissions = {
    ...user,
    permissions: getUserPermissions(user.role)
  };
  
  return hasAllPermissions(userWithPermissions, permissions);
}

// Hook to check if user can access specific store
export function useStoreAccess(storeId: string): boolean {
  const { user } = useAuth();
  
  if (!user) return false;
  
  const userWithPermissions: UserWithPermissions = {
    ...user,
    permissions: getUserPermissions(user.role)
  };
  
  return canAccessStore(userWithPermissions, storeId);
}

// Hook to check if user can view all stores
export function useCanViewAllStores(): boolean {
  const { user } = useAuth();
  
  if (!user) return false;
  
  const userWithPermissions: UserWithPermissions = {
    ...user,
    permissions: getUserPermissions(user.role)
  };
  
  return canViewAllStores(userWithPermissions);
}

// Hook to get accessible navigation items
export function useAccessibleNavigation() {
  const { user } = useAuth();
  
  if (!user) return [];
  
  const userWithPermissions: UserWithPermissions = {
    ...user,
    permissions: getUserPermissions(user.role)
  };
  
  return getAccessibleNavigation(userWithPermissions);
}

// Hook to get user with permissions
export function useUserWithPermissions(): UserWithPermissions | null {
  const { user } = useAuth();
  
  if (!user) return null;
  
  return {
    ...user,
    permissions: getUserPermissions(user.role)
  };
}
