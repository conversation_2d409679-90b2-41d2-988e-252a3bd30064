// Role-Based Access Control (RBAC) System
export type UserRole = 'ADMIN' | 'WAREHOUSE_MANAGER' | 'STORE_MANAGER' | 'STAFF';

export type Permission = 
  // Dashboard permissions
  | 'dashboard.view'
  
  // Product permissions
  | 'products.view'
  | 'products.create'
  | 'products.edit'
  | 'products.delete'
  
  // Category permissions
  | 'categories.view'
  | 'categories.create'
  | 'categories.edit'
  | 'categories.delete'
  
  // Order permissions
  | 'orders.view'
  | 'orders.create'
  | 'orders.edit'
  | 'orders.delete'
  | 'orders.view_all_stores'
  
  // Customer permissions
  | 'customers.view'
  | 'customers.create'
  | 'customers.edit'
  | 'customers.delete'
  | 'customers.view_all_stores'
  
  // User management permissions
  | 'users.view'
  | 'users.create'
  | 'users.edit'
  | 'users.delete'
  | 'users.manage_roles'
  
  // Store permissions
  | 'stores.view'
  | 'stores.create'
  | 'stores.edit'
  | 'stores.delete'
  | 'stores.manage_access'
  
  // Inventory permissions
  | 'inventory.view'
  | 'inventory.edit'
  | 'inventory.transfer'
  | 'inventory.view_all_stores'
  
  // Raw materials permissions
  | 'raw_materials.view'
  | 'raw_materials.create'
  | 'raw_materials.edit'
  | 'raw_materials.delete'
  
  // Recipe permissions
  | 'recipes.view'
  | 'recipes.create'
  | 'recipes.edit'
  | 'recipes.delete'
  
  // Production permissions
  | 'production.view'
  | 'production.create'
  | 'production.edit'
  | 'production.delete'
  
  // Sales/POS permissions
  | 'sales.view'
  | 'sales.create'
  | 'sales.process'
  
  // Purchase permissions
  | 'purchases.view'
  | 'purchases.create'
  | 'purchases.edit'
  | 'purchases.delete'
  
  // Expense permissions
  | 'expenses.view'
  | 'expenses.create'
  | 'expenses.edit'
  | 'expenses.delete'
  
  // Wastage permissions
  | 'wastage.view'
  | 'wastage.create'
  | 'wastage.edit'
  | 'wastage.delete'
  
  // Report permissions
  | 'reports.view'
  | 'reports.sales'
  | 'reports.inventory'
  | 'reports.financial'
  | 'reports.production'
  | 'reports.all_stores'
  
  // Settings permissions
  | 'settings.view'
  | 'settings.edit'
  | 'settings.system';

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  ADMIN: [
    // Full access to everything
    'dashboard.view',
    'products.view', 'products.create', 'products.edit', 'products.delete',
    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
    'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.view_all_stores',
    'customers.view', 'customers.create', 'customers.edit', 'customers.delete', 'customers.view_all_stores',
    'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles',
    'stores.view', 'stores.create', 'stores.edit', 'stores.delete', 'stores.manage_access',
    'inventory.view', 'inventory.edit', 'inventory.transfer', 'inventory.view_all_stores',
    'raw_materials.view', 'raw_materials.create', 'raw_materials.edit', 'raw_materials.delete',
    'recipes.view', 'recipes.create', 'recipes.edit', 'recipes.delete',
    'production.view', 'production.create', 'production.edit', 'production.delete',
    'sales.view', 'sales.create', 'sales.process',
    'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
    'expenses.view', 'expenses.create', 'expenses.edit', 'expenses.delete',
    'wastage.view', 'wastage.create', 'wastage.edit', 'wastage.delete',
    'reports.view', 'reports.sales', 'reports.inventory', 'reports.financial', 'reports.production', 'reports.all_stores',
    'settings.view', 'settings.edit', 'settings.system'
  ],
  
  WAREHOUSE_MANAGER: [
    'dashboard.view',
    'products.view', 'products.create', 'products.edit',
    'categories.view', 'categories.create', 'categories.edit',
    'orders.view', 'orders.edit',
    'inventory.view', 'inventory.edit', 'inventory.transfer', 'inventory.view_all_stores',
    'raw_materials.view', 'raw_materials.create', 'raw_materials.edit', 'raw_materials.delete',
    'recipes.view', 'recipes.create', 'recipes.edit',
    'production.view', 'production.create', 'production.edit', 'production.delete',
    'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
    'wastage.view', 'wastage.create', 'wastage.edit',
    'reports.view', 'reports.inventory', 'reports.production', 'reports.all_stores'
  ],
  
  STORE_MANAGER: [
    'dashboard.view',
    'products.view', 'products.edit',
    'categories.view',
    'orders.view', 'orders.create', 'orders.edit', 'orders.delete',
    'customers.view', 'customers.create', 'customers.edit', 'customers.delete',
    'inventory.view', 'inventory.edit',
    'sales.view', 'sales.create', 'sales.process',
    'expenses.view', 'expenses.create', 'expenses.edit',
    'wastage.view', 'wastage.create', 'wastage.edit',
    'reports.view', 'reports.sales', 'reports.inventory'
  ],
  
  STAFF: [
    'dashboard.view',
    'products.view',
    'categories.view',
    'orders.view', 'orders.create', 'orders.edit',
    'customers.view', 'customers.create', 'customers.edit',
    'sales.view', 'sales.create', 'sales.process'
  ]
};

// Navigation items with required permissions
export const NAVIGATION_ITEMS = [
  { path: '/dashboard', label: 'Dashboard', permission: 'dashboard.view' as Permission },
  { path: '/dashboard/products', label: 'Products', permission: 'products.view' as Permission },
  { path: '/dashboard/categories', label: 'Categories', permission: 'categories.view' as Permission },
  { path: '/dashboard/orders', label: 'Orders', permission: 'orders.view' as Permission },
  { path: '/dashboard/customers', label: 'Customers', permission: 'customers.view' as Permission },
  { path: '/dashboard/users', label: 'Users', permission: 'users.view' as Permission },
  { path: '/dashboard/stores', label: 'Stores', permission: 'stores.view' as Permission },
  { path: '/dashboard/warehouses', label: 'Warehouses', permission: 'stores.view' as Permission },
  { path: '/dashboard/inventory', label: 'Inventory', permission: 'inventory.view' as Permission },
  { path: '/dashboard/raw-materials', label: 'Raw Materials', permission: 'raw_materials.view' as Permission },
  { path: '/dashboard/recipes', label: 'Recipes', permission: 'recipes.view' as Permission },
  { path: '/dashboard/production', label: 'Production', permission: 'production.view' as Permission },
  { path: '/dashboard/sales', label: 'Sales', permission: 'sales.view' as Permission },
  { path: '/dashboard/purchases', label: 'Purchases', permission: 'purchases.view' as Permission },
  { path: '/dashboard/expenses', label: 'Expenses', permission: 'expenses.view' as Permission },
  { path: '/dashboard/wastage', label: 'Wastage', permission: 'wastage.view' as Permission },
  { path: '/dashboard/reports', label: 'Reports', permission: 'reports.view' as Permission },
  { path: '/dashboard/settings', label: 'Settings', permission: 'settings.view' as Permission },
];

// User type with permissions
export type UserWithPermissions = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  storeId?: string | null;
  store?: {
    name: string;
  };
  permissions: Permission[];
};

// Check if user has specific permission
export function hasPermission(user: UserWithPermissions | null, permission: Permission): boolean {
  if (!user) return false;
  return user.permissions.includes(permission);
}

// Check if user has any of the specified permissions
export function hasAnyPermission(user: UserWithPermissions | null, permissions: Permission[]): boolean {
  if (!user) return false;
  return permissions.some(permission => user.permissions.includes(permission));
}

// Check if user has all of the specified permissions
export function hasAllPermissions(user: UserWithPermissions | null, permissions: Permission[]): boolean {
  if (!user) return false;
  return permissions.every(permission => user.permissions.includes(permission));
}

// Get user permissions based on role
export function getUserPermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

// Check if user can access specific store data
export function canAccessStore(user: UserWithPermissions | null, storeId: string): boolean {
  if (!user) return false;
  
  // Admins can access all stores
  if (user.role === 'ADMIN') return true;
  
  // Warehouse managers can access all stores
  if (user.role === 'WAREHOUSE_MANAGER') return true;
  
  // Store managers and staff can only access their assigned store
  return user.storeId === storeId;
}

// Check if user can view all stores data
export function canViewAllStores(user: UserWithPermissions | null): boolean {
  if (!user) return false;
  return user.role === 'ADMIN' || user.role === 'WAREHOUSE_MANAGER';
}

// Filter navigation items based on user permissions
export function getAccessibleNavigation(user: UserWithPermissions | null) {
  if (!user) return [];
  
  return NAVIGATION_ITEMS.filter(item => hasPermission(user, item.permission));
}
