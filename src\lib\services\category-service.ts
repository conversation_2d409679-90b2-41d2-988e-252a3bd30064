import { prisma } from '@/lib/db';
import { ProductService } from './product-service';

export interface CategoryData {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  productCount: number;
  isActive: boolean;
  slug?: string;
  displayOrder?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CategoryProductInfo {
  productContains: Array<{ label: string; value: string; placeholder?: string }>;
  careInstructions: string[];
  badges: string[];
  showNameField: boolean;
  weightLabel: string;
  specifications: Array<{ label: string; placeholder: string; required: boolean }>;
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  imageUrl?: string;
  slug?: string;
  displayOrder?: number;
  isActive?: boolean;
  productInfo?: CategoryProductInfo;
}

export class CategoryService {
  /**
   * Get all categories with product counts and metadata
   */
  static async getAllCategories(): Promise<CategoryData[]> {
    try {
      // Get unique categories from products
      const productCategories = await prisma.product.groupBy({
        by: ['category'],
        _count: {
          id: true
        },
        orderBy: {
          category: 'asc'
        }
      });

      // Get category metadata from system settings
      const categoryMetadata = await this.getCategoryMetadata();

      // Combine product categories with metadata
      const categoryData: CategoryData[] = productCategories.map(cat => {
        const metadata = categoryMetadata[cat.category] || {};
        return {
          id: cat.category,
          name: cat.category,
          description: metadata.description || `Products in ${cat.category} category`,
          imageUrl: metadata.imageUrl || this.getCategoryImageUrl(cat.category),
          productCount: cat._count.id,
          isActive: metadata.isActive !== undefined ? metadata.isActive : true,
          slug: metadata.slug || cat.category.toLowerCase().replace(/\s+/g, '-'),
          displayOrder: metadata.displayOrder || 0,
          createdAt: metadata.createdAt || new Date().toISOString(),
          updatedAt: metadata.updatedAt || new Date().toISOString(),
        };
      });

      // Add categories that exist in metadata but don't have products yet
      const metadataOnlyCategories = Object.keys(categoryMetadata).filter(
        catName => !productCategories.some(pc => pc.category === catName)
      );

      metadataOnlyCategories.forEach(catName => {
        const metadata = categoryMetadata[catName];
        if (metadata.isActive !== false) { // Only show active categories
          categoryData.push({
            id: catName,
            name: catName,
            description: metadata.description || `Products in ${catName} category`,
            imageUrl: metadata.imageUrl || this.getCategoryImageUrl(catName),
            productCount: 0,
            isActive: metadata.isActive !== undefined ? metadata.isActive : true,
            slug: metadata.slug || catName.toLowerCase().replace(/\s+/g, '-'),
            displayOrder: metadata.displayOrder || 0,
            createdAt: metadata.createdAt || new Date().toISOString(),
            updatedAt: metadata.updatedAt || new Date().toISOString(),
          });
        }
      });

      // Sort by display order, then by name
      categoryData.sort((a, b) => {
        if (a.displayOrder !== b.displayOrder) {
          return (a.displayOrder || 0) - (b.displayOrder || 0);
        }
        return a.name.localeCompare(b.name);
      });

      return categoryData;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new Error('Failed to fetch categories');
    }
  }

  /**
   * Get category by name
   */
  static async getCategoryByName(name: string): Promise<CategoryData | null> {
    try {
      const result = await prisma.product.groupBy({
        by: ['category'],
        _count: {
          id: true
        },
        where: {
          category: name
        }
      });

      if (result.length === 0) {
        return null;
      }

      const cat = result[0];
      return {
        id: cat.category,
        name: cat.category,
        description: `Products in ${cat.category} category`,
        imageUrl: this.getCategoryImageUrl(cat.category),
        productCount: cat._count.id,
        isActive: true
      };
    } catch (error) {
      console.error('Error fetching category:', error);
      throw new Error('Failed to fetch category');
    }
  }

  /**
   * Delete category by moving all products to "Uncategorized"
   */
  static async deleteCategory(categoryName: string): Promise<{ success: boolean; message: string; productsAffected: number }> {
    try {
      console.log('🗑️ CategoryService.deleteCategory called for:', categoryName);

      // Check if category exists in metadata (for categories without products)
      const categoryMetadata = await this.getCategoryMetadata();
      const hasMetadata = categoryMetadata[categoryName] !== undefined;
      console.log('📋 Category has metadata:', hasMetadata);

      // Check if category has products
      const categoryData = await this.getCategoryByName(categoryName);
      const hasProducts = categoryData !== null;
      const productsAffected = categoryData?.productCount || 0;
      console.log('📦 Category has products:', hasProducts, 'Count:', productsAffected);

      // Category must exist either in metadata or have products
      if (!hasMetadata && !hasProducts) {
        console.log('❌ Category not found in metadata or products');
        return {
          success: false,
          message: 'Category not found',
          productsAffected: 0
        };
      }

      // Update all products in this category to "Uncategorized"
      if (productsAffected > 0) {
        console.log('🔄 Moving products to Uncategorized...');
        await ProductService.updateProductsCategory(categoryName, 'Uncategorized');
      }

      // Delete category metadata if it exists
      if (hasMetadata) {
        console.log('🗑️ Deleting category metadata...');
        try {
          await prisma.systemSetting.deleteMany({
            where: {
              category: 'category_metadata',
              key: categoryName
            }
          });
          console.log('✅ Category metadata deleted');
        } catch (metadataError) {
          console.error('❌ Error deleting category metadata:', metadataError);
          // Don't fail the entire operation if metadata deletion fails
        }
      }

      console.log('✅ Category deletion completed successfully');
      return {
        success: true,
        message: productsAffected > 0
          ? `Category deleted. ${productsAffected} products moved to 'Uncategorized'`
          : 'Category deleted. No products were affected',
        productsAffected
      };
    } catch (error) {
      console.error('Error deleting category:', error);
      throw new Error('Failed to delete category');
    }
  }

  /**
   * Get category image URL based on category name
   */
  private static getCategoryImageUrl(categoryName: string): string {
    const categoryLower = categoryName.toLowerCase();

    if (categoryLower.includes('cake')) {
      return '/categories/cakes.svg';
    } else if (categoryLower.includes('flower')) {
      return '/categories/flowers.svg';
    } else if (categoryLower.includes('gift')) {
      return '/categories/gifts.svg';
    } else if (categoryLower.includes('plant')) {
      return '/categories/plants.svg';
    } else if (categoryLower.includes('combo')) {
      return '/categories/combos.svg';
    } else if (categoryLower.includes('occasion')) {
      return '/categories/occasions.svg';
    } else {
      return '/categories/gifts.svg'; // Default fallback
    }
  }

  /**
   * Get products in a category
   */
  static async getProductsInCategory(categoryName: string): Promise<any[]> {
    try {
      return await ProductService.getProductsByCategory(categoryName);
    } catch (error) {
      console.error('Error fetching products in category:', error);
      throw new Error('Failed to fetch products in category');
    }
  }

  /**
   * Check if category exists
   */
  static async categoryExists(categoryName: string): Promise<boolean> {
    try {
      const category = await this.getCategoryByName(categoryName);
      return category !== null;
    } catch (error) {
      console.error('Error checking category existence:', error);
      return false;
    }
  }

  /**
   * Create a new category with metadata
   */
  static async createCategory(data: CreateCategoryData): Promise<CategoryData> {
    try {
      const categoryData: CategoryData = {
        id: data.name,
        name: data.name,
        description: data.description || `Products in ${data.name} category`,
        imageUrl: data.imageUrl || this.getCategoryImageUrl(data.name),
        productCount: 0,
        isActive: data.isActive !== undefined ? data.isActive : true,
        slug: data.slug || data.name.toLowerCase().replace(/\s+/g, '-'),
        displayOrder: data.displayOrder || 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Store category metadata in system settings
      await this.saveCategoryMetadata(data.name, {
        description: categoryData.description,
        imageUrl: categoryData.imageUrl,
        slug: categoryData.slug,
        displayOrder: categoryData.displayOrder,
        isActive: categoryData.isActive,
        createdAt: categoryData.createdAt,
        updatedAt: categoryData.updatedAt,
      });

      // Store product information if provided
      if (data.productInfo) {
        await this.saveCategoryProductInfo(data.name, data.productInfo);
      }

      return categoryData;
    } catch (error) {
      console.error('Error creating category:', error);
      throw new Error('Failed to create category');
    }
  }

  /**
   * Update category metadata
   */
  static async updateCategory(categoryName: string, data: Partial<CreateCategoryData>): Promise<CategoryData> {
    try {
      // Get existing metadata
      const existingMetadata = await this.getCategoryMetadata();
      const currentMetadata = existingMetadata[categoryName] || {};

      // Update metadata
      const updatedMetadata = {
        ...currentMetadata,
        ...data,
        updatedAt: new Date().toISOString(),
      };

      // Save updated metadata
      await this.saveCategoryMetadata(categoryName, updatedMetadata);

      // Update product information if provided
      if (data.productInfo) {
        await this.saveCategoryProductInfo(categoryName, data.productInfo);
      }

      // Return updated category data
      const categories = await this.getAllCategories();
      const updatedCategory = categories.find(cat => cat.name === categoryName);

      if (!updatedCategory) {
        throw new Error('Category not found after update');
      }

      return updatedCategory;
    } catch (error) {
      console.error('Error updating category:', error);
      throw new Error('Failed to update category');
    }
  }

  /**
   * Get category metadata from system settings
   */
  private static async getCategoryMetadata(): Promise<Record<string, any>> {
    try {
      const settings = await prisma.systemSetting.findMany({
        where: {
          category: 'category_metadata'
        }
      });

      const metadata: Record<string, any> = {};
      settings.forEach(setting => {
        try {
          metadata[setting.key] = JSON.parse(setting.value);
        } catch (error) {
          console.error(`Error parsing metadata for category ${setting.key}:`, error);
        }
      });

      return metadata;
    } catch (error) {
      console.error('Error fetching category metadata:', error);
      return {};
    }
  }

  /**
   * Save category metadata to system settings
   */
  private static async saveCategoryMetadata(categoryName: string, metadata: any): Promise<void> {
    try {
      await prisma.systemSetting.upsert({
        where: {
          category_key: {
            category: 'category_metadata',
            key: categoryName
          }
        },
        update: {
          value: JSON.stringify(metadata),
          updatedAt: new Date()
        },
        create: {
          category: 'category_metadata',
          key: categoryName,
          value: JSON.stringify(metadata)
        }
      });
    } catch (error) {
      console.error('Error saving category metadata:', error);
      throw new Error('Failed to save category metadata');
    }
  }

  /**
   * Save category product information to system settings
   */
  private static async saveCategoryProductInfo(categoryName: string, productInfo: CategoryProductInfo): Promise<void> {
    try {
      await prisma.systemSetting.upsert({
        where: {
          category_key: {
            category: 'product_category_info',
            key: categoryName.toLowerCase()
          }
        },
        update: {
          value: JSON.stringify(productInfo),
          updatedAt: new Date()
        },
        create: {
          category: 'product_category_info',
          key: categoryName.toLowerCase(),
          value: JSON.stringify(productInfo)
        }
      });
    } catch (error) {
      console.error('Error saving category product info:', error);
      throw new Error('Failed to save category product info');
    }
  }

  /**
   * Get category statistics
   */
  static async getCategoryStats(): Promise<{
    totalCategories: number;
    totalProducts: number;
    averageProductsPerCategory: number;
  }> {
    try {
      const categories = await this.getAllCategories();
      const totalCategories = categories.length;
      const totalProducts = categories.reduce((sum, cat) => sum + cat.productCount, 0);
      const averageProductsPerCategory = totalCategories > 0 ? totalProducts / totalCategories : 0;

      return {
        totalCategories,
        totalProducts,
        averageProductsPerCategory: Math.round(averageProductsPerCategory * 100) / 100
      };
    } catch (error) {
      console.error('Error fetching category stats:', error);
      throw new Error('Failed to fetch category statistics');
    }
  }
}
