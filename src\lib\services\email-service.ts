// Email service for order confirmations and notifications
export interface OrderEmailData {
  orderNumber: string;
  customerName: string;
  customerEmail: string;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  subtotal: number;
  shipping: number;
  total: number;
  shippingAddress: {
    firstName: string;
    lastName: string;
    street: string;
    city: string;
    state: string;
    pincode: string;
    phone: string;
  };
  paymentMethod: string;
  estimatedDelivery: string;
}

export class EmailService {
  /**
   * Generate order confirmation email HTML
   */
  static generateOrderConfirmationHTML(data: OrderEmailData): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - ${data.orderNumber}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #ddd; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; }
        .order-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .total-row { font-weight: bold; font-size: 18px; color: #667eea; }
        .button { background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; }
        .address { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Order Confirmed!</h1>
            <p>Thank you for your order, ${data.customerName}!</p>
            <h2>Order #${data.orderNumber}</h2>
        </div>
        
        <div class="content">
            <p>Hi ${data.customerName},</p>
            <p>We're excited to confirm that we've received your order and it's being prepared with care. Here are your order details:</p>
            
            <div class="order-details">
                <h3>📦 Order Items</h3>
                ${data.items.map(item => `
                    <div class="item">
                        <div>
                            <strong>${item.name}</strong><br>
                            <small>Quantity: ${item.quantity} × ₹${item.price.toFixed(2)}</small>
                        </div>
                        <div><strong>₹${item.total.toFixed(2)}</strong></div>
                    </div>
                `).join('')}
                
                <div class="item">
                    <div>Subtotal</div>
                    <div>₹${data.subtotal.toFixed(2)}</div>
                </div>
                <div class="item">
                    <div>Shipping</div>
                    <div>${data.shipping === 0 ? 'Free' : `₹${data.shipping.toFixed(2)}`}</div>
                </div>
                <div class="item total-row">
                    <div>Total</div>
                    <div>₹${data.total.toFixed(2)}</div>
                </div>
            </div>
            
            <h3>🚚 Delivery Information</h3>
            <div class="address">
                <strong>Shipping Address:</strong><br>
                ${data.shippingAddress.firstName} ${data.shippingAddress.lastName}<br>
                ${data.shippingAddress.street}<br>
                ${data.shippingAddress.city}, ${data.shippingAddress.state} ${data.shippingAddress.pincode}<br>
                Phone: ${data.shippingAddress.phone}
            </div>
            
            <p><strong>Payment Method:</strong> ${data.paymentMethod}</p>
            <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
            
            <div style="text-align: center;">
                <a href="#" class="button">Track Your Order</a>
            </div>
            
            <h3>📞 Need Help?</h3>
            <p>If you have any questions about your order, please don't hesitate to contact us:</p>
            <ul>
                <li>📧 Email: <EMAIL></li>
                <li>📱 Phone: +91 98765 43210</li>
                <li>🕒 Hours: 9 AM - 8 PM (Mon-Sun)</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>Thank you for choosing us! We appreciate your business.</p>
            <p><small>This is an automated email. Please do not reply to this email.</small></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Send order confirmation email (mock implementation)
   * In production, integrate with email service like SendGrid, Mailgun, etc.
   */
  static async sendOrderConfirmation(data: OrderEmailData): Promise<boolean> {
    try {
      // Mock email sending - in production, integrate with actual email service
      console.log('📧 Sending order confirmation email...');
      console.log(`To: ${data.customerEmail}`);
      console.log(`Subject: Order Confirmation - #${data.orderNumber}`);
      console.log('Email HTML generated successfully');
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('✅ Order confirmation email sent successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to send order confirmation email:', error);
      return false;
    }
  }

  /**
   * Send order status update email
   */
  static async sendOrderStatusUpdate(
    orderNumber: string,
    customerEmail: string,
    customerName: string,
    status: string,
    trackingNumber?: string
  ): Promise<boolean> {
    try {
      console.log('📧 Sending order status update email...');
      console.log(`To: ${customerEmail}`);
      console.log(`Subject: Order Update - #${orderNumber}`);
      console.log(`Status: ${status}`);
      
      if (trackingNumber) {
        console.log(`Tracking: ${trackingNumber}`);
      }
      
      // Mock email sending
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log('✅ Order status update email sent successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to send order status update email:', error);
      return false;
    }
  }

  /**
   * Generate estimated delivery date
   */
  static getEstimatedDelivery(city: string = 'Bhubaneswar'): string {
    const now = new Date();
    const deliveryDate = new Date(now);
    
    // Same day delivery for Bhubaneswar if ordered before 2 PM
    if (city.toLowerCase().includes('bhubaneswar') && now.getHours() < 14) {
      deliveryDate.setDate(now.getDate());
      return `Today, ${deliveryDate.toLocaleDateString('en-IN', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}`;
    }
    
    // Next day delivery for Bhubaneswar
    if (city.toLowerCase().includes('bhubaneswar')) {
      deliveryDate.setDate(now.getDate() + 1);
      return `Tomorrow, ${deliveryDate.toLocaleDateString('en-IN', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}`;
    }
    
    // 2-3 days for other cities
    deliveryDate.setDate(now.getDate() + 2);
    const endDate = new Date(now);
    endDate.setDate(now.getDate() + 3);
    
    return `${deliveryDate.toLocaleDateString('en-IN', { 
      month: 'short', 
      day: 'numeric' 
    })} - ${endDate.toLocaleDateString('en-IN', { 
      month: 'short', 
      day: 'numeric' 
    })}`;
  }
}
