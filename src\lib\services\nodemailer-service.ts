import nodemailer from 'nodemailer';
import { createTransport } from 'nodemailer';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

class NodemailerService {
  private transporter: nodemailer.Transporter | null = null;
  private isConfigured = false;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      // Check if email configuration is available
      const emailConfig = this.getEmailConfig();

      if (!emailConfig) {
        console.log('📧 Email service not configured - emails will be logged to console');
        return;
      }

      // Create transporter
      this.transporter = createTransport(emailConfig);
      this.isConfigured = true;

      console.log('📧 Email service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize email service:', error);
    }
  }

  private getEmailConfig(): EmailConfig | null {
    const {
      SMTP_HOST,
      SMTP_PORT,
      SMTP_SECURE,
      SMTP_USER,
      SMTP_PASS,
    } = process.env;

    if (!SMTP_HOST || !SMTP_PORT || !SMTP_USER || !SMTP_PASS) {
      return null;
    }

    return {
      host: SMTP_HOST,
      port: parseInt(SMTP_PORT),
      secure: SMTP_SECURE === 'true', // true for 465, false for other ports
      auth: {
        user: SMTP_USER,
        pass: SMTP_PASS,
      },
    };
  }

  private async verifyConnection(): Promise<boolean> {
    if (!this.transporter) {
      return false;
    }

    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error);
      return false;
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.isConfigured || !this.transporter) {
        // Fallback to console logging with better formatting
        console.log('\n📧 =============== EMAIL DEMO MODE ===============');
        console.log('📧 Email service not configured - showing email content:');
        console.log('📧 ===============================================');
        console.log(`📧 To: ${options.to}`);
        console.log(`📧 Subject: ${options.subject}`);
        console.log('📧 ===============================================');
        console.log('📧 HTML Content:');
        console.log(options.html);
        console.log('📧 ===============================================');
        console.log('📧 To enable actual email sending, configure SMTP settings in .env');
        console.log('📧 See docs/EMAIL_SETUP_GUIDE.md for setup instructions');
        console.log('📧 ===============================================\n');
        return true;
      }

      // Verify connection before sending
      const isConnected = await this.verifyConnection();
      if (!isConnected) {
        throw new Error('Email service connection failed');
      }

      const mailOptions = {
        from: `"${process.env.EMAIL_FROM_NAME || 'Mispri'}" <${process.env.SMTP_USER}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      return false;
    }
  }

  async sendPasswordResetEmail(
    email: string,
    name: string,
    resetUrl: string
  ): Promise<boolean> {
    const subject = 'Reset Your Password - Mispri';

    const html = this.generatePasswordResetHTML(name, resetUrl);
    const text = this.generatePasswordResetText(name, resetUrl);

    return this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  async sendPasswordResetOTP(
    email: string,
    name: string,
    otp: string
  ): Promise<boolean> {
    const subject = 'Your Password Reset Code - Mispri';

    const html = this.generatePasswordResetOTPHTML(name, otp);
    const text = this.generatePasswordResetOTPText(name, otp);

    return this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  private generatePasswordResetHTML(name: string, resetUrl: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
          }
          .container {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 28px;
            font-weight: bold;
            color: #ff7700;
            margin-bottom: 10px;
          }
          .title {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
          }
          .content {
            margin-bottom: 30px;
          }
          .button {
            display: inline-block;
            background-color: #ff7700;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
          }
          .button:hover {
            background-color: #e66a00;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
            text-align: center;
          }
          .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🌸 Mispri</div>
            <h1 class="title">Reset Your Password</h1>
          </div>

          <div class="content">
            <p>Hello ${name},</p>

            <p>We received a request to reset your password for your Mispri account. If you didn't make this request, you can safely ignore this email.</p>

            <p>To reset your password, click the button below:</p>

            <div style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset My Password</a>
            </div>

            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666; font-size: 14px;">${resetUrl}</p>

            <div class="warning">
              <strong>⚠️ Important:</strong> This link will expire in 1 hour for security reasons.
            </div>

            <p>If you're having trouble with the button above, copy and paste the URL into your web browser.</p>
          </div>

          <div class="footer">
            <p>Best regards,<br>The Mispri Team</p>
            <p style="font-size: 12px; color: #999;">
              This email was sent to ${email}. If you didn't request a password reset, please ignore this email.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generatePasswordResetText(name: string, resetUrl: string): string {
    return `
Hello ${name},

We received a request to reset your password for your Mispri account.

To reset your password, please visit the following link:
${resetUrl}

This link will expire in 1 hour for security reasons.

If you didn't request a password reset, you can safely ignore this email.

Best regards,
The Mispri Team
    `.trim();
  }

  private generatePasswordResetOTPHTML(name: string, otp: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Password Reset Code</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
          }
          .container {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 28px;
            font-weight: bold;
            color: #ff7700;
            margin-bottom: 10px;
          }
          .title {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
          }
          .content {
            margin-bottom: 30px;
          }
          .otp-container {
            background-color: #f8f9fa;
            border: 2px dashed #ff7700;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
          }
          .otp-code {
            font-size: 36px;
            font-weight: bold;
            color: #ff7700;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
          }
          .otp-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
            text-align: center;
          }
          .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .steps {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #0d47a1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🌸 Mispri</div>
            <h1 class="title">Password Reset Code</h1>
          </div>

          <div class="content">
            <p>Hello ${name},</p>

            <p>We received a request to reset your password for your Mispri account. Use the verification code below to proceed:</p>

            <div class="otp-container">
              <div class="otp-label">Your 6-Digit Verification Code</div>
              <div class="otp-code">${otp}</div>
              <div style="font-size: 12px; color: #999; margin-top: 10px;">
                Enter this code on the password reset page
              </div>
            </div>

            <div class="steps">
              <h4 style="margin-top: 0; color: #0d47a1;">📝 How to Reset Your Password:</h4>
              <ol style="margin-bottom: 0;">
                <li>Go to the password reset page</li>
                <li>Enter your email address</li>
                <li>Enter the 6-digit code: <strong>${otp}</strong></li>
                <li>Create your new password</li>
                <li>Confirm your new password</li>
              </ol>
            </div>

            <div class="warning">
              <strong>⚠️ Important Security Information:</strong>
              <ul style="margin: 10px 0 0 0;">
                <li>This code will expire in <strong>10 minutes</strong></li>
                <li>Don't share this code with anyone</li>
                <li>If you didn't request this, please ignore this email</li>
                <li>Only use this code on the official Mispri website</li>
              </ul>
            </div>

            <p>If you're having trouble, copy and paste the code exactly as shown above.</p>
          </div>

          <div class="footer">
            <p>Best regards,<br>The Mispri Team</p>
            <p style="font-size: 12px; color: #999;">
              This email was sent to ${name}. If you didn't request a password reset, please ignore this email.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generatePasswordResetOTPText(name: string, otp: string): string {
    return `
Hello ${name},

We received a request to reset your password for your Mispri account.

Your 6-Digit Verification Code: ${otp}

How to Reset Your Password:
1. Go to the password reset page
2. Enter your email address
3. Enter the 6-digit code: ${otp}
4. Create your new password
5. Confirm your new password

⚠️ Important Security Information:
- This code will expire in 10 minutes
- Don't share this code with anyone
- If you didn't request this, please ignore this email
- Only use this code on the official Mispri website

Best regards,
The Mispri Team
    `.trim();
  }

  async sendWelcomeEmail(email: string, name: string): Promise<boolean> {
    const subject = 'Welcome to Mispri! 🌸';

    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Mispri</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .container { background-color: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
          .header { text-align: center; margin-bottom: 30px; }
          .logo { font-size: 28px; font-weight: bold; color: #ff7700; margin-bottom: 10px; }
          .button { display: inline-block; background-color: #ff7700; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🌸 Mispri</div>
            <h1>Welcome to Mispri!</h1>
          </div>
          <p>Hello ${name},</p>
          <p>Welcome to Mispri! We're excited to have you join our community.</p>
          <p>You can now browse our collection of flowers, cakes, gifts, and more!</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_WEBSITE_URL || 'http://localhost:3001'}" class="button">Start Shopping</a>
          </div>
          <p>Best regards,<br>The Mispri Team</p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: email,
      subject,
      html,
    });
  }
}

// Export singleton instance
export const emailService = new NodemailerService();
export default emailService;
