import { prisma } from '@/lib/db';
import { Product } from '@prisma/client';

export interface CreateProductData {
  name: string;
  description?: string;
  category: string;
  price: number;
  discountedPrice?: number;
  costPrice: number;
  unit: string;
  lowStockThreshold?: number;
  sku?: string;
  imageUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  isActive?: boolean;
}

export interface UpdateProductData {
  name?: string;
  description?: string;
  category?: string;
  price?: number;
  discountedPrice?: number;
  costPrice?: number;
  unit?: string;
  lowStockThreshold?: number;
  sku?: string;
  imageUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  isActive?: boolean;
}

export class ProductService {
  /**
   * Get all products from database
   */
  static async getAllProducts(): Promise<Product[]> {
    try {
      const products = await prisma.product.findMany({
        orderBy: {
          createdAt: 'desc'
        }
      });
      return products;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  /**
   * Get all products with inventory data
   */
  static async getAllProductsWithInventory(): Promise<any[]> {
    try {
      const products = await prisma.product.findMany({
        include: {
          warehouseInventory: {
            include: {
              warehouse: true
            }
          },
          storeInventory: {
            include: {
              store: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      return products;
    } catch (error) {
      console.error('Error fetching products with inventory:', error);
      throw new Error('Failed to fetch products with inventory');
    }
  }

  /**
   * Get product by ID
   */
  static async getProductById(id: string): Promise<Product | null> {
    try {
      const product = await prisma.product.findUnique({
        where: { id }
      });
      return product;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  /**
   * Create new product
   */
  static async createProduct(data: CreateProductData): Promise<Product> {
    try {
      console.log('📦 ProductService.createProduct called with:', {
        ...data,
        imageUrl: data.imageUrl ? 'Image URL present' : 'No image URL',
        imageUrlLength: data.imageUrl ? data.imageUrl.length : 0
      });

      const product = await prisma.product.create({
        data: {
          name: data.name,
          description: data.description,
          category: data.category,
          price: data.price,
          discountedPrice: data.discountedPrice,
          costPrice: data.costPrice,
          unit: data.unit,
          lowStockThreshold: data.lowStockThreshold || 10,
          sku: data.sku,
          imageUrl: data.imageUrl,
          metaTitle: data.metaTitle,
          metaDescription: data.metaDescription,
          isActive: data.isActive ?? true,
        }
      });

      console.log('✅ Product created in database:', {
        id: product.id,
        name: product.name,
        imageUrl: product.imageUrl ? 'Image URL saved' : 'No image URL saved',
        imageUrlLength: product.imageUrl ? product.imageUrl.length : 0
      });

      return product;
    } catch (error) {
      console.error('Error creating product:', error);
      throw new Error('Failed to create product');
    }
  }

  /**
   * Update product
   */
  static async updateProduct(id: string, data: UpdateProductData): Promise<Product> {
    try {
      const product = await prisma.product.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date()
        }
      });
      return product;
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  /**
   * Delete product
   */
  static async deleteProduct(id: string): Promise<void> {
    try {
      console.log('🗑️ Starting product deletion process for ID:', id);

      // Delete related records in the correct order to avoid foreign key constraint errors

      // 1. Delete cart items
      console.log('Deleting cart items...');
      await prisma.cartItem.deleteMany({
        where: { productId: id }
      });

      // 2. Delete order items
      console.log('Deleting order items...');
      await prisma.orderItem.deleteMany({
        where: { productId: id }
      });

      // 3. Delete transaction items
      console.log('Deleting transaction items...');
      await prisma.transactionItem.deleteMany({
        where: { productId: id }
      });

      // 4. Delete warehouse inventory
      console.log('Deleting warehouse inventory...');
      await prisma.warehouseInventory.deleteMany({
        where: { productId: id }
      });

      // 5. Delete store inventory
      console.log('Deleting store inventory...');
      await prisma.storeInventory.deleteMany({
        where: { productId: id }
      });

      // 6. Delete inventory transfers
      console.log('Deleting inventory transfers...');
      await prisma.inventoryTransfer.deleteMany({
        where: { productId: id }
      });

      // 7. Delete product raw materials
      console.log('Deleting product raw materials...');
      await prisma.productRawMaterial.deleteMany({
        where: { productId: id }
      });

      // 8. Delete wastage records
      console.log('Deleting wastage records...');
      await prisma.wastage.deleteMany({
        where: { productId: id }
      });

      // 9. Delete product reviews
      console.log('Deleting product reviews...');
      await prisma.product_reviews.deleteMany({
        where: { product_id: id }
      });

      // 10. Delete wishlist items
      console.log('Deleting wishlist items...');
      await prisma.wishlist_items.deleteMany({
        where: { product_id: id }
      });

      // 11. Delete product images (these have CASCADE delete, but let's be explicit)
      console.log('Deleting product images...');
      await prisma.productImage.deleteMany({
        where: { productId: id }
      });

      // 12. Delete product relations (these have CASCADE delete, but let's be explicit)
      console.log('Deleting product relations...');
      await prisma.productRelation.deleteMany({
        where: {
          OR: [
            { productId: id },
            { relatedProductId: id }
          ]
        }
      });

      // 13. Finally, delete the product itself
      console.log('Deleting product...');
      await prisma.product.delete({
        where: { id }
      });

      console.log('✅ Product deletion completed successfully');
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  }

  /**
   * Get products by category
   */
  static async getProductsByCategory(category: string): Promise<Product[]> {
    try {
      const products = await prisma.product.findMany({
        where: { category },
        orderBy: {
          createdAt: 'desc'
        }
      });
      return products;
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw new Error('Failed to fetch products by category');
    }
  }

  /**
   * Get all unique categories from products
   */
  static async getCategories(): Promise<Array<{ name: string; productCount: number }>> {
    try {
      const categories = await prisma.product.groupBy({
        by: ['category'],
        _count: {
          id: true
        },
        where: {
          isActive: true
        }
      });

      return categories.map(cat => ({
        name: cat.category,
        productCount: cat._count.id
      }));
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new Error('Failed to fetch categories');
    }
  }

  /**
   * Update products category (used when deleting categories)
   */
  static async updateProductsCategory(oldCategory: string, newCategory: string): Promise<number> {
    try {
      const result = await prisma.product.updateMany({
        where: {
          category: oldCategory
        },
        data: {
          category: newCategory
        }
      });
      return result.count;
    } catch (error) {
      console.error('Error updating products category:', error);
      throw new Error('Failed to update products category');
    }
  }

  /**
   * Get featured products
   */
  static async getFeaturedProducts(): Promise<Product[]> {
    try {
      const products = await prisma.product.findMany({
        where: {
          isActive: true,
          // Add isFeatured field to schema if needed
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 8
      });
      return products;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      throw new Error('Failed to fetch featured products');
    }
  }

  /**
   * Search products
   */
  static async searchProducts(query: string): Promise<Product[]> {
    try {
      const products = await prisma.product.findMany({
        where: {
          OR: [
            {
              name: {
                contains: query,
                mode: 'insensitive'
              }
            },
            {
              description: {
                contains: query,
                mode: 'insensitive'
              }
            },
            {
              category: {
                contains: query,
                mode: 'insensitive'
              }
            }
          ],
          isActive: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      return products;
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }
}
