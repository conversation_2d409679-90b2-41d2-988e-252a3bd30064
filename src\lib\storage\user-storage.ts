// Real user storage system (replace with database in production)
export type StoredUser = {
  id: string;
  name: string;
  email: string;
  password: string; // In production, this should be hashed
  role: 'ADMIN' | 'WAREHOUSE_MANAGER' | 'STORE_MANAGER' | 'STAFF';
  storeId: string | null;
  createdAt: string;
  updatedAt: string;
};

export type StoredStore = {
  id: string;
  name: string;
  location: string;
  createdAt: string;
  updatedAt: string;
};

export type StoredProduct = {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  costPrice: number;
  unit: string;
  lowStockThreshold: number;
  sku: string;
  barcode: string | null;
  weight: string | null;
  dimensions: string | null;
  isActive: boolean;
  isFeatured: boolean;
  imageUrl: string | null;
  tags: string | null;
  metaTitle: string | null;
  metaDescription: string | null;
  createdAt: string;
  updatedAt: string;
};

// In-memory storage (replace with database in production)
let users: StoredUser[] = [
  {
    id: 'admin-user-1',
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123', // In production, hash this
    role: 'ADMIN',
    storeId: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'manager-user-1',
    name: 'Store Manager',
    email: '<EMAIL>',
    password: 'manager123',
    role: 'STORE_MANAGER',
    storeId: 'store-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'staff-user-1',
    name: 'Staff Member',
    email: '<EMAIL>',
    password: 'staff123',
    role: 'STAFF',
    storeId: 'store-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

let stores: StoredStore[] = [
  {
    id: 'store-1',
    name: 'Maa Tarini',
    location: 'Chandrasekharpur',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'store-2',
    name: 'Downtown Bakery',
    location: 'City Center',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// Start with empty products array - no mock data
let products: StoredProduct[] = [];

// User management functions
export const userStorage = {
  // Get all users
  getAllUsers: (): StoredUser[] => {
    return users.map(user => ({
      ...user,
      password: undefined // Don't return password
    })) as StoredUser[];
  },

  // Get user by ID
  getUserById: (id: string): StoredUser | null => {
    const user = users.find(u => u.id === id);
    if (user) {
      return { ...user, password: undefined } as StoredUser;
    }
    return null;
  },

  // Get user by email
  getUserByEmail: (email: string): StoredUser | null => {
    const user = users.find(u => u.email === email);
    if (user) {
      return { ...user, password: undefined } as StoredUser;
    }
    return null;
  },

  // Get user by email with password (for authentication)
  getUserByEmailWithPassword: (email: string): StoredUser | null => {
    return users.find(u => u.email === email) || null;
  },

  // Create new user
  createUser: (userData: Omit<StoredUser, 'id' | 'createdAt' | 'updatedAt'>): StoredUser => {
    const newUser: StoredUser = {
      ...userData,
      id: 'user-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    users.push(newUser);
    return { ...newUser, password: undefined } as StoredUser;
  },

  // Update user
  updateUser: (id: string, updates: Partial<Omit<StoredUser, 'id' | 'createdAt'>>): StoredUser | null => {
    const userIndex = users.findIndex(u => u.id === id);
    if (userIndex === -1) return null;

    users[userIndex] = {
      ...users[userIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    return { ...users[userIndex], password: undefined } as StoredUser;
  },

  // Delete user
  deleteUser: (id: string): boolean => {
    const userIndex = users.findIndex(u => u.id === id);
    if (userIndex === -1) return false;

    users.splice(userIndex, 1);
    return true;
  },

  // Check if email exists
  emailExists: (email: string, excludeId?: string): boolean => {
    return users.some(u => u.email === email && u.id !== excludeId);
  }
};

// Store management functions
export const storeStorage = {
  // Get all stores
  getAllStores: (): StoredStore[] => {
    return stores;
  },

  // Get store by ID
  getStoreById: (id: string): StoredStore | null => {
    return stores.find(s => s.id === id) || null;
  },

  // Create new store
  createStore: (storeData: Omit<StoredStore, 'id' | 'createdAt' | 'updatedAt'>): StoredStore => {
    const newStore: StoredStore = {
      ...storeData,
      id: 'store-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    stores.push(newStore);
    return newStore;
  },

  // Update store
  updateStore: (id: string, updates: Partial<Omit<StoredStore, 'id' | 'createdAt'>>): StoredStore | null => {
    const storeIndex = stores.findIndex(s => s.id === id);
    if (storeIndex === -1) return null;

    stores[storeIndex] = {
      ...stores[storeIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    return stores[storeIndex];
  },

  // Delete store
  deleteStore: (id: string): boolean => {
    const storeIndex = stores.findIndex(s => s.id === id);
    if (storeIndex === -1) return false;

    stores.splice(storeIndex, 1);
    return true;
  }
};

// Helper function to get user with store information
export const getUserWithStore = (userId: string) => {
  const user = userStorage.getUserById(userId);
  if (!user) return null;

  const store = user.storeId ? storeStorage.getStoreById(user.storeId) : null;

  return {
    ...user,
    store: store ? { name: store.name } : null
  };
};

// Product management functions
export const productStorage = {
  // Get all products
  getAllProducts: (): StoredProduct[] => {
    return products;
  },

  // Get product by ID
  getProductById: (id: string): StoredProduct | null => {
    return products.find(p => p.id === id) || null;
  },

  // Create new product
  createProduct: (productData: Omit<StoredProduct, 'id' | 'createdAt' | 'updatedAt'>): StoredProduct => {
    const newProduct: StoredProduct = {
      ...productData,
      id: 'product-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    products.push(newProduct);
    return newProduct;
  },

  // Update product
  updateProduct: (id: string, updates: Partial<Omit<StoredProduct, 'id' | 'createdAt'>>): StoredProduct | null => {
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex === -1) return null;

    products[productIndex] = {
      ...products[productIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    return products[productIndex];
  },

  // Delete product
  deleteProduct: (id: string): boolean => {
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex === -1) return false;

    products.splice(productIndex, 1);
    return true;
  },

  // Get products by category
  getProductsByCategory: (category: string): StoredProduct[] => {
    return products.filter(p => p.category === category);
  },

  // Get featured products
  getFeaturedProducts: (): StoredProduct[] => {
    return products.filter(p => p.isFeatured && p.isActive);
  },

  // Search products
  searchProducts: (query: string): StoredProduct[] => {
    const lowercaseQuery = query.toLowerCase();
    return products.filter(p =>
      p.name.toLowerCase().includes(lowercaseQuery) ||
      p.description.toLowerCase().includes(lowercaseQuery) ||
      p.category.toLowerCase().includes(lowercaseQuery) ||
      (p.tags && p.tags.toLowerCase().includes(lowercaseQuery))
    );
  }
};

// Helper function to get all users with store information
export const getAllUsersWithStores = () => {
  const allUsers = userStorage.getAllUsers();
  return allUsers.map(user => {
    const store = user.storeId ? storeStorage.getStoreById(user.storeId) : null;
    return {
      ...user,
      store: store ? { name: store.name } : null
    };
  });
};
