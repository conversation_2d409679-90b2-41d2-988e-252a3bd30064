import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const footerPages = [
  {
    slug: 'about-us',
    title: 'About Us',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Welcome to Mispri</h2>
        <p class="text-lg text-gray-700">
          At Mispri, we believe that every special moment deserves to be celebrated with beauty, love, and the finest quality products. 
          We are your trusted partner in making life's precious moments unforgettable.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">Our Story</h3>
        <p class="text-gray-700">
          Founded with a passion for bringing joy and beauty into people's lives, <PERSON><PERSON><PERSON> has grown from a small local business 
          into a trusted name in flowers, cakes, gifts, and personalized items. Our journey began with a simple belief: 
          that every celebration, big or small, deserves the perfect touch.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">What We Offer</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li><strong>Fresh Flowers:</strong> Handpicked, vibrant blooms for every occasion</li>
          <li><strong>Delicious Cakes:</strong> Freshly baked, customizable cakes that taste as good as they look</li>
          <li><strong>Thoughtful Gifts:</strong> Curated selection of gifts for birthdays, anniversaries, and special moments</li>
          <li><strong>Personalized Items:</strong> Custom-made products that add a personal touch to your celebrations</li>
          <li><strong>Plants:</strong> Beautiful indoor and outdoor plants to brighten your space</li>
          <li><strong>Combo Packages:</strong> Perfectly paired combinations for maximum impact</li>
        </ul>
        
        <h3 class="text-xl font-semibold text-gray-900">Our Promise</h3>
        <p class="text-gray-700">
          We are committed to delivering excellence in every product and service. From the freshness of our flowers 
          to the taste of our cakes, from the quality of our gifts to the reliability of our delivery, 
          we ensure that your experience with Mispri is nothing short of exceptional.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">Serving Bhubaneswar</h3>
        <p class="text-gray-700">
          Currently, we proudly serve the beautiful city of Bhubaneswar and its surrounding areas. 
          Our local presence allows us to maintain the highest standards of freshness and provide 
          timely delivery for all your special occasions.
        </p>
      </div>
    `
  },
  {
    slug: 'privacy-policy',
    title: 'Privacy Policy',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Privacy Policy</h2>
        <p class="text-sm text-gray-600">Last updated: ${new Date().toLocaleDateString()}</p>
        
        <h3 class="text-xl font-semibold text-gray-900">Information We Collect</h3>
        <p class="text-gray-700">
          We collect information you provide directly to us, such as when you create an account, 
          make a purchase, or contact us for support.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">How We Use Your Information</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>To process and fulfill your orders</li>
          <li>To communicate with you about your orders and our services</li>
          <li>To improve our products and services</li>
          <li>To send you promotional materials (with your consent)</li>
        </ul>
        
        <h3 class="text-xl font-semibold text-gray-900">Information Sharing</h3>
        <p class="text-gray-700">
          We do not sell, trade, or otherwise transfer your personal information to third parties 
          without your consent, except as described in this policy.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">Contact Us</h3>
        <p class="text-gray-700">
          If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>
        </p>
      </div>
    `
  },
  {
    slug: 'terms-conditions',
    title: 'Terms and Conditions',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Terms and Conditions</h2>
        <p class="text-sm text-gray-600">Last updated: ${new Date().toLocaleDateString()}</p>
        
        <h3 class="text-xl font-semibold text-gray-900">Acceptance of Terms</h3>
        <p class="text-gray-700">
          By accessing and using this website, you accept and agree to be bound by the terms 
          and provision of this agreement.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">Products and Services</h3>
        <p class="text-gray-700">
          All products and services are subject to availability. We reserve the right to 
          discontinue any product or service without notice.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">Delivery</h3>
        <p class="text-gray-700">
          We currently deliver within Bhubaneswar city limits. Delivery times are estimates 
          and may vary based on location and availability.
        </p>
        
        <h3 class="text-xl font-semibold text-gray-900">Payment</h3>
        <p class="text-gray-700">
          We accept various payment methods as displayed during checkout. All prices are 
          in Indian Rupees (INR) and include applicable taxes.
        </p>
      </div>
    `
  },
  {
    slug: 'cancellation-refund',
    title: 'Cancellation & Refund Policy',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Cancellation & Refund Policy</h2>
        
        <h3 class="text-xl font-semibold text-gray-900">Cancellation Policy</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>Orders can be cancelled up to 2 hours before the scheduled delivery time</li>
          <li>Same-day delivery orders cannot be cancelled once confirmed</li>
          <li>Custom/personalized items cannot be cancelled once production has started</li>
        </ul>
        
        <h3 class="text-xl font-semibold text-gray-900">Refund Policy</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>Full refund for orders cancelled within the allowed timeframe</li>
          <li>Refunds for damaged or incorrect items delivered</li>
          <li>Refunds will be processed within 5-7 business days</li>
          <li>Refunds will be credited to the original payment method</li>
        </ul>
        
        <h3 class="text-xl font-semibold text-gray-900">How to Request Cancellation/Refund</h3>
        <p class="text-gray-700">
          Contact our customer support <NAME_EMAIL> or call us at +91 98765 43210 
          with your order details.
        </p>
      </div>
    `
  },
  {
    slug: 'coupons-deals',
    title: 'Coupons & Deals',
    content: `
      <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Coupons & Deals</h2>
        
        <h3 class="text-xl font-semibold text-gray-900">Current Offers</h3>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 class="font-semibold text-green-800">Welcome Offer</h4>
          <p class="text-green-700">Get 10% off on your first order! Use code: WELCOME10</p>
        </div>
        
        <h3 class="text-xl font-semibold text-gray-900">How to Use Coupons</h3>
        <ol class="list-decimal list-inside text-gray-700 space-y-2">
          <li>Add items to your cart</li>
          <li>Proceed to checkout</li>
          <li>Enter the coupon code in the "Promo Code" field</li>
          <li>Click "Apply" to see the discount</li>
        </ol>
        
        <h3 class="text-xl font-semibold text-gray-900">Terms & Conditions</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-2">
          <li>Coupons cannot be combined with other offers</li>
          <li>Minimum order value may apply</li>
          <li>Valid for limited time only</li>
          <li>One coupon per customer per order</li>
        </ul>
      </div>
    `
  }
];

async function seedFooterPages() {
  try {
    console.log('🌱 Seeding footer pages...');
    
    for (const page of footerPages) {
      const pageData = {
        title: page.title,
        slug: page.slug,
        content: page.content,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Check if page already exists
      const existingPage = await prisma.systemSetting.findFirst({
        where: {
          category: 'static_pages',
          key: page.slug
        }
      });

      if (existingPage) {
        console.log(`📄 Updating existing page: ${page.title}`);
        await prisma.systemSetting.update({
          where: { id: existingPage.id },
          data: {
            value: JSON.stringify(pageData),
            updatedAt: new Date()
          }
        });
      } else {
        console.log(`📄 Creating new page: ${page.title}`);
        await prisma.systemSetting.create({
          data: {
            category: 'static_pages',
            key: page.slug,
            value: JSON.stringify(pageData),
            description: `Static page: ${page.title}`
          }
        });
      }
    }
    
    console.log('✅ Footer pages seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding footer pages:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
seedFooterPages();
