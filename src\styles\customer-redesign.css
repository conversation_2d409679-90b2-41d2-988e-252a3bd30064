/* Customer Management Redesign Styles */

/* Ensure proper spacing and layout */
.customer-management-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding: 1.5rem;
}

/* Stats cards hover effects */
.stats-card {
  transition: all 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Customer card animations */
.customer-card {
  transition: all 0.2s ease-in-out;
}

.customer-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

/* Search input focus styles */
.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tag styles */
.customer-tag {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

/* Button hover effects */
.action-button {
  transition: all 0.2s ease-in-out;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Tab navigation */
.tab-nav {
  border-bottom: 2px solid #e5e7eb;
}

.tab-button {
  position: relative;
  padding: 1rem 1.5rem;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease-in-out;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: #374151;
  background-color: #f9fafb;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background-color: #eff6ff;
}

/* Customer detail sections */
.detail-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Order history cards */
.order-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease-in-out;
}

.order-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* Status badges */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Address cards */
.address-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: #fafafa;
  transition: all 0.2s ease-in-out;
}

.address-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-state-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  color: #d1d5db;
}

/* Responsive design */
@media (max-width: 768px) {
  .customer-management-container {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .customer-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filter-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .customer-management-container {
    background-color: #111827;
    color: #f9fafb;
  }
  
  .detail-section {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .customer-card {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .stats-card {
    background-color: #1f2937;
    border-color: #374151;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .action-button,
  .search-filter-container {
    display: none;
  }
  
  .customer-card,
  .detail-section {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}
