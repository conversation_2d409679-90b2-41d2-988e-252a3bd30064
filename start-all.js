/**
 * <PERSON><PERSON>t to run both the admin panel and the website simultaneously
 * 
 * Usage: node start-all.js
 */

const { spawn } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(`${colors.yellow}=== Starting Bakery Shop Applications ===${colors.reset}`);

// Start admin panel (root directory)
console.log(`${colors.blue}Starting Admin Panel on port 3000...${colors.reset}`);
const admin = spawn('npm', ['run', 'dev'], { 
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

// Start website
console.log(`${colors.magenta}Starting Website on port 3001...${colors.reset}`);
const website = spawn('npm', ['run', 'dev'], { 
  cwd: path.join(__dirname, 'website'),
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

// Handle admin panel output
admin.stdout.on('data', (data) => {
  console.log(`${colors.cyan}[ADMIN] ${colors.reset}${data.toString().trim()}`);
});

admin.stderr.on('data', (data) => {
  console.error(`${colors.cyan}[ADMIN ERROR] ${colors.reset}${data.toString().trim()}`);
});

// Handle website output
website.stdout.on('data', (data) => {
  console.log(`${colors.magenta}[WEBSITE] ${colors.reset}${data.toString().trim()}`);
});

website.stderr.on('data', (data) => {
  console.error(`${colors.magenta}[WEBSITE ERROR] ${colors.reset}${data.toString().trim()}`);
});

// Handle process exit
process.on('SIGINT', () => {
  console.log(`${colors.yellow}\nShutting down applications...${colors.reset}`);
  admin.kill();
  website.kill();
  process.exit();
});

console.log(`${colors.green}Both applications are starting...${colors.reset}`);
console.log(`${colors.green}Admin Panel: http://localhost:3000${colors.reset}`);
console.log(`${colors.green}Website: http://localhost:3001${colors.reset}`);
console.log(`${colors.yellow}Press Ctrl+C to stop both applications${colors.reset}`);
