
// Skip Prisma generation and run next build directly
const { execSync } = require('child_process');

try {
  execSync('next build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_DISABLE_ESLINT: '1',
      NODE_OPTIONS: '--max-old-space-size=4096',
      NEXT_TELEMETRY_DISABLED: '1',
    }
  });
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
