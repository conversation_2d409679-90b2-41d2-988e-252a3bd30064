// Test the API endpoints to see if they return variant data
async function testAPIs() {
  try {
    console.log('🧪 Testing API endpoints for variant data...\n');

    // Test the admin panel API
    console.log('1. Testing admin panel products-with-variants API:');
    try {
      const response1 = await fetch('http://localhost:3000/api/products-with-variants');
      if (response1.ok) {
        const data1 = await response1.json();
        console.log(`   ✅ Status: ${response1.status}`);
        console.log(`   📦 Products returned: ${data1.products?.length || 0}`);
        
        if (data1.products && data1.products.length > 0) {
          const productWithVariants = data1.products.find(p => p.variants && p.variants.length > 0);
          if (productWithVariants) {
            console.log(`   🎯 Sample product: ${productWithVariants.name}`);
            console.log(`   🎛️ Variants: ${productWithVariants.variants.length}`);
            productWithVariants.variants.forEach((v, i) => {
              console.log(`      ${i + 1}. ${v.weight} - ₹${v.price} ${v.isDefault ? '(Default)' : ''}`);
            });
          }
        }
      } else {
        console.log(`   ❌ Status: ${response1.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    console.log('\n2. Testing specific product API:');
    try {
      // Test with the Chocolate Truffle Cake ID
      const productId = 'cmbymr1an000unid4n26ikks7';
      const response2 = await fetch(`http://localhost:3000/api/products-with-variants/${productId}`);
      if (response2.ok) {
        const data2 = await response2.json();
        console.log(`   ✅ Status: ${response2.status}`);
        console.log(`   📦 Product: ${data2.name}`);
        console.log(`   💰 Base Price: ₹${data2.price}`);
        console.log(`   🎛️ Variants: ${data2.variants?.length || 0}`);
        
        if (data2.variants && data2.variants.length > 0) {
          data2.variants.forEach((v, i) => {
            console.log(`      ${i + 1}. ${v.weight} - ₹${v.price} ${v.isDefault ? '(Default)' : ''} ${v.isActive ? '✅' : '❌'}`);
          });
        }
      } else {
        const errorText = await response2.text();
        console.log(`   ❌ Status: ${response2.status}`);
        console.log(`   ❌ Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    console.log('\n3. Testing website API (if running):');
    try {
      const response3 = await fetch('http://localhost:3001/api/products-with-variants/cmbymr1an000unid4n26ikks7');
      if (response3.ok) {
        const data3 = await response3.json();
        console.log(`   ✅ Website API working`);
        console.log(`   📦 Product: ${data3.name}`);
        console.log(`   🎛️ Variants: ${data3.variants?.length || 0}`);
      } else {
        console.log(`   ⚠️ Website API not accessible (Status: ${response3.status})`);
      }
    } catch (error) {
      console.log(`   ⚠️ Website API not running: ${error.message}`);
    }

    console.log('\n4. Testing deployed admin API:');
    try {
      const response4 = await fetch('https://mispri24.vercel.app/api/products-with-variants/cmbymr1an000unid4n26ikks7');
      if (response4.ok) {
        const data4 = await response4.json();
        console.log(`   ✅ Deployed API working`);
        console.log(`   📦 Product: ${data4.name}`);
        console.log(`   🎛️ Variants: ${data4.variants?.length || 0}`);
      } else {
        console.log(`   ❌ Deployed API Status: ${response4.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Deployed API Error: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testAPIs();
