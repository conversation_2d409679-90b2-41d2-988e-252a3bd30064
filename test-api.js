// Test the products-with-variants API
const testProductAPI = async () => {
  try {
    console.log('🧪 Testing products-with-variants API...');

    // Test data for creating a product with variants
    const testProductData = {
      name: 'Test Chocolate Cake',
      description: 'A delicious test chocolate cake with multiple weight options',
      category: 'cm5aqhqhj0000uy8ixqhqhqhj', // Use a real category ID from your database
      warehouseId: 'cm5aqhqhj0001uy8ixqhqhqhj', // Use a real warehouse ID from your database
      sku: `TEST-CAKE-${Date.now()}`,
      unit: 'piece',
      lowStockThreshold: '10',
      initialStock: '50',
      isActive: true,
      isFeatured: false,
      variants: [
        {
          weight: '0.5 Kg',
          price: 595,
          costPrice: 300,
          isDefault: true,
          isActive: true,
          sortOrder: 1
        },
        {
          weight: '1 Kg',
          price: 1045,
          costPrice: 500,
          isDefault: false,
          isActive: true,
          sortOrder: 2
        },
        {
          weight: '1.5 Kg',
          price: 1545,
          costPrice: 750,
          isDefault: false,
          isActive: true,
          sortOrder: 3
        }
      ]
    };

    console.log('📦 Test product data:', {
      name: testProductData.name,
      variantsCount: testProductData.variants.length,
      category: testProductData.category,
      warehouseId: testProductData.warehouseId
    });

    // Make API call to create product
    const response = await fetch('http://localhost:3000/api/products-with-variants', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-user-email': '<EMAIL>' // Add auth header
      },
      body: JSON.stringify(testProductData)
    });

    console.log('📊 API Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ Product created successfully!');
    console.log('📋 Product details:', {
      id: result.id,
      name: result.name,
      variantsCount: result.variants?.length || 0,
      basePrice: result.price
    });

    if (result.variants && result.variants.length > 0) {
      console.log('🎯 Variants created:');
      result.variants.forEach(variant => {
        console.log(`  - ${variant.weight}: ₹${variant.price} ${variant.isDefault ? '(Default)' : ''}`);
      });
    }

    // Test fetching the product
    console.log('\n🔍 Testing product fetch...');
    const fetchResponse = await fetch(`http://localhost:3000/api/products-with-variants/${result.id}`);
    
    if (fetchResponse.ok) {
      const fetchedProduct = await fetchResponse.json();
      console.log('✅ Product fetched successfully');
      console.log('📋 Fetched variants:', fetchedProduct.variants?.length || 0);
    } else {
      console.error('❌ Failed to fetch product');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

// Run the test
testProductAPI();
