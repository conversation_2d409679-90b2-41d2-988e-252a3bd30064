<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Admin Panel API Test</h1>
    <p>Testing APIs for: <strong>https://mispri24-qyegen0fg-bhardwajvaishnavis-projects.vercel.app</strong></p>

    <button onclick="testDashboard()">Test Dashboard API</button>
    <button onclick="testWarehouses()">Test Warehouses API</button>
    <button onclick="testUsers()">Test Users API</button>
    <button onclick="testStores()">Test Stores API</button>
    <button onclick="testTransactions()">Test Transactions API</button>
    <button onclick="testAll()">Test All APIs</button>

    <div id="results"></div>

    <script>
        const baseUrl = 'https://mispri24-qyegen0fg-bhardwajvaishnavis-projects.vercel.app';

        function addResult(test, success, data) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <h3>${test}</h3>
                <p>Status: ${success ? 'SUCCESS' : 'FAILED'}</p>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(div);
        }

        async function testApi(endpoint, name) {
            try {
                const response = await fetch(`${baseUrl}/api/${endpoint}`);
                const data = await response.json();

                if (response.ok) {
                    addResult(name, true, data);
                    return true;
                } else {
                    addResult(name, false, data);
                    return false;
                }
            } catch (error) {
                addResult(name, false, { error: error.message });
                return false;
            }
        }

        async function testDashboard() {
            await testApi('dashboard', 'Dashboard API');
        }

        async function testWarehouses() {
            await testApi('warehouses', 'Warehouses API');
        }

        async function testUsers() {
            await testApi('users', 'Users API');
        }

        async function testStores() {
            await testApi('stores', 'Stores API');
        }

        async function testTransactions() {
            await testApi('transactions', 'Transactions API');
        }

        async function testAll() {
            document.getElementById('results').innerHTML = '';

            console.log('Testing all APIs...');
            await testDashboard();
            await testWarehouses();
            await testUsers();
            await testStores();
            await testTransactions();

            console.log('All tests completed!');
        }
    </script>
</body>
</html>
