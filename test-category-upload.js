// Test script to debug category image upload
async function testCategoryImageUpload() {
  try {
    console.log('🧪 TESTING CATEGORY IMAGE UPLOAD\n');

    // Test 1: Check if upload API is accessible
    console.log('1. 🔍 TESTING UPLOAD API ENDPOINT:');
    
    // Create a small test image (1x1 pixel red PNG)
    const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    
    try {
      const uploadResponse = await fetch('http://localhost:3000/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: testImageBase64,
          folder: 'categories'
        }),
      });

      console.log(`   📊 Upload API Status: ${uploadResponse.status}`);
      
      if (uploadResponse.ok) {
        const uploadData = await uploadResponse.json();
        console.log('   ✅ Upload API Response:', {
          success: uploadData.success,
          uploadMethod: uploadData.uploadMethod,
          isBase64: uploadData.isBase64,
          imageUrlLength: uploadData.imageUrl ? uploadData.imageUrl.length : 0,
          imageUrlPreview: uploadData.imageUrl ? uploadData.imageUrl.substring(0, 50) + '...' : 'No URL'
        });
      } else {
        const errorData = await uploadResponse.text();
        console.log('   ❌ Upload API Error:', errorData);
      }
    } catch (uploadError) {
      console.log('   ❌ Upload API Network Error:', uploadError.message);
    }

    // Test 2: Check categories API
    console.log('\n2. 🔍 TESTING CATEGORIES API:');
    try {
      const categoriesResponse = await fetch('http://localhost:3000/api/categories');
      console.log(`   📊 Categories API Status: ${categoriesResponse.status}`);
      
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        console.log(`   ✅ Found ${categoriesData.length || 0} categories`);
        
        // Check if any categories have images
        const categoriesWithImages = categoriesData.filter(cat => cat.imageUrl && cat.imageUrl.length > 0);
        console.log(`   📸 Categories with images: ${categoriesWithImages.length}`);
        
        if (categoriesWithImages.length > 0) {
          console.log('   🖼️ Sample category with image:');
          const sample = categoriesWithImages[0];
          console.log(`      Name: ${sample.name}`);
          console.log(`      Image URL: ${sample.imageUrl.substring(0, 50)}...`);
          console.log(`      Image Type: ${sample.imageUrl.startsWith('data:') ? 'Base64' : 'File Path'}`);
        }
      } else {
        console.log('   ❌ Categories API failed');
      }
    } catch (categoriesError) {
      console.log('   ❌ Categories API Error:', categoriesError.message);
    }

    // Test 3: Test creating a category with image
    console.log('\n3. 🧪 TESTING CATEGORY CREATION WITH IMAGE:');
    try {
      const testCategoryData = {
        name: 'Test Category Upload',
        description: 'Testing image upload functionality',
        imageUrl: testImageBase64,
        slug: 'test-category-upload',
        displayOrder: 999,
        isActive: true,
        productInfo: {
          productContains: [
            { label: 'Type', value: 'Test Product' }
          ],
          careInstructions: [
            'This is a test category'
          ],
          badges: ['TEST'],
          showNameField: false,
          weightLabel: 'Size',
          specifications: []
        }
      };

      const createResponse = await fetch('http://localhost:3000/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-email': '<EMAIL>' // Add auth header
        },
        body: JSON.stringify(testCategoryData),
      });

      console.log(`   📊 Create Category Status: ${createResponse.status}`);
      
      if (createResponse.ok) {
        const createData = await createResponse.json();
        console.log('   ✅ Category created successfully:', {
          id: createData.id,
          name: createData.name,
          hasImage: !!createData.imageUrl,
          imageType: createData.imageUrl?.startsWith('data:') ? 'Base64' : 'File Path'
        });
        
        // Clean up - delete the test category
        try {
          await fetch(`http://localhost:3000/api/categories/${createData.id}`, {
            method: 'DELETE',
            headers: {
              'x-user-email': '<EMAIL>'
            }
          });
          console.log('   🧹 Test category cleaned up');
        } catch (cleanupError) {
          console.log('   ⚠️ Could not clean up test category');
        }
      } else {
        const errorData = await createResponse.text();
        console.log('   ❌ Create Category Error:', errorData);
      }
    } catch (createError) {
      console.log('   ❌ Create Category Network Error:', createError.message);
    }

    console.log('\n📋 DIAGNOSIS:');
    console.log('   1. Check if upload API is working ✓');
    console.log('   2. Check if categories API accepts images ✓');
    console.log('   3. Check if category creation works with images ✓');
    console.log('\n   If all tests pass but UI still fails:');
    console.log('   - Check browser console for JavaScript errors');
    console.log('   - Check network tab for failed requests');
    console.log('   - Verify file input is triggering correctly');
    console.log('   - Check if image preview is showing');

  } catch (error) {
    console.error('❌ Test script failed:', error);
  }
}

// Run the test
testCategoryImageUpload();
