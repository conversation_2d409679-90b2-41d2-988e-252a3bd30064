const fs = require('fs');
const path = require('path');

// Read the CSS file
const cssPath = path.join(__dirname, 'src', 'app', 'globals.css');
const cssContent = fs.readFileSync(cssPath, 'utf8');

console.log('CSS file read successfully.');
console.log(`File size: ${cssContent.length} bytes`);

// Check for basic CSS syntax issues
try {
  // This is a very basic check - it won't catch all CSS issues
  // but it will catch basic syntax errors like missing brackets
  const lines = cssContent.split('\n');
  let openBraces = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Count braces
    for (const char of line) {
      if (char === '{') openBraces++;
      if (char === '}') openBraces--;
    }
    
    // Check for negative counts (more closing than opening)
    if (openBraces < 0) {
      console.error(`Error: Too many closing braces at line ${i + 1}`);
      process.exit(1);
    }
  }
  
  // Check for unclosed braces
  if (openBraces > 0) {
    console.error(`Error: ${openBraces} unclosed braces`);
    process.exit(1);
  }
  
  console.log('Basic CSS syntax check passed.');
  
  // Check for common CSS issues
  if (cssContent.includes('{;')) {
    console.warn('Warning: Empty CSS rule found');
  }
  
  if (cssContent.includes('};')) {
    console.warn('Warning: Semicolon after closing brace found');
  }
  
  console.log('CSS file appears to be valid.');
  
} catch (error) {
  console.error('Error checking CSS file:', error);
  process.exit(1);
}
