// Test database connection and create demo customer
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "postgresql://neondb_owner:<EMAIL>/neondb?schema=bakery&sslmode=require"
    }
  }
});

async function testDatabaseAndCreateCustomer() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test connection
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // Check if demo customer user exists
    console.log('\n🔍 Checking for demo customer user...');
    let user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { customer: true }
    });

    if (!user) {
      console.log('❌ Demo customer user not found. Creating...');
      
      // Create demo customer user
      user = await prisma.user.create({
        data: {
          name: 'Demo Customer',
          email: '<EMAIL>',
          password: 'customer123', // Plain text for demo
          role: 'CUSTOMER',
          customer: {
            create: {
              firstName: 'Demo',
              lastName: 'Customer',
              phone: '+91 9876543210',
              isSubscribed: false,
              loyaltyPoints: 100,
            }
          }
        },
        include: { customer: true }
      });
      
      console.log('✅ Demo customer user created:', user);
    } else {
      console.log('✅ Demo customer user exists:', user);
    }

    // Check for products
    console.log('\n🔍 Checking for products...');
    const products = await prisma.product.findMany({
      take: 3,
      select: {
        id: true,
        name: true,
        price: true,
        costPrice: true,
      }
    });

    if (products.length === 0) {
      console.log('❌ No products found. Creating demo product...');
      
      // Create a demo product
      const demoProduct = await prisma.product.create({
        data: {
          name: 'Demo Cake',
          description: 'A delicious demo cake for testing',
          price: 299.99,
          costPrice: 150.00,
          sku: 'DEMO-CAKE-001',
          unit: 'piece',
          isActive: true,
        }
      });
      
      console.log('✅ Demo product created:', demoProduct);
    } else {
      console.log('✅ Products found:', products.length);
      products.forEach(product => {
        console.log(`  - ${product.name}: ₹${product.price}`);
      });
    }

    // Test order creation
    console.log('\n🔍 Testing order creation...');
    
    const testProduct = products[0] || await prisma.product.findFirst();
    if (!testProduct) {
      console.error('❌ No product available for testing');
      return;
    }

    const orderData = {
      customerId: user.customer.id,
      orderNumber: `TEST-${Date.now()}`,
      totalAmount: testProduct.price,
      subtotal: testProduct.price,
      shipping: 0,
      status: 'PENDING_ASSIGNMENT',
      paymentMethod: 'COD',
      paymentStatus: 'PENDING',
      orderType: 'ONLINE',
    };

    const order = await prisma.order.create({
      data: orderData,
    });

    console.log('✅ Test order created:', order);

    // Create order item
    const orderItem = await prisma.orderItem.create({
      data: {
        orderId: order.id,
        productId: testProduct.id,
        quantity: 1,
        unitPrice: testProduct.price,
      }
    });

    console.log('✅ Test order item created:', orderItem);

    // Clean up test order
    await prisma.orderItem.delete({ where: { id: orderItem.id } });
    await prisma.order.delete({ where: { id: order.id } });
    console.log('✅ Test order cleaned up');

    console.log('\n🎉 All tests passed! Database is ready for order creation.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabaseAndCreateCustomer();
