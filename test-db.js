// Simple script to test database connection
const { PrismaClient } = require('./src/generated/prisma');

async function testConnection() {
  console.log('Testing database connection...');
  
  try {
    const prisma = new PrismaClient();
    
    // Try to query something simple
    console.log('Attempting to connect to the database...');
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    
    console.log('Connection successful!', result);
    
    await prisma.$disconnect();
    
    return true;
  } catch (error) {
    console.error('Failed to connect to the database:');
    console.error(error);
    
    return false;
  }
}

testConnection()
  .then(success => {
    console.log('Test completed. Success:', success);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
