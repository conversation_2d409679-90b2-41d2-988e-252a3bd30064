const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDeletionLogic() {
  try {
    console.log('🧪 Testing product deletion logic directly...');

    // Get a product to test with
    const products = await prisma.product.findMany({ take: 1 });
    
    if (products.length === 0) {
      console.log('No products found to test deletion');
      return;
    }

    const testProduct = products[0];
    console.log(`\nTesting deletion logic for: ${testProduct.name} (ID: ${testProduct.id})`);

    // Check related records before deletion
    const relatedRecords = {
      cartItems: await prisma.cartItem.count({ where: { productId: testProduct.id } }),
      orderItems: await prisma.orderItem.count({ where: { productId: testProduct.id } }),
      transactionItems: await prisma.transactionItem.count({ where: { productId: testProduct.id } }),
      warehouseInventory: await prisma.warehouseInventory.count({ where: { productId: testProduct.id } }),
      storeInventory: await prisma.storeInventory.count({ where: { productId: testProduct.id } }),
      inventoryTransfers: await prisma.inventoryTransfer.count({ where: { productId: testProduct.id } }),
      productRawMaterials: await prisma.productRawMaterial.count({ where: { productId: testProduct.id } }),
      wastage: await prisma.wastage.count({ where: { productId: testProduct.id } }),
      productReviews: await prisma.product_reviews.count({ where: { product_id: testProduct.id } }),
      wishlistItems: await prisma.wishlist_items.count({ where: { product_id: testProduct.id } }),
      productImages: await prisma.productImage.count({ where: { productId: testProduct.id } }),
      productRelations: await prisma.productRelation.count({ 
        where: { 
          OR: [
            { productId: testProduct.id },
            { relatedProductId: testProduct.id }
          ]
        }
      })
    };

    console.log('\nRelated records before deletion:');
    Object.entries(relatedRecords).forEach(([table, count]) => {
      if (count > 0) {
        console.log(`  ${table}: ${count}`);
      }
    });

    const totalRelatedRecords = Object.values(relatedRecords).reduce((sum, count) => sum + count, 0);
    console.log(`Total related records: ${totalRelatedRecords}`);

    // Now test the deletion logic step by step
    console.log('\n🗑️ Starting deletion process...');

    // 1. Delete cart items
    console.log('Deleting cart items...');
    const deletedCartItems = await prisma.cartItem.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedCartItems.count} cart items`);

    // 2. Delete order items
    console.log('Deleting order items...');
    const deletedOrderItems = await prisma.orderItem.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedOrderItems.count} order items`);

    // 3. Delete transaction items
    console.log('Deleting transaction items...');
    const deletedTransactionItems = await prisma.transactionItem.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedTransactionItems.count} transaction items`);

    // 4. Delete warehouse inventory
    console.log('Deleting warehouse inventory...');
    const deletedWarehouseInventory = await prisma.warehouseInventory.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedWarehouseInventory.count} warehouse inventory records`);

    // 5. Delete store inventory
    console.log('Deleting store inventory...');
    const deletedStoreInventory = await prisma.storeInventory.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedStoreInventory.count} store inventory records`);

    // 6. Delete inventory transfers
    console.log('Deleting inventory transfers...');
    const deletedInventoryTransfers = await prisma.inventoryTransfer.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedInventoryTransfers.count} inventory transfers`);

    // 7. Delete product raw materials
    console.log('Deleting product raw materials...');
    const deletedProductRawMaterials = await prisma.productRawMaterial.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedProductRawMaterials.count} product raw materials`);

    // 8. Delete wastage records
    console.log('Deleting wastage records...');
    const deletedWastage = await prisma.wastage.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedWastage.count} wastage records`);

    // 9. Delete product reviews
    console.log('Deleting product reviews...');
    const deletedProductReviews = await prisma.product_reviews.deleteMany({
      where: { product_id: testProduct.id }
    });
    console.log(`  Deleted ${deletedProductReviews.count} product reviews`);

    // 10. Delete wishlist items
    console.log('Deleting wishlist items...');
    const deletedWishlistItems = await prisma.wishlist_items.deleteMany({
      where: { product_id: testProduct.id }
    });
    console.log(`  Deleted ${deletedWishlistItems.count} wishlist items`);

    // 11. Delete product images
    console.log('Deleting product images...');
    const deletedProductImages = await prisma.productImage.deleteMany({
      where: { productId: testProduct.id }
    });
    console.log(`  Deleted ${deletedProductImages.count} product images`);

    // 12. Delete product relations
    console.log('Deleting product relations...');
    const deletedProductRelations = await prisma.productRelation.deleteMany({
      where: { 
        OR: [
          { productId: testProduct.id },
          { relatedProductId: testProduct.id }
        ]
      }
    });
    console.log(`  Deleted ${deletedProductRelations.count} product relations`);

    // 13. Finally, delete the product itself
    console.log('Deleting product...');
    await prisma.product.delete({
      where: { id: testProduct.id }
    });
    console.log(`  ✅ Product "${testProduct.name}" deleted successfully`);

    console.log('\n🎉 Product deletion test completed successfully!');
    console.log('The deletion logic works correctly and handles all foreign key constraints.');

  } catch (error) {
    console.error('❌ Error during deletion test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDeletionLogic();
