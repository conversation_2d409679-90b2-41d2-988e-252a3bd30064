// Test script to check image upload functionality
const fs = require('fs');
const path = require('path');

// Create a simple test base64 image (1x1 pixel red PNG)
const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

async function testImageUpload() {
  try {
    console.log('🧪 Testing image upload API...');
    
    const response = await fetch('http://localhost:3002/api/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image: testImageBase64,
        folder: 'products'
      }),
    });

    console.log('📊 Response status:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ Upload failed:', errorData);
      return;
    }

    const data = await response.json();
    console.log('✅ Upload successful:', data);

    // Check if file was actually created (for development mode)
    if (data.uploadMethod === 'filesystem') {
      const publicDir = path.join(process.cwd(), 'public');
      const imagePath = path.join(publicDir, data.imageUrl.substring(1)); // Remove leading slash
      
      if (fs.existsSync(imagePath)) {
        const stats = fs.statSync(imagePath);
        console.log(`📁 File created successfully: ${imagePath}`);
        console.log(`📏 File size: ${stats.size} bytes`);
      } else {
        console.log('⚠️ File was not found on disk');
      }
    } else {
      console.log('📱 Using base64 storage (production mode)');
    }

  } catch (error) {
    console.error('❌ Error testing image upload:', error);
  }
}

testImageUpload();
