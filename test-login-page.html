<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON>y Admin Login - Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom animations for login page */
    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    @keyframes slideUp {
      from {
        transform: translateY(20px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-5px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    .animate-fadeIn {
      animation: fadeIn 0.8s ease-in-out forwards;
    }

    .animate-slideUp {
      animation: slideUp 0.6s ease-out forwards;
    }

    .animate-float {
      animation: float 3s ease-in-out infinite;
    }

    .animation-delay-100 {
      animation-delay: 0.1s;
    }

    .animation-delay-200 {
      animation-delay: 0.2s;
    }

    .animation-delay-300 {
      animation-delay: 0.3s;
    }
  </style>
</head>
<body>
  <div class="flex min-h-screen">
    <!-- Left side - Bakery Image -->
    <div class="hidden md:flex md:w-1/2 relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-amber-500/80 to-amber-600/80 z-10"></div>
      <div 
        class="absolute inset-0 bg-cover bg-center"
        style="
          background-image: url('https://images.unsplash.com/photo-1517433670267-08bbd4be890f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80');
          filter: brightness(0.9)
        "
      ></div>
      <div class="relative z-20 flex flex-col items-center justify-center w-full p-12 text-white">
        <div class="mb-8">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-16 h-16">
            <path d="M15.75 8.25a.75.75 0 0 1 .75.75c0 1.12-.492 2.126-1.27 2.812a.75.75 0 1 1-.992-1.124A2.243 2.243 0 0 0 15 9a.75.75 0 0 1 .75-.75Z" />
            <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM4.575 15.6a8.25 8.25 0 0 0 9.348 4.425 1.966 1.966 0 0 0-1.84-1.275.983.983 0 0 1-.97-.822l-.073-.437c-.094-.565.25-1.11.8-1.267l.99-.282c.427-.123.783-.418.982-.816l.036-.073a1.453 1.453 0 0 1 2.328-.377L16.5 15.9h.008a8.25 8.25 0 0 0-11.933-.3Z" clip-rule="evenodd" />
          </svg>
        </div>
        <h1 class="text-4xl font-bold mb-4 text-center">Bakery Market</h1>
        <p class="text-xl mb-8 text-center max-w-md">Manage your bakery products, orders, and customers with our powerful admin dashboard</p>
        <div class="space-y-4 text-center">
          <div class="flex items-center space-x-2">
            <div class="bg-white/20 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path fill-rule="evenodd" d="M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6ZM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5Z" clip-rule="evenodd" />
                <path fill-rule="evenodd" d="M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375ZM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
              </svg>
            </div>
            <span>Manage inventory with ease</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="bg-white/20 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M4.5 3.75a3 3 0 0 0-3 3v.75h21v-.75a3 3 0 0 0-3-3h-15Z" />
                <path fill-rule="evenodd" d="M22.5 9.75h-21v7.5a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-7.5Zm-18 3.75a.75.75 0 0 1 .75-.75h6a.75.75 0 0 1 0 1.5h-6a.75.75 0 0 1-.75-.75Zm.75 2.25a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z" clip-rule="evenodd" />
              </svg>
            </div>
            <span>Track sales and revenue</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="bg-white/20 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z" />
              </svg>
            </div>
            <span>Delight your customers</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right side - Login Form -->
    <div class="w-full md:w-1/2 flex items-center justify-center bg-gradient-to-b from-amber-50 to-white p-4">
      <div class="w-full max-w-md space-y-8 px-4 animate-fadeIn">
        <div class="text-center">
          <div class="flex justify-center mb-4">
            <div class="bg-amber-100 p-3 rounded-full shadow-md animate-float">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#f59e0b" class="w-8 h-8">
                <path d="M11.25 3v4.046a3 3 0 0 0-4.277 4.204H1.5v-6A2.25 2.25 0 0 1 3.75 3h7.5ZM12.75 3v4.011a3 3 0 0 1 4.239 4.239H22.5v-6A2.25 2.25 0 0 0 20.25 3h-7.5ZM22.5 12.75h-8.983a4.125 4.125 0 0 0 4.108 3.75.75.75 0 0 1 0 1.5 5.623 5.623 0 0 1-4.875-2.817V21h7.5a2.25 2.25 0 0 0 2.25-2.25v-6ZM11.25 21v-5.817A5.623 5.623 0 0 1 6.375 18a.75.75 0 0 1 0-1.5 4.126 4.126 0 0 0 4.108-3.75H1.5v6A2.25 2.25 0 0 0 3.75 21h7.5Z" />
                <path d="M11.085 10.354c.03.297.038.575.036.805a7.484 7.484 0 0 1-.805-.036c-.833-.084-1.677-.325-2.195-.843a1.5 1.5 0 0 1 0-2.121c.518-.518 1.362-.76 2.195-.843C10.85 7.271 11.5 7.5 12 7.5s1.15-.229 1.684-.183c.833.084 1.677.325 2.195.843a1.5 1.5 0 0 1 0 2.121c-.518.518-1.362-.76-2.195-.843-.292.03-.57.038-.8.036a7.489 7.489 0 0 1 .036.805c.084.833.325 1.677.843 2.195a1.5 1.5 0 0 1 0 2.121c-.518.518-1.362.76-2.195.843-.292.03-.57.038-.8.036a7.49 7.49 0 0 1 .036.805c.084.833.325 1.677.843 2.195a1.5 1.5 0 0 1-2.121 2.121c-.518-.518-.76-1.362-.843-2.195-.03-.292-.038-.57-.036-.8a7.489 7.489 0 0 1-.805.036c-.833-.084-1.677-.325-2.195-.843a1.5 1.5 0 0 1 0-2.121c.518-.518 1.362-.76 2.195-.843a7.488 7.488 0 0 1 .805-.036 7.49 7.49 0 0 1-.036-.805c-.084-.833-.325-1.677-.843-2.195a1.5 1.5 0 0 1 0-2.121c.518-.518 1.362-.76 2.195-.843Z" />
              </svg>
            </div>
          </div>
          <h1 class="text-3xl font-bold text-gray-800 animate-slideUp">Admin Dashboard</h1>
          <p class="mt-2 text-gray-600 animate-slideUp animation-delay-100">Sign in to your account</p>
        </div>

        <form class="mt-8 space-y-6 animate-fadeIn animation-delay-200" onsubmit="return false;">
          <div class="space-y-5 rounded-xl border border-amber-100 bg-white p-6 shadow-lg relative overflow-hidden">
            <div class="absolute -top-10 -right-10 w-40 h-40 bg-amber-50 rounded-full opacity-50"></div>
            <div class="absolute -bottom-10 -left-10 w-40 h-40 bg-amber-50 rounded-full opacity-50"></div>
            <div class="relative z-10">
              <div id="error-message" class="rounded-md bg-red-50 p-4 text-sm text-red-500 border border-red-100 hidden">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 mr-2">
                    <path fill-rule="evenodd" d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5z" clip-rule="evenodd" />
                  </svg>
                  <span id="error-text">Invalid email or password</span>
                </div>
              </div>

              <div class="space-y-2">
                <label for="email" class="text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#9ca3af" class="w-5 h-5">
                      <path d="M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0L1.5 8.67Z" />
                      <path d="M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0L22.5 6.908Z" />
                    </svg>
                  </div>
                  <input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    class="pl-10 border border-amber-200 focus:border-amber-400 focus:ring-amber-400 w-full rounded-md py-2 px-3"
                    required
                  />
                </div>
              </div>

              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <label for="password" class="text-sm font-medium text-gray-700">
                    Password
                  </label>
                  <a
                    href="#"
                    class="text-xs text-amber-600 hover:text-amber-800 hover:underline"
                  >
                    Forgot password?
                  </a>
                </div>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#9ca3af" class="w-5 h-5">
                      <path fill-rule="evenodd" d="M12 1.5a5.25 5.25 0 0 0-5.25 5.25v3a3 3 0 0 0-3 3v6.75a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3v-6.75a3 3 0 0 0-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 1 0-7.5 0v3h7.5z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    class="pl-10 border border-amber-200 focus:border-amber-400 focus:ring-amber-400 w-full rounded-md py-2 px-3"
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                class="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white shadow-md transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] py-2 px-4 rounded-md mt-4"
                onclick="showLoading()"
              >
                <span id="button-text">Sign in</span>
                <span id="button-loading" class="hidden flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </span>
              </button>

              <div class="pt-2 text-center text-sm text-gray-500">
                <p>Demo credentials: <span class="font-medium text-amber-600"><EMAIL> / admin123</span></p>
              </div>
            </div>
          </div>
        </form>

        <div class="text-center text-sm text-gray-500 animate-fadeIn animation-delay-300">
          <p>© 2024 Bakery Market Admin. All rights reserved.</p>
          <div class="flex items-center justify-center mt-2 space-x-1">
            <span>Made with</span>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#f59e0b" class="w-4 h-4 animate-float">
              <path d="m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z" />
            </svg>
            <span>for your bakery business</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    function showLoading() {
      document.getElementById('button-text').classList.add('hidden');
      document.getElementById('button-loading').classList.remove('hidden');
      
      // Simulate login process
      setTimeout(() => {
        document.getElementById('error-message').classList.remove('hidden');
        document.getElementById('button-text').classList.remove('hidden');
        document.getElementById('button-loading').classList.add('hidden');
      }, 2000);
    }
  </script>
</body>
</html>
