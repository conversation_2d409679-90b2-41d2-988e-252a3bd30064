const fs = require('fs');
const path = require('path');

// Read the login page file
const loginPagePath = path.join(__dirname, 'src', 'app', 'auth', 'login', 'page.tsx');
const loginPageContent = fs.readFileSync(loginPagePath, 'utf8');

console.log('Login page file read successfully.');
console.log(`File size: ${loginPageContent.length} bytes`);

// Check for basic syntax issues
try {
  // This is a very basic check - it won't catch TypeScript or React-specific issues
  // but it will catch basic syntax errors like missing brackets
  const lines = loginPageContent.split('\n');
  let openBraces = 0;
  let openParens = 0;
  let openBrackets = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Count braces
    for (const char of line) {
      if (char === '{') openBraces++;
      if (char === '}') openBraces--;
      if (char === '(') openParens++;
      if (char === ')') openParens--;
      if (char === '[') openBrackets++;
      if (char === ']') openBrackets--;
    }
    
    // Check for negative counts (more closing than opening)
    if (openBraces < 0) {
      console.error(`Error: Too many closing braces at line ${i + 1}`);
      process.exit(1);
    }
    if (openParens < 0) {
      console.error(`Error: Too many closing parentheses at line ${i + 1}`);
      process.exit(1);
    }
    if (openBrackets < 0) {
      console.error(`Error: Too many closing brackets at line ${i + 1}`);
      process.exit(1);
    }
  }
  
  // Check for unclosed braces, parentheses, or brackets
  if (openBraces > 0) {
    console.error(`Error: ${openBraces} unclosed braces`);
    process.exit(1);
  }
  if (openParens > 0) {
    console.error(`Error: ${openParens} unclosed parentheses`);
    process.exit(1);
  }
  if (openBrackets > 0) {
    console.error(`Error: ${openBrackets} unclosed brackets`);
    process.exit(1);
  }
  
  console.log('Basic syntax check passed.');
  
  // Check for common React/JSX issues
  if (!loginPageContent.includes('export default function')) {
    console.warn('Warning: No default export found');
  }
  
  if (!loginPageContent.includes('return (')) {
    console.warn('Warning: No return statement found');
  }
  
  console.log('Login page appears to be valid.');
  
} catch (error) {
  console.error('Error checking login page:', error);
  process.exit(1);
}
