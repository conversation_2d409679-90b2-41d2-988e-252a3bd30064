const baseURL = 'http://localhost:3000';

async function testCompletePOSWorkflow() {
  console.log('🧪 Testing Complete POS Workflow...\n');

  try {
    // Test 1: Fetch Products
    console.log('📦 Step 1: Fetching Products...');
    const productsResponse = await fetch(`${baseURL}/api/pos/products`);
    
    if (!productsResponse.ok) {
      throw new Error(`Products API failed: ${productsResponse.status}`);
    }
    
    const products = await productsResponse.json();
    console.log(`✅ Products loaded: ${products.length} products`);
    products.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name} - ₹${product.price} (${product.category})`);
    });
    console.log('');

    // Test 2: Fetch Customers
    console.log('👥 Step 2: Fetching Customers...');
    const customersResponse = await fetch(`${baseURL}/api/pos/customers`);
    
    if (!customersResponse.ok) {
      throw new Error(`Customers API failed: ${customersResponse.status}`);
    }
    
    const customers = await customersResponse.json();
    console.log(`✅ Customers loaded: ${customers.length} customers`);
    customers.forEach((customer, index) => {
      console.log(`   ${index + 1}. ${customer.name} (${customer.loyaltyPoints} points)`);
    });
    console.log('');

    // Test 3: Create a Complete Sale
    console.log('💰 Step 3: Creating Complete Sale...');
    const saleData = {
      items: [
        {
          productId: products[0].id,
          name: products[0].name,
          price: products[0].price,
          quantity: 2,
          discount: 0,
          notes: 'Test order'
        },
        {
          productId: products[1].id,
          name: products[1].name,
          price: products[1].price,
          quantity: 1,
          discount: 10,
          notes: 'With discount'
        }
      ],
      customer: customers[1], // Regular customer with loyalty points
      discount: 0,
      tax: 0,
      paymentDetails: {
        method: 'cash',
        amountPaid: 1500,
        change: 0
      }
    };

    const salesResponse = await fetch(`${baseURL}/api/pos/sales`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(saleData)
    });

    if (!salesResponse.ok) {
      const errorData = await salesResponse.json();
      throw new Error(`Sales API failed: ${salesResponse.status} - ${errorData.error}`);
    }

    const sale = await salesResponse.json();
    console.log(`✅ Sale created successfully!`);
    console.log(`   Receipt Number: ${sale.receiptNumber}`);
    console.log(`   Customer: ${sale.customer?.name || 'Walk-in'}`);
    console.log(`   Items: ${sale.items.length}`);
    console.log(`   Total: ₹${sale.total}`);
    console.log(`   Payment: ${sale.paymentDetails.method}`);
    console.log('');

    // Test 4: Verify POS Page Accessibility
    console.log('🖥️ Step 4: Testing POS Page...');
    const pageResponse = await fetch(`${baseURL}/dashboard/pos`);
    
    if (!pageResponse.ok) {
      throw new Error(`POS page failed: ${pageResponse.status}`);
    }
    
    console.log('✅ POS page is accessible');
    console.log('');

    // Summary
    console.log('🎉 POS SYSTEM FULLY FUNCTIONAL!');
    console.log('');
    console.log('📋 Test Results:');
    console.log(`✅ Products API: ${products.length} products available`);
    console.log(`✅ Customers API: ${customers.length} customers available`);
    console.log(`✅ Sales API: Sale created with receipt ${sale.receiptNumber}`);
    console.log('✅ POS Page: Accessible and working');
    console.log('');
    console.log('🛒 Available Products:');
    products.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name} - ₹${product.price}`);
    });
    console.log('');
    console.log('🔗 Access POS at: http://localhost:3000/dashboard/pos');
    console.log('');
    console.log('✨ The POS system is ready for use!');

  } catch (error) {
    console.error('❌ POS Test Failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Make sure development server is running (npm run dev)');
    console.log('2. Check if APIs are accessible');
    console.log('3. Verify browser console for errors');
  }
}

// Run the complete test
testCompletePOSWorkflow();
