const baseURL = 'http://localhost:3000';

async function testPOSSystem() {
  console.log('🧪 Testing POS System...\n');

  try {
    // Test 1: Fetch Products
    console.log('📦 Test 1: Fetching POS Products...');
    const productsResponse = await fetch(`${baseURL}/api/pos/products`);
    
    if (!productsResponse.ok) {
      throw new Error(`Products API failed: ${productsResponse.status}`);
    }
    
    const products = await productsResponse.json();
    console.log(`✅ Products API working: ${products.length} products found`);
    console.log('Sample product:', products[0]);
    console.log('');

    // Test 2: Fetch Customers
    console.log('👥 Test 2: Fetching POS Customers...');
    const customersResponse = await fetch(`${baseURL}/api/pos/customers`);
    
    if (!customersResponse.ok) {
      throw new Error(`Customers API failed: ${customersResponse.status}`);
    }
    
    const customers = await customersResponse.json();
    console.log(`✅ Customers API working: ${customers.length} customers found`);
    console.log('Sample customer:', customers[0]);
    console.log('');

    // Test 3: Create a Test Sale
    console.log('💰 Test 3: Creating a test POS sale...');
    const saleData = {
      items: [
        {
          productId: products[0].id,
          name: products[0].name,
          price: products[0].price,
          quantity: 2,
          discount: 0,
          notes: 'Test sale item'
        }
      ],
      customer: customers[0],
      discount: 0,
      tax: 0,
      paymentDetails: {
        method: 'cash',
        amountPaid: products[0].price * 2,
        change: 0
      }
    };

    const salesResponse = await fetch(`${baseURL}/api/pos/sales`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(saleData)
    });

    if (!salesResponse.ok) {
      const errorData = await salesResponse.json();
      throw new Error(`Sales API failed: ${salesResponse.status} - ${errorData.error}`);
    }

    const sale = await salesResponse.json();
    console.log(`✅ Sales API working: Sale created with receipt ${sale.receiptNumber}`);
    console.log('Sale details:', {
      id: sale.id,
      receiptNumber: sale.receiptNumber,
      total: sale.total,
      items: sale.items.length
    });
    console.log('');

    // Test 4: Check POS Page Accessibility
    console.log('🖥️ Test 4: Checking POS page accessibility...');
    const pageResponse = await fetch(`${baseURL}/dashboard/pos`);
    
    if (!pageResponse.ok) {
      throw new Error(`POS page failed: ${pageResponse.status}`);
    }
    
    const pageContent = await pageResponse.text();
    const hasPointOfSale = pageContent.includes('Point of Sale');
    
    if (hasPointOfSale) {
      console.log('✅ POS page is accessible and contains expected content');
    } else {
      console.log('⚠️ POS page is accessible but may not be rendering correctly');
    }
    console.log('');

    console.log('🎉 All POS tests completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`- Products API: ✅ (${products.length} products)`);
    console.log(`- Customers API: ✅ (${customers.length} customers)`);
    console.log(`- Sales API: ✅ (Receipt: ${sale.receiptNumber})`);
    console.log('- POS Page: ✅ (Accessible)');
    console.log('');
    console.log('🔗 You can now access the POS system at: http://localhost:3000/dashboard/pos');

  } catch (error) {
    console.error('❌ POS System Test Failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting steps:');
    console.log('1. Make sure the development server is running (npm run dev)');
    console.log('2. Check if the database connection is working');
    console.log('3. Verify all POS API endpoints are accessible');
    console.log('4. Check browser console for any JavaScript errors');
  }
}

// Run the test
testPOSSystem();
