const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testProductDeletion() {
  try {
    console.log('🧪 Testing product deletion functionality...');

    // First, let's see if we have any products
    const products = await prisma.product.findMany({
      take: 5
    });

    console.log(`Found ${products.length} products in database`);

    if (products.length === 0) {
      console.log('No products found to test deletion');
      return;
    }

    // Let's check what related records exist for the first product
    const testProduct = products[0];
    console.log(`\nTesting with product: ${testProduct.name} (ID: ${testProduct.id})`);

    // Check related records
    const relatedRecords = {
      cartItems: await prisma.cartItem.count({ where: { productId: testProduct.id } }),
      orderItems: await prisma.orderItem.count({ where: { productId: testProduct.id } }),
      transactionItems: await prisma.transactionItem.count({ where: { productId: testProduct.id } }),
      warehouseInventory: await prisma.warehouseInventory.count({ where: { productId: testProduct.id } }),
      storeInventory: await prisma.storeInventory.count({ where: { productId: testProduct.id } }),
      inventoryTransfers: await prisma.inventoryTransfer.count({ where: { productId: testProduct.id } }),
      productRawMaterials: await prisma.productRawMaterial.count({ where: { productId: testProduct.id } }),
      wastage: await prisma.wastage.count({ where: { productId: testProduct.id } }),
      productReviews: await prisma.product_reviews.count({ where: { product_id: testProduct.id } }),
      wishlistItems: await prisma.wishlist_items.count({ where: { product_id: testProduct.id } }),
      productImages: await prisma.productImage.count({ where: { productId: testProduct.id } }),
      productRelations: await prisma.productRelation.count({ 
        where: { 
          OR: [
            { productId: testProduct.id },
            { relatedProductId: testProduct.id }
          ]
        }
      })
    };

    console.log('\nRelated records count:');
    Object.entries(relatedRecords).forEach(([table, count]) => {
      if (count > 0) {
        console.log(`  ${table}: ${count}`);
      }
    });

    const totalRelatedRecords = Object.values(relatedRecords).reduce((sum, count) => sum + count, 0);
    console.log(`\nTotal related records: ${totalRelatedRecords}`);

    if (totalRelatedRecords > 0) {
      console.log('\n⚠️  This product has related records. Testing deletion with cleanup...');
    } else {
      console.log('\n✅ This product has no related records. Safe to delete directly.');
    }

    console.log('\n🧪 Test completed. Product deletion should now work properly.');

  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testProductDeletion();
