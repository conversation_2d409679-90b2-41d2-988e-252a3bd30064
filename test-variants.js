const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testVariants() {
  try {
    console.log('🧪 Testing ProductVariant model...');

    // First, check if we can query existing products
    const products = await prisma.product.findMany({
      take: 1,
      include: {
        variants: true
      }
    });

    console.log('📊 Found products:', products.length);
    
    if (products.length > 0) {
      console.log('📦 First product:', {
        id: products[0].id,
        name: products[0].name,
        variants: products[0].variants.length
      });
    }

    // Test creating a product with variants
    console.log('\n🆕 Creating test product with variants...');
    
    const testProduct = await prisma.product.create({
      data: {
        name: 'Test Cake with Variants',
        description: 'A test cake to verify variant functionality',
        category: 'Cakes',
        price: 595,
        costPrice: 300,
        unit: 'piece',
        sku: `TEST-${Date.now()}`,
        isActive: true
      }
    });

    console.log('✅ Test product created:', testProduct.id);

    // Create variants for the test product
    const variants = [
      { weight: '0.5 Kg', price: 595, costPrice: 300, isDefault: true, sortOrder: 1 },
      { weight: '1 Kg', price: 1045, costPrice: 500, isDefault: false, sortOrder: 2 },
      { weight: '1.5 Kg', price: 1545, costPrice: 750, isDefault: false, sortOrder: 3 }
    ];

    console.log('\n🎯 Creating variants...');
    
    for (const variantData of variants) {
      const variant = await prisma.productVariant.create({
        data: {
          productId: testProduct.id,
          weight: variantData.weight,
          price: variantData.price,
          costPrice: variantData.costPrice,
          isDefault: variantData.isDefault,
          isActive: true,
          sortOrder: variantData.sortOrder,
          sku: `${testProduct.sku}-${variantData.weight.replace(/\s+/g, '')}`
        }
      });
      
      console.log(`✅ Created variant: ${variant.weight} - ₹${variant.price}`);
    }

    // Fetch the product with variants
    const productWithVariants = await prisma.product.findUnique({
      where: { id: testProduct.id },
      include: {
        variants: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    });

    console.log('\n📋 Product with variants:');
    console.log(`Product: ${productWithVariants.name}`);
    console.log(`Variants: ${productWithVariants.variants.length}`);
    
    productWithVariants.variants.forEach(variant => {
      console.log(`  - ${variant.weight}: ₹${variant.price} ${variant.isDefault ? '(Default)' : ''}`);
    });

    // Clean up - delete the test product (variants will be deleted due to cascade)
    await prisma.product.delete({
      where: { id: testProduct.id }
    });

    console.log('\n🧹 Test product and variants cleaned up');
    console.log('✅ ProductVariant model is working correctly!');

  } catch (error) {
    console.error('❌ Error testing variants:', error);
    console.error('Error details:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testVariants();
