// Test the website API after fixing the environment
async function testWebsiteAPI() {
  try {
    console.log('🧪 Testing website API after environment fix...\n');

    // Test 1: Check if admin panel API is accessible
    console.log('1. Testing admin panel API directly:');
    try {
      const response1 = await fetch('http://localhost:3000/api/products-with-variants/cmbymr1an000unid4n26ikks7');
      if (response1.ok) {
        const data1 = await response1.json();
        console.log(`   ✅ Admin API Status: ${response1.status}`);
        console.log(`   📦 Product: ${data1.name}`);
        console.log(`   🎛️ Variants: ${data1.variants?.length || 0}`);
      } else {
        console.log(`   ❌ Admin API Status: ${response1.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Admin API Error: ${error.message}`);
    }

    // Test 2: Test website API forwarding (if website is running)
    console.log('\n2. Testing website API forwarding:');
    try {
      const response2 = await fetch('http://localhost:3001/api/products-with-variants/cmbymr1an000unid4n26ikks7');
      if (response2.ok) {
        const data2 = await response2.json();
        console.log(`   ✅ Website API Status: ${response2.status}`);
        console.log(`   📦 Product: ${data2.name}`);
        console.log(`   🎛️ Variants: ${data2.variants?.length || 0}`);
        
        if (data2.variants && data2.variants.length > 0) {
          console.log('   🎯 Variant details:');
          data2.variants.forEach((v, i) => {
            console.log(`      ${i + 1}. ${v.weight} - ₹${v.price} ${v.isDefault ? '(Default)' : ''}`);
          });
        }
      } else {
        const errorText = await response2.text();
        console.log(`   ❌ Website API Status: ${response2.status}`);
        console.log(`   ❌ Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ⚠️ Website API not running: ${error.message}`);
    }

    // Test 3: Test website products list API
    console.log('\n3. Testing website products list API:');
    try {
      const response3 = await fetch('http://localhost:3001/api/products-with-variants');
      if (response3.ok) {
        const data3 = await response3.json();
        console.log(`   ✅ Products List Status: ${response3.status}`);
        console.log(`   📦 Products returned: ${data3.products?.length || 0}`);
        
        if (data3.products && data3.products.length > 0) {
          const productWithVariants = data3.products.find(p => p.variants && p.variants.length > 0);
          if (productWithVariants) {
            console.log(`   🎯 Sample product with variants: ${productWithVariants.name} (${productWithVariants.variants.length} variants)`);
          }
        }
      } else {
        console.log(`   ❌ Products List Status: ${response3.status}`);
      }
    } catch (error) {
      console.log(`   ⚠️ Products List API Error: ${error.message}`);
    }

    console.log('\n📋 Summary:');
    console.log('   - Admin panel should be running on http://localhost:3000');
    console.log('   - Website should be running on http://localhost:3001');
    console.log('   - Website API should forward to admin panel API');
    console.log('   - If website API is working, variants should display correctly');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testWebsiteAPI();
