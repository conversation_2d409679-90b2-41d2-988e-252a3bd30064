// 🔧 CORS Update Script for Admin Panel
// Run this after deploying your website to update CORS settings

const fs = require('fs');
const path = require('path');

// Your new website domain (update this after deployment)
const WEBSITE_DOMAIN = 'https://your-website-domain.vercel.app';

// CORS middleware to add to API routes
const corsMiddleware = `
// CORS headers for website access
const corsHeaders = {
  'Access-Control-Allow-Origin': '${WEBSITE_DOMAIN}',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Credentials': 'true',
};

// Add CORS headers to response
function addCorsHeaders(response) {
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  return response;
}

// Handle preflight requests
export async function OPTIONS(request) {
  return addCorsHeaders(new Response(null, { status: 200 }));
}
`;

console.log('🔧 CORS Update Instructions');
console.log('============================');
console.log('');
console.log('After deploying your website, follow these steps:');
console.log('');
console.log('1. 📝 Update the WEBSITE_DOMAIN variable above with your actual domain');
console.log('2. 🔧 Add CORS headers to your admin panel API routes');
console.log('3. 🚀 Redeploy your admin panel');
console.log('');
console.log('Example API route with CORS:');
console.log('');
console.log('```javascript');
console.log('import { NextRequest, NextResponse } from "next/server";');
console.log('');
console.log('// CORS headers');
console.log('const corsHeaders = {');
console.log(`  'Access-Control-Allow-Origin': '${WEBSITE_DOMAIN}',`);
console.log(`  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',`);
console.log(`  'Access-Control-Allow-Headers': 'Content-Type, Authorization',`);
console.log('};');
console.log('');
console.log('export async function GET(request) {');
console.log('  try {');
console.log('    // Your API logic here');
console.log('    const data = await fetchData();');
console.log('    ');
console.log('    const response = NextResponse.json(data);');
console.log('    ');
console.log('    // Add CORS headers');
console.log('    Object.entries(corsHeaders).forEach(([key, value]) => {');
console.log('      response.headers.set(key, value);');
console.log('    });');
console.log('    ');
console.log('    return response;');
console.log('  } catch (error) {');
console.log('    const errorResponse = NextResponse.json({ error: "Failed" }, { status: 500 });');
console.log('    ');
console.log('    // Add CORS headers to error response too');
console.log('    Object.entries(corsHeaders).forEach(([key, value]) => {');
console.log('      errorResponse.headers.set(key, value);');
console.log('    });');
console.log('    ');
console.log('    return errorResponse;');
console.log('  }');
console.log('}');
console.log('');
console.log('// Handle preflight requests');
console.log('export async function OPTIONS(request) {');
console.log('  const response = new Response(null, { status: 200 });');
console.log('  ');
console.log('  Object.entries(corsHeaders).forEach(([key, value]) => {');
console.log('    response.headers.set(key, value);');
console.log('  });');
console.log('  ');
console.log('  return response;');
console.log('}');
console.log('```');
console.log('');
console.log('🎯 Apply this pattern to all API routes that the website will use:');
console.log('- /api/products');
console.log('- /api/categories');
console.log('- /api/orders');
console.log('- /api/customers');
console.log('- And any other routes your website calls');
console.log('');
console.log('✅ After updating CORS, your website will be able to communicate with the admin panel APIs!');

// Create a template file for easy copying
const templateContent = `// Template for adding CORS to API routes
import { NextRequest, NextResponse } from 'next/server';

// CORS headers - UPDATE THE DOMAIN AFTER WEBSITE DEPLOYMENT
const corsHeaders = {
  'Access-Control-Allow-Origin': '${WEBSITE_DOMAIN}',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Credentials': 'true',
};

// Helper function to add CORS headers
function addCorsHeaders(response) {
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  return response;
}

// Handle preflight requests
export async function OPTIONS(request) {
  return addCorsHeaders(new Response(null, { status: 200 }));
}

// Example GET route with CORS
export async function GET(request) {
  try {
    // Your API logic here
    const data = { message: 'Hello from API' };
    
    const response = NextResponse.json(data);
    return addCorsHeaders(response);
  } catch (error) {
    const errorResponse = NextResponse.json(
      { error: 'Internal Server Error' }, 
      { status: 500 }
    );
    return addCorsHeaders(errorResponse);
  }
}

// Example POST route with CORS
export async function POST(request) {
  try {
    const body = await request.json();
    
    // Your API logic here
    const result = { success: true, data: body };
    
    const response = NextResponse.json(result);
    return addCorsHeaders(response);
  } catch (error) {
    const errorResponse = NextResponse.json(
      { error: 'Internal Server Error' }, 
      { status: 500 }
    );
    return addCorsHeaders(errorResponse);
  }
}
`;

fs.writeFileSync('cors-template.js', templateContent);
console.log('');
console.log('📄 Created cors-template.js file for easy reference!');
console.log('');
console.log('🚀 Ready to deploy your website separately!');
