// This script updates the UserRole enum to include CUSTOMER
const { Client } = require('pg');
require('dotenv').config();

async function updateUserRoleEnum() {
  try {
    console.log('Updating UserRole enum to include CUSTOMER...');
    
    // Parse the DATABASE_URL to get connection details
    const url = process.env.DATABASE_URL;
    const match = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):?(\d+)?\/([^?]+)\?(.+)/);
    
    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }
    
    const [, user, password, host, port = '5432', database, params] = match;
    const schema = 'bakery'; // Explicitly use the bakery schema
    
    // Create a new PostgreSQL client
    const client = new Client({
      user,
      password,
      host,
      port,
      database,
      ssl: {
        rejectUnauthorized: false
      }
    });
    
    // Connect to the database
    await client.connect();
    console.log('Connected to the database');
    
    // Check if CUSTOMER already exists in the enum
    const checkQuery = `
      SELECT EXISTS (
        SELECT 1 FROM pg_type t
        JOIN pg_enum e ON t.oid = e.enumtypid
        WHERE t.typname = 'userrole'
        AND e.enumlabel = 'CUSTOMER'
      );
    `;
    const checkResult = await client.query(checkQuery);
    
    if (checkResult.rows[0].exists) {
      console.log('UserRole enum already has CUSTOMER value');
    } else {
      // Update the enum to include CUSTOMER
      const updateQuery = `
        ALTER TYPE "userrole" ADD VALUE 'CUSTOMER';
      `;
      await client.query(updateQuery);
      console.log('Successfully added CUSTOMER to UserRole enum');
    }
    
    // Verify the enum values
    const verifyQuery = `
      SELECT e.enumlabel
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      WHERE t.typname = 'userrole'
      ORDER BY e.enumsortorder;
    `;
    const verifyResult = await client.query(verifyQuery);
    
    console.log('\nUserRole enum values:');
    verifyResult.rows.forEach(row => {
      console.log(`- ${row.enumlabel}`);
    });
    
    // Disconnect from the database
    await client.end();
    console.log('\nUserRole enum update completed successfully!');
  } catch (error) {
    console.error('Error updating UserRole enum:', error);
    process.exit(1);
  }
}

// Run the function
updateUserRoleEnum();
