-- Update existing order statuses to match new enum values
-- This script safely migrates existing order data

BEGIN;

-- First, update existing orders to use new status values
UPDATE "bakery"."orders" 
SET status = 'PENDING_ASSIGNMENT' 
WHERE status = 'PENDING';

UPDATE "bakery"."orders" 
SET status = 'ASSIGNED' 
WHERE status = 'PROCESSING';

UPDATE "bakery"."orders" 
SET status = 'DELIVERED' 
WHERE status = 'SHIPPED';

UPDATE "bakery"."orders" 
SET status = 'CANCELLED' 
WHERE status = 'RETURNED';

-- Create new enum type
CREATE TYPE "OrderStatus_new" AS ENUM (
  'PENDING_ASSIGNMENT',
  'ASSIGNED',
  'IN_PROGRESS',
  'COMPLETED',
  'DELIVERED',
  'CANCELLED'
);

-- Update the column to use the new enum
ALTER TABLE "bakery"."orders" 
ALTER COLUMN status TYPE "OrderStatus_new" 
USING status::text::"OrderStatus_new";

-- Drop the old enum and rename the new one
DROP TYPE "OrderStatus";
ALTER TYPE "OrderStatus_new" RENAME TO "OrderStatus";

COMMIT;
