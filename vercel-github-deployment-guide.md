# Deploying to Vercel Using GitHub Integration

This guide will walk you through deploying your Next.js application to Vercel using GitHub integration. This approach bypasses local build issues and leverages Vercel's optimized build environment.

## Prerequisites

- Your code is already in a GitHub repository (confirmed: https://github.com/BhardwajVaishnavi/mispri24.git)
- You have a Vercel account (or can create one)

## Step 1: Prepare Your Repository

Before deploying, make sure your repository is up to date with your latest changes:

1. Commit any pending changes:
   ```bash
   git add .
   git commit -m "Prepare for Vercel deployment"
   ```

2. Push to GitHub:
   ```bash
   git push origin main
   ```

## Step 2: Create a Vercel Configuration File

Create a `vercel.json` file in your project root to optimize the deployment:

```json
{
  "version": 2,
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "env": {
    "NEXT_TELEMETRY_DISABLED": "1",
    "NEXT_DISABLE_ESLINT": "1"
  }
}
```

This configuration:
- Specifies that you're using Next.js
- Disables telemetry (which can help avoid permission issues)
- Disables ESLint during build (to avoid build failures due to linting)

## Step 3: Connect Vercel to GitHub

1. Go to [Vercel's website](https://vercel.com/) and sign in (or create an account)
2. Click on "Add New..." > "Project"
3. Under "Import Git Repository", select "GitHub" and click "Continue"
4. If prompted, install the Vercel GitHub app and grant it access to your repository
5. Find and select your repository (`BhardwajVaishnavi/mispri24`)

## Step 4: Configure Project Settings

On the configuration page:

1. **Framework Preset**: Ensure "Next.js" is selected
2. **Root Directory**: Leave empty (since your Next.js app is in the root)
3. **Build Command**: `npm run build` (this should be auto-detected)
4. **Output Directory**: `.next` (this should be auto-detected)
5. **Environment Variables**: Add the following:
   - `DATABASE_URL`: Your NeonDB PostgreSQL connection string
   - `NEXT_TELEMETRY_DISABLED`: `1`
   - `NEXT_DISABLE_ESLINT`: `1`

## Step 5: Deploy

1. Click "Deploy"
2. Vercel will clone your repository, install dependencies, and build your application
3. Once the deployment is complete, you'll receive a URL where your application is live

## Step 6: Set Up Custom Domain (Optional)

If you want to use a custom domain:

1. Go to your project dashboard in Vercel
2. Click on "Settings" > "Domains"
3. Add your custom domain and follow the instructions to configure DNS settings

## Troubleshooting

If you encounter issues during deployment:

1. **Build Errors**:
   - Check the build logs in Vercel for specific error messages
   - You can modify the build settings in your project settings

2. **Database Connection Issues**:
   - Make sure your `DATABASE_URL` environment variable is correctly set
   - Ensure your database is accessible from Vercel's servers

3. **Missing Dependencies**:
   - Check that all dependencies are listed in your `package.json`
   - If using private packages, make sure Vercel has access to them

## Continuous Deployment

One of the benefits of using GitHub integration is continuous deployment:

1. Any push to your main branch will trigger a new deployment
2. You can configure preview deployments for pull requests
3. You can set up branch deployments for different environments (e.g., staging, production)

## Monitoring and Logs

After deployment:

1. Go to your project dashboard in Vercel
2. Click on "Deployments" to see all deployments
3. Click on a deployment to see build logs and runtime logs
4. Use the "Functions" tab to see serverless function performance

## Next Steps

After successful deployment:

1. Set up monitoring and analytics
2. Configure custom domains
3. Set up environment variables for different environments
4. Explore Vercel's Edge Functions and other features
