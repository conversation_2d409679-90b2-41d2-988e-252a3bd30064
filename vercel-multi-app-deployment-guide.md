# Deploying Admin Panel and Website as Separate Applications on Vercel

This guide will help you deploy both your admin panel and website as separate applications on Vercel, each with its own URL.

## Overview

Based on your project structure, you have:
1. An admin panel in the root directory
2. A website in the `/website` directory

We'll deploy these as separate Vercel projects, each with its own URL.

## Step 1: Deploy the Admin Panel

### Prepare the Admin Panel Repository

1. Make sure you're in the root directory of your project:
   ```bash
   cd E:\Ongoing\mispri24
   ```

2. Commit any pending changes:
   ```bash
   git add .
   git commit -m "Prepare admin panel for Vercel deployment"
   git push origin main
   ```

### Deploy to Vercel

1. Go to [Vercel's website](https://vercel.com/) and sign in
2. Click on "Add New..." > "Project"
3. Under "Import Git Repository", select "GitHub" and click "Continue"
4. Find and select your repository (`BhardwajVaishnavi/mispri24`)
5. Configure the project:
   - **Framework Preset**: Next.js
   - **Root Directory**: Leave empty (since the admin panel is in the root)
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Environment Variables**: Add your environment variables, including:
     - `DATABASE_URL`: Your NeonDB PostgreSQL connection string
     - `NEXT_TELEMETRY_DISABLED`: `1`
     - `NEXT_DISABLE_ESLINT`: `1`
6. Click "Deploy"

### Set Up Custom Domain (Optional)

1. Go to your project dashboard in Vercel
2. Click on "Settings" > "Domains"
3. Add your custom domain (e.g., `admin.yourdomain.com`) and follow the instructions

## Step 2: Deploy the Website as a Separate Application

### Create a New Repository for the Website

Since Vercel doesn't support deploying multiple applications from different directories in the same repository, we'll create a new repository for the website:

1. Create a new GitHub repository (e.g., `mispri24-website`)
2. Clone your existing repository to a temporary location:
   ```bash
   git clone https://github.com/BhardwajVaishnavi/mispri24.git mispri24-temp
   ```
3. Copy the website directory to a new location:
   ```bash
   mkdir mispri24-website
   cp -r mispri24-temp/website/* mispri24-website/
   ```
4. Initialize a new Git repository in the website directory:
   ```bash
   cd mispri24-website
   git init
   git add .
   git commit -m "Initial commit for website"
   git remote add origin https://github.com/BhardwajVaishnavi/mispri24-website.git
   git push -u origin main
   ```

### Prepare the Website for Deployment

1. Create a `vercel.json` file in the website directory:
   ```json
   {
     "version": 2,
     "buildCommand": "npm run build",
     "outputDirectory": ".next",
     "framework": "nextjs",
     "env": {
       "NEXT_TELEMETRY_DISABLED": "1",
       "NEXT_DISABLE_ESLINT": "1",
       "NEXT_PUBLIC_API_URL": "https://mispri24.vercel.app/api"
     }
   }
   ```

2. Make sure the website's `next.config.js` is compatible with Vercel

3. Create a `.env.production` file with your environment variables

### Deploy the Website to Vercel

1. Go to [Vercel's website](https://vercel.com/) and sign in
2. Click on "Add New..." > "Project"
3. Under "Import Git Repository", select "GitHub" and click "Continue"
4. Find and select your new website repository
5. Configure the project:
   - **Framework Preset**: Next.js
   - **Root Directory**: Leave empty
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Environment Variables**: Add your environment variables, including:
     - `DATABASE_URL`: Your NeonDB PostgreSQL connection string
     - `NEXT_PUBLIC_API_URL`: The URL of your admin panel API (e.g., `https://mispri24.vercel.app/api`)
     - `NEXT_TELEMETRY_DISABLED`: `1`
     - `NEXT_DISABLE_ESLINT`: `1`
6. Click "Deploy"

### Set Up Custom Domain (Optional)

1. Go to your project dashboard in Vercel
2. Click on "Settings" > "Domains"
3. Add your custom domain (e.g., `www.yourdomain.com`) and follow the instructions

## Alternative: Deploy the Website from a Subdirectory

If you prefer not to create a separate repository, you can use Vercel CLI to deploy the website from a subdirectory:

1. Install Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Navigate to the website directory:
   ```bash
   cd E:\Ongoing\mispri24\website
   ```

3. Deploy to Vercel:
   ```bash
   vercel
   ```

4. Follow the prompts to configure your project

## Connecting the Applications

Make sure the website is configured to use the admin panel's API:

1. Set the `NEXT_PUBLIC_API_URL` environment variable in the website project to point to your admin panel's API URL (e.g., `https://mispri24.vercel.app/api`)

2. Update any hardcoded URLs in your code to use this environment variable

## Monitoring and Management

After deploying both applications:

1. You can manage them separately in the Vercel dashboard
2. Each application will have its own deployment history, logs, and analytics
3. You can set up team access to allow multiple people to manage the applications

## Troubleshooting

If you encounter issues:

1. Check the build logs in Vercel for specific error messages
2. Make sure your environment variables are correctly set
3. Ensure your database is accessible from Vercel's servers
4. Check that the API URL is correctly configured in the website application
