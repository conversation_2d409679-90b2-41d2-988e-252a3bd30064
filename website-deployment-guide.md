# Mispri Website Deployment Guide

This guide provides step-by-step instructions for deploying the Mispri e-commerce website to Vercel.

## Prerequisites

1. A Vercel account
2. Access to the NeonDB PostgreSQL database (same as used by the admin panel)
3. GitHub account

## Step 1: Prepare the Repository

1. Create a new GitHub repository for your website
2. Push your code to this repository:
   ```bash
   cd website
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/mispri-website.git
   git push -u origin main
   ```

## Step 2: Deploy to Vercel

1. Go to [Vercel](https://vercel.com/) and sign in
2. Click "Add New" > "Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Next.js
   - Root Directory: Leave empty if your repository only contains the website code
   - Build Command: `npm run build`
   - Install Command: `npm install`
   - Output Directory: `.next`

5. Configure Environment Variables:
   - Add `DATABASE_URL` with your NeonDB PostgreSQL connection string (same as admin panel)
   - Add `NEXT_PUBLIC_API_URL` with your admin panel API URL (https://mispri24.vercel.app/api)

6. Click "Deploy"

## Step 3: Configure Custom Domain (Optional)

1. In your Vercel project dashboard, go to "Settings" > "Domains"
2. Add your custom domain (e.g., www.mispri.com)
3. Follow Vercel's instructions to configure your DNS settings

## Important Notes

1. **Database Connection**:
   - Both the admin panel and website use the same database
   - Make sure the `DATABASE_URL` environment variable is correctly set

2. **API Connection**:
   - The website uses the admin panel's API for data
   - Make sure the `NEXT_PUBLIC_API_URL` environment variable is correctly set

3. **Images**:
   - Images are stored in the public folder
   - Make sure all image paths are correct

4. **Environment Variables**:
   - Double-check all environment variables before deploying
   - You can update environment variables in the Vercel project settings

## Troubleshooting

If you encounter issues during deployment:

1. **Build Errors**:
   - Check the build logs in Vercel for specific error messages
   - We've configured the project to ignore TypeScript and ESLint errors during build

2. **Database Connection Issues**:
   - Make sure your `DATABASE_URL` is correct and includes the protocol (`postgresql://`)
   - Check that your database is accessible from Vercel's servers
   - Verify that your database credentials are correct

3. **API Connection Issues**:
   - Make sure your `NEXT_PUBLIC_API_URL` is correct
   - Verify that your admin panel API is accessible

## Post-Deployment

After successful deployment:

1. Test your website thoroughly
2. Check that all pages load correctly
3. Verify that API calls to the admin panel work properly
4. Test the authentication system
5. Test the checkout process

## Updating Your Website

To update your website after making changes:

1. Commit your changes to your GitHub repository
2. Vercel will automatically deploy the changes

If you need to manually trigger a deployment:

1. Go to your project in Vercel
2. Click "Deployments"
3. Click "Redeploy" on the latest deployment
