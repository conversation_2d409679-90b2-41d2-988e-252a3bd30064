# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Logs
logs
*.log

# Cache
.cache/
.parcel-cache/

# Temporary folders
tmp/
temp/

# Lock files (optional - you can keep package-lock.json)
# package-lock.json
# yarn.lock

# Build outputs
public/sw.js
public/workbox-*.js

# Database
*.db
*.sqlite

# Backup files
*.backup
*.bak

# Large files that shouldn't be in git
*.zip
*.tar.gz
*.rar
