# Deploying the Mispri Website Using Vercel CLI

This guide provides step-by-step instructions for deploying the Mispri e-commerce website directly to Vercel using the Vercel CLI, without pushing to GitHub.

## Prerequisites

1. A Vercel account
2. Node.js installed on your computer
3. Vercel CLI installed on your computer

## Step 1: Install Vercel CLI

If you haven't already installed the Vercel CLI, you can do so with the following command:

```bash
npm install -g vercel
```

## Step 2: Login to Vercel

```bash
vercel login
```

Follow the prompts to log in to your Vercel account.

## Step 3: Prepare Your Website for Deployment

1. Make sure you're in the website directory:
   ```bash
   cd website
   ```

2. Create a `.env` file with your environment variables:
   ```
   DATABASE_URL=your_postgresql_connection_string
   NEXT_PUBLIC_API_URL=https://mispri24.vercel.app/api
   ```

## Step 4: Deploy to Vercel

Run the deployment command:

```bash
vercel
```

When prompted:
- Set up and deploy: Yes
- Link to existing project: No (unless you've deployed this project before)
- Project name: mispri-website (or your preferred name)
- Directory: ./ (current directory)
- Override settings: No

Vercel will automatically detect that you're using Next.js and configure the build settings accordingly.

## Step 5: Configure Environment Variables

After the initial deployment, you'll need to set up your environment variables in the Vercel dashboard:

1. Go to your project in the Vercel dashboard
2. Click on "Settings" > "Environment Variables"
3. Add the following environment variables:
   - `DATABASE_URL`: Your NeonDB PostgreSQL connection string
   - `NEXT_PUBLIC_API_URL`: https://mispri24.vercel.app/api

## Step 6: Redeploy with Environment Variables

After setting up the environment variables, redeploy your website:

```bash
vercel --prod
```

## Step 7: Set Up Custom Domain (Optional)

1. In your Vercel project dashboard, go to "Settings" > "Domains"
2. Add your custom domain
3. Follow Vercel's instructions to configure your DNS settings

## Troubleshooting

If you encounter issues during deployment:

1. **Build Errors**:
   - Check the build logs in Vercel for specific error messages
   - Make sure your Next.js configuration is correct

2. **Database Connection Issues**:
   - Make sure your `DATABASE_URL` is correct and includes the protocol (`postgresql://`)
   - Check that your database is accessible from Vercel's servers

3. **API Connection Issues**:
   - Make sure your `NEXT_PUBLIC_API_URL` is correct
   - Verify that your admin panel API is accessible

## Updating Your Website

To update your website after making changes:

1. Make your changes locally
2. Run the deployment command again:
   ```bash
   vercel --prod
   ```

This will deploy the latest version of your website to Vercel.
