# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/bakery"

# API Configuration
NEXT_PUBLIC_API_URL="https://mispri24.vercel.app/api"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# Payment Gateway Configuration (Optional - for future use)
NEXT_PUBLIC_RAZORPAY_KEY_ID="your_razorpay_key_id"
RAZORPAY_KEY_SECRET="your_razorpay_key_secret"

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="your_stripe_publishable_key"
STRIPE_SECRET_KEY="your_stripe_secret_key"

# Email Configuration (Optional - for future use)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"

# File Upload Configuration (Optional)
CLOUDINARY_CLOUD_NAME="your_cloudinary_cloud_name"
CLOUDINARY_API_KEY="your_cloudinary_api_key"
CLOUDINARY_API_SECRET="your_cloudinary_api_secret"

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="your_ga_id"

# Security
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"
