{"name": "bakery-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^5.7.1", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.14", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "lucide-react": "^0.516.0", "next": "14.1.0", "nodemailer": "^6.9.8", "react": "^18", "react-dom": "^18", "react-icons": "^5.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.14", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8.4.31", "prisma": "^5.7.1", "tailwindcss": "^3.3.0", "typescript": "^5"}}