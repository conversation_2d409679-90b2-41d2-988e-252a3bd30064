// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String               @id @default(cuid())
  name                String
  email               String               @unique
  password            String
  role                UserRole             @default(STAFF)
  storeId             String?              @map("store_id")
  resetToken          String?              @map("reset_token")
  resetTokenExpiry    DateTime?            @map("reset_token_expiry")
  resetOTP            String?              @map("reset_otp")
  resetOTPExpiry      DateTime?            @map("reset_otp_expiry")
  createdAt           DateTime             @default(now()) @map("created_at")
  updatedAt           DateTime             @updatedAt @map("updated_at")
  customerVisits      CustomerVisit[]
  expenses            Expense[]
  orders              Order[]
  productionRecords   ProductionRecord[]
  rawMaterialEntries  RawMaterialEntry[]
  recipes             Recipe[]
  stores              Store[]
  transactions        Transaction[]

  @@map("users")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  STAFF
  CUSTOMER

  @@map("user_role")
}

model Store {
  id          String   @id @default(cuid())
  name        String
  location    String
  managerId   String   @map("manager_id")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  manager     User     @relation(fields: [managerId], references: [id])
  products    Product[]
  orders      Order[]

  @@map("stores")
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  products    Product[]

  @@map("categories")
}

model Product {
  id              String            @id @default(cuid())
  name            String
  description     String?
  price           Float
  costPrice       Float?            @map("cost_price")
  sku             String?           @unique
  categoryId      String            @map("category_id")
  storeId         String            @map("store_id")
  stockQuantity   Int               @default(0) @map("stock_quantity")
  minStockLevel   Int               @default(0) @map("min_stock_level")
  isActive        Boolean           @default(true) @map("is_active")
  imageUrl        String?           @map("image_url")
  createdAt       DateTime          @default(now()) @map("created_at")
  updatedAt       DateTime          @updatedAt @map("updated_at")
  category        Category          @relation(fields: [categoryId], references: [id])
  store           Store             @relation(fields: [storeId], references: [id])
  orderItems      OrderItem[]
  recipeProducts  RecipeProduct[]

  @@map("products")
}

model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique @map("order_number")
  customerId      String      @map("customer_id")
  storeId         String      @map("store_id")
  status          OrderStatus @default(PENDING)
  totalAmount     Float       @map("total_amount")
  paymentStatus   PaymentStatus @default(PENDING) @map("payment_status")
  paymentMethod   String?     @map("payment_method")
  deliveryAddress String?     @map("delivery_address")
  deliveryDate    DateTime?   @map("delivery_date")
  notes           String?
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")
  customer        User        @relation(fields: [customerId], references: [id])
  store           Store       @relation(fields: [storeId], references: [id])
  orderItems      OrderItem[]
  transactions    Transaction[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  READY
  DELIVERED
  CANCELLED

  @@map("order_status")
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED

  @@map("payment_status")
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String   @map("order_id")
  productId String   @map("product_id")
  quantity  Int
  unitPrice Float    @map("unit_price")
  totalPrice Float   @map("total_price")
  createdAt DateTime @default(now()) @map("created_at")
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model Transaction {
  id            String            @id @default(cuid())
  orderId       String?           @map("order_id")
  userId        String            @map("user_id")
  type          TransactionType
  amount        Float
  description   String?
  paymentMethod String?           @map("payment_method")
  status        TransactionStatus @default(PENDING)
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")
  order         Order?            @relation(fields: [orderId], references: [id])
  user          User              @relation(fields: [userId], references: [id])

  @@map("transactions")
}

enum TransactionType {
  SALE
  REFUND
  EXPENSE
  ADJUSTMENT

  @@map("transaction_type")
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED

  @@map("transaction_status")
}

model RawMaterial {
  id                  String              @id @default(cuid())
  name                String              @unique
  unit                String
  costPerUnit         Float               @map("cost_per_unit")
  currentStock        Float               @default(0) @map("current_stock")
  minStockLevel       Float               @default(0) @map("min_stock_level")
  supplierId          String?             @map("supplier_id")
  isActive            Boolean             @default(true) @map("is_active")
  createdAt           DateTime            @default(now()) @map("created_at")
  updatedAt           DateTime            @updatedAt @map("updated_at")
  supplier            Supplier?           @relation(fields: [supplierId], references: [id])
  rawMaterialEntries  RawMaterialEntry[]
  recipeIngredients   RecipeIngredient[]

  @@map("raw_materials")
}

model Supplier {
  id           String        @id @default(cuid())
  name         String
  contactPerson String?      @map("contact_person")
  email        String?       @unique
  phone        String?
  address      String?
  isActive     Boolean       @default(true) @map("is_active")
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  rawMaterials RawMaterial[]

  @@map("suppliers")
}

model RawMaterialEntry {
  id              String      @id @default(cuid())
  rawMaterialId   String      @map("raw_material_id")
  userId          String      @map("user_id")
  type            EntryType
  quantity        Float
  unitCost        Float?      @map("unit_cost")
  totalCost       Float?      @map("total_cost")
  notes           String?
  createdAt       DateTime    @default(now()) @map("created_at")
  rawMaterial     RawMaterial @relation(fields: [rawMaterialId], references: [id])
  user            User        @relation(fields: [userId], references: [id])

  @@map("raw_material_entries")
}

enum EntryType {
  PURCHASE
  USAGE
  ADJUSTMENT
  WASTE

  @@map("entry_type")
}

model Recipe {
  id               String             @id @default(cuid())
  name             String             @unique
  description      String?
  instructions     String?
  prepTime         Int?               @map("prep_time")
  cookTime         Int?               @map("cook_time")
  servings         Int?
  difficulty       DifficultyLevel?
  isActive         Boolean            @default(true) @map("is_active")
  createdBy        String             @map("created_by")
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @updatedAt @map("updated_at")
  creator          User               @relation(fields: [createdBy], references: [id])
  recipeIngredients RecipeIngredient[]
  recipeProducts   RecipeProduct[]
  productionRecords ProductionRecord[]

  @@map("recipes")
}

enum DifficultyLevel {
  EASY
  MEDIUM
  HARD

  @@map("difficulty_level")
}

model RecipeIngredient {
  id              String      @id @default(cuid())
  recipeId        String      @map("recipe_id")
  rawMaterialId   String      @map("raw_material_id")
  quantity        Float
  unit            String
  notes           String?
  recipe          Recipe      @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  rawMaterial     RawMaterial @relation(fields: [rawMaterialId], references: [id])

  @@map("recipe_ingredients")
}

model RecipeProduct {
  id        String  @id @default(cuid())
  recipeId  String  @map("recipe_id")
  productId String  @map("product_id")
  yield     Float
  recipe    Recipe  @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("recipe_products")
}

model ProductionRecord {
  id              String   @id @default(cuid())
  recipeId        String   @map("recipe_id")
  userId          String   @map("user_id")
  batchSize       Float    @map("batch_size")
  actualYield     Float?   @map("actual_yield")
  productionDate  DateTime @map("production_date")
  notes           String?
  createdAt       DateTime @default(now()) @map("created_at")
  recipe          Recipe   @relation(fields: [recipeId], references: [id])
  user            User     @relation(fields: [userId], references: [id])

  @@map("production_records")
}

model Expense {
  id          String      @id @default(cuid())
  category    String
  description String
  amount      Float
  date        DateTime
  userId      String      @map("user_id")
  receiptUrl  String?     @map("receipt_url")
  isApproved  Boolean     @default(false) @map("is_approved")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  user        User        @relation(fields: [userId], references: [id])

  @@map("expenses")
}

model CustomerVisit {
  id         String   @id @default(cuid())
  customerId String   @map("customer_id")
  visitDate  DateTime @map("visit_date")
  notes      String?
  createdAt  DateTime @default(now()) @map("created_at")
  customer   User     @relation(fields: [customerId], references: [id])

  @@map("customer_visits")
}

model Page {
  id        String   @id @default(cuid())
  title     String
  slug      String   @unique
  content   String
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("pages")
}
