import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// GET /api/customer-orders - Get user's orders
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🌐 Website API: Fetching orders for user:', userId);

    // Check if we're in development mode and should use local processing
    const isDevelopment = process.env.NODE_ENV === 'development';
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://mispri24.vercel.app/api';

    if (isDevelopment && process.env.DATABASE_URL) {
      console.log('🔄 Development mode: Fetching orders locally');

      try {
        // Import Prisma client for local processing
        const { PrismaClient } = require('@prisma/client');

        const prisma = new PrismaClient({
          datasources: {
            db: {
              url: process.env.DATABASE_URL
            }
          }
        });

        console.log('🔍 Looking for customer with userId:', userId);

        // Find customer by userId
        const customer = await prisma.customer.findUnique({
          where: { userId },
        });

        if (!customer) {
          await prisma.$disconnect();
          console.log('❌ Customer not found for userId:', userId);
          return NextResponse.json([]);
        }

        // Fetch orders for this customer
        const orders = await prisma.order.findMany({
          where: { customerId: customer.id },
          include: {
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    imageUrl: true,
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        });

        // Fetch addresses for orders that have addressId
        const ordersWithAddresses = await Promise.all(
          orders.map(async (order) => {
            if (order.addressId) {
              const address = await prisma.address.findUnique({
                where: { id: order.addressId },
                select: {
                  city: true,
                  state: true,
                  street: true,
                  postalCode: true,
                  country: true,
                }
              });
              return { ...order, address };
            }
            return order;
          })
        );

        await prisma.$disconnect();

        console.log(`✅ Found ${ordersWithAddresses.length} orders locally for user:`, userId);
        return NextResponse.json(ordersWithAddresses);

      } catch (localError) {
        console.error('❌ Local order fetch failed:', localError);

        // Ensure prisma connection is closed
        try {
          const { PrismaClient } = require('@prisma/client');
          const prisma = new PrismaClient();
          await prisma.$disconnect();
        } catch (disconnectError) {
          console.error('❌ Error disconnecting prisma:', disconnectError);
        }

        // Fall back to API forwarding
        console.log('🔄 Falling back to admin panel API due to local error');
      }
    }

    // Forward the request to the admin panel API (production or fallback)
    console.log('🔄 Forwarding to admin panel API:', API_BASE_URL);

    const response = await fetch(`${API_BASE_URL}/customer-orders?userId=${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      return NextResponse.json(
        { error: error.error || 'Failed to fetch orders' },
        { status: response.status }
      );
    }

    const orders = await response.json();
    console.log(`📋 Found ${orders.length} orders for user:`, userId);

    return NextResponse.json(orders);
  } catch (error) {
    console.error('❌ Website API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST /api/customer-orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();

    console.log('🌐 Website API: Creating order:', orderData);

    // Check if we're in development mode and should use local processing
    const isDevelopment = process.env.NODE_ENV === 'development';
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://mispri24.vercel.app/api';

    if (isDevelopment && process.env.DATABASE_URL) {
      console.log('🔄 Development mode: Processing order locally');

      // Import Prisma client for local processing
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: process.env.DATABASE_URL
          }
        }
      });

      try {
        const { userId, items, shippingAddress, paymentMethod, totalAmount, subtotal, shipping } = orderData;

        // Find or create customer
        let customer = await prisma.customer.findUnique({
          where: { userId },
        });

        if (!customer) {
          let user = await prisma.user.findUnique({
            where: { id: userId },
          });

          if (!user) {
            // Create user if doesn't exist
            const customerEmail = shippingAddress?.email || `customer-${Date.now()}@example.com`;
            const customerName = shippingAddress ?
              `${shippingAddress.firstName || ''} ${shippingAddress.lastName || ''}`.trim() :
              'Customer';

            user = await prisma.user.create({
              data: {
                name: customerName || 'Customer',
                email: customerEmail,
                password: 'temp-password',
                role: 'CUSTOMER',
              },
            });
          }

          customer = await prisma.customer.create({
            data: {
              userId: user.id,
              firstName: shippingAddress?.firstName || user.name.split(' ')[0] || '',
              lastName: shippingAddress?.lastName || user.name.split(' ').slice(1).join(' ') || '',
              phone: shippingAddress?.phone || null,
            },
          });
        }

        // Create address if provided
        let addressId = null;
        if (shippingAddress) {
          const address = await prisma.address.create({
            data: {
              customerId: customer.id,
              street: shippingAddress.street || '',
              city: shippingAddress.city || '',
              state: shippingAddress.state || '',
              postalCode: shippingAddress.pincode || shippingAddress.postalCode || '',
              country: shippingAddress.country || 'India',
              isDefault: false,
            },
          });
          addressId = address.id;
        }

        // Generate order number
        const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

        // Create order
        const order = await prisma.order.create({
          data: {
            customerId: customer.id,
            addressId,
            orderNumber,
            totalAmount: totalAmount || 0,
            subtotal: subtotal || 0,
            shipping: shipping || 0,
            status: 'PENDING_ASSIGNMENT',
            paymentMethod: paymentMethod || 'COD',
            paymentStatus: 'PENDING',
            orderType: 'ONLINE',
          },
        });

        // Create order items
        const orderItems = await Promise.all(
          items.map((item: any) =>
            prisma.orderItem.create({
              data: {
                orderId: order.id,
                productId: item.productId || item.id,
                quantity: item.quantity,
                unitPrice: item.price || item.unitPrice || 0,
              },
            })
          )
        );

        // Return complete order
        const completeOrder = await prisma.order.findUnique({
          where: { id: order.id },
          include: {
            orderItems: {
              include: {
                product: true,
              },
            },
            address: true,
            customer: {
              include: {
                user: true,
              },
            },
          },
        });

        await prisma.$disconnect();

        console.log('✅ Order created locally:', order.orderNumber);
        return NextResponse.json(completeOrder);

      } catch (localError) {
        await prisma.$disconnect();
        console.error('❌ Local order creation failed:', localError);
        // Fall back to API forwarding
      }
    }

    // Forward the request to the admin panel API (production or fallback)
    console.log('🔄 Forwarding to admin panel API:', API_BASE_URL);
    const response = await fetch(`${API_BASE_URL}/customer-orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      const error = await response.json();
      return NextResponse.json(
        { error: error.error || 'Failed to create order' },
        { status: response.status }
      );
    }

    const order = await response.json();
    console.log('✅ Website API: Order created successfully:', order.orderNumber);

    return NextResponse.json(order);
  } catch (error) {
    console.error('❌ Website API Error:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
