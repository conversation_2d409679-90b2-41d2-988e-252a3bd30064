import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// GET /api/orders/by-number/[orderNumber] - Get order by order number
export async function GET(
  request: NextRequest,
  { params }: { params: { orderNumber: string } }
) {
  try {
    const { orderNumber } = params;

    if (!orderNumber) {
      return NextResponse.json(
        { error: 'Order number is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Fetching order by number:', orderNumber);

    // Check if we're in development mode and should use local processing
    const isDevelopment = process.env.NODE_ENV === 'development';
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://mispri24.vercel.app/api';

    if (isDevelopment && process.env.DATABASE_URL) {
      console.log('🔄 Development mode: Fetching order locally');
      
      try {
        // Import Prisma client for local processing
        const { PrismaClient } = require('@prisma/client');
        
        const prisma = new PrismaClient({
          datasources: {
            db: {
              url: process.env.DATABASE_URL
            }
          }
        });

        // Find order by order number
        const order = await prisma.order.findFirst({
          where: { 
            OR: [
              { orderNumber: orderNumber },
              { id: orderNumber }
            ]
          },
          include: {
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    imageUrl: true,
                  }
                }
              }
            },
            address: true,
            customer: {
              select: {
                firstName: true,
                lastName: true,
                phone: true,
              }
            }
          }
        });

        await prisma.$disconnect();

        if (!order) {
          console.log('❌ Order not found:', orderNumber);
          return NextResponse.json(
            { error: 'Order not found' },
            { status: 404 }
          );
        }

        console.log('✅ Order found locally:', order.orderNumber || order.id);
        return NextResponse.json(order);

      } catch (localError) {
        console.error('❌ Local order fetch failed:', localError);
        // Fall back to API forwarding
      }
    }

    // Forward to admin panel API (production or fallback)
    console.log('🔄 Forwarding to admin panel API:', API_BASE_URL);

    const response = await fetch(`${API_BASE_URL}/orders/by-number/${orderNumber}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json(
          { error: 'Order not found' },
          { status: 404 }
        );
      }
      
      const error = await response.json();
      return NextResponse.json(
        { error: error.error || 'Failed to fetch order' },
        { status: response.status }
      );
    }

    const order = await response.json();
    console.log('✅ Order fetched from admin API');
    return NextResponse.json(order);

  } catch (error) {
    console.error('❌ Error fetching order by number:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    );
  }
}
