'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function TestAuthPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Test login
  const testLogin = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Testing login...');
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'customer123',
        }),
      });

      console.log('📡 Login response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Login failed:', errorData);
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();
      console.log('✅ Login successful:', data);
      setResult({ action: 'login', success: true, data });
    } catch (err: any) {
      console.error('❌ Login error:', err);
      setError(err.message || 'Login failed');
      setResult({ action: 'login', success: false, error: err.message });
    } finally {
      setLoading(false);
    }
  };

  // Test registration
  const testRegister = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Testing registration...');
      const testEmail = `test-${Date.now()}@example.com`;
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test User',
          email: testEmail,
          password: 'test123456',
          phone: '+91 9876543210',
        }),
      });

      console.log('📡 Register response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Registration failed:', errorData);
        throw new Error(errorData.error || 'Registration failed');
      }

      const data = await response.json();
      console.log('✅ Registration successful:', data);
      setResult({ action: 'register', success: true, data, testEmail });
    } catch (err: any) {
      console.error('❌ Registration error:', err);
      setError(err.message || 'Registration failed');
      setResult({ action: 'register', success: false, error: err.message });
    } finally {
      setLoading(false);
    }
  };

  // Test admin panel API directly
  const testAdminAPI = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Testing admin panel API directly...');
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://mispri24.vercel.app/api';
      
      // Test login
      const loginResponse = await fetch(`${API_BASE_URL}/auth/customer-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'customer123',
        }),
      });

      console.log('📡 Admin API login response status:', loginResponse.status);

      if (!loginResponse.ok) {
        const errorData = await loginResponse.json();
        console.error('❌ Admin API login failed:', errorData);
        throw new Error(errorData.error || 'Admin API login failed');
      }

      const loginData = await loginResponse.json();
      console.log('✅ Admin API login successful:', loginData);

      // Test registration
      const testEmail = `admin-test-${Date.now()}@example.com`;
      const registerResponse = await fetch(`${API_BASE_URL}/auth/customer-register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: 'Admin',
          lastName: 'Test',
          email: testEmail,
          password: 'test123456',
          phone: '+91 9876543210',
        }),
      });

      console.log('📡 Admin API register response status:', registerResponse.status);

      if (!registerResponse.ok) {
        const errorData = await registerResponse.json();
        console.error('❌ Admin API registration failed:', errorData);
        throw new Error(errorData.error || 'Admin API registration failed');
      }

      const registerData = await registerResponse.json();
      console.log('✅ Admin API registration successful:', registerData);

      setResult({ 
        action: 'admin-api', 
        success: true, 
        data: { login: loginData, register: registerData, testEmail } 
      });
    } catch (err: any) {
      console.error('❌ Admin API error:', err);
      setError(err.message || 'Admin API test failed');
      setResult({ action: 'admin-api', success: false, error: err.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Authentication Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Login</h2>
          <p className="text-gray-600 mb-4">
            Test login with demo credentials (<EMAIL> / customer123)
          </p>
          <button
            onClick={testLogin}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
          >
            {loading ? 'Testing...' : 'Test Login'}
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Registration</h2>
          <p className="text-gray-600 mb-4">
            Test registration with a new random email address
          </p>
          <button
            onClick={testRegister}
            disabled={loading}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded"
          >
            {loading ? 'Testing...' : 'Test Registration'}
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Admin API</h2>
          <p className="text-gray-600 mb-4">
            Test admin panel API directly (bypassing website API)
          </p>
          <button
            onClick={testAdminAPI}
            disabled={loading}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded"
          >
            {loading ? 'Testing...' : 'Test Admin API'}
          </button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-8">
          <p className="font-bold">Error</p>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <div className={`bg-${result.success ? 'green' : 'red'}-100 border border-${result.success ? 'green' : 'red'}-400 text-${result.success ? 'green' : 'red'}-700 px-4 py-3 rounded mb-8`}>
          <p className="font-bold">
            {result.action === 'login' ? 'Login' : result.action === 'register' ? 'Registration' : 'Admin API'} Test {result.success ? 'Successful' : 'Failed'}
          </p>
          <pre className="mt-2 text-sm overflow-auto max-h-60 whitespace-pre-wrap">
            {JSON.stringify(result.success ? result.data : result.error, null, 2)}
          </pre>
          {result.testEmail && (
            <p className="mt-2 text-sm">
              <strong>Test Email:</strong> {result.testEmail}
            </p>
          )}
        </div>
      )}

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold mb-2">How to Test</h3>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Click "Test Login" to test with existing demo credentials</li>
          <li>Click "Test Registration" to create a new account with random email</li>
          <li>Click "Test Admin API" to test the admin panel API directly</li>
          <li>Check the browser console for detailed logs</li>
          <li>If tests pass, try the actual login page at <Link href="/login" className="text-blue-600 hover:underline">/login</Link></li>
        </ol>
      </div>
      
      <div className="mt-8">
        <Link href="/" className="text-blue-600 hover:underline">
          Back to Home
        </Link>
      </div>
    </div>
  );
}
